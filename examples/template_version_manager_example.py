#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息模板版本管理系统使用示例
演示如何使用模板版本控制、A/B测试和效果追踪功能

使用方法:
python examples/template_version_manager_example.py
"""

import asyncio
import sys
import os
import random

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.agents.template_version_manager import (
    TemplateVersionManager, ABTestStatus
)
from backend.data.db.database_manager import DatabaseManager


async def test_template_version_creation():
    """测试模板版本创建"""
    print("=== 测试模板版本创建 ===")
    
    # 使用内存数据库进行测试
    db_manager = DatabaseManager(":memory:")
    version_manager = TemplateVersionManager(db_manager)

    # 初始化数据库表
    await version_manager.initialize_database()
    
    # 创建第一个版本
    template_v1 = await version_manager.create_template_version(
        template_id="greeting_message",
        content="您好！欢迎使用我们的服务。",
        variables=[],
        category="greeting",
        created_by="admin",
        description="基础问候消息"
    )
    
    print(f"创建模板版本: {template_v1.template_id} v{template_v1.version}")
    print(f"内容: {template_v1.content}")
    print(f"状态: {template_v1.status.value}")
    print()
    
    # 创建第二个版本
    template_v2 = await version_manager.create_template_version(
        template_id="greeting_message",
        content="您好！很高兴为您服务，{user_name}！有什么可以帮助您的吗？",
        variables=["user_name"],
        category="greeting",
        created_by="admin",
        description="个性化问候消息"
    )
    
    print(f"创建模板版本: {template_v2.template_id} v{template_v2.version}")
    print(f"内容: {template_v2.content}")
    print(f"变量: {template_v2.variables}")
    print()
    
    return version_manager, template_v1, template_v2


async def test_template_activation():
    """测试模板激活"""
    print("=== 测试模板激活 ===")
    
    version_manager, template_v1, template_v2 = await test_template_version_creation()
    
    # 激活第一个版本
    success = await version_manager.activate_template_version(
        template_id="greeting_message",
        version=template_v1.version
    )
    print(f"激活 v{template_v1.version}: {'成功' if success else '失败'}")
    
    # 获取当前激活版本
    active_template = await version_manager.get_template_version("greeting_message")
    if active_template:
        print(f"当前激活版本: v{active_template.version}")
        print(f"内容: {active_template.content}")
    print()
    
    # 激活第二个版本
    success = await version_manager.activate_template_version(
        template_id="greeting_message",
        version=template_v2.version
    )
    print(f"激活 v{template_v2.version}: {'成功' if success else '失败'}")
    
    # 再次获取当前激活版本
    active_template = await version_manager.get_template_version("greeting_message")
    if active_template:
        print(f"当前激活版本: v{active_template.version}")
        print(f"内容: {active_template.content}")
    print()
    
    return version_manager


async def test_ab_testing():
    """测试A/B测试功能"""
    print("=== 测试A/B测试功能 ===")
    
    version_manager = await test_template_activation()
    
    # 创建A/B测试
    ab_test = await version_manager.create_ab_test(
        test_name="问候消息优化测试",
        template_id="greeting_message",
        variant_a_version="1.0",
        variant_b_version="1.1",
        traffic_split=0.5,
        duration_days=7,
        target_metrics=["success_rate", "user_satisfaction"],
        created_by="product_manager",
        description="测试个性化问候消息的效果"
    )
    
    print(f"创建A/B测试: {ab_test.test_name}")
    print(f"测试ID: {ab_test.test_id}")
    print(f"变体A: v{ab_test.variant_a_version}")
    print(f"变体B: v{ab_test.variant_b_version}")
    print(f"流量分配: {ab_test.traffic_split:.1%} / {1-ab_test.traffic_split:.1%}")
    print()
    
    # 启动A/B测试（模拟）
    # 在实际应用中，这应该通过管理界面或API来操作
    await version_manager.db_manager.execute_update(
        "UPDATE ab_tests SET status = ? WHERE test_id = ?",
        (ABTestStatus.RUNNING.value, ab_test.test_id)
    )
    
    print("A/B测试已启动")
    print()
    
    return version_manager, ab_test


async def test_user_template_selection():
    """测试用户模板选择（A/B测试）"""
    print("=== 测试用户模板选择（A/B测试） ===")
    
    version_manager, ab_test = await test_ab_testing()
    
    # 模拟多个用户获取模板
    test_users = [f"user_{i:03d}" for i in range(1, 21)]
    
    variant_distribution = {"1.0": 0, "1.1": 0}
    
    for user_id in test_users:
        template, test_id = await version_manager.get_template_for_user(
            template_id="greeting_message",
            user_id=user_id
        )
        
        if template and test_id:
            variant_distribution[template.version] += 1
            print(f"用户 {user_id}: 使用变体 v{template.version} (测试: {test_id[:8]}...)")
    
    print()
    print("变体分布统计:")
    for variant, count in variant_distribution.items():
        percentage = count / len(test_users) * 100
        print(f"  变体 v{variant}: {count} 用户 ({percentage:.1f}%)")
    print()
    
    return version_manager, ab_test, test_users


async def test_usage_tracking():
    """测试使用情况追踪"""
    print("=== 测试使用情况追踪 ===")
    
    version_manager, ab_test, test_users = await test_user_template_selection()
    
    # 模拟用户使用模板并记录结果
    for user_id in test_users:
        template, test_id = await version_manager.get_template_for_user(
            template_id="greeting_message",
            user_id=user_id
        )
        
        if template:
            # 模拟使用结果
            success = random.choice([True, True, True, False])  # 75%成功率
            response_time = random.uniform(0.1, 2.0)  # 0.1-2.0秒响应时间
            user_satisfaction = random.randint(3, 5) if success else random.randint(1, 3)  # 满意度1-5
            
            # 记录使用情况
            await version_manager.record_template_usage(
                template_id="greeting_message",
                version=template.version,
                success=success,
                response_time=response_time,
                user_satisfaction=user_satisfaction,
                user_id=user_id,
                session_id=f"session_{user_id}",
                test_id=test_id
            )
    
    print(f"已记录 {len(test_users)} 次模板使用情况")
    print()
    
    return version_manager, ab_test


async def test_metrics_analysis():
    """测试指标分析"""
    print("=== 测试指标分析 ===")
    
    version_manager, ab_test = await test_usage_tracking()
    
    # 获取模板整体指标
    overall_metrics = await version_manager.get_template_metrics("greeting_message")
    print("模板整体指标:")
    print(f"  统计周期: {overall_metrics['period_days']} 天")
    
    for version, metrics in overall_metrics.get("versions", {}).items():
        print(f"  版本 v{version}:")
        print(f"    总使用次数: {metrics['total_usage']}")
        print(f"    成功次数: {metrics['total_success']}")
        print(f"    成功率: {metrics['success_rate']:.1%}")
        print(f"    平均满意度: {metrics['avg_satisfaction']:.1f}")
        print(f"    平均响应时间: {metrics['avg_response_time']:.3f}秒")
    print()
    
    # 获取A/B测试结果
    ab_results = await version_manager.get_ab_test_results(ab_test.test_id)
    print("A/B测试结果:")
    print(f"  测试名称: {ab_results['test_name']}")
    print(f"  测试状态: {ab_results['status']}")
    
    for variant, stats in ab_results.get("variant_stats", {}).items():
        print(f"  变体 v{variant}:")
        print(f"    参与用户数: {stats['total_users']}")
        print(f"    成功次数: {stats['success_count']}")
        print(f"    成功率: {stats['success_rate']:.1%}")
        print(f"    平均满意度: {stats['avg_satisfaction']:.1f}")
        print(f"    平均响应时间: {stats['avg_response_time']:.3f}秒")
    
    if ab_results.get("confidence"):
        print(f"  置信度: {ab_results['confidence']:.1%}")
    
    print(f"  建议: {ab_results['recommendation']}")
    print()
    
    return version_manager


async def test_template_listing():
    """测试模板列表功能"""
    print("=== 测试模板列表功能 ===")
    
    version_manager = await test_metrics_analysis()
    
    # 列出所有模板版本
    all_versions = await version_manager.list_template_versions()
    print("所有模板版本:")
    for version in all_versions:
        print(f"  {version['template_id']} v{version['version']} ({version['status']})")
        print(f"    类别: {version['category']}")
        print(f"    创建者: {version['created_by']}")
        print(f"    创建时间: {version['created_at']}")
        if version['description']:
            print(f"    描述: {version['description']}")
    print()
    
    # 列出特定模板的版本
    greeting_versions = await version_manager.list_template_versions(template_id="greeting_message")
    print("问候消息模板版本:")
    for version in greeting_versions:
        print(f"  v{version['version']} - {version['status']} - {version['description']}")
    print()


async def test_version_rollback():
    """测试版本回滚"""
    print("=== 测试版本回滚 ===")
    
    version_manager = await test_template_listing()
    
    # 获取当前激活版本
    current_template = await version_manager.get_template_version("greeting_message")
    print(f"当前激活版本: v{current_template.version}")
    print(f"内容: {current_template.content}")
    print()
    
    # 回滚到v1.0
    success = await version_manager.activate_template_version(
        template_id="greeting_message",
        version="1.0"
    )
    print(f"回滚到 v1.0: {'成功' if success else '失败'}")
    
    # 验证回滚结果
    rollback_template = await version_manager.get_template_version("greeting_message")
    if rollback_template:
        print(f"回滚后版本: v{rollback_template.version}")
        print(f"内容: {rollback_template.content}")
    print()


async def main():
    """主函数"""
    print("消息模板版本管理系统使用示例")
    print("=" * 50)
    
    try:
        await test_template_version_creation()
        await test_template_activation()
        await test_ab_testing()
        await test_user_template_selection()
        await test_usage_tracking()
        await test_metrics_analysis()
        await test_template_listing()
        await test_version_rollback()
        
        print("✅ 所有测试完成！")
        print("\n📋 总结:")
        print("1. ✅ 模板版本创建功能正常")
        print("2. ✅ 模板激活和切换功能正常")
        print("3. ✅ A/B测试创建和管理功能正常")
        print("4. ✅ 用户模板选择（A/B测试）功能正常")
        print("5. ✅ 使用情况追踪功能正常")
        print("6. ✅ 指标分析和A/B测试结果功能正常")
        print("7. ✅ 模板列表和查询功能正常")
        print("8. ✅ 版本回滚功能正常")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
