#!/usr/bin/env python3
"""
混合AI代理演示脚本

展示混合AI代理系统的核心功能：
1. 配置管理和功能开关
2. 知识库问答功能
3. 需求采集功能
4. 智能路由和模式切换
"""

import asyncio
import sys
import os
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from backend.agents.factory import agent_factory
from backend.config.knowledge_base_config import get_knowledge_base_config_manager


class HybridAgentDemo:
    """混合AI代理演示类"""
    
    def __init__(self):
        self.session_id = "demo_session_001"
        self.kb_config_manager = None
        self.conversation_agent = None
        
    async def initialize(self):
        """初始化演示环境"""
        try:
            print("🚀 初始化混合AI代理演示环境...")
            
            # 获取配置管理器
            self.kb_config_manager = get_knowledge_base_config_manager()
            
            # 创建对话流程Agent
            self.conversation_agent = agent_factory.get_conversation_flow_agent(self.session_id)
            
            print("✅ 初始化完成！")
            return True
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            return False
    
    def show_current_config(self):
        """显示当前配置状态"""
        print("\n📋 当前配置状态:")
        print("-" * 50)
        
        if self.kb_config_manager:
            config = self.kb_get_unified_config().get_config()
            print(f"知识库功能启用: {'✅' if config.enabled else '❌'}")
            print(f"RAG查询功能: {'✅' if config.features.get('rag_query', False) else '❌'}")
            print(f"意图识别增强: {'✅' if config.features.get('intent_enhancement', False) else '❌'}")
            print(f"模式切换功能: {'✅' if config.features.get('mode_switching', False) else '❌'}")
        else:
            print("❌ 配置管理器不可用")
        
        print("-" * 50)
    
    async def demo_basic_conversation(self):
        """演示基础对话功能"""
        print("\n🗣️  演示基础对话功能:")
        print("=" * 60)
        
        test_messages = [
            "你好！",
            "我想开发一个电商网站",
            "需要包含用户注册和商品管理功能"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n👤 用户消息 {i}: {message}")
            
            try:
                result = await self.conversation_agent.process_message_async(message)
                
                if result.get("success"):
                    reply = result.get("reply", "无回复")
                    print(f"🤖 AI回复: {reply[:200]}...")
                    
                    # 显示处理信息
                    if "mode" in result:
                        print(f"📍 处理模式: {result['mode']}")
                else:
                    print(f"❌ 处理失败: {result.get('error', '未知错误')}")
                    
            except Exception as e:
                print(f"❌ 处理异常: {e}")
            
            print("-" * 40)
    
    async def demo_knowledge_base_toggle(self):
        """演示知识库功能开关"""
        print("\n🔧 演示知识库功能开关:")
        print("=" * 60)
        
        if not self.kb_config_manager:
            print("❌ 配置管理器不可用")
            return
        
        # 显示当前状态
        print("📋 当前状态:")
        self.show_current_config()
        
        # 尝试启用知识库功能
        print("\n🔄 尝试启用知识库功能...")
        try:
            success = self.kb_get_unified_config().enable_knowledge_base()
            if success:
                print("✅ 知识库功能已启用")
                self.show_current_config()
            else:
                print("❌ 启用失败")
        except Exception as e:
            print(f"❌ 启用异常: {e}")
        
        # 测试启用后的对话
        print("\n🧪 测试启用后的对话:")
        knowledge_query = "如何注册账号？"
        print(f"👤 用户查询: {knowledge_query}")
        
        try:
            result = await self.conversation_agent.process_message_async(knowledge_query)
            if result.get("success"):
                reply = result.get("reply", "无回复")
                print(f"🤖 AI回复: {reply[:200]}...")
                
                if "mode" in result:
                    print(f"📍 处理模式: {result['mode']}")
            else:
                print(f"❌ 处理失败: {result.get('error', '未知错误')}")
        except Exception as e:
            print(f"❌ 处理异常: {e}")
        
        # 禁用知识库功能
        print("\n🔄 禁用知识库功能...")
        try:
            success = self.kb_get_unified_config().disable_knowledge_base()
            if success:
                print("✅ 知识库功能已禁用")
                self.show_current_config()
            else:
                print("❌ 禁用失败")
        except Exception as e:
            print(f"❌ 禁用异常: {e}")
    
    async def demo_hybrid_routing(self):
        """演示混合路由功能"""
        print("\n🔀 演示混合路由功能:")
        print("=" * 60)
        
        try:
            # 获取混合路由器
            router = agent_factory.get_hybrid_conversation_router()
            
            if not router:
                print("❌ 混合路由器不可用")
                return
            
            # 启用知识库功能进行测试
            if self.kb_config_manager:
                self.kb_get_unified_config().enable_knowledge_base()
            
            # 测试不同类型的消息
            test_scenarios = [
                {
                    "message": "如何注册账号？",
                    "expected_mode": "knowledge_base",
                    "description": "知识库查询"
                },
                {
                    "message": "我想开发一个社交应用",
                    "expected_mode": "requirement_collection", 
                    "description": "需求采集"
                },
                {
                    "message": "你好",
                    "expected_mode": "clarification",
                    "description": "澄清对话"
                }
            ]
            
            for i, scenario in enumerate(test_scenarios, 1):
                print(f"\n🧪 测试场景 {i}: {scenario['description']}")
                print(f"👤 用户消息: {scenario['message']}")
                
                session_context = {
                    "session_id": self.session_id,
                    "user_id": "demo_user",
                    "current_mode": None,
                    "conversation_history": [],
                    "current_state": {}
                }
                
                try:
                    result = await router.route_message(scenario['message'], session_context)
                    
                    print(f"🤖 路由结果:")
                    print(f"   成功: {'✅' if result.success else '❌'}")
                    print(f"   模式: {result.mode.value if result.mode else 'Unknown'}")
                    print(f"   回复: {result.content[:100]}...")
                    
                    if result.processing_info:
                        routing_time = result.processing_info.get('routing_time', 0)
                        print(f"   处理时间: {routing_time:.3f}s")
                    
                except Exception as e:
                    print(f"❌ 路由异常: {e}")
                
                print("-" * 40)
            
            # 显示路由统计
            print("\n📊 路由统计:")
            stats = router.get_routing_stats()
            for key, value in stats.items():
                print(f"   {key}: {value}")
            
        except Exception as e:
            print(f"❌ 混合路由演示失败: {e}")
        finally:
            # 清理：禁用知识库功能
            if self.kb_config_manager:
                self.kb_get_unified_config().disable_knowledge_base()
    
    def show_factory_info(self):
        """显示工厂信息"""
        print("\n🏭 Agent工厂信息:")
        print("=" * 60)
        
        try:
            factory_info = agent_factory.get_factory_info()
            
            print(f"工厂名称: {factory_info['factory_name']}")
            print(f"注册服务数: {factory_info['container_info']['total_services']}")
            
            print("\n📦 可用代理:")
            for agent in factory_info['available_agents']:
                status = "✅" if "hybrid" in agent or "knowledge_base" in agent else "📦"
                print(f"   {status} {agent}")
            
        except Exception as e:
            print(f"❌ 获取工厂信息失败: {e}")
    
    async def run_demo(self):
        """运行完整演示"""
        print("🎭 混合AI代理系统演示")
        print("=" * 80)
        
        # 初始化
        if not await self.initialize():
            return
        
        # 显示工厂信息
        self.show_factory_info()
        
        # 显示当前配置
        self.show_current_config()
        
        # 演示基础对话
        await self.demo_basic_conversation()
        
        # 演示知识库开关
        await self.demo_knowledge_base_toggle()
        
        # 演示混合路由
        await self.demo_hybrid_routing()
        
        print("\n🎉 演示完成！")
        print("=" * 80)


async def main():
    """主函数"""
    demo = HybridAgentDemo()
    await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
