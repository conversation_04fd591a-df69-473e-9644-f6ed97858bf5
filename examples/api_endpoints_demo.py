#!/usr/bin/env python3
"""
API端点演示脚本

展示混合AI代理系统的API端点功能：
1. 知识库查询API
2. 配置管理API
3. 安全监控API
4. 文档管理API
"""

import asyncio
import aiohttp
import json
import sys
import os
from typing import Dict, Any

# API基础URL
BASE_URL = "http://localhost:8000"


class APIEndpointsDemo:
    """API端点演示类"""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def make_request(self, method: str, endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == "GET":
                async with self.session.get(url) as response:
                    return await response.json()
            elif method.upper() == "POST":
                async with self.session.post(url, json=data) as response:
                    return await response.json()
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
                
        except Exception as e:
            return {"error": str(e), "status": "failed"}
    
    def print_response(self, title: str, response: Dict[str, Any]):
        """打印响应结果"""
        print(f"\n📡 {title}")
        print("-" * 60)
        
        if "error" in response:
            print(f"❌ 错误: {response['error']}")
        else:
            print(f"✅ 状态: {response.get('status', 'unknown')}")
            
            # 格式化输出主要数据
            if "data" in response:
                data = response["data"]
                if isinstance(data, dict):
                    for key, value in data.items():
                        if isinstance(value, dict):
                            print(f"   {key}:")
                            for sub_key, sub_value in value.items():
                                print(f"     {sub_key}: {sub_value}")
                        elif isinstance(value, list):
                            print(f"   {key}: {len(value)} 项")
                        else:
                            print(f"   {key}: {value}")
                else:
                    print(f"   数据: {data}")
            
            # 显示其他重要字段
            for key in ["message", "enabled", "answer"]:
                if key in response:
                    print(f"   {key}: {response[key]}")
        
        print("-" * 60)
    
    async def demo_knowledge_base_config(self):
        """演示知识库配置API"""
        print("\n🔧 知识库配置API演示:")
        print("=" * 80)
        
        # 1. 获取当前配置
        response = await self.make_request("GET", "/knowledge-base/config")
        self.print_response("获取知识库配置", response)
        
        # 2. 启用知识库功能
        response = await self.make_request("POST", "/knowledge-base/toggle", {
            "enabled": True,
            "reason": "API演示测试"
        })
        self.print_response("启用知识库功能", response)
        
        # 3. 再次获取配置确认
        response = await self.make_request("GET", "/knowledge-base/config")
        self.print_response("确认配置变更", response)
        
        # 4. 禁用知识库功能
        response = await self.make_request("POST", "/knowledge-base/toggle", {
            "enabled": False,
            "reason": "演示结束"
        })
        self.print_response("禁用知识库功能", response)
    
    async def demo_knowledge_base_query(self):
        """演示知识库查询API"""
        print("\n🔍 知识库查询API演示:")
        print("=" * 80)
        
        # 先启用知识库功能
        await self.make_request("POST", "/knowledge-base/toggle", {
            "enabled": True,
            "reason": "查询演示"
        })
        
        # 测试查询
        test_queries = [
            {
                "query": "如何注册账号？",
                "session_id": "demo_session_1",
                "role_filter": "company"
            },
            {
                "query": "开发者认证流程",
                "session_id": "demo_session_2",
                "role_filter": "developer"
            },
            {
                "query": "项目发布要求",
                "session_id": "demo_session_3"
            }
        ]
        
        for i, query_data in enumerate(test_queries, 1):
            response = await self.make_request("POST", "/knowledge-base/query", query_data)
            self.print_response(f"查询 {i}: {query_data['query']}", response)
        
        # 恢复原状态
        await self.make_request("POST", "/knowledge-base/toggle", {
            "enabled": False,
            "reason": "演示结束"
        })
    
    async def demo_hybrid_agent_status(self):
        """演示混合代理状态API"""
        print("\n🤖 混合代理状态API演示:")
        print("=" * 80)
        
        response = await self.make_request("GET", "/hybrid-agent/status")
        self.print_response("混合代理状态", response)
    
    async def demo_safety_management(self):
        """演示安全管理API"""
        print("\n🛡️  安全管理API演示:")
        print("=" * 80)
        
        # 1. 获取安全状态
        response = await self.make_request("GET", "/safety/status")
        self.print_response("获取安全状态", response)
        
        # 2. 强制降级
        response = await self.make_request("POST", "/safety/force-degradation", {
            "level": "degraded",
            "reason": "API演示测试"
        })
        self.print_response("强制安全降级", response)
        
        # 3. 再次检查状态
        response = await self.make_request("GET", "/safety/status")
        self.print_response("降级后的安全状态", response)
        
        # 4. 强制恢复
        response = await self.make_request("POST", "/safety/force-recovery", {
            "reason": "演示结束，恢复正常"
        })
        self.print_response("强制安全恢复", response)
        
        # 5. 重置安全指标
        response = await self.make_request("POST", "/safety/reset")
        self.print_response("重置安全指标", response)
    
    async def demo_document_management(self):
        """演示文档管理API"""
        print("\n📄 文档管理API演示:")
        print("=" * 80)
        
        # 1. 摄入测试文档
        test_document = {
            "content": """# API演示文档
            
这是一个通过API摄入的测试文档。

## 功能特点
- 支持Markdown格式
- 自动分块处理
- 元数据管理

## 使用说明
1. 准备文档内容
2. 调用摄入API
3. 查询验证结果
""",
            "source_path": "demo/api_test_document.md",
            "metadata": {
                "role": "general",
                "category": "demo",
                "tags": ["API", "测试", "演示"]
            }
        }
        
        response = await self.make_request("POST", "/knowledge-base/documents", test_document)
        self.print_response("摄入测试文档", response)
        
        # 2. 列出文档
        response = await self.make_request("GET", "/knowledge-base/documents")
        self.print_response("列出所有文档", response)
        
        # 3. 按类别过滤
        response = await self.make_request("GET", "/knowledge-base/documents?category=demo")
        self.print_response("按类别过滤文档", response)
        
        # 4. 获取统计信息
        response = await self.make_request("GET", "/knowledge-base/statistics")
        self.print_response("知识库统计信息", response)
    
    async def demo_chat_integration(self):
        """演示聊天集成"""
        print("\n💬 聊天集成演示:")
        print("=" * 80)
        
        # 启用知识库功能
        await self.make_request("POST", "/knowledge-base/toggle", {
            "enabled": True,
            "reason": "聊天集成演示"
        })
        
        # 测试聊天消息
        test_messages = [
            {
                "message": "你好，我想了解注册流程",
                "session_id": "chat_demo_1"
            },
            {
                "message": "开发者如何进行技能认证？",
                "session_id": "chat_demo_2"
            }
        ]
        
        for i, msg_data in enumerate(test_messages, 1):
            response = await self.make_request("POST", "/chat", msg_data)
            self.print_response(f"聊天消息 {i}: {msg_data['message']}", response)
        
        # 恢复原状态
        await self.make_request("POST", "/knowledge-base/toggle", {
            "enabled": False,
            "reason": "演示结束"
        })
    
    async def run_demo(self):
        """运行完整演示"""
        print("🌐 混合AI代理API端点演示")
        print("=" * 100)
        print("注意: 请确保API服务器正在运行 (python -m uvicorn backend.api.main:app --reload)")
        print("=" * 100)
        
        try:
            # 测试服务器连接
            response = await self.make_request("GET", "/")
            if "error" in response:
                print("❌ 无法连接到API服务器，请检查服务器是否运行")
                return
            
            print("✅ API服务器连接成功")
            
            # 运行各项演示
            await self.demo_knowledge_base_config()
            await self.demo_hybrid_agent_status()
            await self.demo_safety_management()
            await self.demo_document_management()
            await self.demo_knowledge_base_query()
            await self.demo_chat_integration()
            
            print("\n🎉 API端点演示完成！")
            print("=" * 100)
            
        except Exception as e:
            print(f"❌ 演示过程中出现错误: {e}")


async def main():
    """主函数"""
    async with APIEndpointsDemo() as demo:
        await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
