#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息回复监控和分析系统使用示例
演示如何使用监控系统进行实时监控、分析和告警

使用方法:
python examples/reply_monitoring_example.py
"""

import asyncio
import sys
import os
import random
import time
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.agents.reply_monitoring_system import (
    ReplyMonitoringSystem, ReplyMetric, SatisfactionLevel, AlertLevel
)
from backend.data.db.database_manager import DatabaseManager


async def test_monitoring_initialization():
    """测试监控系统初始化"""
    print("=== 测试监控系统初始化 ===")
    
    # 使用文件数据库
    db_path = "/tmp/test_reply_monitoring.db"
    if os.path.exists(db_path):
        os.remove(db_path)
    
    db_manager = DatabaseManager(db_path)
    monitoring_system = ReplyMonitoringSystem(db_manager)
    
    # 初始化数据库
    await monitoring_system.initialize_database()
    print("监控系统初始化完成")
    
    # 检查默认告警规则
    stats = monitoring_system.get_stats()
    print(f"默认告警规则数量: {stats['alert_summary']['enabled_rules']}")
    print()
    
    return monitoring_system


async def test_metric_recording():
    """测试指标记录"""
    print("=== 测试指标记录 ===")
    
    monitoring_system = await test_monitoring_initialization()
    
    # 模拟不同类型的回复指标
    test_metrics = [
        # 成功的静态回复
        ReplyMetric(
            session_id="session_001",
            user_id="user_001",
            reply_key="greeting",
            reply_type="static",
            content="您好！欢迎使用我们的服务。",
            response_time=0.1,
            success=True,
            satisfaction_score=4,
            quality_score=0.85,
            timestamp=datetime.now(),
            context={"intent": "greeting"}
        ),
        
        # 成功的动态回复
        ReplyMetric(
            session_id="session_002",
            user_id="user_002",
            reply_key="clarification",
            reply_type="dynamic",
            content="为了更好地帮助您，能否详细描述一下您的需求？",
            response_time=1.5,
            success=True,
            satisfaction_score=5,
            quality_score=0.92,
            timestamp=datetime.now(),
            context={"intent": "clarification", "llm_model": "doubao-pro-32k"}
        ),
        
        # 失败的回复（触发回退）
        ReplyMetric(
            session_id="session_003",
            user_id="user_003",
            reply_key="complex_query",
            reply_type="dynamic",
            content="抱歉，我无法理解您的问题。",
            response_time=3.2,
            success=False,
            satisfaction_score=2,
            quality_score=0.3,
            timestamp=datetime.now(),
            context={"intent": "unknown"},
            error_message="LLM调用超时",
            fallback_used=True
        ),
        
        # 响应时间较长的回复
        ReplyMetric(
            session_id="session_004",
            user_id="user_004",
            reply_key="document_generation",
            reply_type="dynamic",
            content="已为您生成需求文档...",
            response_time=8.5,  # 超过告警阈值
            success=True,
            satisfaction_score=4,
            quality_score=0.88,
            timestamp=datetime.now(),
            context={"intent": "document_generation", "llm_model": "doubao-pro-32k"}
        )
    ]
    
    # 记录指标
    for metric in test_metrics:
        await monitoring_system.record_reply_metric(metric)
        print(f"记录指标: {metric.reply_key} - 成功: {metric.success} - 响应时间: {metric.response_time}s")
    
    print(f"已记录 {len(test_metrics)} 个指标")
    print()
    
    return monitoring_system


async def test_satisfaction_feedback():
    """测试满意度反馈"""
    print("=== 测试满意度反馈 ===")
    
    monitoring_system = await test_metric_recording()
    
    # 模拟用户满意度反馈
    feedback_data = [
        ("session_001", "user_001", "greeting", 4, "回复很及时"),
        ("session_002", "user_002", "clarification", 5, "问题问得很好，帮助我澄清了需求"),
        ("session_003", "user_003", "complex_query", 2, "没有理解我的问题"),
        ("session_004", "user_004", "document_generation", 4, "文档质量不错，但生成时间有点长"),
        ("session_005", "user_005", "greeting", 3, "普通的问候"),
    ]
    
    for session_id, user_id, reply_key, score, feedback in feedback_data:
        await monitoring_system.record_satisfaction_feedback(
            session_id=session_id,
            user_id=user_id,
            reply_key=reply_key,
            satisfaction_score=score,
            feedback_text=feedback,
            context={"source": "manual_feedback"}
        )
        print(f"记录满意度反馈: {reply_key} - {score}分 - {feedback}")
    
    print(f"已记录 {len(feedback_data)} 个满意度反馈")
    print()
    
    return monitoring_system


async def test_real_time_monitoring():
    """测试实时监控"""
    print("=== 测试实时监控 ===")
    
    monitoring_system = await test_satisfaction_feedback()
    
    # 获取实时指标
    real_time_metrics = await monitoring_system.get_real_time_metrics(time_window=60)
    
    print("实时监控指标 (最近60分钟):")
    print(f"  总回复数: {real_time_metrics['total_replies']}")
    print(f"  成功率: {real_time_metrics['success_rate']:.1%}")
    print(f"  错误率: {real_time_metrics['error_rate']:.1%}")
    print(f"  回退率: {real_time_metrics['fallback_rate']:.1%}")
    
    response_time = real_time_metrics['response_time']
    print(f"  响应时间:")
    print(f"    平均: {response_time['average']:.2f}s")
    print(f"    中位数: {response_time['median']:.2f}s")
    print(f"    最大: {response_time['max']:.2f}s")
    print(f"    P95: {response_time['p95']:.2f}s")
    
    satisfaction = real_time_metrics['satisfaction']
    print(f"  用户满意度:")
    print(f"    平均分: {satisfaction['average']:.1f}")
    print(f"    反馈数量: {satisfaction['count']}")
    print(f"    分布: {satisfaction['distribution']}")
    
    print(f"  活跃告警数: {real_time_metrics['active_alerts']}")
    print(f"  回复类型分布: {real_time_metrics['reply_types']}")
    print()
    
    return monitoring_system


async def test_alert_system():
    """测试告警系统"""
    print("=== 测试告警系统 ===")
    
    monitoring_system = await test_real_time_monitoring()
    
    # 获取活跃告警
    active_alerts = await monitoring_system.get_active_alerts()
    print(f"当前活跃告警数: {len(active_alerts)}")
    
    for alert in active_alerts:
        print(f"  告警: {alert['message']}")
        print(f"    级别: {alert['alert_level']}")
        print(f"    当前值: {alert['current_value']:.3f}")
        print(f"    阈值: {alert['threshold']}")
        print(f"    持续时间: {alert['duration']}")
        print()
    
    # 获取告警历史
    alert_history = await monitoring_system.get_alert_history(hours=24)
    print(f"最近24小时告警历史: {len(alert_history)} 个")
    
    for alert in alert_history[:3]:  # 显示前3个
        print(f"  {alert['timestamp']}: {alert['message']}")
        print(f"    状态: {'已解决' if alert['resolved'] else '活跃'}")
        if alert['resolved']:
            print(f"    持续时间: {alert['duration']}")
        print()
    
    return monitoring_system


async def test_trend_analysis():
    """测试趋势分析"""
    print("=== 测试趋势分析 ===")
    
    monitoring_system = await test_alert_system()
    
    # 获取趋势分析
    trend_analysis = await monitoring_system.get_trend_analysis(days=7)
    
    print("趋势分析 (最近7天):")
    print(f"  分析周期: {trend_analysis['period']}")
    
    trends = trend_analysis.get('trends', {})
    print("  趋势方向:")
    for metric, trend in trends.items():
        metric_name = {
            "success_rate": "成功率",
            "avg_response_time": "平均响应时间",
            "avg_quality_score": "平均质量分数",
            "avg_satisfaction": "平均满意度"
        }.get(metric, metric)
        print(f"    {metric_name}: {trend}")
    
    print(f"  趋势摘要: {trend_analysis.get('summary', '暂无数据')}")
    
    # 显示每日数据
    daily_data = trend_analysis.get('daily_data', [])
    if daily_data:
        print("  每日数据:")
        for day in daily_data[-3:]:  # 显示最近3天
            print(f"    {day['date']}: 回复{day['total_replies']}次, 成功率{day['success_rate']:.1%}, 响应时间{day['avg_response_time']:.2f}s")
    
    print()
    
    return monitoring_system


async def test_satisfaction_analysis():
    """测试满意度分析"""
    print("=== 测试满意度分析 ===")
    
    monitoring_system = await test_trend_analysis()
    
    # 获取满意度分析
    satisfaction_analysis = await monitoring_system.get_satisfaction_analysis(days=7)
    
    print("满意度分析 (最近7天):")
    print(f"  分析周期: {satisfaction_analysis['period']}")
    print(f"  总反馈数: {satisfaction_analysis['total_feedback']}")
    print(f"  平均满意度: {satisfaction_analysis['average_satisfaction']:.1f}分")
    
    print("  评分分布:")
    distribution = satisfaction_analysis['score_distribution']
    for score in range(1, 6):
        count = distribution.get(str(score), 0)
        percentage = count / satisfaction_analysis['total_feedback'] * 100 if satisfaction_analysis['total_feedback'] > 0 else 0
        print(f"    {score}分: {count}次 ({percentage:.1f}%)")
    
    print("  回复类型排名 (按满意度):")
    rankings = satisfaction_analysis.get('reply_rankings', [])
    for i, ranking in enumerate(rankings[:5], 1):
        print(f"    {i}. {ranking['reply_key']}: {ranking['average_score']:.1f}分 ({ranking['total_feedback']}次反馈)")
    
    print("  洞察分析:")
    insights = satisfaction_analysis.get('insights', [])
    for insight in insights:
        print(f"    • {insight}")
    
    print()
    
    return monitoring_system


async def test_performance_report():
    """测试性能报告"""
    print("=== 测试性能报告 ===")
    
    monitoring_system = await test_satisfaction_analysis()
    
    # 生成性能报告
    performance_report = await monitoring_system.get_performance_report(days=7)
    
    print("性能报告:")
    print(f"  报告周期: {performance_report['report_period']}")
    print(f"  生成时间: {performance_report['generated_at']}")
    
    # 总体评分
    overall_score = performance_report['overall_score']
    print(f"  总体评分: {overall_score['score']}分 ({overall_score['grade']})")
    
    components = overall_score['components']
    print("  评分组成:")
    print(f"    成功率: {components['success_rate']:.1%}")
    print(f"    响应时间: {components['response_time']:.2f}s")
    print(f"    满意度: {components['satisfaction']:.1f}分")
    print(f"    错误率: {components['error_rate']:.1%}")
    
    # 系统健康状态
    print(f"  系统健康状态: {performance_report['system_health']}")
    
    # 改进建议
    print("  改进建议:")
    recommendations = performance_report.get('recommendations', [])
    for i, rec in enumerate(recommendations, 1):
        print(f"    {i}. {rec}")
    
    # 告警摘要
    alerts = performance_report.get('alerts', {})
    active_alerts = alerts.get('active', [])
    print(f"  当前活跃告警: {len(active_alerts)}个")
    
    print()
    
    return monitoring_system


async def test_system_stats():
    """测试系统统计"""
    print("=== 测试系统统计 ===")
    
    monitoring_system = await test_performance_report()
    
    # 获取系统统计
    stats = monitoring_system.get_stats()
    
    print("系统统计信息:")
    print(f"  总回复数: {stats['total_replies']}")
    print(f"  成功回复数: {stats['successful_replies']}")
    print(f"  失败回复数: {stats['failed_replies']}")
    print(f"  回退回复数: {stats['fallback_replies']}")
    print(f"  平均响应时间: {stats['average_response_time']:.2f}s")
    print(f"  平均满意度: {stats['average_satisfaction']:.1f}分")
    print(f"  平均质量分数: {stats['average_quality_score']:.2f}")
    
    print("  缓存大小:")
    cache_sizes = stats['cache_sizes']
    print(f"    指标缓存: {cache_sizes['metrics']}")
    print(f"    满意度缓存: {cache_sizes['satisfaction']}")
    print(f"    响应时间缓存: {cache_sizes['response_time']}")
    
    print("  告警摘要:")
    alert_summary = stats['alert_summary']
    print(f"    活跃告警: {alert_summary['active_alerts']}")
    print(f"    总规则数: {alert_summary['total_rules']}")
    print(f"    启用规则数: {alert_summary['enabled_rules']}")
    
    print()


async def main():
    """主函数"""
    print("消息回复监控和分析系统使用示例")
    print("=" * 50)
    
    try:
        await test_monitoring_initialization()
        await test_metric_recording()
        await test_satisfaction_feedback()
        await test_real_time_monitoring()
        await test_alert_system()
        await test_trend_analysis()
        await test_satisfaction_analysis()
        await test_performance_report()
        await test_system_stats()
        
        print("✅ 所有测试完成！")
        print("\n📋 总结:")
        print("1. ✅ 监控系统初始化功能正常")
        print("2. ✅ 指标记录和统计功能正常")
        print("3. ✅ 满意度反馈收集功能正常")
        print("4. ✅ 实时监控功能正常")
        print("5. ✅ 告警系统功能正常")
        print("6. ✅ 趋势分析功能正常")
        print("7. ✅ 满意度分析功能正常")
        print("8. ✅ 性能报告生成功能正常")
        print("9. ✅ 系统统计功能正常")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
