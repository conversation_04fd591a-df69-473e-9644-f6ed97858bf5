#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态LLM回复生成器使用示例
演示如何使用新的统一动态回复生成系统

使用方法:
python examples/dynamic_reply_generator_example.py
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.agents.dynamic_reply_generator import (
    DynamicReplyGenerator, DynamicReplyFactory, GenerationContext, 
    LLMCallConfig, PromptStrategy
)


class MockLLMClient:
    """模拟LLM客户端用于测试"""
    
    def __init__(self):
        self.call_count = 0
        self.responses = {
            "greeting_generator": "您好！我是AI需求采集助手，很高兴为您服务。请问您今天有什么项目需求需要我帮助整理吗？",
            "empathy_generator": "我理解您现在可能感到有些困惑或沮丧。让我们一步步来梳理您的需求，我会尽力帮助您找到最合适的解决方案。",
            "clarification_generator": "为了更好地理解您的需求，能否请您详细描述一下具体的使用场景和期望达到的效果呢？",
            "apology_generator": "非常抱歉给您带来了困扰。为了能够更好地为您服务，请您告诉我具体需要改进的地方，我会认真对待您的反馈。",
            "question_polisher": "针对您提到的电商平台需求，我想了解一下：您希望这个平台主要面向哪类用户群体？比如是B2C的消费者购物平台，还是B2B的企业采购平台？",
            "domain_guidance_generator": "根据您的描述，我想进一步了解：这个项目的主要目标用户是谁？预期的用户规模大概是多少？这将帮助我们更好地规划技术方案。"
        }
    
    async def call_llm(self, messages, agent_name=None, temperature=0.7, max_tokens=200, **kwargs):
        """模拟LLM调用"""
        self.call_count += 1
        
        # 模拟网络延迟
        await asyncio.sleep(0.1)
        
        # 根据agent_name返回不同的回复
        content = self.responses.get(agent_name, "这是一个模拟的LLM回复。")
        
        return {
            "content": content,
            "usage": {
                "total_tokens": len(content)
            }
        }


async def test_basic_generation():
    """测试基本的动态回复生成"""
    print("=== 测试基本动态回复生成 ===")
    
    mock_llm = MockLLMClient()
    generator = DynamicReplyGenerator(llm_client=mock_llm)
    
    # 测试简单策略
    context = GenerationContext(
        prompt_instruction="用户向你问候，请友好回应并简要介绍你是一个AI需求采集助手。",
        user_message="你好",
        session_id="test_session_001"
    )
    
    config = LLMCallConfig(
        agent_name="greeting_generator",
        temperature=0.7,
        max_tokens=150
    )
    
    reply = await generator.generate_reply(
        context=context,
        config=config,
        strategy=PromptStrategy.SIMPLE,
        fallback_message="您好！很高兴为您服务。"
    )
    
    print(f"问候回复: {reply}")
    print()


async def test_factory_methods():
    """测试工厂方法"""
    print("=== 测试工厂方法 ===")
    
    mock_llm = MockLLMClient()
    generator = DynamicReplyGenerator(llm_client=mock_llm)
    factory = DynamicReplyFactory(generator)
    
    # 测试问候回复
    greeting_reply = await factory.generate_greeting_reply(
        prompt_instruction="用户向你问候，请友好回应。",
        user_message="你好",
        session_id="test_session_002"
    )
    print(f"工厂方法 - 问候回复: {greeting_reply}")
    
    # 测试共情回复
    empathy_reply = await factory.generate_empathy_reply(
        prompt_instruction="用户表达了负面情绪，请先共情再引导。",
        user_message="我很沮丧，这个项目太复杂了",
        session_id="test_session_002"
    )
    print(f"工厂方法 - 共情回复: {empathy_reply}")
    
    # 测试澄清回复
    clarification_reply = await factory.generate_clarification_reply(
        prompt_instruction="用户的回答不够清晰，请礼貌地请求更多细节。",
        user_message="我想做个网站",
        session_id="test_session_002"
    )
    print(f"工厂方法 - 澄清回复: {clarification_reply}")
    
    print()


async def test_template_based_generation():
    """测试基于模板的生成"""
    print("=== 测试基于模板的生成 ===")
    
    mock_llm = MockLLMClient()
    generator = DynamicReplyGenerator(llm_client=mock_llm)
    factory = DynamicReplyFactory(generator)
    
    # 测试优化问题生成（新方法，一步到位）
    optimized_question = await factory.generate_optimized_question(
        name="功能需求",
        description="请描述您的功能需求",
        user_input="我想开发一个电商平台",
        project_type="软件开发",
        session_id="test_session_003"
    )
    print(f"优化问题: {optimized_question}")
    
    # 测试澄清问题生成
    clarification_question = await factory.generate_clarification_question(
        focus_point_name="目标用户",
        focus_point_description="明确项目的目标用户群体",
        user_answer="主要是年轻人",
        conversation_history="user: 我想开发一个电商平台\nassistant: 好的，您的目标用户是谁？\nuser: 主要是年轻人",
        session_id="test_session_003"
    )
    print(f"澄清问题: {clarification_question}")
    
    print()


async def test_error_handling():
    """测试错误处理和回退机制"""
    print("=== 测试错误处理和回退机制 ===")
    
    # 测试没有LLM客户端的情况
    generator_without_llm = DynamicReplyGenerator(llm_client=None)
    
    context = GenerationContext(
        prompt_instruction="测试提示",
        user_message="测试消息"
    )
    
    reply = await generator_without_llm.generate_reply(
        context=context,
        fallback_message="这是回退消息"
    )
    print(f"无LLM客户端时的回退: {reply}")
    
    # 测试LLM调用失败的情况
    class FailingLLMClient:
        async def call_llm(self, **kwargs):
            raise Exception("模拟LLM调用失败")
    
    generator_with_failing_llm = DynamicReplyGenerator(llm_client=FailingLLMClient())
    
    reply = await generator_with_failing_llm.generate_reply(
        context=context,
        fallback_message="LLM失败时的回退消息"
    )
    print(f"LLM失败时的回退: {reply}")
    
    print()


async def test_caching():
    """测试缓存功能"""
    print("=== 测试缓存功能 ===")
    
    mock_llm = MockLLMClient()
    generator = DynamicReplyGenerator(llm_client=mock_llm)
    
    context = GenerationContext(
        prompt_instruction="测试缓存",
        user_message="相同的输入"
    )
    
    config = LLMCallConfig(
        agent_name="test_agent",
        enable_cache=True,
        cache_ttl=60
    )
    
    # 第一次调用
    start_calls = mock_llm.call_count
    reply1 = await generator.generate_reply(context=context, config=config)
    calls_after_first = mock_llm.call_count
    
    # 第二次调用（应该使用缓存）
    reply2 = await generator.generate_reply(context=context, config=config)
    calls_after_second = mock_llm.call_count
    
    print(f"第一次调用: {reply1}")
    print(f"第二次调用: {reply2}")
    print(f"第一次调用后LLM调用次数: {calls_after_first - start_calls}")
    print(f"第二次调用后LLM调用次数: {calls_after_second - calls_after_first}")
    print(f"缓存是否生效: {'是' if calls_after_second == calls_after_first else '否'}")
    
    print()


async def test_performance_stats():
    """测试性能统计"""
    print("=== 测试性能统计 ===")
    
    mock_llm = MockLLMClient()
    generator = DynamicReplyGenerator(llm_client=mock_llm)
    factory = DynamicReplyFactory(generator)
    
    # 执行多次调用
    for i in range(5):
        await factory.generate_greeting_reply(
            prompt_instruction=f"测试提示 {i}",
            user_message=f"测试消息 {i}"
        )
    
    # 获取统计信息
    stats = generator.get_stats()
    print("性能统计:")
    print(f"  总调用次数: {stats['total_calls']}")
    print(f"  成功次数: {stats['successful_calls']}")
    print(f"  失败次数: {stats['failed_calls']}")
    print(f"  成功率: {stats['success_rate']:.2%}")
    print(f"  平均响应时间: {stats['average_response_time']:.3f}秒")
    print(f"  质量分布: {stats['quality_distribution']}")
    
    print()


async def test_conversation_flow_integration():
    """测试与ConversationFlow的集成"""
    print("=== 测试ConversationFlow集成 ===")
    
    mock_llm = MockLLMClient()
    generator = DynamicReplyGenerator(llm_client=mock_llm)
    factory = DynamicReplyFactory(generator)
    
    # 模拟ConversationFlow中的handler方法
    async def handle_greeting_optimized(message: str, decision_result: dict):
        """优化后的问候处理方法"""
        prompt_instruction = decision_result.get('decision', {}).get('prompt_instruction')
        if prompt_instruction:
            return await factory.generate_greeting_reply(
                prompt_instruction=prompt_instruction,
                user_message=message
            )
        else:
            # 使用配置化的回退回复
            from backend.config.unified_config_loader import get_unified_config
            return get_unified_config().get_message_template("interaction.greeting.simple", "您好！很高兴为您服务。请问有什么可以帮您？")
    
    async def handle_empathy_optimized(message: str, decision_result: dict):
        """优化后的共情处理方法"""
        prompt_instruction = decision_result.get('decision', {}).get('prompt_instruction')
        if prompt_instruction:
            return await factory.generate_empathy_reply(
                prompt_instruction=prompt_instruction,
                user_message=message
            )
        else:
            # 使用配置化的回退回复
            from backend.config.unified_config_loader import get_unified_config
            return get_unified_config().get_message_template("user_interaction.defaults.fallback_default", "我理解您的感受。请问有什么我可以帮助您的吗？")
    
    # 测试优化后的方法
    decision_result = {
        "decision": {
            "prompt_instruction": "用户向你问候，请友好回应并介绍自己。"
        }
    }
    
    greeting_reply = await handle_greeting_optimized("你好", decision_result)
    print(f"集成测试 - 问候回复: {greeting_reply}")
    
    empathy_decision = {
        "decision": {
            "prompt_instruction": "用户表达了困惑，请表示理解并提供帮助。"
        }
    }
    
    empathy_reply = await handle_empathy_optimized("我很困惑", empathy_decision)
    print(f"集成测试 - 共情回复: {empathy_reply}")
    
    print()


async def main():
    """主函数"""
    print("动态LLM回复生成器使用示例")
    print("=" * 50)
    
    try:
        await test_basic_generation()
        await test_factory_methods()
        await test_template_based_generation()
        await test_error_handling()
        await test_caching()
        await test_performance_stats()
        await test_conversation_flow_integration()
        
        print("✅ 所有测试完成！")
        print("\n📋 总结:")
        print("1. ✅ 基本动态回复生成功能正常")
        print("2. ✅ 工厂方法便捷调用正常")
        print("3. ✅ 基于模板的生成正常")
        print("4. ✅ 错误处理和回退机制正常")
        print("5. ✅ 缓存功能正常")
        print("6. ✅ 性能统计功能正常")
        print("7. ✅ ConversationFlow集成正常")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
