#!/usr/bin/env python3
"""
安全管理器演示脚本

展示安全管理器的核心功能：
1. 错误监控和统计
2. 自动降级和恢复
3. 安全状态查询
4. 强制操作
"""

import asyncio
import sys
import os
import time
import random
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from backend.utils.safety_manager import get_safety_manager, SafetyLevel


class SafetyManagerDemo:
    """安全管理器演示类"""
    
    def __init__(self):
        self.safety_manager = get_safety_manager()
        
    def show_safety_status(self):
        """显示安全状态"""
        status = self.safety_manager.get_safety_status()
        
        print("\n🛡️  安全状态:")
        print("-" * 50)
        print(f"安全级别: {self._get_level_emoji(status['safety_level'])} {status['safety_level'].upper()}")
        print(f"知识库启用: {'✅' if status['kb_enabled'] else '❌'}")
        print(f"自动恢复: {'✅' if status['auto_recovery_enabled'] else '❌'}")
        
        metrics = status['metrics']
        print(f"\n📊 指标:")
        print(f"   总请求数: {metrics['total_requests']}")
        print(f"   成功请求: {metrics['successful_requests']}")
        print(f"   失败请求: {metrics['failed_requests']}")
        print(f"   错误率: {metrics['error_rate']:.2%}")
        print(f"   平均响应时间: {metrics['avg_response_time']:.3f}s")
        print(f"   连续错误: {metrics['consecutive_errors']}")
        print(f"   最近错误数: {status['recent_errors']}")
        
        if metrics['last_error_time']:
            print(f"   最后错误时间: {metrics['last_error_time']}")
        
        print("-" * 50)
    
    def _get_level_emoji(self, level: str) -> str:
        """获取安全级别对应的表情符号"""
        emoji_map = {
            "normal": "🟢",
            "warning": "🟡", 
            "degraded": "🟠",
            "emergency": "🔴"
        }
        return emoji_map.get(level, "❓")
    
    def demo_normal_operations(self):
        """演示正常操作"""
        print("\n✅ 演示正常操作:")
        print("=" * 60)
        
        # 模拟成功请求
        for i in range(10):
            response_time = random.uniform(0.1, 0.5)
            self.safety_manager.record_request(
                success=True,
                response_time=response_time,
                component="knowledge_base"
            )
            print(f"   请求 {i+1}: 成功 (响应时间: {response_time:.3f}s)")
        
        self.show_safety_status()
    
    def demo_error_monitoring(self):
        """演示错误监控"""
        print("\n⚠️  演示错误监控:")
        print("=" * 60)
        
        # 模拟一些错误
        error_scenarios = [
            ("CONNECTION_ERROR", "ChromaDB连接失败", "knowledge_base"),
            ("TIMEOUT_ERROR", "查询超时", "rag_agent"),
            ("EMBEDDING_ERROR", "嵌入模型错误", "embedding_service"),
            ("LLM_ERROR", "LLM服务不可用", "llm_service")
        ]
        
        for error_type, error_msg, component in error_scenarios:
            self.safety_manager.record_error(
                error_type=error_type,
                error_message=error_msg,
                component=component,
                severity="ERROR"
            )
            print(f"   记录错误: {component}.{error_type} - {error_msg}")
        
        self.show_safety_status()
    
    def demo_degradation_trigger(self):
        """演示降级触发"""
        print("\n🔻 演示降级触发:")
        print("=" * 60)
        
        print("模拟连续错误以触发降级...")
        
        # 模拟连续错误
        for i in range(6):  # 超过阈值(5)
            self.safety_manager.record_error(
                error_type="CRITICAL_ERROR",
                error_message=f"严重错误 #{i+1}",
                component="knowledge_base",
                severity="CRITICAL"
            )
            print(f"   连续错误 {i+1}/6")
            
            # 检查是否已经降级
            status = self.safety_manager.get_safety_status()
            if status['safety_level'] != 'normal':
                print(f"   🚨 触发降级! 当前级别: {status['safety_level']}")
                break
        
        self.show_safety_status()
    
    def demo_mixed_requests(self):
        """演示混合请求（成功和失败）"""
        print("\n🔀 演示混合请求:")
        print("=" * 60)
        
        print("模拟混合成功和失败请求...")
        
        for i in range(20):
            # 70% 成功率
            success = random.random() > 0.3
            response_time = random.uniform(0.1, 1.0)
            
            if success:
                self.safety_manager.record_request(
                    success=True,
                    response_time=response_time,
                    component="knowledge_base"
                )
                print(f"   请求 {i+1}: ✅ 成功")
            else:
                self.safety_manager.record_error(
                    error_type="RANDOM_ERROR",
                    error_message=f"随机错误 #{i+1}",
                    component="knowledge_base"
                )
                print(f"   请求 {i+1}: ❌ 失败")
            
            # 每5个请求显示一次状态
            if (i + 1) % 5 == 0:
                status = self.safety_manager.get_safety_status()
                level = status['safety_level']
                error_rate = status['metrics']['error_rate']
                print(f"     状态检查: {self._get_level_emoji(level)} {level} (错误率: {error_rate:.2%})")
        
        self.show_safety_status()
    
    def demo_force_operations(self):
        """演示强制操作"""
        print("\n🔧 演示强制操作:")
        print("=" * 60)
        
        # 强制降级
        print("1. 强制降级到紧急状态...")
        self.safety_manager.force_degradation(
            level=SafetyLevel.EMERGENCY,
            reason="演示强制降级功能"
        )
        self.show_safety_status()
        
        time.sleep(2)
        
        # 强制恢复
        print("2. 强制恢复到正常状态...")
        self.safety_manager.force_recovery("演示强制恢复功能")
        self.show_safety_status()
    
    def demo_callback_system(self):
        """演示回调系统"""
        print("\n📞 演示回调系统:")
        print("=" * 60)
        
        # 定义回调函数
        def degradation_callback(level):
            print(f"   🔔 降级回调触发: 级别 {level.value}")
        
        def recovery_callback():
            print(f"   🔔 恢复回调触发")
        
        # 注册回调
        self.safety_manager.add_degradation_callback(degradation_callback)
        self.safety_manager.add_recovery_callback(recovery_callback)
        
        print("已注册降级和恢复回调函数")
        
        # 触发降级
        print("\n触发降级以测试回调...")
        self.safety_manager.force_degradation(
            level=SafetyLevel.DEGRADED,
            reason="测试回调系统"
        )
        
        time.sleep(1)
        
        # 触发恢复
        print("\n触发恢复以测试回调...")
        self.safety_manager.force_recovery("测试回调系统")
    
    def demo_metrics_reset(self):
        """演示指标重置"""
        print("\n🔄 演示指标重置:")
        print("=" * 60)
        
        print("重置前的状态:")
        self.show_safety_status()
        
        print("执行指标重置...")
        self.safety_manager.reset_metrics()
        
        print("重置后的状态:")
        self.show_safety_status()
    
    def demo_auto_recovery(self):
        """演示自动恢复"""
        print("\n🔄 演示自动恢复:")
        print("=" * 60)
        
        print("注意: 自动恢复需要时间窗口，此演示仅显示概念")
        
        # 触发降级
        self.safety_manager.force_degradation(
            level=SafetyLevel.DEGRADED,
            reason="测试自动恢复"
        )
        
        print("系统已降级，自动恢复定时器已启动")
        print("在实际环境中，系统会在错误率降低后自动恢复")
        
        # 模拟一段时间后的恢复
        time.sleep(2)
        print("模拟自动恢复...")
        self.safety_manager.force_recovery("模拟自动恢复")
    
    def run_demo(self):
        """运行完整演示"""
        print("🛡️  安全管理器功能演示")
        print("=" * 80)
        
        # 显示初始状态
        print("📋 初始状态:")
        self.show_safety_status()
        
        # 正常操作
        self.demo_normal_operations()
        
        # 错误监控
        self.demo_error_monitoring()
        
        # 混合请求
        self.demo_mixed_requests()
        
        # 降级触发
        self.demo_degradation_trigger()
        
        # 强制操作
        self.demo_force_operations()
        
        # 回调系统
        self.demo_callback_system()
        
        # 自动恢复
        self.demo_auto_recovery()
        
        # 指标重置
        self.demo_metrics_reset()
        
        print("\n🎉 安全管理器演示完成！")
        print("=" * 80)


def main():
    """主函数"""
    demo = SafetyManagerDemo()
    demo.run_demo()


if __name__ == "__main__":
    main()
