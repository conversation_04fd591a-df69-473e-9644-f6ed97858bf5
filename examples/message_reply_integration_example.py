#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息回复管理器集成示例
演示如何在现有的conversation_flow.py中集成新的消息回复管理器

使用方法:
python examples/message_reply_integration_example.py
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.agents.message_reply_manager import MessageReplyManager, MessageType, ReplyCategory


class MockLLMClient:
    """模拟LLM客户端用于测试"""
    
    async def call_llm(self, messages, agent_name=None, temperature=0.7, max_tokens=200, **kwargs):
        """模拟LLM调用"""
        user_content = messages[0].get("content", "") if messages else ""
        
        # 根据不同的agent_name返回不同的模拟回复
        if agent_name == "greeting_generator":
            return {
                "content": "您好！我是AI需求采集助手，很高兴为您服务。请问您今天有什么项目需求需要我帮助整理吗？"
            }
        elif agent_name == "empathy_generator":
            return {
                "content": "我理解您现在可能感到有些困惑或沮丧。让我们一步步来梳理您的需求，我会尽力帮助您找到最合适的解决方案。"
            }
        elif agent_name == "clarification_generator":
            return {
                "content": "为了更好地理解您的需求，能否请您详细描述一下具体的使用场景和期望达到的效果呢？"
            }
        else:
            return {
                "content": "这是一个模拟的LLM回复，用于测试目的。"
            }


async def test_static_replies():
    """测试静态回复功能"""
    print("=== 测试静态回复功能 ===")
    
    # 初始化管理器（不需要LLM客户端）
    reply_manager = MessageReplyManager()
    
    # 测试基本静态回复
    greeting = await reply_manager.get_reply(
        reply_key="greeting",
        message_type=MessageType.STATIC
    )
    print(f"问候消息: {greeting}")
    
    # 测试带参数的静态回复
    error_reply = await reply_manager.get_reply(
        reply_key="processing_error",
        message_type=MessageType.STATIC,
        context={"error_msg": "网络连接超时"}
    )
    print(f"错误消息: {error_reply}")
    
    # 测试重置确认
    reset_reply = await reply_manager.get_reply(
        reply_key="reset_confirmation",
        message_type=MessageType.STATIC
    )
    print(f"重置确认: {reset_reply}")
    
    print()


async def test_dynamic_replies():
    """测试动态回复功能"""
    print("=== 测试动态回复功能 ===")
    
    # 初始化管理器（需要LLM客户端）
    mock_llm = MockLLMClient()
    reply_manager = MessageReplyManager(llm_client=mock_llm)
    
    # 测试动态问候回复
    dynamic_greeting = await reply_manager.get_reply(
        reply_key="greeting_response",
        message_type=MessageType.DYNAMIC,
        context={
            "prompt_instruction": "用户向你问候，请友好回应并简要介绍你是一个AI需求采集助手。",
            "message": "你好"
        }
    )
    print(f"动态问候: {dynamic_greeting}")
    
    # 测试共情回复
    empathy_reply = await reply_manager.get_reply(
        reply_key="empathy_and_clarify",
        message_type=MessageType.DYNAMIC,
        context={
            "prompt_instruction": "用户表达了负面情绪，请先共情再引导。",
            "message": "我很沮丧，这个项目太复杂了"
        }
    )
    print(f"共情回复: {empathy_reply}")
    
    # 测试澄清回复
    clarification_reply = await reply_manager.get_reply(
        reply_key="clarification_response",
        message_type=MessageType.DYNAMIC,
        context={
            "prompt_instruction": "用户的回答不够清晰，请礼貌地请求更多细节。",
            "message": "我想做个网站"
        }
    )
    print(f"澄清回复: {clarification_reply}")
    
    print()


async def test_fallback_mechanism():
    """测试回退机制"""
    print("=== 测试回退机制 ===")
    
    reply_manager = MessageReplyManager()
    
    # 测试不存在的静态模板
    fallback_reply = await reply_manager.get_reply(
        reply_key="non_existent_template",
        message_type=MessageType.STATIC
    )
    print(f"不存在模板的回退: {fallback_reply}")
    
    # 测试动态回复失败时的回退（没有LLM客户端）
    dynamic_fallback = await reply_manager.get_reply(
        reply_key="greeting_response",
        message_type=MessageType.DYNAMIC,
        context={"prompt_instruction": "测试提示"}
    )
    print(f"动态回复失败的回退: {dynamic_fallback}")
    
    print()


async def test_statistics():
    """测试统计功能"""
    print("=== 测试统计功能 ===")
    
    mock_llm = MockLLMClient()
    reply_manager = MessageReplyManager(llm_client=mock_llm)
    
    # 执行一些回复操作
    await reply_manager.get_reply("greeting", MessageType.STATIC)
    await reply_manager.get_reply("greeting_response", MessageType.DYNAMIC, 
                                context={"prompt_instruction": "测试"})
    await reply_manager.get_reply("non_existent", MessageType.STATIC)
    
    # 获取统计信息
    stats = reply_manager.get_reply_stats()
    print("回复统计信息:")
    print(f"  总回复数: {stats['total_replies']}")
    print(f"  成功数: {stats['success_count']}")
    print(f"  回退数: {stats['fallback_count']}")
    print(f"  错误数: {stats['error_count']}")
    print(f"  成功率: {stats['success_rate']:.2%}")
    print(f"  回退率: {stats['fallback_rate']:.2%}")
    
    print()


async def test_template_management():
    """测试模板管理功能"""
    print("=== 测试模板管理功能 ===")
    
    reply_manager = MessageReplyManager()
    
    # 列出可用模板
    templates = reply_manager.list_available_templates()
    print("可用模板:")
    print(f"  静态模板数量: {len(templates['static_templates'])}")
    print(f"  动态生成器数量: {len(templates['dynamic_generators'])}")
    
    # 添加自定义静态模板
    reply_manager.add_static_template(
        key="custom_welcome",
        template="欢迎使用我们的服务！{user_name}，有什么可以帮您的吗？",
        category=ReplyCategory.GREETING
    )
    
    # 测试新添加的模板
    custom_reply = await reply_manager.get_reply(
        reply_key="custom_welcome",
        message_type=MessageType.STATIC,
        context={"user_name": "张先生"}
    )
    print(f"自定义模板回复: {custom_reply}")
    
    print()


class ConversationFlowExample:
    """演示如何在ConversationFlow中集成MessageReplyManager"""
    
    def __init__(self, llm_client=None):
        self.llm_client = llm_client
        self.reply_manager = MessageReplyManager(llm_client=llm_client)
    
    async def handle_greeting(self, message: str = "", session_id: str = "", decision_result: dict = None, **kwargs) -> str:
        """处理问候 - 使用新的回复管理器"""
        if decision_result and decision_result.get('decision', {}).get('prompt_instruction'):
            # 动态生成回复
            return await self.reply_manager.get_reply(
                reply_key="greeting_response",
                message_type=MessageType.DYNAMIC,
                context={
                    "prompt_instruction": decision_result.get('decision', {}).get('prompt_instruction'),
                    "message": message
                }
            )
        else:
            # 静态回复
            return await self.reply_manager.get_reply(
                reply_key="greeting",
                message_type=MessageType.STATIC
            )
    
    async def handle_reset_conversation(self, **kwargs) -> str:
        """处理重置会话 - 使用新的回复管理器"""
        return await self.reply_manager.get_reply(
            reply_key="reset_confirmation",
            message_type=MessageType.STATIC
        )
    
    async def handle_show_empathy_and_clarify(self, message: str, decision_result: dict = None, **kwargs) -> str:
        """处理共情和澄清 - 使用新的回复管理器"""
        if decision_result and decision_result.get('decision', {}).get('prompt_instruction'):
            return await self.reply_manager.get_reply(
                reply_key="empathy_and_clarify",
                message_type=MessageType.DYNAMIC,
                context={
                    "prompt_instruction": decision_result.get('decision', {}).get('prompt_instruction'),
                    "message": message
                }
            )
        else:
            # 使用静态回退
            return await self.reply_manager.get_reply(
                reply_key="clarification_request",
                message_type=MessageType.STATIC
            )


async def test_conversation_flow_integration():
    """测试ConversationFlow集成"""
    print("=== 测试ConversationFlow集成 ===")
    
    mock_llm = MockLLMClient()
    conversation_flow = ConversationFlowExample(llm_client=mock_llm)
    
    # 测试问候处理
    greeting_reply = await conversation_flow.handle_greeting(
        message="你好",
        decision_result={
            "decision": {
                "prompt_instruction": "用户向你问候，请友好回应并介绍自己。"
            }
        }
    )
    print(f"集成测试 - 问候回复: {greeting_reply}")
    
    # 测试重置处理
    reset_reply = await conversation_flow.handle_reset_conversation()
    print(f"集成测试 - 重置回复: {reset_reply}")
    
    # 测试共情处理
    empathy_reply = await conversation_flow.handle_show_empathy_and_clarify(
        message="我很困惑",
        decision_result={
            "decision": {
                "prompt_instruction": "用户表达了困惑，请表示理解并提供帮助。"
            }
        }
    )
    print(f"集成测试 - 共情回复: {empathy_reply}")
    
    print()


async def main():
    """主函数"""
    print("消息回复管理器集成示例")
    print("=" * 50)
    
    try:
        await test_static_replies()
        await test_dynamic_replies()
        await test_fallback_mechanism()
        await test_statistics()
        await test_template_management()
        await test_conversation_flow_integration()
        
        print("✅ 所有测试完成！")
        print("\n📋 总结:")
        print("1. ✅ 静态回复功能正常")
        print("2. ✅ 动态回复功能正常")
        print("3. ✅ 回退机制工作正常")
        print("4. ✅ 统计功能正常")
        print("5. ✅ 模板管理功能正常")
        print("6. ✅ ConversationFlow集成正常")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
