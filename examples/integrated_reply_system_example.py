#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合决策引擎与回复系统使用示例
演示如何使用新的整合回复系统统一处理决策到回复的完整流程

使用方法:
python examples/integrated_reply_system_example.py
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.agents.integrated_reply_system import (
    IntegratedReplySystem, DecisionContext, DecisionType, ReplyResult
)


class MockLLMClient:
    """模拟LLM客户端用于测试"""
    
    def __init__(self):
        self.call_count = 0
        self.responses = {
            "greeting_generator": "您好！我是AI需求采集助手，很高兴为您服务。请问您今天有什么项目需求需要我帮助整理吗？",
            "empathy_generator": "我理解您现在可能感到有些困惑或沮丧。让我们一步步来梳理您的需求，我会尽力帮助您找到最合适的解决方案。",
            "clarification_generator": "为了更好地理解您的需求，能否请您详细描述一下具体的使用场景和期望达到的效果呢？",
            "apology_generator": "非常抱歉给您带来了困扰。为了能够更好地为您服务，请您告诉我具体需要改进的地方，我会认真对待您的反馈。"
        }
    
    async def call_llm(self, messages, agent_name=None, temperature=0.7, max_tokens=200, **kwargs):
        """模拟LLM调用"""
        self.call_count += 1
        
        # 模拟网络延迟
        await asyncio.sleep(0.05)
        
        # 根据agent_name返回不同的回复
        content = self.responses.get(agent_name, "这是一个模拟的LLM回复。")
        
        return {
            "content": content,
            "usage": {"total_tokens": len(content)}
        }


class MockConversationFlow:
    """模拟ConversationFlow用于测试handler执行"""
    
    def __init__(self):
        self.logger = None
        self.call_count = 0
    
    async def _process_intent(self, message: str, session_id: str, decision_result: dict, **kwargs) -> str:
        """模拟意图处理"""
        self.call_count += 1
        # 使用配置化的回复模板
        from backend.config.unified_config_loader import get_unified_config
        template = get_unified_config().get_message_template("user_interaction.processing.answer_processing", "正在处理您的需求：{message}。请稍等，我来为您分析...")
        return template.format(message=message)
    
    async def handle_process_answer_and_ask_next(self, message: str, session_id: str, decision_result: dict, **kwargs) -> str:
        """模拟处理回答并问下一个问题"""
        self.call_count += 1
        # 使用配置化的回复模板
        from backend.config.unified_config_loader import get_unified_config
        template = get_unified_config().get_message_template("business.question.base_template", "收到您的回答：{message}。让我问您下一个问题：您希望这个项目的主要用户群体是谁？")
        return template.format(message=message)
    
    async def handle_skip_question_and_ask_next(self, message: str, session_id: str, decision_result: dict, **kwargs) -> str:
        """模拟跳过问题"""
        self.call_count += 1
        # 使用配置化的回复模板
        from backend.config.unified_config_loader import get_unified_config
        return get_unified_config().get_message_template("business.focus_points.skip_continue", "好的，我们跳过这个问题。让我问您下一个问题：您的预算范围大概是多少？")
    
    async def handle_provide_suggestions(self, message: str, session_id: str, decision_result: dict, **kwargs) -> str:
        """模拟提供建议"""
        self.call_count += 1
        # 使用配置化的回复模板
        from backend.config.unified_config_loader import get_unified_config
        return get_unified_config().get_message_template("business.suggestions.single_point", "根据您的描述，我建议您可以考虑以下几个方向：1. 移动端优先设计 2. 云端部署 3. 模块化架构。您觉得哪个方向更符合您的需求？")


async def test_static_reply_decisions():
    """测试静态回复决策"""
    print("=== 测试静态回复决策 ===")
    
    integrated_system = IntegratedReplySystem()
    
    # 测试重置会话决策
    context = DecisionContext(
        intent="reset",
        emotion="neutral",
        current_state="GLOBAL",
        session_id="test_session_001",
        user_message="重置会话"
    )
    
    result = await integrated_system.process_decision_to_reply(context)
    
    print(f"决策类型: {result.decision_type}")
    print(f"回复内容: {result.content}")
    print(f"执行成功: {result.success}")
    print(f"执行时间: {result.execution_time:.3f}秒")
    print()


async def test_dynamic_reply_decisions():
    """测试动态回复决策"""
    print("=== 测试动态回复决策 ===")
    
    mock_llm = MockLLMClient()
    integrated_system = IntegratedReplySystem(llm_client=mock_llm)
    
    # 测试问候决策
    context = DecisionContext(
        intent="greeting",
        emotion="neutral",
        current_state="GLOBAL",
        session_id="test_session_002",
        user_message="你好"
    )
    
    result = await integrated_system.process_decision_to_reply(context)
    
    print(f"决策类型: {result.decision_type}")
    print(f"回复内容: {result.content}")
    print(f"执行成功: {result.success}")
    print(f"LLM调用次数: {mock_llm.call_count}")
    print()
    
    # 测试共情决策
    context2 = DecisionContext(
        intent="provide_information",
        emotion="negative",
        current_state="GLOBAL",
        session_id="test_session_002",
        user_message="我很沮丧，这个项目太复杂了"
    )
    
    result2 = await integrated_system.process_decision_to_reply(context2)
    
    print(f"共情决策类型: {result2.decision_type}")
    print(f"共情回复内容: {result2.content}")
    print(f"执行成功: {result2.success}")
    print()


async def test_handler_execution_decisions():
    """测试处理器执行决策"""
    print("=== 测试处理器执行决策 ===")
    
    mock_llm = MockLLMClient()
    mock_conversation_flow = MockConversationFlow()
    integrated_system = IntegratedReplySystem(llm_client=mock_llm)
    
    # 测试开始需求收集决策
    context = DecisionContext(
        intent="ask_question",
        emotion="neutral",
        current_state="IDLE",
        session_id="test_session_003",
        user_message="我想开发一个电商平台"
    )
    
    result = await integrated_system.process_decision_to_reply(
        context=context,
        conversation_flow_instance=mock_conversation_flow
    )
    
    print(f"决策类型: {result.decision_type}")
    print(f"回复内容: {result.content}")
    print(f"执行成功: {result.success}")
    print(f"Handler调用次数: {mock_conversation_flow.call_count}")
    print()
    
    # 测试处理回答决策
    context2 = DecisionContext(
        intent="provide_information",
        emotion="neutral",
        current_state="COLLECTING_INFO",
        session_id="test_session_003",
        user_message="主要面向年轻消费者"
    )
    
    result2 = await integrated_system.process_decision_to_reply(
        context=context2,
        conversation_flow_instance=mock_conversation_flow
    )
    
    print(f"处理回答决策类型: {result2.decision_type}")
    print(f"处理回答回复内容: {result2.content}")
    print(f"执行成功: {result2.success}")
    print()


async def test_hybrid_reply_decisions():
    """测试混合回复决策"""
    print("=== 测试混合回复决策 ===")
    
    mock_llm = MockLLMClient()
    integrated_system = IntegratedReplySystem(llm_client=mock_llm)
    
    # 测试确认并引导决策
    context = DecisionContext(
        intent="provide_information",
        emotion="neutral",
        current_state="GLOBAL",
        session_id="test_session_004",
        user_message="今天天气不错"
    )
    
    result = await integrated_system.process_decision_to_reply(context)
    
    print(f"决策类型: {result.decision_type}")
    print(f"回复内容: {result.content}")
    print(f"执行成功: {result.success}")
    print()


async def test_fallback_mechanisms():
    """测试回退机制"""
    print("=== 测试回退机制 ===")
    
    integrated_system = IntegratedReplySystem()
    
    # 测试未知意图的回退
    context = DecisionContext(
        intent="unknown_intent",
        emotion="neutral",
        current_state="UNKNOWN_STATE",
        session_id="test_session_005",
        user_message="这是一个未知的请求"
    )
    
    result = await integrated_system.process_decision_to_reply(context)
    
    print(f"回退决策类型: {result.decision_type}")
    print(f"回退回复内容: {result.content}")
    print(f"是否使用回退: {result.fallback_used}")
    print(f"执行成功: {result.success}")
    print()


async def test_performance_stats():
    """测试性能统计"""
    print("=== 测试性能统计 ===")
    
    mock_llm = MockLLMClient()
    mock_conversation_flow = MockConversationFlow()
    integrated_system = IntegratedReplySystem(llm_client=mock_llm)
    
    # 执行多种类型的决策
    test_contexts = [
        DecisionContext("greeting", "neutral", "GLOBAL", "session1", "你好"),
        DecisionContext("reset", "neutral", "GLOBAL", "session1", "重置"),
        DecisionContext("ask_question", "neutral", "IDLE", "session1", "我想做个网站"),
        DecisionContext("provide_information", "negative", "GLOBAL", "session1", "我很困惑"),
        DecisionContext("unknown", "neutral", "UNKNOWN", "session1", "未知请求")
    ]
    
    for i, context in enumerate(test_contexts):
        if i == 2:  # 需要handler执行的情况
            await integrated_system.process_decision_to_reply(
                context=context,
                conversation_flow_instance=mock_conversation_flow
            )
        else:
            await integrated_system.process_decision_to_reply(context=context)
    
    # 获取统计信息
    stats = integrated_system.get_stats()
    print("性能统计:")
    print(f"  总决策数: {stats['total_decisions']}")
    print(f"  成功回复数: {stats['successful_replies']}")
    print(f"  回退回复数: {stats['fallback_replies']}")
    print(f"  处理器执行数: {stats['handler_executions']}")
    print(f"  成功率: {stats['success_rate']:.2%}")
    print(f"  回退率: {stats['fallback_rate']:.2%}")
    print(f"  决策类型分布: {stats['decision_type_distribution']}")
    print()


async def test_config_validation():
    """测试配置验证"""
    print("=== 测试配置验证 ===")
    
    integrated_system = IntegratedReplySystem()
    
    # 验证统一配置中的策略配置
    validation_result = integrated_system.validate_strategies_config()
    
    print(f"配置验证结果: {'✅ 通过' if validation_result['valid'] else '❌ 失败'}")
    
    if validation_result['missing_actions']:
        print(f"缺失的action映射: {validation_result['missing_actions']}")
    
    if validation_result['unmapped_actions']:
        print(f"多余的action映射: {validation_result['unmapped_actions']}")
    
    if validation_result['recommendations']:
        print("建议:")
        for rec in validation_result['recommendations']:
            print(f"  - {rec}")
    
    print()


async def test_conversation_flow_integration():
    """测试与ConversationFlow的集成"""
    print("=== 测试ConversationFlow集成 ===")
    
    mock_llm = MockLLMClient()
    mock_conversation_flow = MockConversationFlow()
    integrated_system = IntegratedReplySystem(llm_client=mock_llm)
    
    # 模拟ConversationFlow中的process_message方法
    async def optimized_process_message(message: str, session_id: str, current_state: str):
        """优化后的消息处理方法"""
        # 这里应该调用意图识别，我们模拟一个结果
        if "你好" in message:
            intent, emotion = "greeting", "neutral"
        elif "重置" in message:
            intent, emotion = "reset", "neutral"
        elif "电商" in message:
            intent, emotion = "ask_question", "neutral"
        else:
            intent, emotion = "unknown", "neutral"
        
        # 创建决策上下文
        context = DecisionContext(
            intent=intent,
            emotion=emotion,
            current_state=current_state,
            session_id=session_id,
            user_message=message
        )
        
        # 使用整合系统处理决策到回复
        result = await integrated_system.process_decision_to_reply(
            context=context,
            conversation_flow_instance=mock_conversation_flow
        )
        
        return result.content
    
    # 测试不同场景
    test_cases = [
        ("你好", "session_001", "GLOBAL"),
        ("我想开发一个电商平台", "session_001", "IDLE"),
        ("重置会话", "session_001", "GLOBAL"),
        ("主要面向年轻用户", "session_001", "COLLECTING_INFO")
    ]
    
    for message, session_id, state in test_cases:
        reply = await optimized_process_message(message, session_id, state)
        print(f"用户: {message}")
        print(f"AI: {reply}")
        print()


async def main():
    """主函数"""
    print("整合决策引擎与回复系统使用示例")
    print("=" * 50)
    
    try:
        await test_static_reply_decisions()
        await test_dynamic_reply_decisions()
        await test_handler_execution_decisions()
        await test_hybrid_reply_decisions()
        await test_fallback_mechanisms()
        await test_performance_stats()
        await test_config_validation()
        await test_conversation_flow_integration()
        
        print("✅ 所有测试完成！")
        print("\n📋 总结:")
        print("1. ✅ 静态回复决策功能正常")
        print("2. ✅ 动态回复决策功能正常")
        print("3. ✅ 处理器执行决策功能正常")
        print("4. ✅ 混合回复决策功能正常")
        print("5. ✅ 回退机制工作正常")
        print("6. ✅ 性能统计功能正常")
        print("7. ✅ 配置验证功能正常")
        print("8. ✅ ConversationFlow集成正常")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
