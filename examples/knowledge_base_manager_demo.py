#!/usr/bin/env python3
"""
知识库管理器演示脚本

展示知识库管理器的核心功能：
1. 文档摄入和更新
2. 批量处理
3. 文档管理和查询
4. 统计信息和健康检查
"""

import asyncio
import sys
import os
from typing import Dict, Any, List

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from backend.agents.factory import agent_factory


class KnowledgeBaseManagerDemo:
    """知识库管理器演示类"""
    
    def __init__(self):
        self.kb_manager = None
        
    async def initialize(self):
        """初始化演示环境"""
        try:
            print("🚀 初始化知识库管理器演示环境...")
            
            # 获取知识库管理器
            self.kb_manager = agent_factory.get_knowledge_base_manager()
            
            if not self.kb_manager:
                print("❌ 知识库管理器不可用")
                return False
            
            print("✅ 初始化完成！")
            return True
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            return False
    
    async def demo_health_check(self):
        """演示健康检查"""
        print("\n🏥 知识库管理器健康检查:")
        print("=" * 60)
        
        try:
            health_info = await self.kb_manager.health_check()
            
            print("📋 健康状态:")
            for key, value in health_info.items():
                status = "✅" if value else "❌"
                print(f"   {key}: {status} {value}")
            
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
    
    async def demo_document_ingestion(self):
        """演示文档摄入"""
        print("\n📄 文档摄入演示:")
        print("=" * 60)
        
        # 测试文档
        test_documents = [
            {
                "content": """# 雇主账号注册指南
                
雇主注册流程：
1. 访问注册页面
2. 填写公司信息
3. 验证邮箱
4. 完成认证

注意事项：
- 确保公司信息真实有效
- 邮箱必须是企业邮箱
- 认证材料需要清晰完整
""",
                "source_path": "docs/company/register-guide.md",
                "metadata": {
                    "role": "company",
                    "category": "account",
                    "tags": ["注册", "雇主", "认证"]
                }
            },
            {
                "content": """# 开发者技能认证
                
开发者可以通过以下方式进行技能认证：

## 技术测试
- 在线编程测试
- 项目作品展示
- 技术面试

## 认证等级
- 初级开发者
- 中级开发者  
- 高级开发者
- 专家级开发者

认证通过后可以获得更多项目机会。
""",
                "source_path": "docs/developer/skill-certification.md",
                "metadata": {
                    "role": "developer",
                    "category": "certification",
                    "tags": ["技能", "认证", "等级"]
                }
            }
        ]
        
        for i, doc in enumerate(test_documents, 1):
            print(f"\n📝 摄入文档 {i}: {doc['source_path']}")
            
            try:
                result = await self.kb_manager.ingest_document(
                    content=doc["content"],
                    source_path=doc["source_path"],
                    metadata=doc["metadata"]
                )
                
                if result.success:
                    print(f"✅ 摄入成功:")
                    print(f"   文档ID: {result.doc_id}")
                    print(f"   处理块数: {result.chunks_processed}")
                    print(f"   处理时间: {result.processing_time:.3f}s")
                    
                    if result.warnings:
                        for warning in result.warnings:
                            print(f"⚠️  警告: {warning}")
                else:
                    print(f"❌ 摄入失败: {result.error}")
                    
            except Exception as e:
                print(f"❌ 摄入异常: {e}")
            
            print("-" * 40)
    
    async def demo_batch_ingestion(self):
        """演示批量摄入"""
        print("\n📦 批量文档摄入演示:")
        print("=" * 60)
        
        # 批量文档
        batch_documents = [
            {
                "content": f"这是测试文档 {i}，用于演示批量摄入功能。内容包含一些示例信息和说明。",
                "source_path": f"test/batch_doc_{i}.md",
                "metadata": {
                    "role": "general",
                    "category": "test",
                    "tags": ["测试", "批量"]
                }
            }
            for i in range(1, 6)  # 创建5个测试文档
        ]
        
        print(f"📋 准备批量摄入 {len(batch_documents)} 个文档...")
        
        try:
            results = await self.kb_manager.ingest_documents_batch(batch_documents)
            
            success_count = sum(1 for r in results if r.success)
            total_chunks = sum(r.chunks_processed for r in results if r.success)
            
            print(f"📊 批量摄入结果:")
            print(f"   成功: {success_count}/{len(results)}")
            print(f"   总块数: {total_chunks}")
            
            # 显示失败的文档
            failed_results = [r for r in results if not r.success]
            if failed_results:
                print(f"\n❌ 失败的文档:")
                for result in failed_results:
                    print(f"   {result.doc_id}: {result.error}")
            
        except Exception as e:
            print(f"❌ 批量摄入异常: {e}")
    
    async def demo_document_management(self):
        """演示文档管理"""
        print("\n📚 文档管理演示:")
        print("=" * 60)
        
        try:
            # 列出所有文档
            all_docs = self.kb_manager.list_documents()
            print(f"📋 总文档数: {len(all_docs)}")
            
            if all_docs:
                print("\n📄 文档列表:")
                for doc in all_docs[:5]:  # 只显示前5个
                    print(f"   📝 {doc.title}")
                    print(f"      ID: {doc.doc_id}")
                    print(f"      角色: {doc.role}")
                    print(f"      类别: {doc.category}")
                    print(f"      块数: {doc.chunk_count}")
                    print(f"      更新时间: {doc.updated_at.strftime('%Y-%m-%d %H:%M:%S')}")
                    print()
            
            # 按角色过滤
            company_docs = self.kb_manager.list_documents(role="company")
            developer_docs = self.kb_manager.list_documents(role="developer")
            
            print(f"🏢 雇主文档: {len(company_docs)} 个")
            print(f"👨‍💻 开发者文档: {len(developer_docs)} 个")
            
            # 测试文档更新
            if all_docs:
                test_doc = all_docs[0]
                print(f"\n🔄 测试更新文档: {test_doc.title}")
                
                updated_content = f"更新后的内容 - {test_doc.title}\n\n这是更新后的文档内容，包含新的信息和说明。"
                
                update_result = await self.kb_manager.update_document(
                    doc_id=test_doc.doc_id,
                    content=updated_content,
                    metadata={"tags": ["更新", "测试"]}
                )
                
                if update_result.success:
                    print(f"✅ 文档更新成功，处理了 {update_result.chunks_processed} 个块")
                else:
                    print(f"❌ 文档更新失败: {update_result.error}")
            
        except Exception as e:
            print(f"❌ 文档管理演示失败: {e}")
    
    async def demo_statistics(self):
        """演示统计信息"""
        print("\n📊 知识库统计信息:")
        print("=" * 60)
        
        try:
            stats = self.kb_manager.get_statistics()
            
            if "error" in stats:
                print(f"❌ 获取统计信息失败: {stats['error']}")
                return
            
            print("📈 基本统计:")
            print(f"   总文档数: {stats.get('total_documents', 0)}")
            print(f"   总块数: {stats.get('total_chunks', 0)}")
            print(f"   集合块数: {stats.get('collection_chunks', 0)}")
            print(f"   ChromaDB可用: {'✅' if stats.get('chroma_available') else '❌'}")
            
            # 角色分布
            role_dist = stats.get('role_distribution', {})
            if role_dist:
                print(f"\n👥 角色分布:")
                for role, count in role_dist.items():
                    print(f"   {role}: {count} 个文档")
            
            # 类别分布
            category_dist = stats.get('category_distribution', {})
            if category_dist:
                print(f"\n📂 类别分布:")
                for category, count in category_dist.items():
                    print(f"   {category}: {count} 个文档")
            
        except Exception as e:
            print(f"❌ 统计信息演示失败: {e}")
    
    async def demo_document_removal(self):
        """演示文档删除"""
        print("\n🗑️  文档删除演示:")
        print("=" * 60)
        
        try:
            # 获取测试文档
            test_docs = self.kb_manager.list_documents(category="test")
            
            if test_docs:
                test_doc = test_docs[0]
                print(f"🎯 删除测试文档: {test_doc.title}")
                
                success = await self.kb_manager.remove_document(test_doc.doc_id)
                
                if success:
                    print("✅ 文档删除成功")
                    
                    # 验证删除
                    remaining_docs = self.kb_manager.list_documents(category="test")
                    print(f"📋 剩余测试文档: {len(remaining_docs)} 个")
                else:
                    print("❌ 文档删除失败")
            else:
                print("ℹ️  没有找到可删除的测试文档")
            
        except Exception as e:
            print(f"❌ 文档删除演示失败: {e}")
    
    async def run_demo(self):
        """运行完整演示"""
        print("📚 知识库管理器功能演示")
        print("=" * 80)
        
        # 初始化
        if not await self.initialize():
            return
        
        # 健康检查
        await self.demo_health_check()
        
        # 文档摄入
        await self.demo_document_ingestion()
        
        # 批量摄入
        await self.demo_batch_ingestion()
        
        # 文档管理
        await self.demo_document_management()
        
        # 统计信息
        await self.demo_statistics()
        
        # 文档删除
        await self.demo_document_removal()
        
        print("\n🎉 知识库管理器演示完成！")
        print("=" * 80)


async def main():
    """主函数"""
    demo = KnowledgeBaseManagerDemo()
    await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
