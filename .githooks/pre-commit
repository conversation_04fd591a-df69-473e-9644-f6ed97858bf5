#!/bin/bash
# Git预提交钩子
# 在代码提交前运行质量检查

set -e

echo "🚀 运行预提交检查..."

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 运行Python预提交检查脚本
python3 "$PROJECT_ROOT/scripts/pre_commit_check.py"

# 检查返回码
if [ $? -eq 0 ]; then
    echo "✅ 预提交检查通过，允许提交"
    exit 0
else
    echo "❌ 预提交检查失败，阻止提交"
    echo ""
    echo "🔧 修复建议："
    echo "1. 查看上述检查失败的详情"
    echo "2. 修复相关问题"
    echo "3. 重新运行: python scripts/pre_commit_check.py"
    echo "4. 确保所有检查通过后再次提交"
    echo ""
    echo "💡 如果需要跳过检查（不推荐），可以使用: git commit --no-verify"
    exit 1
fi
