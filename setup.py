from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="intelligent-requirement-collection-system",
    version="1.0.0",
    author="Intelligent Requirement Collection System Team",
    author_email="<EMAIL>",
    description="智能需求采集系统 - 基于AI的需求分析和文档生成系统",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/requirement-collection-system",
    project_urls={
        "Bug Tracker": "https://github.com/your-org/requirement-collection-system/issues",
        "Documentation": "https://github.com/your-org/requirement-collection-system/docs",
        "Source Code": "https://github.com/your-org/requirement-collection-system",
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Office/Business :: Office Suites",
    ],
    packages=find_packages(),
    python_requires=">=3.11",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
            "pre-commit>=3.0.0",
        ],
        "monitoring": [
            "prometheus-client>=0.16.0",
            "grafana-api>=1.0.3",
        ],
        "production": [
            "gunicorn>=20.1.0",
            "uvicorn[standard]>=0.20.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "requirement-collection-api=run_api:main",
            "requirement-collection-frontend=run_frontend:main",
        ],
    },
    include_package_data=True,
    package_data={
        "backend": [
            "config/*.yaml",
            "prompts/*.txt",
            "data/*.db",
        ],
    },
    zip_safe=False,
)
