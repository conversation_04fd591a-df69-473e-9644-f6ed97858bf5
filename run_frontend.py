"""
启动前端服务器的脚本

用于启动需求采集系统的前端服务器，提供用户界面。
"""

import os
import sys
import subprocess

def main():
    """主函数，启动前端服务器"""
    print("启动需求采集系统前端服务器...")
    
    # 获取前端目录的绝对路径
    frontend_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "frontend")
    
    # 切换到前端目录
    os.chdir(frontend_dir)
    
    # 启动前端服务器
    subprocess.run(["npm", "run", "dev"], check=True)

if __name__ == "__main__":
    main()
