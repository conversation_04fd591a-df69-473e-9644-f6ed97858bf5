#!/usr/bin/env python3
"""
组件池化管理器

实现组件池化管理，提供高性能的Agent创建服务。
核心功能：
1. 共享组件池管理
2. 会话组件工厂
3. 性能监控和指标
4. 线程安全的并发访问
"""

import time
import threading
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
from weakref import WeakValueDictionary

# 避免循环导入，使用简单的打印替代日志
def log_info(message: str):
    """简单的信息日志"""
    print(f"[INFO] {message}")

def log_error(message: str):
    """简单的错误日志"""
    print(f"[ERROR] {message}")

def log_debug(message: str):
    """简单的调试日志"""
    print(f"[DEBUG] {message}")


@dataclass
class PoolMetrics:
    """池化性能指标"""

    # 命中率指标
    cache_hits: int = 0
    cache_misses: int = 0

    # 创建时间指标
    component_creation_times: Dict[str, List[float]] = field(default_factory=dict)

    # 内存使用指标
    pool_memory_usage: Dict[str, float] = field(default_factory=dict)

    # 并发指标
    concurrent_requests: int = 0
    max_concurrent_requests: int = 0

    # 组件使用统计
    component_usage_count: Dict[str, int] = field(default_factory=dict)

    def record_cache_hit(self, component_name: str):
        """记录缓存命中"""
        self.cache_hits += 1
        self.component_usage_count[component_name] = self.component_usage_count.get(component_name, 0) + 1

    def record_cache_miss(self, component_name: str):
        """记录缓存未命中"""
        self.cache_misses += 1

    def record_creation_time(self, component_name: str, duration: float):
        """记录组件创建时间"""
        if component_name not in self.component_creation_times:
            self.component_creation_times[component_name] = []
        self.component_creation_times[component_name].append(duration)

    def get_cache_hit_rate(self) -> float:
        """计算缓存命中率"""
        total = self.cache_hits + self.cache_misses
        return self.cache_hits / total if total > 0 else 0.0

    def get_avg_creation_time(self, component_name: str) -> float:
        """获取组件平均创建时间"""
        times = self.component_creation_times.get(component_name, [])
        return sum(times) / len(times) if times else 0.0

    def get_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        return {
            "cache_hit_rate": self.get_cache_hit_rate(),
            "total_requests": self.cache_hits + self.cache_misses,
            "concurrent_requests": self.concurrent_requests,
            "max_concurrent_requests": self.max_concurrent_requests,
            "component_stats": {
                name: {
                    "avg_creation_time": self.get_avg_creation_time(name),
                    "usage_count": self.component_usage_count.get(name, 0),
                    "creation_count": len(times)
                }
                for name, times in self.component_creation_times.items()
            }
        }


class SharedComponentPool:
    """
    共享组件池

    管理无状态、可安全共享的组件实例。
    使用单例模式确保每个组件类型只有一个实例。
    """

    def __init__(self, metrics: PoolMetrics):
        self._pool: Dict[str, Any] = {}
        self._lock = threading.RLock()
        self._creation_locks: Dict[str, threading.Lock] = {}
        self._metrics = metrics

        # 弱引用缓存，用于临时组件
        self._weak_cache: WeakValueDictionary = WeakValueDictionary()

        log_info("SharedComponentPool初始化完成")

    def get_component(self, component_name: str) -> Any:
        """
        获取共享组件（线程安全的单例模式）

        Args:
            component_name: 组件名称

        Returns:
            组件实例
        """
        # 快速路径：如果组件已存在，直接返回
        if component_name in self._pool:
            self._metrics.record_cache_hit(component_name)
            return self._pool[component_name]

        # 慢速路径：需要创建组件
        self._metrics.record_cache_miss(component_name)

        # 获取或创建组件特定的锁
        if component_name not in self._creation_locks:
            with self._lock:
                if component_name not in self._creation_locks:
                    self._creation_locks[component_name] = threading.Lock()

        # 使用组件特定的锁防止并发创建
        with self._creation_locks[component_name]:
            # 双重检查：可能在等待锁的过程中其他线程已经创建了组件
            if component_name in self._pool:
                self._metrics.record_cache_hit(component_name)
                return self._pool[component_name]

            # 创建组件
            start_time = time.time()
            try:
                component = self._create_component(component_name)
                creation_time = time.time() - start_time

                # 存储到池中
                self._pool[component_name] = component
                self._metrics.record_creation_time(component_name, creation_time)

                log_info(f"创建共享组件: {component_name} (耗时: {creation_time:.3f}s)")
                return component

            except Exception as e:
                creation_time = time.time() - start_time
                self._metrics.record_creation_time(component_name, creation_time)
                log_error(f"创建共享组件失败: {component_name}, 错误: {e}")
                raise

    def _create_component(self, component_name: str) -> Any:
        """
        创建组件实例

        Args:
            component_name: 组件名称

        Returns:
            组件实例
        """
        # 延迟导入避免循环依赖
        try:
            if component_name == "config_service":
                from backend.config import config_service
                return config_service

            elif component_name == "database_manager":
                from backend.data.db.database_manager import DatabaseManager
                from backend.config.settings import DATABASE_PATH
                return DatabaseManager(str(DATABASE_PATH))

            elif component_name == "llm_service":
                from backend.agents.llm_service import AutoGenLLMServiceAgent
                return AutoGenLLMServiceAgent(enable_cache=True, cache_size=100, cache_ttl=3600)

            elif component_name == "message_manager":
                from backend.data.db.message_manager import MessageManager
                database_manager = self.get_component("database_manager")
                return MessageManager(database_manager)

            elif component_name == "document_manager":
                from backend.data.db.document_manager import DocumentManager
                database_manager = self.get_component("database_manager")
                return DocumentManager(database_manager)

            elif component_name == "focus_point_manager":
                from backend.data.db.focus_point_manager import FocusPointManager
                database_manager = self.get_component("database_manager")
                return FocusPointManager(database_manager)

            elif component_name == "knowledge_base_agent":
                from backend.agents.knowledge_base import KnowledgeBaseAgent
                from backend.config.settings import DATABASE_PATH
                return KnowledgeBaseAgent(db_path=str(DATABASE_PATH))

            elif component_name == "document_generator":
                from backend.agents.document_generator import DocumentGenerator
                llm_service = self.get_component("llm_service")
                database_manager = self.get_component("database_manager")
                return DocumentGenerator(llm_client=llm_service, db_manager=database_manager)

            elif component_name == "information_extractor_agent":
                from backend.agents.information_extractor import InformationExtractorAgent
                llm_service = self.get_component("llm_service")
                return InformationExtractorAgent(llm_service=llm_service)

            elif component_name == "domain_classifier_agent":
                from backend.agents.domain_classifier import DomainClassifierAgent
                llm_service = self.get_component("llm_service")
                return DomainClassifierAgent(llm_client=llm_service, agent_name="domain_classifier")

            elif component_name == "category_classifier_agent":
                from backend.agents.category_classifier import CategoryClassifierAgent
                llm_service = self.get_component("llm_service")
                return CategoryClassifierAgent(llm_client=llm_service, agent_name="category_classifier")

            elif component_name == "intent_decision_engine":
                from backend.agents.simplified_decision_engine import get_simplified_decision_engine
                engine = get_simplified_decision_engine()
                engine.enable_acceleration_mode()
                return engine

            elif component_name == "integrated_reply_system":
                from backend.agents.message_reply_manager import MessageReplyManager
                from backend.agents.dynamic_reply_generator import DynamicReplyGenerator
                from backend.agents.dynamic_reply_generator import DynamicReplyFactory
                from backend.agents.integrated_reply_system import IntegratedReplySystem

                llm_service = self.get_component("llm_service")
                reply_manager = MessageReplyManager(llm_client=llm_service)
                dynamic_reply_generator = DynamicReplyGenerator(llm_client=llm_service)
                reply_factory = DynamicReplyFactory(dynamic_reply_generator)
                return IntegratedReplySystem(llm_client=llm_service, reply_manager=reply_manager)

            elif component_name == "prompt_loader":
                from backend.utils.prompt_loader import PromptLoader
                return PromptLoader()

            elif component_name == "knowledge_base_config_manager":
                from backend.config.knowledge_base_config import KnowledgeBaseConfigManager
                return KnowledgeBaseConfigManager()

            else:
                raise ValueError(f"未知的组件名称: {component_name}")

        except Exception as e:
            log_error(f"创建组件 {component_name} 时发生错误: {e}")
            raise

    def get_components(self, component_names: List[str]) -> Dict[str, Any]:
        """
        批量获取组件

        Args:
            component_names: 组件名称列表

        Returns:
            组件字典
        """
        return {name: self.get_component(name) for name in component_names}

    def get_pool_info(self) -> Dict[str, Any]:
        """获取池信息"""
        return {
            "pool_size": len(self._pool),
            "components": list(self._pool.keys()),
            "creation_locks": len(self._creation_locks)
        }


class SessionComponentFactory:
    """
    会话组件工厂

    为每个会话创建独立的有状态组件，确保会话隔离。
    """

    def __init__(self, shared_pool: SharedComponentPool, metrics: PoolMetrics):
        self.shared_pool = shared_pool
        self.metrics = metrics
        log_info("SessionComponentFactory初始化完成")

    def create_session_components(self, session_id: str, user_id: str,
                                shared_components: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建会话特定组件

        Args:
            session_id: 会话ID
            user_id: 用户ID
            shared_components: 共享组件字典

        Returns:
            会话组件字典
        """
        start_time = time.time()

        try:
            # 创建会话上下文管理器
            from backend.agents.session_context import SessionContextManager
            session_context_manager = SessionContextManager(shared_components["database_manager"])

            # 创建状态管理器
            from backend.agents.unified_state_manager import UnifiedStateManager
            state_manager = UnifiedStateManager(shared_components["database_manager"])

            # 创建会话管理器
            from backend.agents.conversation_flow.session_manager import SessionManager
            from backend.services.conversation_history_service import get_history_service
            history_service = get_history_service(shared_components["message_manager"])
            session_manager = SessionManager(
                shared_components["database_manager"],
                session_context_manager,
                history_service
            )

            session_components = {
                "session_context_manager": session_context_manager,
                "state_manager": state_manager,
                "session_manager": session_manager,
                "history_service": history_service,
                "session_id": session_id,
                "user_id": user_id
            }

            creation_time = time.time() - start_time
            self.metrics.record_creation_time("session_components", creation_time)

            log_info(f"创建会话组件完成: {session_id} (耗时: {creation_time:.3f}s)")
            return session_components

        except Exception as e:
            creation_time = time.time() - start_time
            self.metrics.record_creation_time("session_components", creation_time)
            log_error(f"创建会话组件失败: {session_id}, 错误: {e}")
            raise


class ComponentPoolManager:
    """
    组件池化管理器

    核心组件池化管理器，协调共享组件池和会话组件工厂，
    提供高性能的Agent创建服务。
    """

    def __init__(self):
        self.metrics = PoolMetrics()
        self.shared_pool = SharedComponentPool(self.metrics)
        self.session_factory = SessionComponentFactory(self.shared_pool, self.metrics)

        # 并发控制
        self._concurrent_lock = threading.Lock()

        log_info("ComponentPoolManager初始化完成")

    @asynccontextmanager
    async def _track_concurrent_request(self):
        """跟踪并发请求"""
        with self._concurrent_lock:
            self.metrics.concurrent_requests += 1
            self.metrics.max_concurrent_requests = max(
                self.metrics.max_concurrent_requests,
                self.metrics.concurrent_requests
            )

        try:
            yield
        finally:
            with self._concurrent_lock:
                self.metrics.concurrent_requests -= 1

    def get_conversation_flow_agent(self, session_id: str, user_id: str = None, **kwargs) -> Any:
        """
        快速创建ConversationFlowAgent

        Args:
            session_id: 会话ID
            user_id: 用户ID（可选，默认使用session_id）
            **kwargs: 额外参数

        Returns:
            配置好的ConversationFlowAgent实例
        """
        if user_id is None:
            user_id = session_id

        start_time = time.time()

        try:
            # 1. 从共享池获取无状态组件
            shared_component_names = [
                "config_service",
                "database_manager",
                "llm_service",
                "message_manager",
                "document_manager",
                "focus_point_manager",
                "knowledge_base_agent",
                "document_generator",
                "information_extractor_agent",
                "domain_classifier_agent",
                "category_classifier_agent",
                "intent_decision_engine",
                "integrated_reply_system",
                "prompt_loader",
                "knowledge_base_config_manager"
            ]

            shared_components = self.shared_pool.get_components(shared_component_names)

            # 2. 创建会话特定组件
            session_components = self.session_factory.create_session_components(
                session_id, user_id, shared_components
            )

            # 3. 组装Agent
            agent = self._assemble_agent(session_id, shared_components, session_components, **kwargs)

            creation_time = time.time() - start_time
            self.metrics.record_creation_time("conversation_flow_agent", creation_time)

            log_info(f"快速创建ConversationFlowAgent完成: {session_id} (总耗时: {creation_time:.3f}s)")
            return agent

        except Exception as e:
            creation_time = time.time() - start_time
            self.metrics.record_creation_time("conversation_flow_agent", creation_time)
            log_error(f"创建ConversationFlowAgent失败: {session_id}, 错误: {e}")
            raise

    def _assemble_agent(self, session_id: str, shared_components: Dict[str, Any],
                       session_components: Dict[str, Any], **kwargs) -> Any:
        """
        组装ConversationFlowAgent

        Args:
            session_id: 会话ID
            shared_components: 共享组件字典
            session_components: 会话组件字典
            **kwargs: 额外参数

        Returns:
            组装好的ConversationFlowAgent实例
        """
        try:
            # 合并所有依赖
            dependencies = {
                # 共享组件
                "config_service": shared_components["config_service"],
                "llm_service": shared_components["llm_service"],
                "document_generator": shared_components["document_generator"],
                "information_extractor_agent": shared_components["information_extractor_agent"],
                "knowledge_base_agent": shared_components["knowledge_base_agent"],
                "message_manager": shared_components["message_manager"],
                "document_manager": shared_components["document_manager"],
                "focus_point_manager": shared_components["focus_point_manager"],
                "database_manager": shared_components["database_manager"],
                "intent_decision_engine": shared_components["intent_decision_engine"],
                "integrated_reply_system": shared_components["integrated_reply_system"],
                "domain_classifier_agent": shared_components["domain_classifier_agent"],
                "category_classifier_agent": shared_components["category_classifier_agent"],
                "prompt_loader": shared_components["prompt_loader"],
                "knowledge_base_config_manager": shared_components["knowledge_base_config_manager"],

                # 会话组件
                "state_manager": session_components["state_manager"],
                "session_manager": session_components["session_manager"],

                # 会话信息
                "session_id": session_id,

                # 兼容性参数
                "hybrid_conversation_router": None,  # 保持兼容性
            }

            # 添加额外参数
            dependencies.update(kwargs)

            # 创建Agent实例
            from backend.agents.conversation_flow.core_refactored import AutoGenConversationFlowAgent
            agent = AutoGenConversationFlowAgent(**dependencies)

            # 创建MessageProcessor（解决循环依赖）
            message_processor = self._create_message_processor_for_agent(
                agent, shared_components, session_components
            )
            agent.message_processor = message_processor

            return agent

        except Exception as e:
            log_error(f"组装Agent失败: {session_id}, 错误: {e}")
            raise

    def _create_message_processor_for_agent(self, agent: Any, shared_components: Dict[str, Any],
                                          session_components: Dict[str, Any]) -> Any:
        """
        为Agent创建MessageProcessor

        Args:
            agent: ConversationFlowAgent实例
            shared_components: 共享组件字典
            session_components: 会话组件字典

        Returns:
            MessageProcessor实例
        """
        try:
            from backend.agents.conversation_flow.message_processor import MessageProcessor
            from backend.handlers.action_executor import ActionExecutor

            # 创建ActionExecutor
            action_executor = ActionExecutor(conversation_flow=agent)

            # 创建MessageProcessor
            return MessageProcessor(
                shared_components["message_manager"],
                session_components["session_context_manager"],
                action_executor,
                shared_components["knowledge_base_agent"],
                shared_components["intent_decision_engine"]
            )

        except Exception as e:
            log_error(f"创建MessageProcessor失败: {e}")
            raise

    def prewarm_components(self, component_names: List[str] = None) -> Dict[str, bool]:
        """
        预热组件

        Args:
            component_names: 要预热的组件名称列表，None表示预热所有关键组件

        Returns:
            预热结果字典
        """
        if component_names is None:
            component_names = [
                "config_service",
                "database_manager",
                "llm_service",
                "intent_decision_engine",
                "knowledge_base_agent"
            ]

        results = {}
        start_time = time.time()

        log_info(f"开始预热组件: {component_names}")

        for component_name in component_names:
            try:
                self.shared_pool.get_component(component_name)
                results[component_name] = True
                log_info(f"预热组件成功: {component_name}")
            except Exception as e:
                results[component_name] = False
                log_error(f"预热组件失败: {component_name}, 错误: {e}")

        total_time = time.time() - start_time
        success_count = sum(1 for success in results.values() if success)

        log_info(f"组件预热完成: {success_count}/{len(component_names)} 成功, 耗时: {total_time:.3f}s")

        return results

    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取性能指标摘要"""
        return {
            "pool_metrics": self.metrics.get_summary(),
            "shared_pool_info": self.shared_pool.get_pool_info(),
            "timestamp": time.time()
        }

    def cleanup_expired_sessions(self, max_age_seconds: int = 3600):
        """
        清理过期会话（预留接口）

        Args:
            max_age_seconds: 最大存活时间（秒）
        """
        # 这里可以实现会话清理逻辑
        # 当前版本中会话组件是临时创建的，不需要特殊清理
        log_info(f"会话清理检查完成（当前实现中会话组件自动回收）")

    def get_pool_status(self) -> Dict[str, Any]:
        """获取池状态信息"""
        return {
            "shared_pool": self.shared_pool.get_pool_info(),
            "metrics": self.metrics.get_summary(),
            "status": "healthy" if self.metrics.get_cache_hit_rate() > 0.5 else "warning"
        }


class PoolMonitoringDashboard:
    """池化监控仪表板"""

    def __init__(self, pool_manager: ComponentPoolManager):
        self.pool_manager = pool_manager

    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表板数据"""

        metrics = self.pool_manager.metrics

        return {
            "performance": {
                "cache_hit_rate": metrics.get_cache_hit_rate(),
                "total_requests": metrics.cache_hits + metrics.cache_misses,
                "concurrent_sessions": metrics.concurrent_requests,
                "max_concurrent_sessions": metrics.max_concurrent_requests
            },
            "resource_usage": {
                "shared_components_count": len(self.pool_manager.shared_pool._pool),
                "pooled_components": list(self.pool_manager.shared_pool._pool.keys())
            },
            "component_stats": metrics.component_stats if hasattr(metrics, 'component_stats') else {},
            "pool_status": self.pool_manager.get_pool_status(),
            "timestamp": time.time()
        }

    def get_performance_report(self) -> str:
        """获取性能报告"""
        data = self.get_dashboard_data()

        report = []
        report.append("="*60)
        report.append("组件池化性能报告")
        report.append("="*60)

        perf = data["performance"]
        report.append(f"缓存命中率: {perf['cache_hit_rate']:.2%}")
        report.append(f"总请求数: {perf['total_requests']}")
        report.append(f"当前并发: {perf['concurrent_sessions']}")
        report.append(f"最大并发: {perf['max_concurrent_sessions']}")

        report.append("\n资源使用:")
        resource = data["resource_usage"]
        report.append(f"共享组件数: {resource['shared_components_count']}")
        report.append(f"池化组件: {', '.join(resource['pooled_components'])}")

        report.append("\n组件统计:")
        for name, stats in data["component_stats"].items():
            if isinstance(stats, dict):
                avg_time = stats.get("avg_creation_time", 0)
                usage_count = stats.get("usage_count", 0)
                report.append(f"  {name}: 平均创建时间 {avg_time:.3f}s, 使用次数 {usage_count}")

        return "\n".join(report)


# ==================== 全局实例 ====================

# 创建全局组件池管理器实例
_global_pool_manager: Optional[ComponentPoolManager] = None
_pool_manager_lock = threading.Lock()


def get_component_pool_manager() -> ComponentPoolManager:
    """
    获取全局组件池管理器实例（单例模式）

    Returns:
        ComponentPoolManager实例
    """
    global _global_pool_manager

    if _global_pool_manager is None:
        with _pool_manager_lock:
            if _global_pool_manager is None:
                _global_pool_manager = ComponentPoolManager()
                log_info("全局组件池管理器创建完成")

    return _global_pool_manager


def get_monitoring_dashboard() -> PoolMonitoringDashboard:
    """
    获取监控仪表板实例

    Returns:
        PoolMonitoringDashboard实例
    """
    pool_manager = get_component_pool_manager()
    return PoolMonitoringDashboard(pool_manager)


# ==================== 便捷函数 ====================

def create_conversation_flow_agent_fast(session_id: str, user_id: str = None, **kwargs) -> Any:
    """
    快速创建ConversationFlowAgent的便捷函数

    Args:
        session_id: 会话ID
        user_id: 用户ID
        **kwargs: 额外参数

    Returns:
        ConversationFlowAgent实例
    """
    pool_manager = get_component_pool_manager()
    return pool_manager.get_conversation_flow_agent(session_id, user_id, **kwargs)


def prewarm_critical_components() -> Dict[str, bool]:
    """
    预热关键组件的便捷函数

    Returns:
        预热结果字典
    """
    pool_manager = get_component_pool_manager()
    return pool_manager.prewarm_components()


def get_pool_performance_summary() -> Dict[str, Any]:
    """
    获取池性能摘要的便捷函数

    Returns:
        性能摘要字典
    """
    pool_manager = get_component_pool_manager()
    return pool_manager.get_metrics_summary()


# ==================== 导出接口 ====================

__all__ = [
    # 核心类
    'ComponentPoolManager',
    'SharedComponentPool',
    'SessionComponentFactory',
    'PoolMetrics',
    'PoolMonitoringDashboard',

    # 全局实例获取函数
    'get_component_pool_manager',
    'get_monitoring_dashboard',

    # 便捷函数
    'create_conversation_flow_agent_fast',
    'prewarm_critical_components',
    'get_pool_performance_summary',
]