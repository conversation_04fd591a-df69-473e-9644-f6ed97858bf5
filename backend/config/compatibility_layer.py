"""
兼容性层 - 为旧代码提供向后兼容
"""

from backend.config.unified_config_loader import get_unified_config

# 为了向后兼容，提供旧的接口
class CompatibilityConfigManager:
    """兼容性配置管理器"""
    
    def __init__(self):
        self._loader = get_unified_config()
    
    def get_strategies(self):
        """获取策略配置（兼容性方法）"""
        return self._loader.get_conversation_config()
    
    def get_business_rule(self, key, default=None):
        """获取业务规则（兼容性方法）"""
        return self._loader.get_config_value(f'business_rules.{key}', default)
    
    def get_message_template(self, key, **kwargs):
        """获取消息模板（兼容性方法）"""
        return self._loader.get_template(key)
    
    def get_message_config(self, section):
        """获取消息配置（兼容性方法）"""
        return self._loader.get_config_value(f'message_templates.{section}')
    
    def reload_all(self):
        """重新加载所有配置（兼容性方法）"""
        self._loader.reload()
    
    def get_config_status(self):
        """获取配置状态（兼容性方法）"""
        return self._loader.get_config_status()

# 创建兼容性实例
unified_config_manager = CompatibilityConfigManager()
