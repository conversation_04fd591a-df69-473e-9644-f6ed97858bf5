#!/usr/bin/env python3
"""
配置预加载器 - 优化系统启动性能

在应用启动时预加载所有配置，避免在请求处理过程中重复加载配置文件。
实现配置的预热和验证，确保系统启动后配置已就绪。

核心功能：
1. 配置预加载：在启动时一次性加载所有配置
2. 配置验证：验证配置文件的完整性和正确性
3. 依赖检查：检查配置间的依赖关系
4. 性能监控：记录配置加载时间和状态
5. 错误处理：优雅处理配置加载失败的情况

使用方式：
```python
from backend.config.preloader import config_preloader

# 在应用启动时调用
await config_preloader.preload_all_configs()

# 检查预加载状态
status = config_preloader.get_preload_status()
```
"""

import logging
import time
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class PreloadStatus(Enum):
    """预加载状态枚举"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class ConfigPreloadResult:
    """配置预加载结果"""
    config_name: str
    success: bool
    load_time: float
    error_message: Optional[str] = None
    file_size: Optional[int] = None


class ConfigPreloader:
    """配置预加载器"""
    
    def __init__(self):
        self.status = PreloadStatus.NOT_STARTED
        self.preload_results: Dict[str, ConfigPreloadResult] = {}
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.total_configs = 0
        self.successful_configs = 0
        
        # 配置加载顺序（按依赖关系排序）
        self.config_load_order = [
            "business_rules.yaml",      # 基础业务规则
            "strategies.yaml",          # 决策策略
            "message_config.yaml",      # 消息模板
            "database_queries.yaml",    # 数据库查询
        ]
        
        logger.info("配置预加载器初始化完成")
    
    async def preload_all_configs(self) -> bool:
        """
        预加载所有配置文件
        
        Returns:
            bool: 是否全部加载成功
        """
        if self.status == PreloadStatus.IN_PROGRESS:
            logger.warning("配置预加载已在进行中，跳过重复调用")
            return False
            
        if self.status == PreloadStatus.COMPLETED:
            logger.info("配置已预加载完成，跳过重复调用")
            return True
        
        self.status = PreloadStatus.IN_PROGRESS
        self.start_time = time.time()
        self.total_configs = len(self.config_load_order)
        self.successful_configs = 0
        
        logger.info(f"开始预加载 {self.total_configs} 个配置文件...")
        
        try:
            # 按顺序加载配置
            for config_name in self.config_load_order:
                result = await self._preload_single_config(config_name)
                self.preload_results[config_name] = result
                
                if result.success:
                    self.successful_configs += 1
                else:
                    logger.error(f"配置文件 {config_name} 预加载失败: {result.error_message}")
            
            # 验证配置完整性
            validation_success = await self._validate_preloaded_configs()
            
            self.end_time = time.time()
            total_time = self.end_time - self.start_time
            
            if self.successful_configs == self.total_configs and validation_success:
                self.status = PreloadStatus.COMPLETED
                logger.info(f"配置预加载完成，成功加载 {self.successful_configs}/{self.total_configs} 个配置文件，耗时 {total_time:.2f}s")
                return True
            else:
                self.status = PreloadStatus.FAILED
                logger.error(f"配置预加载失败，成功 {self.successful_configs}/{self.total_configs} 个，耗时 {total_time:.2f}s")
                return False
                
        except Exception as e:
            self.status = PreloadStatus.FAILED
            self.end_time = time.time()
            logger.error(f"配置预加载过程中发生异常: {e}", exc_info=True)
            return False
    
    async def _preload_single_config(self, config_name: str) -> ConfigPreloadResult:
        """
        预加载单个配置文件
        
        Args:
            config_name: 配置文件名
            
        Returns:
            ConfigPreloadResult: 加载结果
        """
        start_time = time.time()
        
        try:
            # 获取统一配置管理器实例
            from .unified_config_loader import get_unified_config
            
            # 获取文件大小
            config_path = Path("backend/config") / config_name
            file_size = config_path.stat().st_size if config_path.exists() else None
            
            # 强制加载配置（使用统一配置）
            config_loader = get_unified_config()
            if config_name == "business_rules.yaml":
                # 加载业务规则配置
                config_loader.get_config_value("business_rules", {})
            elif config_name == "strategies.yaml":
                # 加载策略配置
                config_loader.get_config_value("strategies", {})
            elif config_name == "message_config.yaml":
                # 加载消息配置
                config_loader.get_message_templates()
            elif config_name == "database_queries.yaml":
                # 加载数据库查询配置
                config_loader.get_config_value("database", {})
            else:
                # 通用加载方法
                config_loader.get_config_value(config_name.replace('.yaml', ''), {})
            
            load_time = time.time() - start_time
            
            return ConfigPreloadResult(
                config_name=config_name,
                success=True,
                load_time=load_time,
                file_size=file_size
            )
            
        except Exception as e:
            load_time = time.time() - start_time
            return ConfigPreloadResult(
                config_name=config_name,
                success=False,
                load_time=load_time,
                error_message=str(e)
            )
    
    async def _validate_preloaded_configs(self) -> bool:
        """
        验证预加载的配置
        
        Returns:
            bool: 验证是否通过
        """
        try:
            from .unified_config_loader import get_unified_config
            
            # 验证关键配置项是否存在
            config_loader = get_unified_config()
            validations = [
                ("business_rules.yaml", lambda: config_loader.get_config_value("business_rules", {}) is not None),
                ("strategies.yaml", lambda: len(config_loader.get_config_value("strategies", {})) > 0),
                ("message_config.yaml", lambda: len(config_loader.get_message_templates()) > 0),
                ("database_queries.yaml", lambda: len(config_loader.get_config_value("database", {})) > 0),
            ]
            
            for config_name, validator in validations:
                if not validator():
                    logger.error(f"配置验证失败: {config_name}")
                    return False
            
            logger.info("配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"配置验证过程中发生异常: {e}")
            return False
    
    def get_preload_status(self) -> Dict[str, Any]:
        """
        获取预加载状态信息
        
        Returns:
            Dict: 状态信息
        """
        status_info = {
            "status": self.status.value,
            "total_configs": self.total_configs,
            "successful_configs": self.successful_configs,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "results": {}
        }
        
        if self.start_time and self.end_time:
            status_info["total_time"] = self.end_time - self.start_time
        
        # 添加详细结果
        for config_name, result in self.preload_results.items():
            status_info["results"][config_name] = {
                "success": result.success,
                "load_time": result.load_time,
                "file_size": result.file_size,
                "error_message": result.error_message
            }
        
        return status_info
    
    def is_ready(self) -> bool:
        """
        检查配置是否已就绪
        
        Returns:
            bool: 配置是否就绪
        """
        return self.status == PreloadStatus.COMPLETED
    
    def get_failed_configs(self) -> List[str]:
        """
        获取加载失败的配置列表
        
        Returns:
            List[str]: 失败的配置文件名列表
        """
        return [
            config_name for config_name, result in self.preload_results.items()
            if not result.success
        ]


# 全局配置预加载器实例
config_preloader = ConfigPreloader()


# 导出主要接口
__all__ = [
    'ConfigPreloader',
    'PreloadStatus', 
    'ConfigPreloadResult',
    'config_preloader'
]
