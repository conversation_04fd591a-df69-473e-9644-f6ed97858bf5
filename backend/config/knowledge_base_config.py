"""
知识库配置管理器

负责管理混合AI代理的知识库功能配置，支持运行时配置检查、
热更新和安全的功能启用/禁用控制。
"""

import yaml
import logging
import threading
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass, asdict
from .unified_config_loader import get_unified_config


@dataclass
class KnowledgeBaseConfig:
    """知识库配置数据类"""
    enabled: bool = False
    features: Dict[str, bool] = None
    chroma_db: Dict[str, Any] = None
    document_processing: Dict[str, Any] = None
    retrieval: Dict[str, Any] = None
    safety: Dict[str, Any] = None
    performance: Dict[str, Any] = None
    role_filters: Dict[str, Any] = None
    logging: Dict[str, Any] = None
    
    def __post_init__(self):
        """初始化默认值"""
        if self.features is None:
            self.features = {
                "rag_query": False,
                "intent_enhancement": False,
                "mode_switching": False,
                "document_ingestion": False
            }
        if self.chroma_db is None:
            self.chroma_db = {
                "path": "backend/data/chroma_db",
                "collection_name": "hybrid_knowledge_base",
                "embedding_model": "moka-ai/m3e-base"
            }
        if self.document_processing is None:
            self.document_processing = {
                "chunk_size": 800,
                "chunk_overlap": 100,
                "max_chunks_per_doc": 50,
                "supported_formats": ["md", "txt"]
            }
        if self.retrieval is None:
            self.retrieval = {
                "top_k": 5,
                "similarity_threshold": 0.7,
                "max_context_length": 4000
            }
        if self.safety is None:
            self.safety = {
                "fallback_to_requirement": True,
                "max_retry_attempts": 3,
                "timeout_seconds": 10,
                "error_threshold": 5
            }
        if self.performance is None:
            self.performance = {
                "cache_enabled": True,
                "cache_ttl": 3600,
                "max_concurrent_queries": 5,
                "vector_search_limit": 10
            }
        if self.role_filters is None:
            self.role_filters = {
                "enabled": True,
                "available_roles": ["company", "developer"],
                "default_role": None
            }
        if self.logging is None:
            self.logging = {
                "enabled": True,
                "log_level": "INFO",
                "log_queries": True,
                "log_responses": False
            }


class KnowledgeBaseConfigManager:
    """
    知识库配置管理器
    
    提供知识库功能的配置管理，包括：
    - 配置文件加载和验证
    - 运行时配置检查
    - 热更新支持
    - 安全的功能启用/禁用控制
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器

        Args:
            config_path: 配置文件路径（已废弃，使用统一配置）
        """
        self.logger = logging.getLogger(__name__)

        # 使用统一配置加载器
        self.unified_config_loader = get_unified_config()
        self._config: Optional[KnowledgeBaseConfig] = None
        self._lock = threading.RLock()
        self._error_count = 0

        # 加载初始配置
        self._load_config()
        
    def _load_config(self) -> bool:
        """
        从统一配置加载知识库配置

        Returns:
            bool: 加载是否成功
        """
        try:
            with self._lock:
                # 从统一配置获取知识库配置
                kb_config = self.unified_config_loader.get_config_section('knowledge_base')

                if not kb_config:
                    self.logger.warning("统一配置中未找到 knowledge_base 配置，使用默认配置")
                    self._config = KnowledgeBaseConfig()
                    return False

                # 创建配置对象
                self._config = KnowledgeBaseConfig(
                    enabled=kb_config.get('enabled', False),
                    features=kb_config.get('features', {}),
                    chroma_db=kb_config.get('chroma_db', {}),
                    document_processing=kb_config.get('document_processing', {}),
                    retrieval=kb_config.get('retrieval', {}),
                    safety=kb_config.get('safety', {}),
                    performance=kb_config.get('performance', {}),
                    role_filters=kb_config.get('role_filters', {}),
                    logging=kb_config.get('logging', {})
                )

                self._error_count = 0
                self.logger.info("知识库配置从统一配置加载成功")
                return True

        except Exception as e:
            self._error_count += 1
            self.logger.error(f"配置加载失败: {e}")

            # 如果配置对象不存在，创建默认配置
            if self._config is None:
                self._config = KnowledgeBaseConfig()

            return False
    
    def reload_config(self) -> bool:
        """
        重新加载配置文件（热更新）
        
        Returns:
            bool: 重新加载是否成功
        """
        self.logger.info("开始重新加载知识库配置...")
        success = self._load_config()
        
        if success:
            self.logger.info("配置热更新成功")
        else:
            self.logger.warning("配置热更新失败，使用当前配置")
            
        return success
    
    def is_knowledge_base_enabled(self) -> bool:
        """
        检查知识库功能是否启用
        
        Returns:
            bool: 知识库功能是否启用
        """
        # 自动检查配置文件更新
        self._load_config()
        
        with self._lock:
            if self._config is None:
                return False
            return self._config.enabled

    def is_enabled(self) -> bool:
        """
        检查知识库功能是否启用（别名方法，保持接口兼容性）

        Returns:
            bool: 知识库功能是否启用
        """
        return self.is_knowledge_base_enabled()

    def is_feature_enabled(self, feature_name: str) -> bool:
        """
        检查特定功能是否启用
        
        Args:
            feature_name: 功能名称 (rag_query, intent_enhancement, mode_switching, document_ingestion)
            
        Returns:
            bool: 功能是否启用
        """
        if not self.is_knowledge_base_enabled():
            return False
            
        with self._lock:
            if self._config is None:
                return False
            return self._config.features.get(feature_name, False)
    
    def get_config(self) -> KnowledgeBaseConfig:
        """
        获取完整配置对象
        
        Returns:
            KnowledgeBaseConfig: 配置对象
        """
        self._load_config()
        
        with self._lock:
            if self._config is None:
                return KnowledgeBaseConfig()
            return self._config
    
    def get_chroma_db_config(self) -> Dict[str, Any]:
        """获取ChromaDB配置"""
        config = self.get_config()
        return config.chroma_db
    
    def get_retrieval_config(self) -> Dict[str, Any]:
        """获取检索配置"""
        config = self.get_config()
        return config.retrieval
    
    def get_safety_config(self) -> Dict[str, Any]:
        """获取安全配置"""
        config = self.get_config()
        return config.safety
    
    def get_performance_config(self) -> Dict[str, Any]:
        """获取性能配置"""
        config = self.get_config()
        return config.performance
    
    def get_available_roles(self) -> List[str]:
        """获取可用角色列表"""
        config = self.get_config()
        if not config.role_filters.get('enabled', True):
            return []
        return config.role_filters.get('available_roles', [])
    
    def enable_knowledge_base(self, save_to_file: bool = False) -> bool:
        """
        启用知识库功能
        
        Args:
            save_to_file: 是否保存到配置文件
            
        Returns:
            bool: 操作是否成功
        """
        try:
            with self._lock:
                if self._config is None:
                    self._config = KnowledgeBaseConfig()
                
                self._config.enabled = True
                
                if save_to_file:
                    return self._save_config()
                
                self.logger.info("知识库功能已启用（内存中）")
                return True
                
        except Exception as e:
            self.logger.error(f"启用知识库功能失败: {e}")
            return False
    
    def disable_knowledge_base(self, save_to_file: bool = False) -> bool:
        """
        禁用知识库功能（安全回退）
        
        Args:
            save_to_file: 是否保存到配置文件
            
        Returns:
            bool: 操作是否成功
        """
        try:
            with self._lock:
                if self._config is None:
                    self._config = KnowledgeBaseConfig()
                
                self._config.enabled = False
                # 同时禁用所有子功能
                for feature in self._config.features:
                    self._config.features[feature] = False
                
                if save_to_file:
                    return self._save_config()
                
                self.logger.info("知识库功能已禁用（安全回退）")
                return True
                
        except Exception as e:
            self.logger.error(f"禁用知识库功能失败: {e}")
            return False
    
    def enable_feature(self, feature_name: str, save_to_file: bool = False) -> bool:
        """
        启用特定功能
        
        Args:
            feature_name: 功能名称
            save_to_file: 是否保存到配置文件
            
        Returns:
            bool: 操作是否成功
        """
        if not self.is_knowledge_base_enabled():
            self.logger.warning(f"无法启用功能 {feature_name}：知识库功能未启用")
            return False
        
        try:
            with self._lock:
                if self._config is None:
                    return False
                
                if feature_name not in self._config.features:
                    self.logger.warning(f"未知功能: {feature_name}")
                    return False
                
                self._config.features[feature_name] = True
                
                if save_to_file:
                    return self._save_config()
                
                self.logger.info(f"功能 {feature_name} 已启用")
                return True
                
        except Exception as e:
            self.logger.error(f"启用功能 {feature_name} 失败: {e}")
            return False
    
    def disable_feature(self, feature_name: str, save_to_file: bool = False) -> bool:
        """
        禁用特定功能
        
        Args:
            feature_name: 功能名称
            save_to_file: 是否保存到配置文件
            
        Returns:
            bool: 操作是否成功
        """
        try:
            with self._lock:
                if self._config is None:
                    return False
                
                if feature_name not in self._config.features:
                    self.logger.warning(f"未知功能: {feature_name}")
                    return False
                
                self._config.features[feature_name] = False
                
                if save_to_file:
                    return self._save_config()
                
                self.logger.info(f"功能 {feature_name} 已禁用")
                return True
                
        except Exception as e:
            self.logger.error(f"禁用功能 {feature_name} 失败: {e}")
            return False
    
    def _save_config(self) -> bool:
        """
        保存配置到文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            with self._lock:
                if self._config is None:
                    return False
                
                # 构建配置数据
                config_data = {
                    'knowledge_base': asdict(self._config)
                }
                
                # 确保目录存在
                self.config_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 写入文件
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config_data, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
                
                self.logger.info(f"配置已保存到: {self.config_path}")
                return True
                
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取配置管理器状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        with self._lock:
            return {
                "config_path": str(self.config_path),
                "config_exists": self.config_path.exists(),
                "last_modified": self._last_modified,
                "error_count": self._error_count,
                "knowledge_base_enabled": self.is_knowledge_base_enabled(),
                "enabled_features": [
                    feature for feature, enabled in self._config.features.items()
                    if enabled
                ] if self._config else []
            }
    
    def validate_config(self) -> Dict[str, Any]:
        """
        验证配置有效性
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        try:
            config = self.get_config()
            
            # 检查ChromaDB路径
            chroma_path = Path(config.chroma_db.get('path', ''))
            if not chroma_path.is_absolute():
                chroma_path = Path.cwd() / chroma_path
            
            if not chroma_path.parent.exists():
                validation_result["warnings"].append(
                    f"ChromaDB目录不存在: {chroma_path.parent}"
                )
            
            # 检查文档处理参数
            chunk_size = config.document_processing.get('chunk_size', 0)
            if chunk_size <= 0:
                validation_result["errors"].append("chunk_size 必须大于0")
                validation_result["valid"] = False
            
            chunk_overlap = config.document_processing.get('chunk_overlap', 0)
            if chunk_overlap >= chunk_size:
                validation_result["errors"].append("chunk_overlap 必须小于 chunk_size")
                validation_result["valid"] = False
            
            # 检查检索参数
            top_k = config.retrieval.get('top_k', 0)
            if top_k <= 0:
                validation_result["errors"].append("top_k 必须大于0")
                validation_result["valid"] = False
            
            similarity_threshold = config.retrieval.get('similarity_threshold', 0)
            if not (0 <= similarity_threshold <= 1):
                validation_result["errors"].append("similarity_threshold 必须在0-1之间")
                validation_result["valid"] = False
            
        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"配置验证异常: {str(e)}")
        
        return validation_result


# 全局配置管理器实例
_config_manager: Optional[KnowledgeBaseConfigManager] = None
_config_lock = threading.Lock()


def get_knowledge_base_config_manager() -> KnowledgeBaseConfigManager:
    """
    获取全局知识库配置管理器实例（单例模式）
    
    Returns:
        KnowledgeBaseConfigManager: 配置管理器实例
    """
    global _config_manager
    
    if _config_manager is None:
        with _config_lock:
            if _config_manager is None:
                _config_manager = KnowledgeBaseConfigManager()
    
    return _config_manager


# 便捷函数
def is_knowledge_base_enabled() -> bool:
    """检查知识库功能是否启用"""
    return get_knowledge_base_config_manager().is_knowledge_base_enabled()


def is_feature_enabled(feature_name: str) -> bool:
    """检查特定功能是否启用"""
    return get_knowledge_base_config_manager().is_feature_enabled(feature_name)


def get_knowledge_base_config() -> KnowledgeBaseConfig:
    """获取知识库配置"""
    return get_knowledge_base_config_manager().get_config()