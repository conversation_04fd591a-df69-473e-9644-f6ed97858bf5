"""
统一配置模块

提供应用程序的所有配置访问功能：
- LLM配置和参数管理  
- 业务规则配置
- 消息模板和动态回复配置
- 关键词和意图识别配置
- 数据库查询配置
- 阈值和性能配置

推荐使用方式：
```python
# 推荐：使用统一配置服务
from backend.config import config_service

llm_config = config_service.get_llm_config("intent_recognition")
business_rule = config_service.get_business_rule("retry.max_pending_attempts", 3)
template = config_service.get_message_template("greeting.basic")

# 或者直接导入特定管理器（高级用法）
from backend.config import config_manager, dynamic_keyword_config
```
"""

# 导入主要的配置服务接口
from .service import config_service, ConfigurationService

# 导入统一配置管理器（新版本）
from .unified_config_loader import get_unified_config
from .unified_dynamic_config import (
    dynamic_keyword_config,
    UnifiedDynamicConfigManager,
    DynamicKeywordConfig
)

# 向后兼容
unified_config_manager = get_unified_config()
config_manager = get_unified_config()

# 导入设置（用于直接访问）
from . import settings

# 公开的主要接口
__all__ = [
    # 主要服务接口
    'config_service',
    'ConfigurationService',
    
    # 底层管理器（向后兼容）
    'config_manager',
    'unified_config_manager', 
    'dynamic_keyword_config',
    
    # 类定义（用于类型注解等）
    'UnifiedDynamicConfigManager',
    'DynamicKeywordConfig',
    
    # 设置模块
    'settings',
]