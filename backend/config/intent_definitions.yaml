# 意图管理统一配置文件
# 版本: 1.0
# 目标: 解决模板与代码不同步问题，建立单一数据源

intent_system:
  version: "1.0"
  description: "统一意图识别和决策配置"
  
  # 核心意图定义
  intents:
    # 问候类意图
    greeting:
      description: "用户的问候或打招呼"
      examples:
        - "你好"
        - "早上好"
        - "hi"
        - "hello"
      action: "send_greeting"
      priority: 1
      supported_states: ["IDLE"]
      
    # 业务需求类意图
    business_requirement:
      description: "用户描述具体的业务需求、项目需求或想要实现的目标"
      examples:
        - "我想策划营销活动"
        - "需要开发一个网站"
        - "想做个APP"
        - "需要设计Logo"
      action: "start_requirement_collection"
      priority: 3
      supported_states: ["IDLE"]

    # 知识库查询意图
    search_knowledge_base:
      description: "用户询问系统功能、服务内容、使用方法等知识库相关问题"
      examples:
        - "你们支持哪些功能"
        - "有什么服务"
        - "如何使用"
        - "收费标准是什么"
        - "支持哪些支付方式"
      action: "search_knowledge_base"
      priority: 2
      supported_states: ["IDLE"]

    # 复合意图：知识库查询 + 业务需求
    composite_knowledge_requirement:
      description: "用户同时询问知识库问题并表达业务需求"
      examples:
        - "想先支持Apple Pay、Google Pay。对了，您们平台支持哪些支付方式？"
        - "需要开发网站，你们都提供哪些服务？"
        - "想做营销活动，有什么推广渠道？"
      action: "handle_composite_knowledge_requirement"
      priority: 1  # 最高优先级，优先于单一意图
      supported_states: ["IDLE"]
      
    # 情感支持类意图
    emotional_support:
      description: "用户表达情感困扰或需要心理支持"
      examples:
        - "我很焦虑"
        - "感觉很困惑"
        - "不知道怎么办"
      action: "provide_emotional_support"
      priority: 2
      supported_states: ["IDLE"]
      
    # 自我介绍意图
    ask_introduction:
      description: "用户希望AI进行自我介绍"
      examples:
        - "介绍一下自己"
        - "请介绍一下自己"
        - "自我介绍"
        - "你是谁"
        - "说说你自己"
      action: "provide_self_introduction"
      priority: 2
      supported_states: ["IDLE"]

    # 询问类意图
    ask_question:
      description: "用户询问系统功能、流程或一般性问题"
      examples:
        - "你能做什么"
        - "怎么使用"
        - "有什么功能"
      action: "explain_capabilities"
      priority: 2
      supported_states: ["IDLE"]
      
    # 一般聊天类意图
    general_chat:
      description: "用户进行一般性对话或闲聊"
      examples:
        - "今天天气真好"
        - "你觉得怎么样"
        - "随便聊聊"
      action: "respond_to_general_chat"
      priority: 1
      supported_states: ["IDLE"]
      
    # 提供信息类意图
    provide_information:
      description: "用户提供具体信息或回答问题"
      examples:
        - "我的预算是1万元"
        - "我们公司是做教育的"
        - "目标用户是学生"
      action: "process_answer_and_ask_next"
      priority: 1
      supported_states: ["COLLECTING_INFO"]
      
    # 未知意图
    unknown:
      description: "无法识别的用户意图"
      examples: []
      action: "clarify_intent"
      priority: 0
      supported_states: ["IDLE", "COLLECTING_INFO"]
      
    # 确认类意图
    confirm:
      description: "用户确认或同意某个操作"
      examples:
        - "确认"
        - "好的"
        - "同意"
        - "是的"
      action: "confirm_document"
      priority: 2
      supported_states: ["DOCUMENTING"]
      
    # 重新开始意图
    restart:
      description: "用户要求重新开始或重置对话"
      examples:
        - "重新开始"
        - "重来"
        - "重置"
      action: "restart_conversation"
      priority: 3
      supported_states: ["IDLE", "COLLECTING_INFO", "DOCUMENTING"]
      
    # 修改意图
    modify:
      description: "用户要求修改某些内容"
      examples:
        - "修改"
        - "改一下"
        - "调整"
      action: "modify_document"
      priority: 2
      supported_states: ["DOCUMENTING"]
      
    # 处理回答意图
    process_answer:
      description: "处理用户在信息收集阶段的回答"
      examples:
        - "我的需求是..."
        - "具体来说..."
        - "我希望..."
      action: "process_answer_and_ask_next"
      priority: 3
      supported_states: ["COLLECTING_INFO"]
      
    # 请求澄清意图
    request_clarification:
      description: "用户请求澄清或解释某些概念"
      examples:
        - "什么意思"
        - "不太明白"
        - "能解释一下吗"
      action: "clarify_intent"
      priority: 2
      supported_states: ["IDLE", "COLLECTING_INFO"]
      
    # 领域特定查询意图
    domain_specific_query:
      description: "用户询问特定领域的专业知识"
      examples:
        - "什么是UI设计"
        - "营销策略有哪些"
        - "软件开发流程"
      action: "handle_business_domain_query"
      priority: 2
      supported_states: ["IDLE"]
      
    # 流程查询意图
    process_query:
      description: "用户询问系统流程或操作步骤"
      examples:
        - "流程是什么"
        - "怎么操作"
        - "步骤是什么"
      action: "explain_capabilities"
      priority: 2
      supported_states: ["IDLE"]
      
    # 系统能力查询意图
    system_capability_query:
      description: "用户询问系统的具体能力和功能"
      examples:
        - "你能帮我做什么"
        - "有哪些功能"
        - "支持什么服务"
      action: "explain_capabilities"
      priority: 2
      supported_states: ["IDLE"]
      
    # 用户反馈意图
    feedback:
      description: "用户提供反馈或评价"
      examples:
        - "很好"
        - "不错"
        - "有问题"
        - "建议改进"
      action: "acknowledge_and_redirect"
      priority: 1
      supported_states: ["IDLE"]

  # 状态转换规则
  state_transitions:
    IDLE:
      greeting: "IDLE"
      business_requirement: "COLLECTING_INFO"
      search_knowledge_base: "IDLE"
      emotional_support: "IDLE"
      ask_question: "IDLE"
      general_chat: "IDLE"
      domain_specific_query: "IDLE"
      process_query: "IDLE"
      system_capability_query: "IDLE"
      feedback: "IDLE"
      request_clarification: "IDLE"
      restart: "IDLE"
      unknown: "IDLE"
      
    COLLECTING_INFO:
      provide_information: "COLLECTING_INFO"
      process_answer: "COLLECTING_INFO"
      request_clarification: "COLLECTING_INFO"
      confirm: "DOCUMENTING"
      restart: "IDLE"
      unknown: "COLLECTING_INFO"
      
    DOCUMENTING:
      confirm: "IDLE"
      modify: "DOCUMENTING"
      restart: "IDLE"
      unknown: "DOCUMENTING"

  # 决策规则配置
  decision_rules:
    priority_order: ["restart", "confirm", "business_requirement", "search_knowledge_base", "emotional_support", "ask_question", "domain_specific_query", "process_query", "system_capability_query", "greeting", "provide_information", "process_answer", "request_clarification", "feedback", "general_chat", "modify", "unknown"]
    
    default_action: "clarify_intent"
    default_state: "IDLE"
    
    # 置信度阈值
    confidence_thresholds:
      high: 0.8
      medium: 0.6
      low: 0.4
