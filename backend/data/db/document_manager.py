"""
文档管理器 - 负责文档相关的数据库操作
"""
import logging
from typing import Any, Dict, List, Optional
from backend.config.unified_config_loader import get_unified_config


class DocumentManager:
    """文档数据库操作管理器"""
    
    def __init__(self, db_manager):
        """
        初始化文档管理器
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
    
    async def get_document(self, document_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        根据文档ID获取文档内容

        Args:
            document_id: 文档ID
            user_id: 用户ID

        Returns:
            Dict: 包含文档信息的字典，如果未找到则返回None
        """
        query = get_unified_config().get_database_query("documents.get_content")
        result = await self.db_manager.execute_query(query, (document_id, user_id))
        return result[0] if result else None

    async def get_latest_document(self, conversation_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        根据会话ID获取最新文档

        Args:
            conversation_id: 会话ID
            user_id: 用户ID

        Returns:
            Dict: 包含文档信息的字典，如果未找到则返回None
        """
        query = get_unified_config().get_database_query("documents.get_by_conversation")
        result = await self.db_manager.execute_query(query, (conversation_id, user_id))
        return result[0] if result else None
    
    async def get_documents_by_conversation_id(self, conversation_id: str, user_id: str) -> List[Dict[str, Any]]:
        """
        根据会话ID获取所有相关文档

        Args:
            conversation_id: 会话ID (字符串类型)
            user_id: 用户ID

        Returns:
            List[Dict[str, Any]]: 包含文档信息的字典列表
        """
        query = get_unified_config().get_database_query("documents.get_by_conversation")
        return await self.db_manager.execute_query(query, (conversation_id, user_id))
    
    async def insert_document(self, document_id: str, conversation_id: str, user_id: str,
                              content: str, status: str,
                              created_at: str, updated_at: str, version: int = None) -> bool:
        """
        插入文档记录

        Args:
            document_id: 文档ID
            conversation_id: 会话ID (字符串类型)
            user_id: 用户ID
            content: 文档内容
            status: 文档状态
            created_at: 创建时间
            updated_at: 更新时间
            version: 版本号，如果为None则自动计算下一个版本号

        Returns:
            bool: 操作是否成功
        """
        try:
            self.logger.debug(f"准备插入文档: document_id={document_id}, conversation_id={conversation_id}, user_id={user_id}")

            # 使用事务确保版本号计算的原子性
            queries = []
            
            # 如果没有指定版本号，自动计算下一个版本号
            if version is None:
                max_version_query = """
                    SELECT MAX(version) as max_version
                    FROM documents
                    WHERE conversation_id = ? AND user_id = ?
                """
                queries.append((max_version_query, (conversation_id, user_id)))

            # 插入文档的查询
            query = get_unified_config().get_database_query("documents.save_document")
            if version is None:
                # 在事务中先查询最大版本号，然后插入
                async def execute_in_transaction():
                    # 获取最大版本号
                    result = await self.db_manager.execute_query(max_version_query, (conversation_id, user_id))
                    max_version = None
                    if result and len(result) > 0:
                        row = result[0]
                        if isinstance(row, dict):
                            max_version = row.get('max_version') or row.get('MAX(version)')
                        else:
                            max_version = row[0] if len(row) > 0 else None
                    version = 1 if max_version is None else max_version + 1
                    
                    # 插入文档
                    await self.db_manager.execute_update(
                        query,
                        (document_id, conversation_id, user_id, version, content, status, created_at, updated_at))
                    return version
                
                version = await execute_in_transaction()
            else:
                # 如果指定了版本号，直接插入
                await self.db_manager.execute_update(
                    query,
                    (document_id, conversation_id, user_id, version, content, status, created_at, updated_at))

            self.logger.info(f"成功插入文档: {document_id}, 版本: {version}")
            return True

        except Exception as e:
            self.logger.error(f"插入文档失败: {e}")
            return False
    
    async def update_document_content(self, document_id: str, user_id: str, content: str,
                                      status: str, updated_at: str) -> bool:
        """
        更新文档内容
        
        Args:
            document_id: 文档ID
            user_id: 用户ID
            content: 新的文档内容
            status: 文档状态
            updated_at: 更新时间
            
        Returns:
            bool: 操作是否成功
        """
        try:
            query = get_unified_config().get_database_query("documents.update_content")
            rows_affected = await self.db_manager.execute_update(
                query,
                (content, status, updated_at, document_id, user_id)
            )
            
            if rows_affected > 0:
                self.logger.info(f"成功更新文档: {document_id}")
                return True
            else:
                self.logger.warning(f"文档不存在或未更新: {document_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"更新文档失败: {e}")
            return False
    
    async def update_document_status(self, document_id: str, user_id: str, status: str, updated_at: str) -> bool:
        """
        更新文档状态
        
        Args:
            document_id: 文档ID
            user_id: 用户ID
            status: 新状态
            updated_at: 更新时间
            
        Returns:
            bool: 操作是否成功
        """
        try:
            query = get_unified_config().get_database_query("documents.update_status")
            rows_affected = await self.db_manager.execute_update(
                query,
                (status, updated_at, document_id, user_id)
            )
            
            return rows_affected > 0
            
        except Exception as e:
            self.logger.error(f"更新文档状态失败: {e}")
            return False
    
    async def document_exists(self, conversation_id: str, user_id: str) -> bool:
        """
        检查会话是否有文档
        
        Args:
            conversation_id: 会话ID
            user_id: 用户ID
            
        Returns:
            bool: 是否存在文档
        """
        query = get_unified_config().get_database_query("documents.check_exists")
        return await self.db_manager.record_exists(query, (conversation_id, user_id))
    
    async def delete_document(self, document_id: str, user_id: str) -> bool:
        """
        删除文档
        
        Args:
            document_id: 文档ID
            user_id: 用户ID
            
        Returns:
            bool: 操作是否成功
        """
        try:
            query = get_unified_config().get_database_query("documents.delete_document")
            rows_affected = await self.db_manager.execute_update(query, (document_id, user_id))
            
            if rows_affected > 0:
                self.logger.info(f"成功删除文档: {document_id}")
                return True
            else:
                self.logger.warning(f"文档不存在: {document_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除文档失败: {e}")
            return False
    
    async def get_document_list(self, conversation_id: str, user_id: str) -> List[Dict[str, Any]]:
        """
        获取会话的文档列表
        
        Args:
            conversation_id: 会话ID
            user_id: 用户ID
            
        Returns:
            List[Dict[str, Any]]: 文档列表
        """
        query = get_unified_config().get_database_query("documents.get_list")
        return await self.db_manager.execute_query(query, (conversation_id, user_id))
