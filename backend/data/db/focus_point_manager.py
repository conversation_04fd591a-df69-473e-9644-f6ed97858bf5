"""
关注点状态管理类，用于处理关注点状态的持久化和加载
"""
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime
from .database_manager import DatabaseManager
from backend.config.unified_config_loader import get_unified_config

class FocusPointManager:
    """关注点状态管理类"""

    def __init__(self, db_manager: DatabaseManager):
        """
        初始化关注点状态管理器

        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)

    async def ensure_conversation_exists(self, conversation_id: str, user_id: str) -> bool:
        """
        确保会话存在，如果不存在则创建

        Args:
            conversation_id: 会话ID
            user_id: 用户ID

        Returns:
            bool: 操作是否成功
        """
        try:
            # 检查会话是否存在
            conversation_exists = await self.db_manager.record_exists(
                get_unified_config().get_database_query("conversations.check_exists"),
                (conversation_id, user_id)
            )

            if not conversation_exists:
                # 创建新会话（使用 INSERT OR IGNORE，无需特殊错误处理）
                now = datetime.now().isoformat()
                await self.db_manager.execute_update(
                    get_unified_config().get_database_query("conversations.create_new"),
                    (conversation_id, user_id, "active", now, now, now)
                )
                self.logger.debug(f"尝试创建会话 - conversation_id: {conversation_id} for user: {user_id}")

            return True
        except Exception as e:
            self.logger.error(f"确保会话存在失败: {str(e)}")
            return False

    async def initialize_focus_points(self, conversation_id: str, user_id: str, focus_points: List[Dict[str, Any]]) -> bool:
        """
        初始化关注点状态

        Args:
            conversation_id: 会话ID
            user_id: 用户ID
            focus_points: 关注点列表

        Returns:
            bool: 初始化是否成功
        """
        try:
            # 确保会话存在
            if not await self.ensure_conversation_exists(conversation_id, user_id):
                return False

            # 准备批量插入数据
            params_list = []
            for point in focus_points:
                # 检查关注点是否已存在
                exists = await self.db_manager.record_exists(
                    get_unified_config().get_database_query("focus_points.check_exists"),
                    (conversation_id, user_id, point["id"])
                )

                if not exists:
                    params_list.append((
                        conversation_id,
                        user_id,
                        point["id"],
                        "pending",  # 初始状态
                        0,          # 尝试次数
                        0,          # 是否已覆盖
                        "",         # 提取的信息
                        datetime.now().isoformat()  # 更新时间
                    ))

            # 如果有需要插入的数据
            if params_list:
                await self.db_manager.execute_batch(
                    get_unified_config().get_database_query("focus_points.batch_insert"),
                    params_list
                )

                self.logger.debug(f"初始化关注点状态完成 - conversation_id: {conversation_id}, user_id: {user_id}, 关注点数量: {len(params_list)}")
            else:
                self.logger.debug(f"关注点状态已存在，无需初始化 - conversation_id: {conversation_id}, user_id: {user_id}")

            return True
        except Exception as e:
            self.logger.error(f"初始化关注点状态失败: {str(e)}")
            return False

    async def load_focus_points_status(self, conversation_id: str, user_id: str) -> Dict[str, Dict[str, Any]]:
        """
        加载关注点状态

        Args:
            conversation_id: 会话ID
            user_id: 用户ID

        Returns:
            Dict[str, Dict[str, Any]]: 关注点状态字典
        """
        try:
            # 确保会话存在
            await self.ensure_conversation_exists(conversation_id, user_id)

            results = await self.db_manager.execute_query(
                get_unified_config().get_database_query("focus_points.get_status"),
                (conversation_id, user_id)
            )

            status = {}
            for row in results:
                status[row["focus_id"]] = {
                    "status": row["status"],
                    "attempts": row["attempts"],
                    "value": row["extracted_info"],
                    "is_covered": bool(row["is_covered"]),
                    "updated_at": row["updated_at"]
                }

            self.logger.debug(f"加载关注点状态 - conversation_id: {conversation_id}, user_id: {user_id}, 状态数量: {len(status)}")
            return status
        except Exception as e:
            self.logger.error(f"加载关注点状态失败: {str(e)}")
            return {}

    async def update_focus_point_status(self, conversation_id: str, user_id: str, focus_id: str, status: str, value: Optional[str] = None) -> bool:
        """
        更新关注点状态

        Args:
            conversation_id: 会话ID
            user_id: 用户ID
            focus_id: 关注点ID
            status: 新状态
            value: 提取的值

        Returns:
            bool: 更新是否成功
        """
        try:
            # 确保会话存在
            if not await self.ensure_conversation_exists(conversation_id, user_id):
                return False

            # 检查记录是否存在
            exists = await self.db_manager.record_exists(
                get_unified_config().get_database_query("focus_points.check_exists"),
                (conversation_id, user_id, focus_id)
            )

            if exists:
                # 更新现有记录
                await self.db_manager.execute_update(
                    get_unified_config().get_database_query("focus_points.complex_update"),
                    (status, 1 if value is not None else 0, value or "", datetime.now().isoformat(), conversation_id, user_id, focus_id)
                )
            else:
                # 插入新记录
                await self.db_manager.execute_update(
                    get_unified_config().get_database_query("focus_points.insert_new"),
                    (
                        conversation_id,
                        user_id,
                        focus_id,
                        status,
                        1 if status == "processing" else 0,
                        1 if status == "completed" else 0,
                        value or "",
                        datetime.now().isoformat()
                    )
                )

            self.logger.debug(f"更新关注点状态 - conversation_id: {conversation_id}, user_id: {user_id}, focus_id: {focus_id}, status: {status}")
            return True
        except Exception as e:
            self.logger.error(f"更新关注点状态失败: {str(e)}")
            return False

    async def get_focus_point_status(self, conversation_id: str, user_id: str, focus_id: str) -> Optional[Dict[str, Any]]:
        """
        获取关注点状态

        Args:
            conversation_id: 会话ID
            user_id: 用户ID
            focus_id: 关注点ID

        Returns:
            Optional[Dict[str, Any]]: 关注点状态
        """
        try:
            result = await self.db_manager.get_record(
                get_unified_config().get_database_query("focus_points.get_single_status"),
                (conversation_id, user_id, focus_id)
            )

            if result:
                return {
                    "status": result["status"],
                    "attempts": result["attempts"],
                    "value": result["extracted_info"],
                    "is_covered": bool(result["is_covered"]),
                    "updated_at": result["updated_at"]
                }

            return None
        except Exception as e:
            self.logger.error(f"获取关注点状态失败: {str(e)}")
            return None

    async def get_next_pending_focus_point(self, conversation_id: str, user_id: str, focus_points: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        获取下一个待处理的关注点

        Args:
            conversation_id: 会话ID
            user_id: 用户ID
            focus_points: 关注点列表

        Returns:
            Optional[Dict[str, Any]]: 下一个待处理的关注点
        """
        try:
            # 加载关注点状态
            status_dict = await self.load_focus_points_status(conversation_id, user_id)

            # 首先查找P0或P1优先级的待处理关注点
            for point in focus_points:
                point_id = point["id"]
                point_status = status_dict.get(point_id, {}).get("status", "pending")

                if point_status == "pending" and point["priority"] in ["P0", "P1"]:
                    return point

            # 如果没有P0/P1关注点，查找任何待处理的关注点
            for point in focus_points:
                point_id = point["id"]
                point_status = status_dict.get(point_id, {}).get("status", "pending")

                if point_status == "pending":
                    return point

            # 没有待处理的关注点
            return None
        except Exception as e:
            self.logger.error(f"获取下一个待处理关注点失败: {str(e)}")
            return None

    async def reset_focus_points_status(self, conversation_id: str, user_id: str) -> bool:
        """
        重置关注点状态，将所有关注点状态重置为pending

        Args:
            conversation_id: 会话ID
            user_id: 用户ID

        Returns:
            bool: 重置是否成功
        """
        try:
            # 确保会话存在
            if not await self.ensure_conversation_exists(conversation_id, user_id):
                return False

            # 重置所有关注点状态为pending
            await self.db_manager.execute_update(
                get_unified_config().get_database_query("focus_points.reset_all_status"),
                (datetime.now().isoformat(), conversation_id, user_id)
            )

            self.logger.debug(f"重置关注点状态完成 - conversation_id: {conversation_id}, user_id: {user_id}")
            return True
        except Exception as e:
            self.logger.error(f"重置关注点状态失败: {str(e)}")
            return False
