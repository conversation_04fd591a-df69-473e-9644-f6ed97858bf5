"""
LLM配置集中管理器

功能：
1. 集中管理所有LLM模型配置
2. 为各Agent提供模型配置查询服务
3. 管理Agent与模型的映射关系
4. 确保模型配置的一致性和可维护性

使用方法:
1. 配置在unified_config.yaml中定义
2. 使用set_model_for_agent()设置Agent模型映射
3. 通过get_model_config()获取配置
4. 配置参数会传递给LLM调用接口

配置示例(在unified_config.yaml中):
llm:
  configurations:
    conversation:
      model: "gpt-3.5-turbo"
      temperature: 0.7
      max_tokens: 800
      description: "对话交互"
"""

import logging

class LLMConfigManager:
    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 使用统一配置系统
        try:
            from ..config.unified_config_loader import get_unified_config
            self.unified_config = get_unified_config()
            self.logger.info("LLM配置管理器使用统一配置系统")
        except Exception as e:
            self.logger.error(f"无法加载统一配置系统: {e}")
            raise RuntimeError(f"LLM配置管理器初始化失败: {e}")


    def set_model_for_agent(self, agent_name: str, model_name: str):
        """为指定Agent设置模型
        参数:
            agent_name: Agent名称(如"domain_classifier"、"intent_recognition"等)
            model_name: 要使用的模型名称
        
        当前系统中已定义的Agent名称:
        - domain_classifier: 领域分类器
        - intent_recognition: 意图识别器

        - information_extractor: 信息提取器
        - conversation_flow: 对话流程管理器
        - user_interaction: 用户交互处理器
        - value_extractor: 值提取器
        - question_polisher: 问题润色器
        """
        if agent_name not in ["domain_classifier", "intent_recognition", "information_extractor", "conversation_flow", "user_interaction", "value_extractor", "question_polisher"]:
            raise ValueError(f"未知的Agent名称: {agent_name}")
        if model_name not in self.config:
            raise ValueError(f"未配置的模型: {model_name}")
        self.agent_model_mapping[agent_name] = model_name

    def get_model_config(self, model_name: str = None, agent_name: str = None) -> dict:
        """获取模型配置（包含场景参数）
        参数:
            model_name: 直接指定模型名称(可选)
            agent_name: 通过Agent名称获取配置(可选)
        返回:
            完整的模型配置字典（包含场景参数）

        当找不到指定模型时，将返回默认模型配置
        """
        try:
            # 1. 确定场景名称
            scenario = agent_name or model_name or "default"

            # 2. 从统一配置获取场景到模型的映射
            llm_config = self.unified_config._config.get('llm', {})
            scenario_mapping = llm_config.get('scenario_mapping', {})
            models_config = llm_config.get('models', {})
            scenario_params = llm_config.get('scenario_params', {})
            default_model = llm_config.get('default_model', 'deepseek-chat')

            # 3. 获取模型名称
            if model_name:
                # 直接指定模型名称
                target_model = model_name
            elif scenario in scenario_mapping:
                # 从场景映射获取模型名称
                target_model = scenario_mapping[scenario]
            else:
                # 使用默认模型
                target_model = default_model

            # 4. 获取模型基础配置
            if target_model in models_config:
                config = models_config[target_model].copy()
                # 确保包含必要的字段
                self._ensure_required_fields(config)
            else:
                self.logger.warning(f"模型 '{target_model}' 未在配置中找到，使用默认配置")
                config = self._get_default_config()

            # 5. 合并场景参数
            if scenario in scenario_params:
                scene_params = scenario_params[scenario]
                # 确保scene_params是字典类型
                if isinstance(scene_params, dict):
                    config.update(scene_params)
                    self.logger.debug(f"场景 '{scenario}' 使用模型 '{target_model}' + 场景参数: {scene_params}")
                else:
                    self.logger.warning(f"场景 '{scenario}' 的参数不是字典格式: {type(scene_params)}, 跳过合并")
            else:
                # 使用默认场景参数
                default_params = scenario_params.get('default', {})
                if isinstance(default_params, dict):
                    config.update(default_params)
                    self.logger.debug(f"场景 '{scenario}' 使用默认场景参数: {default_params}")
                else:
                    self.logger.warning(f"默认场景参数不是字典格式: {type(default_params)}, 跳过合并")

            return config

        except Exception as e:
            self.logger.error(f"获取LLM配置失败: {e}", exc_info=True)
            self.logger.error(f"调试信息 - scenario: {scenario}, target_model: {target_model if 'target_model' in locals() else 'N/A'}")
            return self._get_default_config()

    def get_scenario_params(self, scenario: str) -> dict:
        """获取场景参数
        参数:
            scenario: 场景名称
        返回:
            场景参数字典
        """
        try:
            llm_config = self.unified_config._config.get('llm', {})
            scenario_params = llm_config.get('scenario_params', {})

            if scenario in scenario_params:
                params = scenario_params[scenario]
                # 确保返回字典的副本
                if isinstance(params, dict):
                    result = params.copy()
                else:
                    # 如果不是字典，创建默认参数
                    result = {
                        "temperature": 0.7,
                        "max_tokens": 4000,
                        "timeout": 30
                    }
                self.logger.debug(f"获取场景 '{scenario}' 参数: {result}")
                return result
            else:
                # 使用默认参数
                default_params = scenario_params.get('default', {
                    "temperature": 0.7,
                    "max_tokens": 4000,
                    "timeout": 30
                })
                # 确保返回字典的副本
                if isinstance(default_params, dict):
                    result = default_params.copy()
                else:
                    result = {
                        "temperature": 0.7,
                        "max_tokens": 4000,
                        "timeout": 30
                    }
                self.logger.debug(f"场景 '{scenario}' 使用默认参数: {result}")
                return result

        except Exception as e:
            self.logger.error(f"获取场景参数失败: {scenario}, 错误: {e}")
            return {
                "temperature": self.unified_config.get_threshold("confidence.default", 0.7),
                "max_tokens": self.unified_config.get_threshold("limits.max_results", 4000),
                "timeout": self.unified_config.get_threshold("performance.timeout.medium", 30)
            }

    def _get_default_config(self) -> dict:
        """获取默认配置"""
        return {
            "provider": "deepseek",
            "model": "deepseek-chat",
            "model_name": "deepseek-chat",
            "temperature": self.unified_config.get_threshold("confidence.default", 0.7),
            "max_tokens": self.unified_config.get_threshold("limits.max_results", 4000),
            "timeout": self.unified_config.get_threshold("performance.timeout.long", 45),
            "max_retries": self.unified_config.get_threshold("performance.retry.default", 3)
        }

    def _ensure_required_fields(self, config_dict: dict) -> None:
        """确保配置字典包含所有必要的字段"""
        # 确保有model_name字段
        if 'model_name' not in config_dict:
            if 'model' in config_dict:
                config_dict['model_name'] = config_dict['model']
            else:
                config_dict['model_name'] = 'deepseek-chat'

        # 确保有provider字段
        if 'provider' not in config_dict:
            config_dict['provider'] = 'deepseek'

        # 确保有API密钥字段
        if 'api_key' not in config_dict:
            # 根据provider设置默认的API密钥环境变量
            provider = config_dict.get('provider', 'deepseek')
            if provider == 'deepseek':
                config_dict['api_key'] = '${DEEPSEEK_API_KEY}'
            elif provider == 'doubao':
                config_dict['api_key'] = '${DOUBAO_API_KEY}'
            elif provider == 'qwen':
                config_dict['api_key'] = '${QWEN_API_KEY}'
            else:
                config_dict['api_key'] = '${API_KEY}'

        # 确保有基本参数
        if 'temperature' not in config_dict:
            config_dict['temperature'] = 0.7
        if 'max_tokens' not in config_dict:
            config_dict['max_tokens'] = 1000
        if 'timeout' not in config_dict:
            config_dict['timeout'] = 30

    def get_model_info(self) -> list:
        """获取所有可用模型名称列表
        
        返回:
            list: 可用模型名称列表
        """
        return list(self.config.keys())

    def create_for_model(self, model_name: str) -> dict:
        """根据模型名称创建配置(兼容LLMFactory接口)
        
        参数:
            model_name: 模型名称
            
        返回:
            dict: 模型配置字典
            
        抛出:
            ValueError: 当模型未找到时
        """
        if model_name not in self.config:
            raise ValueError(f"未找到模型 '{model_name}' 的配置")
        return self.config[model_name]
