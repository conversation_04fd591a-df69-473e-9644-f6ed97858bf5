"""
统一决策引擎策略模块

包含所有业务决策策略的实现，包括：
- 问候策略
- 需求收集策略  
- 知识库查询策略
- 能力介绍策略
- 情感支持策略
- 回退策略

所有策略都继承自DecisionStrategy基类，实现统一的接口。
"""

from typing import List, Dict, Optional

# 导入基础类型
from ..decision_types import DecisionStrategy

# 策略注册表
_strategy_registry: Dict[str, DecisionStrategy] = {}


def register_strategy(strategy: DecisionStrategy) -> None:
    """
    注册策略到模块级注册表
    
    Args:
        strategy: 要注册的策略实例
    """
    _strategy_registry[strategy.name] = strategy


def get_strategy(strategy_name: str) -> Optional[DecisionStrategy]:
    """
    获取指定名称的策略
    
    Args:
        strategy_name: 策略名称
        
    Returns:
        Optional[DecisionStrategy]: 策略实例，如果不存在则返回None
    """
    return _strategy_registry.get(strategy_name)


def get_all_strategies() -> List[DecisionStrategy]:
    """
    获取所有已注册的策略
    
    Returns:
        List[DecisionStrategy]: 所有策略实例列表
    """
    return list(_strategy_registry.values())


def get_strategies_by_intent(intent: str) -> List[DecisionStrategy]:
    """
    根据意图获取支持的策略
    
    Args:
        intent: 意图名称
        
    Returns:
        List[DecisionStrategy]: 支持该意图的策略列表
    """
    return [
        strategy for strategy in _strategy_registry.values()
        if intent in strategy.supported_intents
    ]


def clear_strategies() -> None:
    """清空所有策略（主要用于测试）"""
    _strategy_registry.clear()


# 策略导入（延迟导入避免循环依赖）
def load_all_strategies() -> None:
    """加载所有策略实现"""
    try:
        # 导入所有策略实现
        from .greeting_strategy import GreetingStrategy
        from .requirement_strategy import RequirementStrategy
        from .knowledge_base_strategy import KnowledgeBaseStrategy
        from .capabilities_strategy import CapabilitiesStrategy
        from .emotional_support_strategy import EmotionalSupportStrategy
        from .fallback_strategy import FallbackStrategy
        
        # 自动注册所有策略
        strategies = [
            GreetingStrategy(),
            RequirementStrategy(),
            KnowledgeBaseStrategy(),
            CapabilitiesStrategy(),
            EmotionalSupportStrategy(),
            FallbackStrategy()
        ]
        
        for strategy in strategies:
            register_strategy(strategy)
            
        print(f"✅ 成功加载 {len(strategies)} 个策略")
        
    except ImportError as e:
        print(f"⚠️ 策略加载失败: {e}")
        print("部分策略可能尚未实现")


# 导出的公共接口
__all__ = [
    'register_strategy',
    'get_strategy', 
    'get_all_strategies',
    'get_strategies_by_intent',
    'clear_strategies',
    'load_all_strategies'
]
