"""
知识库管理器

负责管理知识库文档的摄入、更新、删除和维护，支持批量处理和增量更新。
提供文档版本管理和冲突解决功能。
"""

import logging
import os
import hashlib
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

import chromadb
from chromadb.utils import embedding_functions

from backend.config.knowledge_base_config import get_knowledge_base_config_manager


@dataclass
class DocumentMetadata:
    """文档元数据"""
    doc_id: str
    title: str
    source_path: str
    content_hash: str
    role: str  # company, developer, general
    category: str
    tags: List[str]
    created_at: datetime
    updated_at: datetime
    version: int
    chunk_count: int


@dataclass
class DocumentChunk:
    """文档块"""
    chunk_id: str
    doc_id: str
    content: str
    chunk_index: int
    metadata: Dict[str, Any]


@dataclass
class IngestionResult:
    """摄入结果"""
    success: bool
    doc_id: str
    chunks_processed: int
    processing_time: float
    error: Optional[str] = None
    warnings: List[str] = None


class DocumentProcessor:
    """文档处理器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
    def chunk_document(self, content: str, doc_id: str) -> List[DocumentChunk]:
        """
        文档分块处理
        
        Args:
            content: 文档内容
            doc_id: 文档ID
            
        Returns:
            List[DocumentChunk]: 文档块列表
        """
        try:
            config = self.get_unified_config().get_config()
            chunk_size = config.document_processing.get("chunk_size", 800)
            chunk_overlap = config.document_processing.get("chunk_overlap", 100)
            max_chunks = config.document_processing.get("max_chunks_per_doc", 50)
            
            chunks = []
            content_length = len(content)
            
            if content_length <= chunk_size:
                # 内容较短，直接作为一个块
                chunk = DocumentChunk(
                    chunk_id=f"{doc_id}_chunk_0",
                    doc_id=doc_id,
                    content=content,
                    chunk_index=0,
                    metadata={"total_chunks": 1, "content_length": content_length}
                )
                chunks.append(chunk)
                return chunks
            
            # 递归字符分块
            start = 0
            chunk_index = 0
            
            while start < content_length and chunk_index < max_chunks:
                end = min(start + chunk_size, content_length)
                
                # 尝试在句子边界分割
                if end < content_length:
                    # 寻找最近的句号、问号或感叹号
                    sentence_end = max(
                        content.rfind('。', start, end),
                        content.rfind('？', start, end),
                        content.rfind('！', start, end),
                        content.rfind('\n', start, end)
                    )
                    
                    if sentence_end > start:
                        end = sentence_end + 1
                
                chunk_content = content[start:end].strip()
                
                if chunk_content:
                    chunk = DocumentChunk(
                        chunk_id=f"{doc_id}_chunk_{chunk_index}",
                        doc_id=doc_id,
                        content=chunk_content,
                        chunk_index=chunk_index,
                        metadata={
                            "start_pos": start,
                            "end_pos": end,
                            "content_length": len(chunk_content)
                        }
                    )
                    chunks.append(chunk)
                    chunk_index += 1
                
                # 计算下一个块的起始位置（考虑重叠）
                start = max(start + chunk_size - chunk_overlap, end)
            
            # 更新总块数信息
            for chunk in chunks:
                chunk.metadata["total_chunks"] = len(chunks)
            
            self.logger.info(f"文档 {doc_id} 分块完成: {len(chunks)} 个块")
            return chunks
            
        except Exception as e:
            self.logger.error(f"文档分块失败: {e}")
            return []
    
    def calculate_content_hash(self, content: str) -> str:
        """计算内容哈希"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def extract_metadata_from_content(self, content: str, source_path: str) -> Dict[str, Any]:
        """从内容中提取元数据"""
        metadata = {
            "title": os.path.basename(source_path),
            "role": "general",
            "category": "general",
            "tags": []
        }
        
        # 简单的元数据提取逻辑
        lines = content.split('\n')[:10]  # 检查前10行
        
        for line in lines:
            line = line.strip()
            if line.startswith('# '):
                metadata["title"] = line[2:].strip()
            elif '雇主' in line or '公司' in line:
                metadata["role"] = "company"
            elif '开发者' in line or '自由职业' in line:
                metadata["role"] = "developer"
            elif '注册' in line or '账号' in line:
                metadata["category"] = "account"
            elif '项目' in line or '需求' in line:
                metadata["category"] = "project"
        
        return metadata


class KnowledgeBaseManager:
    """
    知识库管理器
    
    负责管理知识库文档的完整生命周期：
    - 文档摄入和更新
    - 批量处理和增量更新
    - 文档版本管理和冲突解决
    - 向量数据库维护
    """
    
    def __init__(self):
        """初始化知识库管理器"""
        self.logger = logging.getLogger(__name__)
        self.config_manager = get_knowledge_base_config_manager()
        self.document_processor = DocumentProcessor(self.config_manager)
        
        # 初始化ChromaDB客户端
        self.chroma_client = None
        self.collection = None
        self.embedding_function = None
        
        # 文档元数据存储（简化实现，实际应使用数据库）
        self.document_metadata: Dict[str, DocumentMetadata] = {}
        
        self._initialize_chroma_client()
    
    def _initialize_chroma_client(self):
        """初始化ChromaDB客户端"""
        try:
            config = self.get_unified_config().get_config()
            chroma_config = config.chroma_db
            
            # 创建ChromaDB客户端
            chroma_path = chroma_config.get("path", "backend/data/chroma_db")
            self.chroma_client = chromadb.PersistentClient(path=chroma_path)
            
            # 设置嵌入函数
            embedding_model = chroma_config.get("embedding_model", "moka-ai/m3e-base")
            self.embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
                model_name=embedding_model
            )
            
            # 获取或创建集合
            collection_name = chroma_config.get("collection_name", "hybrid_knowledge_base")
            try:
                self.collection = self.chroma_client.get_collection(
                    name=collection_name,
                    embedding_function=self.embedding_function
                )
                self.logger.info(f"连接到现有集合: {collection_name}")
            except Exception:
                self.collection = self.chroma_client.create_collection(
                    name=collection_name,
                    embedding_function=self.embedding_function
                )
                self.logger.info(f"创建新集合: {collection_name}")
            
        except Exception as e:
            self.logger.error(f"初始化ChromaDB客户端失败: {e}")
            self.chroma_client = None
            self.collection = None
    
    async def ingest_document(self, content: str, source_path: str, 
                            metadata: Optional[Dict[str, Any]] = None) -> IngestionResult:
        """
        摄入单个文档
        
        Args:
            content: 文档内容
            source_path: 源文件路径
            metadata: 可选的元数据
            
        Returns:
            IngestionResult: 摄入结果
        """
        start_time = time.time()
        doc_id = self._generate_doc_id(source_path)
        
        try:
            self.logger.info(f"开始摄入文档: {source_path}")
            
            # 检查ChromaDB是否可用
            if not self.collection:
                return IngestionResult(
                    success=False,
                    doc_id=doc_id,
                    chunks_processed=0,
                    processing_time=time.time() - start_time,
                    error="ChromaDB集合不可用"
                )
            
            # 计算内容哈希
            content_hash = self.document_processor.calculate_content_hash(content)
            
            # 检查是否需要更新
            if doc_id in self.document_metadata:
                existing_doc = self.document_metadata[doc_id]
                if existing_doc.content_hash == content_hash:
                    self.logger.info(f"文档内容未变化，跳过摄入: {doc_id}")
                    return IngestionResult(
                        success=True,
                        doc_id=doc_id,
                        chunks_processed=0,
                        processing_time=time.time() - start_time,
                        warnings=["文档内容未变化，跳过处理"]
                    )
            
            # 提取元数据
            extracted_metadata = self.document_processor.extract_metadata_from_content(content, source_path)
            if metadata:
                extracted_metadata.update(metadata)
            
            # 文档分块
            chunks = self.document_processor.chunk_document(content, doc_id)
            
            if not chunks:
                return IngestionResult(
                    success=False,
                    doc_id=doc_id,
                    chunks_processed=0,
                    processing_time=time.time() - start_time,
                    error="文档分块失败"
                )
            
            # 删除现有的文档块（如果存在）
            await self._remove_document_chunks(doc_id)
            
            # 添加新的文档块到ChromaDB
            chunk_ids = [chunk.chunk_id for chunk in chunks]
            chunk_contents = [chunk.content for chunk in chunks]
            chunk_metadatas = []
            
            for chunk in chunks:
                chunk_metadata = {
                    "doc_id": doc_id,
                    "chunk_index": chunk.chunk_index,
                    "source_path": source_path,
                    "role": extracted_metadata.get("role", "general"),
                    "category": extracted_metadata.get("category", "general"),
                    "title": extracted_metadata.get("title", ""),
                    **chunk.metadata
                }
                chunk_metadatas.append(chunk_metadata)
            
            # 批量添加到ChromaDB
            self.collection.add(
                ids=chunk_ids,
                documents=chunk_contents,
                metadatas=chunk_metadatas
            )
            
            # 更新文档元数据
            doc_metadata = DocumentMetadata(
                doc_id=doc_id,
                title=extracted_metadata.get("title", ""),
                source_path=source_path,
                content_hash=content_hash,
                role=extracted_metadata.get("role", "general"),
                category=extracted_metadata.get("category", "general"),
                tags=extracted_metadata.get("tags", []),
                created_at=datetime.now() if doc_id not in self.document_metadata else self.document_metadata[doc_id].created_at,
                updated_at=datetime.now(),
                version=self.document_metadata.get(doc_id, DocumentMetadata("", "", "", "", "", "", [], datetime.now(), datetime.now(), 0, 0)).version + 1,
                chunk_count=len(chunks)
            )
            
            self.document_metadata[doc_id] = doc_metadata
            
            processing_time = time.time() - start_time
            self.logger.info(f"文档摄入成功: {doc_id}, 处理了 {len(chunks)} 个块, 耗时 {processing_time:.2f}s")
            
            return IngestionResult(
                success=True,
                doc_id=doc_id,
                chunks_processed=len(chunks),
                processing_time=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"文档摄入失败: {e}", exc_info=True)
            return IngestionResult(
                success=False,
                doc_id=doc_id,
                chunks_processed=0,
                processing_time=time.time() - start_time,
                error=str(e)
            )

    async def ingest_documents_batch(self, documents: List[Dict[str, Any]]) -> List[IngestionResult]:
        """
        批量摄入文档

        Args:
            documents: 文档列表，每个文档包含 content, source_path, metadata

        Returns:
            List[IngestionResult]: 摄入结果列表
        """
        results = []

        self.logger.info(f"开始批量摄入 {len(documents)} 个文档")

        for i, doc in enumerate(documents):
            try:
                content = doc.get("content", "")
                source_path = doc.get("source_path", f"batch_doc_{i}")
                metadata = doc.get("metadata", {})

                result = await self.ingest_document(content, source_path, metadata)
                results.append(result)

                # 简单的进度报告
                if (i + 1) % 10 == 0:
                    self.logger.info(f"批量摄入进度: {i + 1}/{len(documents)}")

            except Exception as e:
                self.logger.error(f"批量摄入第 {i} 个文档失败: {e}")
                results.append(IngestionResult(
                    success=False,
                    doc_id=f"batch_doc_{i}",
                    chunks_processed=0,
                    processing_time=0,
                    error=str(e)
                ))

        success_count = sum(1 for r in results if r.success)
        self.logger.info(f"批量摄入完成: {success_count}/{len(documents)} 成功")

        return results

    async def update_document(self, doc_id: str, content: str,
                            metadata: Optional[Dict[str, Any]] = None) -> IngestionResult:
        """
        更新现有文档

        Args:
            doc_id: 文档ID
            content: 新内容
            metadata: 可选的新元数据

        Returns:
            IngestionResult: 更新结果
        """
        try:
            if doc_id not in self.document_metadata:
                return IngestionResult(
                    success=False,
                    doc_id=doc_id,
                    chunks_processed=0,
                    processing_time=0,
                    error="文档不存在"
                )

            existing_doc = self.document_metadata[doc_id]
            source_path = existing_doc.source_path

            # 合并元数据
            update_metadata = {
                "role": existing_doc.role,
                "category": existing_doc.category,
                "tags": existing_doc.tags
            }
            if metadata:
                update_metadata.update(metadata)

            # 使用摄入方法更新文档
            return await self.ingest_document(content, source_path, update_metadata)

        except Exception as e:
            self.logger.error(f"更新文档失败: {e}")
            return IngestionResult(
                success=False,
                doc_id=doc_id,
                chunks_processed=0,
                processing_time=0,
                error=str(e)
            )

    async def remove_document(self, doc_id: str) -> bool:
        """
        删除文档

        Args:
            doc_id: 文档ID

        Returns:
            bool: 删除是否成功
        """
        try:
            if doc_id not in self.document_metadata:
                self.logger.warning(f"尝试删除不存在的文档: {doc_id}")
                return False

            # 删除ChromaDB中的文档块
            success = await self._remove_document_chunks(doc_id)

            if success:
                # 删除元数据
                del self.document_metadata[doc_id]
                self.logger.info(f"文档删除成功: {doc_id}")
                return True
            else:
                self.logger.error(f"文档删除失败: {doc_id}")
                return False

        except Exception as e:
            self.logger.error(f"删除文档异常: {e}")
            return False

    async def _remove_document_chunks(self, doc_id: str) -> bool:
        """删除文档的所有块"""
        try:
            if not self.collection:
                return False

            # 查询文档的所有块
            results = self.collection.get(
                where={"doc_id": doc_id}
            )

            if results["ids"]:
                # 删除所有块
                self.collection.delete(ids=results["ids"])
                self.logger.debug(f"删除了文档 {doc_id} 的 {len(results['ids'])} 个块")

            return True

        except Exception as e:
            self.logger.error(f"删除文档块失败: {e}")
            return False

    def get_document_info(self, doc_id: str) -> Optional[DocumentMetadata]:
        """获取文档信息"""
        return self.document_metadata.get(doc_id)

    def list_documents(self, role: Optional[str] = None,
                      category: Optional[str] = None) -> List[DocumentMetadata]:
        """
        列出文档

        Args:
            role: 可选的角色过滤
            category: 可选的类别过滤

        Returns:
            List[DocumentMetadata]: 文档列表
        """
        documents = list(self.document_metadata.values())

        if role:
            documents = [doc for doc in documents if doc.role == role]

        if category:
            documents = [doc for doc in documents if doc.category == category]

        return documents

    def get_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        try:
            total_docs = len(self.document_metadata)
            total_chunks = sum(doc.chunk_count for doc in self.document_metadata.values())

            role_stats = {}
            category_stats = {}

            for doc in self.document_metadata.values():
                role_stats[doc.role] = role_stats.get(doc.role, 0) + 1
                category_stats[doc.category] = category_stats.get(doc.category, 0) + 1

            # ChromaDB集合信息
            collection_count = 0
            if self.collection:
                try:
                    collection_count = self.collection.count()
                except Exception:
                    pass

            return {
                "total_documents": total_docs,
                "total_chunks": total_chunks,
                "collection_chunks": collection_count,
                "role_distribution": role_stats,
                "category_distribution": category_stats,
                "chroma_available": self.collection is not None
            }

        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {"error": str(e)}

    def _generate_doc_id(self, source_path: str) -> str:
        """生成文档ID"""
        # 使用文件路径的哈希作为文档ID
        return hashlib.md5(source_path.encode('utf-8')).hexdigest()[:16]

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            health_info = {
                "config_manager": self.config_manager is not None,
                "chroma_client": self.chroma_client is not None,
                "collection": self.collection is not None,
                "embedding_function": self.embedding_function is not None,
                "document_count": len(self.document_metadata)
            }

            # 测试ChromaDB连接
            if self.collection:
                try:
                    collection_count = self.collection.count()
                    health_info["collection_count"] = collection_count
                    health_info["chroma_connection"] = True
                except Exception as e:
                    health_info["chroma_connection"] = False
                    health_info["chroma_error"] = str(e)

            return health_info

        except Exception as e:
            return {"error": str(e), "healthy": False}

    async def rebuild_index(self) -> Dict[str, Any]:
        """重建索引"""
        try:
            self.logger.info("开始重建知识库索引")

            if not self.collection:
                return {"success": False, "error": "ChromaDB集合不可用"}

            # 获取所有文档
            all_results = self.collection.get()
            original_count = len(all_results["ids"])

            # 清空集合
            if all_results["ids"]:
                self.collection.delete(ids=all_results["ids"])

            # 重新摄入所有文档
            rebuild_results = []
            for doc_id, doc_metadata in self.document_metadata.items():
                try:
                    # 这里需要重新读取文档内容，简化实现中跳过
                    self.logger.info(f"跳过重建文档: {doc_id} (需要原始内容)")
                except Exception as e:
                    self.logger.error(f"重建文档失败: {doc_id}, {e}")

            return {
                "success": True,
                "original_chunks": original_count,
                "rebuilt_documents": len(rebuild_results),
                "message": "索引重建完成（简化实现）"
            }

        except Exception as e:
            self.logger.error(f"重建索引失败: {e}")
            return {"success": False, "error": str(e)}
