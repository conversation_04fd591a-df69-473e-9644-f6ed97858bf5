"""
LLM工具类，包含错误处理、断路器、缓存等功能
"""
from enum import Enum
import time
import threading
from typing import Any, Optional, Tuple
from collections import OrderedDict

class LLMErrorType(Enum):
    """
    LLM错误类型枚举

    定义了LLM调用过程中可能出现的各种错误类型，
    用于错误分类和处理策略选择。
    """
    CLIENT_ERROR = "client_error"          # 客户端错误（4xx）
    SERVER_ERROR = "server_error"          # 服务器错误（5xx）
    RATE_LIMIT = "rate_limit"              # 速率限制错误
    TIMEOUT = "timeout"                    # 超时错误
    PARSE_ERROR = "parse_error"            # 解析错误
    INVALID_REQUEST = "invalid_request"    # 无效请求
    CIRCUIT_BREAKER = "circuit_breaker"    # 断路器开启

class LLMError(Exception):
    """
    LLM错误基类

    封装LLM调用过程中的各种错误，提供结构化的错误信息。

    Args:
        message: 错误消息
        error_type: 错误类型

    Attributes:
        error_type: LLMErrorType枚举值，表示错误类型

    Example:
        >>> try:
        ...     # LLM调用
        ...     pass
        ... except LLMError as e:
        ...     if e.error_type == LLMErrorType.RATE_LIMIT:
        ...         # 处理速率限制
        ...         pass
    """
    def __init__(self, message: str, error_type: LLMErrorType):
        super().__init__(message)
        self.error_type = error_type

    def __str__(self) -> str:
        return f"[{self.error_type.value}] {super().__str__()}"

def classify_error(error: Exception, provider: str) -> LLMError:
    """
    根据异常和提供商分类错误

    Args:
        error: 原始异常对象
        provider: LLM提供商名称 (如 'openai', 'deepseek', 'gemini')

    Returns:
        LLMError: 分类后的LLM错误对象

    Example:
        >>> try:
        ...     # LLM调用
        ...     pass
        ... except Exception as e:
        ...     llm_error = classify_error(e, 'openai')
        ...     print(f"错误类型: {llm_error.error_type}")
    """
    error_msg = str(error).lower()
    original_msg = str(error)

    # HTTP状态码错误处理
    if hasattr(error, 'status_code'):
        status_code = error.status_code
        if status_code == 400:
            return LLMError(original_msg, LLMErrorType.INVALID_REQUEST)
        elif status_code == 401:
            return LLMError(original_msg, LLMErrorType.CLIENT_ERROR)
        elif status_code == 429:
            return LLMError(original_msg, LLMErrorType.RATE_LIMIT)
        elif 400 <= status_code < 500:
            return LLMError(original_msg, LLMErrorType.CLIENT_ERROR)
        elif 500 <= status_code < 600:
            return LLMError(original_msg, LLMErrorType.SERVER_ERROR)

    # 提供商特定错误处理
    provider_patterns = {
        'openai': {
            'rate_limit_exceeded': LLMErrorType.RATE_LIMIT,
            'invalid_request_error': LLMErrorType.INVALID_REQUEST,
            'authentication_error': LLMErrorType.CLIENT_ERROR,
            'permission_error': LLMErrorType.CLIENT_ERROR,
            'not_found_error': LLMErrorType.CLIENT_ERROR,
            'unprocessable_entity_error': LLMErrorType.INVALID_REQUEST,
            'internal_server_error': LLMErrorType.SERVER_ERROR,
        },
        'deepseek': {
            'quota_exceeded': LLMErrorType.RATE_LIMIT,
            'invalid_api_key': LLMErrorType.CLIENT_ERROR,
            'model_not_found': LLMErrorType.CLIENT_ERROR,
            'content_filter': LLMErrorType.INVALID_REQUEST,
        },
        'gemini': {
            'quota_exceeded': LLMErrorType.RATE_LIMIT,
            'invalid_argument': LLMErrorType.INVALID_REQUEST,
            'permission_denied': LLMErrorType.CLIENT_ERROR,
            'resource_exhausted': LLMErrorType.RATE_LIMIT,
        }
    }

    # 检查提供商特定错误
    if provider.lower() in provider_patterns:
        for pattern, error_type in provider_patterns[provider.lower()].items():
            if pattern in error_msg:
                return LLMError(original_msg, error_type)

    # 通用错误模式匹配
    timeout_patterns = ['timeout', 'timed out', 'time out', 'connection timeout', 'read timeout']
    if any(pattern in error_msg for pattern in timeout_patterns):
        return LLMError(original_msg, LLMErrorType.TIMEOUT)

    rate_limit_patterns = ['rate limit', 'quota', 'too many requests', 'throttled', 'rate exceeded']
    if any(pattern in error_msg for pattern in rate_limit_patterns):
        return LLMError(original_msg, LLMErrorType.RATE_LIMIT)

    client_error_patterns = ['unauthorized', 'forbidden', 'bad request', 'invalid', 'authentication']
    if any(pattern in error_msg for pattern in client_error_patterns):
        return LLMError(original_msg, LLMErrorType.CLIENT_ERROR)

    parse_error_patterns = ['json', 'parse', 'decode', 'format', 'malformed']
    if any(pattern in error_msg for pattern in parse_error_patterns):
        return LLMError(original_msg, LLMErrorType.PARSE_ERROR)

    # 默认为服务器错误
    return LLMError(original_msg, LLMErrorType.SERVER_ERROR)

class CircuitBreaker:
    """
    断路器实现，用于防止级联故障

    当失败次数达到阈值时，断路器会打开，阻止后续请求。
    经过恢复时间后，断路器会尝试半开状态。

    Args:
        failure_threshold: 失败阈值，默认5次
        recovery_time: 恢复时间，默认60秒

    Example:
        >>> breaker = CircuitBreaker(failure_threshold=3, recovery_time=30)
        >>> if not breaker.is_open:
        ...     try:
        ...         # 执行操作
        ...         breaker.record_success()
        ...     except Exception:
        ...         breaker.record_failure()
    """
    def __init__(self, failure_threshold: int = 5, recovery_time: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_time = recovery_time
        self.failure_count = 0
        self.last_failure_time = None
        self._is_open = False

    def record_failure(self):
        """记录失败"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        if self.failure_count >= self.failure_threshold:
            self._is_open = True

    def record_success(self):
        """记录成功"""
        self.failure_count = 0
        self._is_open = False

    @property
    def is_open(self) -> bool:
        """检查断路器是否打开"""
        if self._is_open and self.last_failure_time and (time.time() - self.last_failure_time) > self.recovery_time:
            self._is_open = False
            self.failure_count = 0
        return self._is_open

class ResponseCache:
    """
    线程安全的响应缓存实现

    使用LRU策略和TTL过期机制，支持多线程并发访问。

    Args:
        max_size: 最大缓存条目数，默认100
        ttl: 缓存生存时间（秒），默认3600秒（1小时）

    Example:
        >>> cache = ResponseCache(max_size=50, ttl=1800)
        >>> cache.set("key1", {"result": "data"})
        >>> result = cache.get("key1")
        >>> if result is not None:
        ...     print("缓存命中")
    """
    def __init__(self, max_size: int = 100, ttl: int = 3600):
        self.max_size = max_size
        self.ttl = ttl
        self.cache: OrderedDict[str, Tuple[Any, float]] = OrderedDict()
        self._lock = threading.RLock()  # 使用可重入锁

    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值

        Args:
            key: 缓存键

        Returns:
            缓存的值，如果不存在或已过期则返回None
        """
        with self._lock:
            if key not in self.cache:
                return None
            value, timestamp = self.cache[key]
            if time.time() - timestamp > self.ttl:
                del self.cache[key]
                return None
            # 更新访问顺序（LRU）
            self.cache.move_to_end(key)
            return value

    def set(self, key: str, value: Any) -> None:
        """
        设置缓存值

        Args:
            key: 缓存键
            value: 要缓存的值
        """
        with self._lock:
            # 如果缓存已满，删除最旧的条目
            if len(self.cache) >= self.max_size and key not in self.cache:
                self.cache.popitem(last=False)
            self.cache[key] = (value, time.time())

    def clear(self) -> None:
        """清空所有缓存"""
        with self._lock:
            self.cache.clear()

    def size(self) -> int:
        """获取当前缓存条目数"""
        with self._lock:
            return len(self.cache)

    def cleanup_expired(self) -> int:
        """
        清理过期的缓存条目

        Returns:
            清理的条目数
        """
        with self._lock:
            current_time = time.time()
            expired_keys = [
                key for key, (_, timestamp) in self.cache.items()
                if current_time - timestamp > self.ttl
            ]
            for key in expired_keys:
                del self.cache[key]
            return len(expired_keys)
