"""
策略注册中心实现

负责管理所有决策策略的注册、查找和匹配，包括：
- 策略注册和注销
- 策略匹配和查找
- 策略一致性验证
- 策略冲突检测
"""

import logging
from typing import Dict, List, Optional, Set
from collections import defaultdict

from .decision_engine_interface import StrategyRegistryInterface
from .decision_types import DecisionStrategy, AnalyzedContext


class StrategyRegistry(StrategyRegistryInterface):
    """策略注册中心实现"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 策略存储
        self._strategies: Dict[str, DecisionStrategy] = {}
        self._strategies_by_intent: Dict[str, List[DecisionStrategy]] = defaultdict(list)
        self._strategies_by_priority: Dict[int, List[DecisionStrategy]] = defaultdict(list)
        
        # 注册历史
        self._registration_history: List[Dict[str, any]] = []
        
        self.logger.info("策略注册中心初始化完成")
    
    def register_strategy(self, strategy: DecisionStrategy) -> None:
        """
        注册决策策略
        
        Args:
            strategy: 要注册的策略
        """
        strategy_name = strategy.name
        
        # 检查是否已存在
        if strategy_name in self._strategies:
            self.logger.warning(f"策略 {strategy_name} 已存在，将被覆盖")
            self._unregister_internal(strategy_name)
        
        # 注册策略
        self._strategies[strategy_name] = strategy
        
        # 按意图分组
        for intent in strategy.supported_intents:
            self._strategies_by_intent[intent].append(strategy)
        
        # 按优先级分组
        priority = strategy.priority
        self._strategies_by_priority[priority].append(strategy)
        
        # 记录注册历史
        self._registration_history.append({
            "action": "register",
            "strategy_name": strategy_name,
            "supported_intents": strategy.supported_intents,
            "priority": priority,
            "timestamp": __import__('time').time()
        })
        
        self.logger.info(f"策略 {strategy_name} 注册成功，支持意图: {strategy.supported_intents}")
    
    def unregister_strategy(self, strategy_name: str) -> bool:
        """
        注销决策策略
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            bool: 是否成功注销
        """
        if strategy_name not in self._strategies:
            self.logger.warning(f"策略 {strategy_name} 不存在，无法注销")
            return False
        
        success = self._unregister_internal(strategy_name)
        
        if success:
            # 记录注销历史
            self._registration_history.append({
                "action": "unregister",
                "strategy_name": strategy_name,
                "timestamp": __import__('time').time()
            })
            
            self.logger.info(f"策略 {strategy_name} 注销成功")
        
        return success
    
    def _unregister_internal(self, strategy_name: str) -> bool:
        """内部注销方法"""
        try:
            strategy = self._strategies[strategy_name]
            
            # 从主存储中移除
            del self._strategies[strategy_name]
            
            # 从意图分组中移除
            for intent in strategy.supported_intents:
                if intent in self._strategies_by_intent:
                    self._strategies_by_intent[intent] = [
                        s for s in self._strategies_by_intent[intent] 
                        if s.name != strategy_name
                    ]
                    # 如果列表为空，删除键
                    if not self._strategies_by_intent[intent]:
                        del self._strategies_by_intent[intent]
            
            # 从优先级分组中移除
            priority = strategy.priority
            if priority in self._strategies_by_priority:
                self._strategies_by_priority[priority] = [
                    s for s in self._strategies_by_priority[priority]
                    if s.name != strategy_name
                ]
                # 如果列表为空，删除键
                if not self._strategies_by_priority[priority]:
                    del self._strategies_by_priority[priority]
            
            return True
            
        except Exception as e:
            self.logger.error(f"注销策略 {strategy_name} 失败: {e}")
            return False
    
    def get_strategy(self, strategy_name: str) -> Optional[DecisionStrategy]:
        """
        获取指定策略
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            Optional[DecisionStrategy]: 策略对象，如果不存在则返回None
        """
        return self._strategies.get(strategy_name)
    
    def get_all_strategies(self) -> List[DecisionStrategy]:
        """
        获取所有策略
        
        Returns:
            List[DecisionStrategy]: 所有策略列表
        """
        return list(self._strategies.values())
    
    async def match_strategies(self, context: AnalyzedContext) -> List[DecisionStrategy]:
        """
        匹配适用的策略
        
        Args:
            context: 分析后的上下文
            
        Returns:
            List[DecisionStrategy]: 匹配的策略列表，按优先级排序
        """
        matched_strategies = []
        
        # 1. 基于意图匹配
        intent_strategies = self._strategies_by_intent.get(context.intent, [])
        
        # 2. 检查每个策略是否能处理当前上下文
        for strategy in intent_strategies:
            try:
                if await strategy.can_handle(context):
                    matched_strategies.append(strategy)
            except Exception as e:
                self.logger.error(f"策略 {strategy.name} 检查处理能力时出错: {e}")
        
        # 3. 如果没有基于意图的匹配，检查所有策略
        if not matched_strategies:
            for strategy in self._strategies.values():
                try:
                    if await strategy.can_handle(context):
                        matched_strategies.append(strategy)
                except Exception as e:
                    self.logger.error(f"策略 {strategy.name} 检查处理能力时出错: {e}")
        
        # 4. 按优先级排序（优先级高的在前）
        matched_strategies.sort(key=lambda s: s.priority, reverse=True)
        
        self.logger.debug(f"为意图 {context.intent} 匹配到 {len(matched_strategies)} 个策略")
        
        return matched_strategies
    
    def get_strategies_by_intent(self, intent: str) -> List[DecisionStrategy]:
        """
        根据意图获取策略
        
        Args:
            intent: 意图名称
            
        Returns:
            List[DecisionStrategy]: 支持该意图的策略列表
        """
        return self._strategies_by_intent.get(intent, []).copy()
    
    def validate_strategy_consistency(self) -> List[Dict[str, any]]:
        """
        验证策略一致性
        
        Returns:
            List[Dict[str, any]]: 发现的问题列表
        """
        issues = []
        
        # 1. 检查优先级冲突
        for priority, strategies in self._strategies_by_priority.items():
            if len(strategies) > 1:
                # 检查是否有意图重叠
                intent_sets = [set(s.supported_intents) for s in strategies]
                for i, set_a in enumerate(intent_sets):
                    for j, set_b in enumerate(intent_sets[i+1:], i+1):
                        overlap = set_a & set_b
                        if overlap:
                            issues.append({
                                "type": "priority_conflict",
                                "priority": priority,
                                "strategies": [strategies[i].name, strategies[j].name],
                                "overlapping_intents": list(overlap),
                                "severity": "high" if len(overlap) > 1 else "medium"
                            })
        
        # 2. 检查意图覆盖
        all_intents = set()
        for strategy in self._strategies.values():
            all_intents.update(strategy.supported_intents)
        
        for intent in all_intents:
            supporting_strategies = self._strategies_by_intent.get(intent, [])
            if len(supporting_strategies) == 0:
                issues.append({
                    "type": "no_strategy_for_intent",
                    "intent": intent,
                    "severity": "high"
                })
            elif len(supporting_strategies) > 3:
                issues.append({
                    "type": "too_many_strategies_for_intent",
                    "intent": intent,
                    "strategy_count": len(supporting_strategies),
                    "strategies": [s.name for s in supporting_strategies],
                    "severity": "medium"
                })
        
        # 3. 检查策略名称冲突
        strategy_names = [s.name for s in self._strategies.values()]
        if len(strategy_names) != len(set(strategy_names)):
            duplicates = []
            seen = set()
            for name in strategy_names:
                if name in seen:
                    duplicates.append(name)
                seen.add(name)
            
            issues.append({
                "type": "duplicate_strategy_names",
                "duplicates": duplicates,
                "severity": "critical"
            })
        
        return issues
    
    def get_registry_stats(self) -> Dict[str, any]:
        """获取注册中心统计信息"""
        return {
            "total_strategies": len(self._strategies),
            "total_intents": len(self._strategies_by_intent),
            "total_priorities": len(self._strategies_by_priority),
            "strategies_by_priority": {
                priority: len(strategies) 
                for priority, strategies in self._strategies_by_priority.items()
            },
            "intents_coverage": {
                intent: len(strategies)
                for intent, strategies in self._strategies_by_intent.items()
            },
            "registration_history_count": len(self._registration_history)
        }
    
    def get_supported_intents(self) -> Set[str]:
        """获取所有支持的意图"""
        return set(self._strategies_by_intent.keys())
    
    def clear_all_strategies(self) -> None:
        """清空所有策略（主要用于测试）"""
        self._strategies.clear()
        self._strategies_by_intent.clear()
        self._strategies_by_priority.clear()
        self._registration_history.append({
            "action": "clear_all",
            "timestamp": __import__('time').time()
        })
        self.logger.warning("所有策略已清空")


# 全局策略注册中心实例
_global_strategy_registry: Optional[StrategyRegistry] = None


def get_strategy_registry() -> StrategyRegistry:
    """获取全局策略注册中心实例"""
    global _global_strategy_registry
    if _global_strategy_registry is None:
        _global_strategy_registry = StrategyRegistry()
    return _global_strategy_registry


def reset_strategy_registry() -> None:
    """重置全局策略注册中心（主要用于测试）"""
    global _global_strategy_registry
    _global_strategy_registry = None
