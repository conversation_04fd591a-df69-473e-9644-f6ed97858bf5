# -*- coding: utf-8 -*-
from datetime import datetime
import logging
import json
import os
import re
import uuid
from typing import Dict, Optional, Any, List
from .base import AutoGenBaseAgent
from backend.data.db.database_manager import DatabaseManager
# LLM配置已迁移到统一配置系统
from backend.utils.prompt_loader import PromptLoader


logger = logging.getLogger(__name__)

# 常量定义
class DocumentConstants:
    """文档生成器常量"""
    DEFAULT_OVERVIEW = "无项目概述"
    DEFAULT_DELIVERY_TIME = "待定"
    DEFAULT_DELIVERY_AMOUNT = "待定"
    DEFAULT_RECOMMENDATIONS = "无项目建议"
    DEFAULT_REQUIREMENTS = "### 待完善的需求信息\n\n暂无具体需求信息，请继续完善需求采集。"
    
    # 正则表达式模式
    JSON_BLOCK_PATTERN = r'```json\s*([\s\S]*?)\s*```'
    JSON_OBJECT_PATTERN = r'\{[\s\S]*\}'
    
    # 字段提取模式
    FIELD_PATTERNS = {
        'overview': r'\*\*\{?overview\}?\*\*:?\s*(.*?)(?=\n\n|\n\*\*\{?|$)',
        'requirements': r'\*\*\{?requirements\}?\*\*:?\s*(.*?)(?=\n\n|\n\*\*\{?|$)',
        'Delivery_time': r'\*\*\{?Delivery_time\}?\*\*:?\s*(.*?)(?=\n\n|\n\*\*\{?|$)',
        'Delivery_Amount': r'\*\*\{?Delivery_Amount\}?\*\*:?\s*(.*?)(?=\n\n|\n\*\*\{?|$)',
        'recommendations': r'\*\*\{?recommendations\}?\*\*:?\s*(.*?)(?=\n\n|\n\*\*\{?|$|\Z)',
        'project_name': r'\*\*\{?project_name\}?\*\*:?\s*(.*?)(?=\n\n|\n\*\*\{?|$)',
        'service_type': r'\*\*\{?service_type\}?\*\*:?\s*(.*?)(?=\n\n|\n\*\*\{?|$)'
    }
    
    # JSON字段提取模式
    JSON_FIELD_PATTERNS = {
        'project_name': r'"project_name"\s*:\s*"([^"]*)"',
        'service_type': r'"service_type"\s*:\s*"([^"]*)"',
        'overview': r'"overview"\s*:\s*"((?:\\"|[^"])*)"',
        'requirements': r'"requirements"\s*:\s*"(.*?)(?="[,}])',
        'Delivery_time': r'"Delivery_time"\s*:\s*"([^"]*)"',
        'Delivery_Amount': r'"Delivery_Amount"\s*:\s*"?(\d+)"?',
        'recommendations': r'"recommendations"\s*:\s*"(.*?)(?="[,}]|\Z)'
    }


class LLMResponseParser:
    """LLM响应解析器"""
    
    def __init__(self, logger):
        self.logger = logger
    
    def parse(self, content: str) -> Dict[str, Any]:
        """解析LLM响应内容"""
        # 尝试JSON解析
        json_result = self._try_json_parse(content)
        if json_result:
            return json_result
        
        # 回退到正则表达式解析
        self.logger.info("JSON解析失败，使用正则表达式解析")
        return self._regex_parse(content)
    
    def _try_json_parse(self, content: str) -> Optional[Dict[str, Any]]:
        """尝试JSON解析"""
        # 查找JSON代码块
        json_match = re.search(DocumentConstants.JSON_BLOCK_PATTERN, content)
        if json_match:
            return self._parse_json_string(json_match.group(1))
        
        # 查找JSON对象
        json_obj_match = re.search(DocumentConstants.JSON_OBJECT_PATTERN, content)
        if json_obj_match:
            return self._parse_json_string(json_obj_match.group(0))
        
        # 尝试解析整个内容
        return self._parse_json_string(content)
    
    def _parse_json_string(self, json_str: str) -> Optional[Dict[str, Any]]:
        """解析JSON字符串"""
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            # 尝试手动解析
            return self._manual_json_parse(json_str)
    
    def _manual_json_parse(self, json_str: str) -> Optional[Dict[str, Any]]:
        """手动解析JSON"""
        try:
            result = {}
            for field, pattern in DocumentConstants.JSON_FIELD_PATTERNS.items():
                match = re.search(pattern, json_str, re.DOTALL)
                if match:
                    value = match.group(1)
                    if field in ['requirements', 'recommendations']:
                        value = value.replace('\\n', '\n').replace('\\"', '"')
                    result[field] = value
            return result if result else None
        except Exception as e:
            self.logger.warning(f"手动JSON解析失败: {e}")
            return None
    
    def _regex_parse(self, content: str) -> Dict[str, Any]:
        """正则表达式解析"""
        result = {}
        for field, pattern in DocumentConstants.FIELD_PATTERNS.items():
            match = re.search(pattern, content, re.DOTALL)
            if match:
                result[field] = match.group(1).strip()
        return result


class DocumentTemplate:
    """文档模板管理器"""
    
    def __init__(self, logger):
        self.logger = logger
        self._template_cache = None
    
    def get_template(self) -> str:
        """获取文档模板"""
        if self._template_cache is None:
            self._template_cache = self._load_template()
        return self._template_cache
    
    def _load_template(self) -> str:
        """加载模板"""
        template_path = self._get_template_path()
        
        if os.path.exists(template_path):
            try:
                with open(template_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return self._extract_template_from_content(content)
            except Exception as e:
                self.logger.error(f"加载模板文件失败: {e}")
        
        self.logger.warning("使用默认模板")
        return self._get_default_template()
    
    def _get_template_path(self) -> str:
        """获取模板文件路径"""
        return os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'prompts',
            'document_template.md'
        )
    
    def _extract_template_from_content(self, content: str) -> str:
        """从内容中提取模板"""
        start_marker = "```template"
        end_marker = "```"
        
        start_pos = content.find(start_marker)
        if start_pos != -1:
            start_pos += len(start_marker)
            end_pos = content.find(end_marker, start_pos)
            if end_pos != -1:
                return content[start_pos:end_pos].strip()
        
        return self._get_default_template()
    
    def _get_default_template(self) -> str:
        """获取默认模板"""
        return """# {project_name} 需求确认文档

**文档日期**: {date}

## 1. 项目概述

{overview}

## 2. 需求描述

{requirements}

## 3. 预估的交付时间和金额

- **预估交付时间:** {Delivery_time}

- **预估交付金额:** {Delivery_Amount} 元

## 4. 项目建议

{recommendations}

---"""
    
    def apply(self, data: Dict[str, Any]) -> str:
        """应用模板"""
        try:
            template = self.get_template()
            return template.format(**data)
        except KeyError as e:
            self.logger.error(f"模板格式化错误: 缺少键 {e}")
            raise Exception(f"模板格式化错误: {e}")
        except Exception as e:
            self.logger.error(f"模板应用失败: {e}")
            raise Exception(f"模板应用失败: {e}")


class DocumentGenerator(AutoGenBaseAgent):
    """优化后的文档生成器"""
    
    def __init__(self, llm_client: Any = None, agent_name: str = "document_generator", db_manager: DatabaseManager = None):
        self.llm_client = llm_client
        self.agent_name = agent_name
        self.prompt_loader = PromptLoader()
        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager

        # 使用统一配置服务
        from backend.config import config_service
        self.config_service = config_service

        # 缓存统一配置对象，避免重复调用
        from backend.config.unified_config_loader import get_unified_config
        self.unified_config = get_unified_config()

        # 初始化组件
        self.parser = LLMResponseParser(self.logger)
        self.template_manager = DocumentTemplate(self.logger)
    
    async def get_concern_points(self, conversation_id: str, user_id: str) -> List[Dict[str, Any]]:
        """获取指定会话的所有已完成关注点（考虑优先级配置）"""
        self.logger.info(f"查询会话 {conversation_id} (user: {user_id}) 的已完成关注点")
        from backend.config.unified_config_loader import get_unified_config

        # 获取所有已完成的关注点
        query = self.unified_config.get_database_query("focus_points.get_completed")
        all_results = await self.db_manager.execute_query(query, (conversation_id, user_id))

        # 获取关注点优先级配置
        p0_required = self.unified_config.get_business_rule("business_rules.focus_point_priority.p0", True)
        p1_required = self.unified_config.get_business_rule("business_rules.focus_point_priority.p1", True)
        p2_required = self.unified_config.get_business_rule("business_rules.focus_point_priority.p2", False)

        # 获取关注点定义以确定优先级
        filtered_results = []
        for result in all_results:
            focus_id = result.get('focus_id')
            if not focus_id:
                continue

            # 查询关注点定义获取优先级
            definition_query = self.unified_config.get_database_query("focus_point_definitions.get_by_focus_id")
            definition = await self.db_manager.get_record(definition_query, (focus_id,))

            if definition:
                priority = definition.get('priority', 'p0').lower()

                # 判断是否需要包含在文档生成中
                should_include = False
                if priority == 'p0' and p0_required:
                    should_include = True
                elif priority == 'p1' and p1_required:
                    should_include = True
                elif priority == 'p2' and p2_required:
                    should_include = True

                if should_include:
                    filtered_results.append(result)
                else:
                    self.logger.info(f"跳过关注点 {focus_id} (优先级: {priority}, 配置: {priority=='p0' and p0_required or priority=='p1' and p1_required or priority=='p2' and p2_required})")
            else:
                # 如果找不到定义，默认包含（向后兼容）
                filtered_results.append(result)
                self.logger.warning(f"关注点 {focus_id} 没有找到定义，默认包含在文档中")

        self.logger.info(f"查询到 {len(all_results)} 个已完成的关注点，根据优先级配置过滤后剩余 {len(filtered_results)} 个")
        return filtered_results
    
    def format_requirements(self, concerns: List[Dict[str, Any]]) -> str:
        """格式化需求部分内容"""
        if not concerns:
            return DocumentConstants.DEFAULT_REQUIREMENTS

        # 增强格式化，提供更清晰的结构给LLM
        formatted_concerns = []
        for i, concern in enumerate(concerns, 1):
            extracted_info = concern.get('extracted_info', '').strip()
            if extracted_info:
                # 为每个关注点添加序号和明确的标识
                formatted_concerns.append(f"### 需求信息 {i}: {extracted_info}")

        if not formatted_concerns:
            return DocumentConstants.DEFAULT_REQUIREMENTS

        # 在开头添加明确的指示
        result = "以下是用户提供的所有需求信息，请确保在最终文档中完整体现每一项：\n\n"
        result += "\n".join(formatted_concerns)
        result += "\n\n**重要提醒：以上每一项信息都必须在最终文档中有所体现，不得遗漏任何内容。**"

        return result
    
    async def generate_document(self, conversation_id: str, user_id: str, project_name: str, category_name: str = None) -> Optional[str]:
        """生成并保存需求文档"""
        try:
            # 获取关注点数据
            concerns = await self.get_concern_points(conversation_id, user_id)
            if not concerns:
                self.logger.warning(f"会话 {conversation_id} (user: {user_id}) 没有已完成的需求关注点，将生成基础文档模板")

            # 准备用户信息
            user_info = self._prepare_user_info(project_name, concerns, category_name)

            # 调用LLM生成内容
            llm_response = await self._call_llm(user_info, conversation_id)
            if not llm_response:
                return None

            # 解析LLM响应
            parsed_result = self.parser.parse(llm_response.get('content', ''))
            self.logger.info(f"解析结果: {parsed_result}")

            # 详细记录requirements字段的数据类型和内容
            requirements_value = parsed_result.get("requirements")
            if requirements_value is not None:
                self.logger.info(f"requirements字段类型: {type(requirements_value)}")
                self.logger.info(f"requirements字段内容: {requirements_value}")

            # 准备模板数据
            template_data = self._prepare_template_data(parsed_result, project_name, concerns)

            # 生成文档
            document_content = self.template_manager.apply(template_data)

            # 保存到数据库
            doc_id = await self._save_document(conversation_id, user_id, document_content)
            return doc_id

        except Exception as e:
            self.logger.error(f"文档生成失败: {e}", exc_info=True)
            return None
    
    def _prepare_user_info(self, project_name: str, concerns: List[Dict[str, Any]], category_name: str = None) -> Dict[str, Any]:
        """准备用户信息"""
        user_info = {
            "project_name": project_name,
            "date": datetime.now().strftime('%Y-%m-%d'),
            "requirements": self.format_requirements(concerns)
        }

        # 如果有分类信息，添加到用户信息中
        if category_name:
            user_info["service_type"] = category_name
            self.logger.info(f"添加服务类型信息: {category_name}")

        return user_info
    
    async def _call_llm(self, user_info: Dict[str, Any], conversation_id: str) -> Optional[Dict[str, Any]]:
        """调用LLM生成内容"""
        try:
            # 记录模型信息
            self._log_model_info()

            # 使用PromptLoader加载模版并替换占位符
            system_prompt = self.prompt_loader.load_prompt(
                "document_template",
                {"user_focused_info": json.dumps(user_info, ensure_ascii=False)}
            )

            # 记录传递给LLM的详细信息
            self.logger.info(f"传递给LLM的用户信息包含 {len(user_info.get('requirements', '').split('###')) - 1} 个需求项")
            self.logger.debug(f"完整的requirements内容: {user_info.get('requirements', '')[:500]}...")

            # 调用LLM
            self.logger.info(f"使用agent_name={self.agent_name}调用LLM生成文档")
            response = await self.llm_client.call_llm(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"用户提供的信息:\n{json.dumps(user_info, ensure_ascii=False)}"}
                ],
                session_id=str(conversation_id),
                agent_name=self.agent_name
            )

            self.logger.info(f"LLM响应: {response.get('content', '')[:200]}...")
            return response

        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}", exc_info=True)
            return None
    
    def _log_model_info(self):
        """记录模型信息"""
        try:
            model_config = self.config_service.get_llm_config_with_metadata(self.agent_name)
            self.logger.info(
                f"使用模型: {model_config.get('model_name')}, 提供商: {model_config.get('provider')}"
            )
        except Exception as e:
            self.logger.warning(f"获取模型配置失败: {self.agent_name}, 错误: {e}")
    
    def _prepare_template_data(self, parsed_result: Dict[str, Any], project_name: str, concerns: List[Dict[str, Any]]) -> Dict[str, Any]:
        """准备模板数据"""
        # 处理requirements字段 - 支持多种数据类型
        requirements = parsed_result.get("requirements", self.format_requirements(concerns))
        requirements = self._normalize_field_to_string(requirements, "requirements")

        # 处理recommendations字段 - 支持多种数据类型
        recommendations = parsed_result.get("recommendations", DocumentConstants.DEFAULT_RECOMMENDATIONS)
        recommendations = self._normalize_field_to_string(recommendations, "recommendations")

        return {
            "project_name": parsed_result.get("project_name", project_name),
            "date": datetime.now().strftime('%Y-%m-%d'),
            "overview": parsed_result.get("overview", DocumentConstants.DEFAULT_OVERVIEW),
            "requirements": requirements,
            "Delivery_time": parsed_result.get("Delivery_time", DocumentConstants.DEFAULT_DELIVERY_TIME),
            "Delivery_Amount": parsed_result.get("Delivery_Amount", DocumentConstants.DEFAULT_DELIVERY_AMOUNT),
            "recommendations": recommendations
        }

    def _normalize_field_to_string(self, field_value: Any, field_name: str) -> str:
        """将字段值标准化为字符串格式"""
        try:
            if isinstance(field_value, str):
                return field_value
            elif isinstance(field_value, list):
                # 处理列表类型
                string_items = []
                for item in field_value:
                    if isinstance(item, str):
                        string_items.append(item)
                    elif isinstance(item, dict):
                        # 如果是字典，尝试提取有用信息
                        if 'content' in item:
                            string_items.append(str(item['content']))
                        elif 'text' in item:
                            string_items.append(str(item['text']))
                        else:
                            # 将字典转换为可读格式
                            string_items.append(str(item))
                    else:
                        string_items.append(str(item))
                return "\n".join(string_items)
            elif isinstance(field_value, dict):
                # 处理字典类型
                if 'content' in field_value:
                    return str(field_value['content'])
                elif 'text' in field_value:
                    return str(field_value['text'])
                else:
                    # 将字典转换为可读格式
                    return str(field_value)
            else:
                return str(field_value)
        except Exception as e:
            self.logger.warning(f"标准化字段 {field_name} 时出错: {e}, 原始值: {field_value}")
            # 返回默认值
            if field_name == "requirements":
                return DocumentConstants.DEFAULT_REQUIREMENTS
            elif field_name == "recommendations":
                return DocumentConstants.DEFAULT_RECOMMENDATIONS
            else:
                return str(field_value) if field_value else ""
    

    
    async def _save_document(self, conversation_id: str, user_id: str, content: str) -> Optional[str]:
        """保存文档到数据库"""
        try:
            # 生成唯一文档ID
            doc_id = f"doc_{uuid.uuid4().hex}"

            # 使用DocumentManager统一保存文档（版本号自动计算）
            from ..data.db.document_manager import DocumentManager
            document_manager = DocumentManager(self.db_manager)

            from datetime import datetime
            success = await document_manager.insert_document(
                document_id=doc_id,
                conversation_id=conversation_id,
                user_id=user_id,
                content=content,
                status="draft",
                created_at=datetime.now().isoformat(),
                updated_at=datetime.now().isoformat(),
                version=None  # 自动计算版本号
            )

            if success:
                self.logger.info(f"文档生成成功! 文档ID: {doc_id}")
                return doc_id
            else:
                self.logger.error("文档保存失败")
                return None

        except Exception as e:
            self.logger.error(f"保存文档失败: {e}", exc_info=True)
            return None
