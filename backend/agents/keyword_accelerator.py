#!/usr/bin/env python3
"""
关键词匹配加速器

功能：
1. 实现业务逻辑保护的关键词匹配加速
2. 覆盖30%的简单交互（问候、确认、重启等）
3. 保留所有现有业务逻辑和约束
4. 提供完整的回退机制

设计原则：
- 业务逻辑优先：所有业务规则必须严格遵守
- 保守加速策略：只处理明确可以加速的场景
- 完整回退机制：任何不确定的情况都回退到原系统
- 性能监控：提供详细的加速效果统计
"""

import logging
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from backend.config.unified_config_loader import get_unified_config


@dataclass
class BusinessContext:
    """业务上下文"""
    current_state: str = "IDLE"
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    conversation_history: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.conversation_history is None:
            self.conversation_history = []


@dataclass
class AccelerationResult:
    """加速结果"""
    success: bool
    intent: Optional[str] = None
    sub_intent: Optional[str] = None
    confidence: float = 0.0
    processing_time: float = 0.0
    matched_keywords: List[str] = None
    business_rule_applied: Optional[str] = None
    
    def __post_init__(self):
        if self.matched_keywords is None:
            self.matched_keywords = []


class KeywordAccelerator:
    """基础关键词加速器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.performance_stats = {
            "total_attempts": 0,
            "successful_accelerations": 0,
            "average_processing_time": 0.0,
            "keyword_hit_rate": 0.0
        }
    
    def match(self, message: str, context: BusinessContext) -> Optional[AccelerationResult]:
        """
        基础关键词匹配
        
        Args:
            message: 用户输入
            context: 业务上下文
            
        Returns:
            AccelerationResult: 匹配结果，None表示无匹配
        """
        start_time = time.time()
        self.performance_stats["total_attempts"] += 1
        
        try:
            # 基础关键词匹配逻辑
            result = self._perform_basic_matching(message, context)
            
            processing_time = time.time() - start_time
            
            if result:
                result.processing_time = processing_time
                self.performance_stats["successful_accelerations"] += 1
                self._update_performance_stats(processing_time)
                
            return result
            
        except Exception as e:
            self.logger.error(f"关键词匹配失败: {e}", exc_info=True)
            return None
    
    def _perform_basic_matching(self, message: str, context: BusinessContext) -> Optional[AccelerationResult]:
        """执行基础匹配逻辑"""
        # 子类实现具体匹配逻辑
        raise NotImplementedError("子类必须实现此方法")
    
    def _update_performance_stats(self, processing_time: float):
        """更新性能统计"""
        total_time = (self.performance_stats["average_processing_time"] * 
                     (self.performance_stats["successful_accelerations"] - 1) + processing_time)
        self.performance_stats["average_processing_time"] = (
            total_time / self.performance_stats["successful_accelerations"]
        )
        
        self.performance_stats["keyword_hit_rate"] = (
            self.performance_stats["successful_accelerations"] / 
            self.performance_stats["total_attempts"]
        )
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self.performance_stats.copy()


class BusinessLogicProtectedKeywordAccelerator(KeywordAccelerator):
    """业务逻辑保护的关键词加速器"""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        
        # 加载业务规则
        self._load_business_rules()
        
        self.logger.info("业务逻辑保护的关键词加速器初始化完成")
    
    def _load_business_rules(self):
        """加载业务规则"""
        try:
            # 加载快速意图规则
            self.quick_intent_rules = get_unified_config().get_business_rule("conversation.keyword_acceleration.rules", [])
            
            # 按优先级排序
            self.quick_intent_rules.sort(key=lambda x: x.get("priority", 0), reverse=True)
            
            self.logger.info(f"业务规则加载完成，包含 {len(self.quick_intent_rules)} 个快速意图规则")
            
        except Exception as e:
            self.logger.error(f"加载业务规则失败: {e}", exc_info=True)
            self.quick_intent_rules = []
    
    def _perform_basic_matching(self, message: str, context: BusinessContext) -> Optional[AccelerationResult]:
        """
        执行业务逻辑保护的关键词匹配
        
        核心逻辑：
        1. 检查业务上下文是否完整
        2. 遍历快速意图规则（按优先级）
        3. 检查状态限制
        4. 执行关键词匹配
        5. 返回第一个匹配的结果
        """
        
        # 1. 业务上下文检查
        if not self._validate_business_context(context):
            self.logger.warning("缺少必要的业务上下文")
            return None
        
        # 2. 遍历快速意图规则
        for rule in self.quick_intent_rules:
            try:
                result = self._apply_quick_intent_rule(message, context, rule)
                if result:
                    return result
            except Exception as e:
                self.logger.error(f"应用快速意图规则失败: {rule}, 错误: {e}")
                continue
        
        return None
    
    def _validate_business_context(self, context: BusinessContext) -> bool:
        """验证业务上下文"""
        # 基础验证
        if not context.current_state:
            return False
        
        # 可以根据需要添加更多验证逻辑
        return True
    
    def _apply_quick_intent_rule(self, message: str, context: BusinessContext, 
                                rule: Dict[str, Any]) -> Optional[AccelerationResult]:
        """应用快速意图规则"""
        
        # 1. 检查状态限制
        allowed_states = rule.get("allowed_states", [])
        if allowed_states and context.current_state not in allowed_states:
            return None
        
        # 特殊处理：确认、修改、重启意图只在DOCUMENTING状态有效
        intent = rule.get("intent")
        if intent in ["confirm", "modify", "restart"]:  
            if context.current_state != "DOCUMENTING":
                return None
        
        # 2. 获取关键词列表
        keywords_config_key = rule.get("keywords_config_key")
        if not keywords_config_key:
            return None
        
        keywords = get_unified_config().get_business_rule(keywords_config_key, [])
        if not keywords:
            return None
        
        # 3. 执行关键词匹配
        matched_keywords = self._match_keywords(message, keywords)
        if not matched_keywords:
            return None
        
        # 4. 构建结果
        return AccelerationResult(
            success=True,
            intent=rule.get("intent"),
            sub_intent=rule.get("sub_intent"),
            confidence=rule.get("confidence", 0.9),
            matched_keywords=matched_keywords,
            business_rule_applied=keywords_config_key
        )
    
    def _match_keywords(self, message: str, keywords: List[str]) -> List[str]:
        """执行关键词匹配"""
        message_lower = message.lower().strip()
        matched = []
        
        for keyword in keywords:
            if keyword.lower() in message_lower:
                matched.append(keyword)
        
        return matched
    
    def reload_business_rules(self):
        """重新加载业务规则"""
        self.logger.info("重新加载业务规则...")
        self._load_business_rules()


class AcceleratedConversationFlow:
    """
    加速会话流程包装器

    这个类包装原始的会话流程，集成关键词加速器，
    对简单交互提供加速处理，复杂交互回退到原系统。
    """

    def __init__(self, original_flow):
        """
        初始化加速会话流程

        Args:
            original_flow: 原始会话流程实例
        """
        self.original_flow = original_flow
        self.accelerator = BusinessLogicProtectedKeywordAccelerator()
        self.logger = logging.getLogger(__name__)

        # 性能统计
        self.performance_stats = {
            "total_requests": 0,
            "accelerated_requests": 0,
            "fallback_requests": 0,
            "average_processing_time": 0.0,
            "acceleration_rate": 0.0
        }

        self.logger.info("加速会话流程初始化完成")

    async def process_message(self, message: str, session_id: str, user_id: str,
                            current_state: str, **kwargs) -> Dict[str, Any]:
        """
        处理消息，优先尝试加速，失败则回退到原系统

        Args:
            message: 用户消息
            session_id: 会话ID
            user_id: 用户ID
            current_state: 当前状态
            **kwargs: 其他参数

        Returns:
            Dict[str, Any]: 处理结果
        """
        start_time = time.time()
        self.performance_stats["total_requests"] += 1

        try:
            # 1. 构建业务上下文
            business_context = BusinessContext(
                current_state=current_state,
                user_id=user_id,
                session_id=session_id,
                conversation_history=kwargs.get("conversation_history", [])
            )

            # 2. 尝试关键词加速
            accelerated_result = self.accelerator.match(message, business_context)

            if accelerated_result and accelerated_result.success:
                # 加速成功
                processing_time = time.time() - start_time
                self._update_performance_stats(processing_time, accelerated=True)

                self.logger.info(f"关键词加速成功: '{message}' -> {accelerated_result.intent}")

                return {
                    "content": self._generate_accelerated_response(accelerated_result),
                    "intent": accelerated_result.intent,
                    "confidence": accelerated_result.confidence,
                    "processing_method": "keyword_accelerated",
                    "processing_time": processing_time,
                    "matched_keywords": accelerated_result.matched_keywords
                }
            else:
                # 3. 回退到原系统
                self.logger.debug(f"关键词加速失败，回退到原系统: '{message}'")
                result = await self.original_flow.process_message(
                    message, session_id, user_id, current_state, **kwargs
                )

                processing_time = time.time() - start_time
                self._update_performance_stats(processing_time, accelerated=False)

                return result

        except Exception as e:
            self.logger.error(f"加速会话流程处理异常: {e}", exc_info=True)
            # 异常时回退到原系统
            return await self.original_flow.process_message(
                message, session_id, user_id, current_state, **kwargs
            )

    def _generate_accelerated_response(self, result: AccelerationResult) -> str:
        """生成加速响应内容"""
        intent = result.intent

        # 简单的响应生成逻辑
        config = get_unified_config()
        response_templates = {
            "greeting": config.get_config_value("message_templates.keyword_accelerator.greeting"),
            "confirm": config.get_config_value("message_templates.keyword_accelerator.confirm"),
            "restart": config.get_config_value("message_templates.keyword_accelerator.restart"),
            "system_capability_query": config.get_config_value("message_templates.keyword_accelerator.system_capability_query"),
            "modify": config.get_config_value("message_templates.keyword_accelerator.modify")
        }

        default_template = config.get_config_value("message_templates.keyword_accelerator.default_response")
        return response_templates.get(intent, default_template.format(intent=intent))

    def _update_performance_stats(self, processing_time: float, accelerated: bool):
        """更新性能统计"""
        if accelerated:
            self.performance_stats["accelerated_requests"] += 1
        else:
            self.performance_stats["fallback_requests"] += 1

        # 更新平均处理时间
        total_time = (self.performance_stats["average_processing_time"] *
                     (self.performance_stats["total_requests"] - 1) + processing_time)
        self.performance_stats["average_processing_time"] = (
            total_time / self.performance_stats["total_requests"]
        )

        # 更新加速率
        self.performance_stats["acceleration_rate"] = (
            self.performance_stats["accelerated_requests"] /
            self.performance_stats["total_requests"] * 100
        )

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self.performance_stats.copy()

    def reset_performance_stats(self):
        """重置性能统计"""
        self.performance_stats = {
            "total_requests": 0,
            "accelerated_requests": 0,
            "fallback_requests": 0,
            "average_processing_time": 0.0,
            "acceleration_rate": 0.0
        }


# 全局实例
keyword_accelerator = BusinessLogicProtectedKeywordAccelerator()
