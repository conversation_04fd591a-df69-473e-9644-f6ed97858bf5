"""
决策缓存系统

提供智能缓存机制来优化决策引擎性能：
- 基于消息内容的缓存键生成
- LRU缓存策略
- 缓存命中率统计
- 缓存失效机制
- 内存使用监控

优化目标：
- 减少重复决策计算
- 提升响应速度
- 降低CPU使用率
"""

import logging
import hashlib
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from collections import OrderedDict
import threading
import json

from .decision_types import DecisionContext, DecisionResult


@dataclass
class CacheEntry:
    """缓存条目"""
    result: DecisionResult
    timestamp: float
    hit_count: int
    context_hash: str
    
    def is_expired(self, ttl_seconds: int) -> bool:
        """检查是否过期"""
        return time.time() - self.timestamp > ttl_seconds
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "result": asdict(self.result),
            "timestamp": self.timestamp,
            "hit_count": self.hit_count,
            "context_hash": self.context_hash
        }


class DecisionCache:
    """决策缓存管理器"""
    
    def __init__(self, 
                 max_size: int = 1000,
                 ttl_seconds: int = 3600,
                 enable_stats: bool = True):
        """
        初始化决策缓存
        
        Args:
            max_size: 最大缓存条目数
            ttl_seconds: 缓存生存时间（秒）
            enable_stats: 是否启用统计功能
        """
        self.logger = logging.getLogger(__name__)
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.enable_stats = enable_stats
        
        # 使用OrderedDict实现LRU缓存
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
        
        # 统计信息
        self._stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "cache_evictions": 0,
            "cache_expirations": 0
        }
        
        self.logger.info(f"决策缓存初始化: max_size={max_size}, ttl={ttl_seconds}s")
    
    def get(self, context: DecisionContext) -> Optional[DecisionResult]:
        """
        从缓存获取决策结果
        
        Args:
            context: 决策上下文
            
        Returns:
            Optional[DecisionResult]: 缓存的决策结果，如果未命中则返回None
        """
        if not self.enable_stats:
            return None
            
        cache_key = self._generate_cache_key(context)
        
        with self._lock:
            self._stats["total_requests"] += 1
            
            if cache_key not in self._cache:
                self._stats["cache_misses"] += 1
                return None
            
            entry = self._cache[cache_key]
            
            # 检查是否过期
            if entry.is_expired(self.ttl_seconds):
                del self._cache[cache_key]
                self._stats["cache_expirations"] += 1
                self._stats["cache_misses"] += 1
                return None
            
            # 更新访问统计和LRU顺序
            entry.hit_count += 1
            self._cache.move_to_end(cache_key)
            self._stats["cache_hits"] += 1
            
            self.logger.debug(f"缓存命中: key={cache_key[:16]}..., hits={entry.hit_count}")
            return entry.result
    
    def put(self, context: DecisionContext, result: DecisionResult) -> None:
        """
        将决策结果存入缓存
        
        Args:
            context: 决策上下文
            result: 决策结果
        """
        if not self.enable_stats:
            return
            
        cache_key = self._generate_cache_key(context)
        
        with self._lock:
            # 如果缓存已满，移除最旧的条目
            if len(self._cache) >= self.max_size and cache_key not in self._cache:
                oldest_key = next(iter(self._cache))
                del self._cache[oldest_key]
                self._stats["cache_evictions"] += 1
            
            # 创建缓存条目
            entry = CacheEntry(
                result=result,
                timestamp=time.time(),
                hit_count=0,
                context_hash=cache_key
            )
            
            self._cache[cache_key] = entry
            self.logger.debug(f"缓存存储: key={cache_key[:16]}..., size={len(self._cache)}")
    
    def _generate_cache_key(self, context: DecisionContext) -> str:
        """
        生成缓存键
        
        Args:
            context: 决策上下文
            
        Returns:
            str: 缓存键
        """
        # 构建用于缓存键的数据
        cache_data = {
            "message": context.message,
            "current_state": context.current_state.value if context.current_state else "IDLE",
            "user_id": context.user_id,
            # 不包含session_id，因为相同用户的相同消息应该有相同的决策
            # 不包含conversation_history，因为它变化太频繁
        }
        
        # 生成MD5哈希
        cache_str = json.dumps(cache_data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(cache_str.encode('utf-8')).hexdigest()
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            cleared_count = len(self._cache)
            self._cache.clear()
            self.logger.info(f"缓存已清空: {cleared_count} 个条目")
    
    def cleanup_expired(self) -> int:
        """
        清理过期的缓存条目
        
        Returns:
            int: 清理的条目数量
        """
        with self._lock:
            expired_keys = []
            current_time = time.time()
            
            for key, entry in self._cache.items():
                if current_time - entry.timestamp > self.ttl_seconds:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
                self._stats["cache_expirations"] += 1
            
            if expired_keys:
                self.logger.info(f"清理过期缓存: {len(expired_keys)} 个条目")
            
            return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            total_requests = self._stats["total_requests"]
            cache_hits = self._stats["cache_hits"]
            
            hit_rate = (cache_hits / total_requests * 100) if total_requests > 0 else 0
            
            return {
                "cache_size": len(self._cache),
                "max_size": self.max_size,
                "total_requests": total_requests,
                "cache_hits": cache_hits,
                "cache_misses": self._stats["cache_misses"],
                "hit_rate_percent": round(hit_rate, 2),
                "cache_evictions": self._stats["cache_evictions"],
                "cache_expirations": self._stats["cache_expirations"],
                "ttl_seconds": self.ttl_seconds
            }
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取详细的缓存信息
        
        Returns:
            Dict[str, Any]: 缓存详细信息
        """
        with self._lock:
            entries_info = []
            for key, entry in list(self._cache.items())[-10:]:  # 只显示最近10个
                entries_info.append({
                    "key": key[:16] + "...",
                    "timestamp": entry.timestamp,
                    "hit_count": entry.hit_count,
                    "age_seconds": int(time.time() - entry.timestamp),
                    "action": entry.result.action,
                    "strategy": entry.result.strategy_name
                })
            
            return {
                "stats": self.get_stats(),
                "recent_entries": entries_info,
                "memory_usage_estimate": len(self._cache) * 1024  # 粗略估计
            }
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        with self._lock:
            self._stats = {
                "total_requests": 0,
                "cache_hits": 0,
                "cache_misses": 0,
                "cache_evictions": 0,
                "cache_expirations": 0
            }
            self.logger.info("缓存统计信息已重置")


# 全局缓存实例
_global_cache: Optional[DecisionCache] = None


def get_decision_cache(max_size: int = 1000, 
                      ttl_seconds: int = 3600,
                      enable_stats: bool = True) -> DecisionCache:
    """
    获取全局决策缓存实例
    
    Args:
        max_size: 最大缓存条目数
        ttl_seconds: 缓存生存时间（秒）
        enable_stats: 是否启用统计功能
        
    Returns:
        DecisionCache: 缓存实例
    """
    global _global_cache
    if _global_cache is None:
        _global_cache = DecisionCache(max_size, ttl_seconds, enable_stats)
    return _global_cache


def reset_decision_cache() -> None:
    """重置全局缓存实例（主要用于测试）"""
    global _global_cache
    _global_cache = None


# 导出公共接口
__all__ = [
    'DecisionCache',
    'CacheEntry',
    'get_decision_cache',
    'reset_decision_cache'
]
