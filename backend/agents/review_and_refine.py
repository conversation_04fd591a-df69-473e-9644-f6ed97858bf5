# -*- coding: utf-8 -*-
"""
review_and_refine.py

文档审核与优化模块 (v2.1 - 修复接口问题)
"""
import logging
from typing import Dict, Any, Optional
import uuid
from datetime import datetime  # 添加导入语句

from .base import AutoGenBaseAgent
from ..data.db.database_manager import DatabaseManager
from ..data.db.document_manager import DocumentManager
from ..config import settings
from backend.config.unified_config_loader import get_unified_config


# 设置日志记录器，用于记录模块运行过程中的信息和错误
logger = logging.getLogger(__name__)

class DocumentRepository:
    """
    文档数据访问层（Repository模式）
    """
    def __init__(self, db_manager: DatabaseManager, user_id: str = None):
        self.db_manager = db_manager
        self.document_manager = DocumentManager(db_manager)
        self.user_id = user_id

    async def save_document(self, document_id, conversation_id, user_id, content, version=None, created_at=None, updated_at=None, status='draft'):
        """保存新版本文档到数据库，并返回新的文档ID"""
        try:
            # 使用DocumentManager的insert_document方法，传递版本号
            success = await self.document_manager.insert_document(
                document_id=document_id,
                conversation_id=conversation_id,
                user_id=user_id,
                content=content,
                status=status,
                created_at=created_at.isoformat() if hasattr(created_at, 'isoformat') else str(created_at),
                updated_at=updated_at.isoformat() if hasattr(updated_at, 'isoformat') else str(updated_at),
                version=version
            )
            if success:
                # 如果版本号是自动计算的，记录实际使用的版本号
                actual_version = version if version is not None else "自动计算"
                logger.info(f"新版本文档已保存，文档ID: {document_id}, 版本号: {actual_version}")
                return document_id
            else:
                raise Exception("文档保存失败")
        except Exception as e:
            logger.error(f"保存文档失败: {str(e)}")
            raise

    async def get_latest_document(self, conversation_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """获取指定会话的最新文档"""
        try:
            # 使用DocumentManager获取最新文档
            document = await self.document_manager.get_latest_document(conversation_id, self.user_id or user_id)
            return document
        except Exception as e:
            logger.error(f"获取最新文档失败: {e}")
            return None
            
class AutoGenReviewAndRefineAgent(AutoGenBaseAgent):
    """
    基于AutoGen框架的文档审核与优化代理
    """
    def __init__(self,
                 llm_client: Any,
                 llm_config: Dict[str, Any] = None,
                 agent_name: str = "review_and_refine",
                 **kwargs):
        
        super().__init__(name=agent_name, **kwargs)

        self.llm_client = llm_client
        self.agent_name = agent_name
        
        from ..utils.prompt_loader import PromptLoader
        self.prompt_loader = PromptLoader()

        db_path = llm_config.get("db_path") if llm_config else None
        self.db_manager = DatabaseManager(db_path or str(settings.DATABASE_PATH))
        self.document_repository = DocumentRepository(self.db_manager, user_id=kwargs.get("user_id"))


    async def process_message(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        【简化版】处理文档修改请求的核心方法。

        Args:
            data (Dict[str, Any]): 包含 'text' (用户修改意见) 和 'session_id' 的字典。

        Returns:
            Dict[str, Any]: 包含操作结果和响应文本的字典。
        """
        # 输入验证
        validation_result = self._validate_input(data)
        if not validation_result["valid"]:
            return {"success": False, "text_response": validation_result["error_message"]}

        modification_request = data.get("text")
        session_id = data.get("session_id")
        user_id = data.get("user_id")

        try:
            # 1. 获取最新的文档版本
            latest_doc = await self.document_repository.get_latest_document(session_id, user_id)
            if not latest_doc:
                logger.error(f"在处理修改请求时，未找到会话 {session_id} (user: {user_id}) 的任何文档。")
                return {"success": False, "text_response": get_unified_config().get_config_value("message_templates.error.document_not_found")}

            original_content = latest_doc.get("content", "")

            # 2. 调用LLM处理修改请求（可能是修改文档或澄清问题）
            llm_response = await self.generate_new_document(
                original_content,
                modification_request
            )

            if not llm_response:
                return {"success": False, "text_response": get_unified_config().get_config_value("message_templates.error.processing_failed")}

            # 3. 判断LLM返回的是修改后的文档还是澄清问题
            if self._is_clarification_request(llm_response):
                # 返回澄清问题，不保存文档
                return {
                    "success": True,
                    "next_action": "clarify",
                    "text_response": llm_response
                }

            # 4. 如果是修改后的文档，保存新版本（版本号自动计算）
            new_document_id = await self.document_repository.save_document(
                document_id=f"doc_{str(uuid.uuid4()).replace('-', '')}",
                conversation_id=session_id,
                user_id=user_id,
                content=llm_response,
                version=None,  # 让DocumentManager自动计算版本号
                created_at=datetime.now(),
                updated_at=datetime.now(),
                status='draft'
            )

            if not new_document_id:
                return {"success": False, "text_response": get_unified_config().get_config_value("message_templates.error.document_save_failed")}

            logger.info(f"文档修改成功，新版本文档ID: {new_document_id}")

            # 5. 构建成功响应
            response_text = "好的，我已经根据您的要求对文档进行了修改。请查看新版本：\n\n" + llm_response
            return {
                "success": True,
                "next_action": "review_again",
                "text_response": response_text,
                "modified_document": llm_response
            }

        except Exception as e:
            self.logger.error(f"处理文档修改时发生严重错误: {e}", exc_info=True)
            return {"success": False, "text_response": get_unified_config().get_config_value("message_templates.error.internal_error")}

    def _validate_input(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证输入数据的有效性

        Args:
            data: 输入数据字典

        Returns:
            Dict: 包含验证结果的字典
        """
        modification_request = data.get("text")
        session_id = data.get("session_id")

        # 检查必要字段
        if not modification_request or not session_id:
            return {
                "valid": False,
                "error_message": "处理修改请求时缺少必要信息。"
            }

        # 检查文本长度
        if len(modification_request.strip()) < 2:
            return {
                "valid": False,
                "error_message": "修改请求内容过短，请提供更详细的说明。"
            }

        if len(modification_request) > 5000:
            return {
                "valid": False,
                "error_message": "修改请求内容过长，请简化您的要求。"
            }

        # 检查session_id格式
        if not isinstance(session_id, str) or len(session_id.strip()) == 0:
            return {
                "valid": False,
                "error_message": "会话ID格式不正确。"
            }

        # 检查潜在的恶意输入
        dangerous_patterns = ["<script", "javascript:", "eval(", "exec("]
        if any(pattern in modification_request.lower() for pattern in dangerous_patterns):
            return {
                "valid": False,
                "error_message": "输入内容包含不安全的字符，请重新输入。"
            }

        return {"valid": True, "error_message": ""}

    def _is_clarification_request(self, response: str) -> bool:
        """
        判断LLM返回的是澄清问题还是修改后的文档
        使用多重判断条件提高准确性
        """
        if not response or not response.strip():
            return False

        response = response.strip()

        # 强澄清指示词（权重高）
        strong_clarification_indicators = [
            "需要澄清", "请明确", "不太清楚", "无法理解",
            "请问您具体", "能否详细说明", "您是希望"
        ]

        # 弱澄清指示词（权重低）
        weak_clarification_indicators = [
            "请问", "能否", "您希望", "具体是指"
        ]

        # 文档特征词（如果包含这些，更可能是文档）
        document_indicators = [
            "# ", "## ", "### ", "**", "- ", "1. ", "2. ",
            "项目", "需求", "功能", "描述", "概述"
        ]

        # 强指示词直接判断为澄清
        if any(indicator in response for indicator in strong_clarification_indicators):
            return True

        # 包含文档特征词，更可能是文档
        if any(indicator in response for indicator in document_indicators):
            return False

        # 综合判断：短文本 + 问号 + 弱指示词
        has_question_mark = ("?" in response or "？" in response)
        has_weak_indicator = any(indicator in response for indicator in weak_clarification_indicators)
        is_short = len(response) < 300

        # 多条件组合判断
        if has_question_mark and (has_weak_indicator or is_short):
            return True

        return False

    async def generate_new_document(self, current_document: str, feedback: str) -> str:
        """
        基于用户反馈生成优化后的文档内容
        """
        try:
            prompt = self.prompt_loader.load_prompt(
                "review_and_refine",
                {
                    "current_document": current_document,
                    "user_feedback": feedback
                }
            )

            response = await self.llm_client.call_llm(
                messages=[{"role": "user", "content": prompt}],
                agent_name="review_and_refine",
                temperature=0.5,
                max_tokens=4096
            )

            content = response.get("content", "")
            if not content:
                logger.error("LLM为文档优化返回了空内容")
                return ""

            # 清理和处理返回内容
            content = self._clean_llm_response(content)
            return content

        except Exception as e:
            self.logger.error(f"调用LLM生成新文档失败: {str(e)}", exc_info=True)
            return ""

    def _clean_llm_response(self, content: str) -> str:
        """
        清理LLM响应内容，统一处理各种格式问题

        Args:
            content: LLM原始响应内容

        Returns:
            str: 清理后的内容
        """
        if not content:
            return ""

        content = content.strip()

        # 移除Markdown代码块标记
        content = self._remove_markdown_blocks(content)

        # 处理JSON格式响应
        if self._is_json_response(content):
            content = self._extract_content_from_json(content)

        return content.strip()

    def _remove_markdown_blocks(self, content: str) -> str:
        """移除Markdown代码块标记"""
        # 移除 ```markdown 包装
        if content.startswith("```markdown\n") and content.endswith("\n```"):
            return content[len("```markdown\n"):-len("\n```")]

        # 移除普通 ``` 包装
        if content.startswith("```") and content.endswith("```"):
            content = content[3:-3]
            # 移除可能的语言标识
            if content.startswith("markdown\n"):
                content = content[len("markdown\n"):]

        return content

    def _is_json_response(self, content: str) -> bool:
        """判断是否为JSON格式响应"""
        return content.startswith("{") and content.endswith("}")

    def _extract_content_from_json(self, content: str) -> str:
        """从JSON响应中提取有用内容"""
        try:
            import json
            parsed_json = json.loads(content)

            # 处理数组格式
            if isinstance(parsed_json, list):
                return self._convert_json_array_to_markdown(parsed_json)

            # 处理对象格式，查找内容字段
            if isinstance(parsed_json, dict):
                content_keys = ['content', 'document', 'modified_content', 'text', 'response']
                for key in content_keys:
                    if key in parsed_json and parsed_json[key]:
                        return str(parsed_json[key])

                # 如果没有找到明显的内容字段，返回整个JSON的字符串表示
                self.logger.warning(f"JSON响应中未找到内容字段，可用字段: {list(parsed_json.keys())}")
                return str(parsed_json)

        except json.JSONDecodeError as e:
            self.logger.warning(f"无法解析JSON内容: {e}")

        return content

    def _convert_json_array_to_markdown(self, json_array: list) -> str:
        """
        将JSON数组格式转换为Markdown文档

        处理类似这样的格式：
        [{'title': '标题', 'description': '描述'}, ...]
        """
        try:
            markdown_content = ""

            for i, item in enumerate(json_array):
                if isinstance(item, dict):
                    title = item.get('title', f'第{i+1}部分')
                    description = item.get('description', '')

                    # 添加标题
                    markdown_content += f"## {title}\n\n"

                    # 添加描述
                    if description:
                        markdown_content += f"{description}\n\n"
                else:
                    # 如果不是字典，直接添加
                    markdown_content += f"- {str(item)}\n\n"

            return markdown_content.strip()

        except Exception as e:
            self.logger.error(f"转换JSON数组到Markdown失败: {e}")
            return str(json_array)  # 回退到字符串表示
