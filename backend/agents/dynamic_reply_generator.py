"""
动态回复管理器 (Dynamic Reply Manager)

本模块负责集中管理所有需要通过大型语言模型（LLM）动态生成的回复。
它提供了一个统一的、可配置的框架，用于处理从提示词构建、LLM调用、响应验证到最终格式化的完整流程。

核心功能:
1.  **策略化提示词构建**: 支持多种提示词构建策略（简单、模板、自定义等）。
2.  **健壮的LLM调用**: 封装了带重试和超时的LLM调用逻辑。
3.  **响应后处理**: 包括响应清理、质量验证和智能Markdown格式化。
4.  **性能与缓存**: 内置了性能统计和可配置的缓存机制。
5.  **预置回复工厂**: 提供了常用回复场景（如问候、共情）的快速生成方法。

设计目标:
- **解耦**: 将回复生成逻辑与业务逻辑分离。
- **复用**: 避免在不同业务场景下重复编写LLM调用代码。
- **稳定**: 提供统一的错误处理和回退机制，确保系统稳定性。
- **可维护**: 通过配置和策略模式，简化新回复类型的添加和维护。
"""

import logging
import asyncio
import time
import re
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from dataclasses import dataclass
from backend.utils.prompt_loader import PromptLoader
from backend.services.conversation_history_service import get_history_service, HistoryConfig, HistoryFormat
from backend.config.unified_config_loader import get_unified_config


class PromptStrategy(Enum):
    """提示词构建策略"""
    SIMPLE = "simple"                    # 简单策略：直接使用prompt_instruction
    WITH_USER_INPUT = "with_user_input"  # 包含用户输入：prompt_instruction + 用户输入
    TEMPLATE_BASED = "template_based"    # 基于模板：使用PromptLoader加载模板
    CUSTOM = "custom"                    # 自定义策略：使用自定义构建函数


class ResponseQuality(Enum):
    """响应质量等级"""
    EXCELLENT = "excellent"  # 优秀：长度适中，内容丰富
    GOOD = "good"           # 良好：基本符合要求
    ACCEPTABLE = "acceptable"  # 可接受：有一定问题但可用
    POOR = "poor"           # 较差：质量不佳，需要回退


@dataclass
class LLMCallConfig:
    """LLM调用配置"""
    agent_name: str = "default_generator"
    temperature: float = 0.7
    max_tokens: int = 200
    timeout: int = 10
    max_retries: int = 2
    enable_cache: bool = False
    cache_ttl: int = 300  # 缓存时间（秒）


@dataclass
class GenerationContext:
    """生成上下文"""
    prompt_instruction: str
    user_message: str = ""
    session_id: str = ""
    emotion: str = "neutral"  # 用户情绪状态：positive, negative, neutral, anxious, confused
    additional_context: Dict[str, Any] = None
    template_name: str = ""
    template_variables: Dict[str, Any] = None

    def __post_init__(self):
        if self.additional_context is None:
            self.additional_context = {}
        if self.template_variables is None:
            self.template_variables = {}

        # 验证情绪值的有效性
        valid_emotions = ["positive", "negative", "neutral", "anxious", "confused"]
        if self.emotion not in valid_emotions:
            self.emotion = "neutral"


class ResponseValidator:
    """
    响应质量验证器
    
    负责评估LLM生成的响应内容的质量，并提供清理功能。
    """
    
    def __init__(self, logger):
        """
        初始化验证器。

        Args:
            logger: 日志记录器实例。
        """
        self.logger = logger
    
    def validate_response(self, response: str, context: GenerationContext = None) -> ResponseQuality:
        """
        验证响应质量。
        
        通过一系列规则（如长度、关键词、内容评估）对响应进行分级。

        Args:
            response (str): 需要验证的LLM响应。
            context (GenerationContext, optional): 生成上下文，可用于更复杂的验证。Defaults to None.

        Returns:
            ResponseQuality: 响应的质量等级。
        """
        if not response or not response.strip():
            self.logger.warning("响应内容为空，质量评定为POOR")
            return ResponseQuality.POOR
        
        response = response.strip()
        
        # 基本质量检查
        if len(response) < 10:
            return ResponseQuality.POOR
        
        # 检查是否包含明显的错误标识
        error_indicators = [
            "抱歉，我无法", "系统错误", "处理失败", "出现问题",
            "Error", "Exception", "Failed", "null", "undefined"
        ]
        
        for indicator in error_indicators:
            if indicator.lower() in response.lower():
                return ResponseQuality.POOR
        
        # 长度和内容质量评估
        if len(response) > 500:
            return ResponseQuality.EXCELLENT
        elif len(response) > 100:
            return ResponseQuality.GOOD
        elif len(response) > 30:
            return ResponseQuality.ACCEPTABLE
        else:
            return ResponseQuality.POOR
    
    def clean_response(self, response: str) -> str:
        """
        清理响应内容，移除不必要的格式和前缀。

        例如，移除 "回复："、多余的引号等，使响应更纯净。

        Args:
            response (str): 原始LLM响应。

        Returns:
            str: 清理后的响应。
        """
        if not response:
            return ""
        
        response = response.strip()
        
        # 移除常见的前缀
        prefixes_to_remove = [
            "以下是回复：", "回复：", "我的回复是：", "生成的回复：",
            "建议回复：", "以下是我生成的回复：", "回答：", "答案：",
            "AI回复：", "助手回复：", "系统回复："
        ]
        
        for prefix in prefixes_to_remove:
            if response.startswith(prefix):
                response = response[len(prefix):].strip()
        
        # 移除多余的引号
        if response.startswith('"') and response.endswith('"'):
            response = response[1:-1].strip()
        if response.startswith("'") and response.endswith("'"):
            response = response[1:-1].strip()
        
        return response

    


class PromptBuilder:
    """
    提示词构建器
    
    根据指定的策略（PromptStrategy）和上下文（GenerationContext）构建最终发送给LLM的提示词。
    """
    
    def __init__(self, prompt_loader: PromptLoader, logger):
        """
        初始化构建器。

        Args:
            prompt_loader (PromptLoader): 用于加载提示词模板的加载器。
            logger: 日志记录器实例。
        """
        self.prompt_loader = prompt_loader
        self.logger = logger
    
    def build_prompt(self, strategy: PromptStrategy, context: GenerationContext, custom_builder: Callable = None) -> str:
        """
        根据策略构建提示词。
        
        这是提示词构建的主要入口点，它会根据策略分派到不同的构建方法。

        Args:
            strategy (PromptStrategy): 提示词构建策略。
            context (GenerationContext): 生成上下文，包含所有需要的信息。
            custom_builder (Callable, optional): 自定义构建函数，当策略为CUSTOM时使用。Defaults to None.

        Returns:
            str: 构建完成的提示词。
        """
        try:
            # 根据策略选择不同的构建方法
            if strategy == PromptStrategy.SIMPLE:
                return self._build_simple_prompt(context)
            elif strategy == PromptStrategy.WITH_USER_INPUT:
                return self._build_with_user_input_prompt(context)
            elif strategy == PromptStrategy.TEMPLATE_BASED:
                return self._build_template_based_prompt(context)
            elif strategy == PromptStrategy.CUSTOM and custom_builder:
                return custom_builder(context)
            else:
                self.logger.warning(f"未知的提示词策略: {strategy}, 使用简单策略")
                return self._build_simple_prompt(context)
        except Exception as e:
            self.logger.error(f"构建提示词失败: {e}")
            return context.prompt_instruction
    
    def _build_simple_prompt(self, context: GenerationContext) -> str:
        """构建简单提示词"""
        return context.prompt_instruction
    
    def _build_with_user_input_prompt(self, context: GenerationContext) -> str:
        """构建包含用户输入的提示词"""
        # 如果有情绪信息且不是中性情绪，使用情绪感知模板
        if context.emotion and context.emotion != "neutral":
            try:
                return self.prompt_loader.load_prompt(
                    "emotion_aware_reply",
                    {
                        "emotion": context.emotion,
                        "user_input": context.user_message,
                        "prompt_instruction": context.prompt_instruction
                    }
                )
            except Exception as e:
                self.logger.warning(f"加载情绪感知模板失败: {e}，使用标准模板")

        # 标准模板
        if context.user_message:
            return f"{context.prompt_instruction}\n\n用户输入: {context.user_message}"
        return context.prompt_instruction
    
    def _build_template_based_prompt(self, context: GenerationContext) -> str:
        """构建基于模板的提示词"""
        if not context.template_name:
            self.logger.warning("模板名称为空，使用简单策略")
            return self._build_simple_prompt(context)
        
        try:
            # 准备模板变量
            template_vars = {
                "user_input": context.user_message,
                "prompt_instruction": context.prompt_instruction,
                "session_id": context.session_id,
                "emotion": context.emotion,  # 添加情绪信息
                **context.template_variables,
                **context.additional_context
            }

            return self.prompt_loader.load_prompt(context.template_name, template_vars)
        except Exception as e:
            self.logger.error(f"加载模板失败: {e}")
            return self._build_with_user_input_prompt(context)


class DynamicReplyGenerator:
    """
    动态LLM回复生成器

    作为模块的核心，该类编排了整个动态回复的生成过程。
    它整合了提示词构建、LLM调用、响应验证和缓存等所有子组件，
    对外提供一个统一的 `generate_reply` 接口。
    """

    def __init__(self, llm_client=None, message_manager=None):
        """
        初始化动态回复生成器

        Args:
            llm_client: LLM客户端实例
            message_manager: 消息管理器实例，用于获取历史会话信息
        """
        self.logger = logging.getLogger(__name__)
        self.llm_client = llm_client
        self.message_manager = message_manager
        self.prompt_loader = PromptLoader()
        self.prompt_builder = PromptBuilder(self.prompt_loader, self.logger)
        self.response_validator = ResponseValidator(self.logger)
        
        # 性能统计
        self.stats = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "cache_hits": 0,
            "average_response_time": 0.0,
            "quality_distribution": {
                "excellent": 0,
                "good": 0,
                "acceptable": 0,
                "poor": 0
            }
        }
        
        # 简单缓存（生产环境建议使用Redis）
        self._cache = {}
        
        self.logger.info("动态LLM回复生成器初始化完成")
    
    async def generate_reply(
        self,
        context: GenerationContext,
        config: LLMCallConfig = None,
        strategy: PromptStrategy = PromptStrategy.WITH_USER_INPUT,
        fallback_message: str = None,
        custom_prompt_builder: Callable = None
    ) -> str:
        """
        生成动态回复
        
        Args:
            context: 生成上下文
            config: LLM调用配置
            strategy: 提示词构建策略
            fallback_message: 回退消息
            custom_prompt_builder: 自定义提示词构建函数
            
        Returns:
            str: 生成的回复
        """
        start_time = time.time()
        self.stats["total_calls"] += 1
        
        if config is None:
            config = LLMCallConfig()
        
        try:
            # 检查LLM客户端
            if not self.llm_client:
                self.logger.warning("LLM客户端未初始化，返回回退消息")
                config = get_unified_config()
                return fallback_message or config.get_config_value("message_templates.error.generation_failed")
            
            # 构建提示词
            prompt = self.prompt_builder.build_prompt(strategy, context, custom_prompt_builder)
            
            # 检查缓存
            if config.enable_cache:
                cache_key = self._generate_cache_key(prompt, config)
                cached_response = self._get_from_cache(cache_key)
                if cached_response:
                    self.stats["cache_hits"] += 1
                    self.logger.debug("使用缓存的回复")
                    return cached_response
            
            # 调用LLM
            response = await self._call_llm_with_retry(prompt, config)
            
            if response:
                # 清理和验证响应
                # 1. 清理响应：移除不必要的前缀和格式
                cleaned_response = self.response_validator.clean_response(response)
                
                # 2. Markdown后处理：对Markdown格式进行优化，便于前端渲染
                final_response = self._post_process_markdown_response(cleaned_response)
                
                # 3. 验证最终响应的质量
                quality = self.response_validator.validate_response(final_response, context)
                
                # 4. 更新质量统计
                self.stats["quality_distribution"][quality.value] += 1
                
                # 如果质量太差，使用回退消息
                if quality == ResponseQuality.POOR:
                    self.logger.warning(f"生成的回复质量较差: {cleaned_response[:50]}...")
                    config = get_unified_config()
                    return fallback_message or config.get_config_value("message_templates.error.language_reorganize")
                
                # 缓存高质量回复
                if config.enable_cache and quality in [ResponseQuality.EXCELLENT, ResponseQuality.GOOD]:
                    cache_key = self._generate_cache_key(prompt, config)
                    self._save_to_cache(cache_key, cleaned_response, config.cache_ttl)
                
                self.stats["successful_calls"] += 1
                self.logger.info(f"成功生成动态回复，质量: {quality.value}")
                return cleaned_response
            else:
                self.logger.warning("LLM返回空响应")
                config = get_unified_config()
                return fallback_message or config.get_config_value("message_templates.error.reply_generation_failed")
                
        except Exception as e:
            self.logger.error(f"生成动态回复失败: {e}", exc_info=True)
            self.stats["failed_calls"] += 1
            config = get_unified_config()
            return fallback_message or config.get_config_value("message_templates.error.reply_error")
        
        finally:
            # 更新响应时间统计
            response_time = time.time() - start_time
            self._update_response_time_stats(response_time)
    
    async def _call_llm_with_retry(self, prompt: str, config: LLMCallConfig) -> Optional[str]:
        """
        带重试机制的LLM调用。
        
        封装了LLM的实际调用过程，包含超时处理、多次重试和错误记录。

        Args:
            prompt (str): 发送给LLM的提示词。
            config (LLMCallConfig): LLM调用配置。

        Returns:
            Optional[str]: 成功时返回LLM的响应内容，否则返回None。
        """
        last_exception = None
        
        # 循环进行调用尝试，总次数为 max_retries + 1
        for attempt in range(config.max_retries + 1):
            try:
                # 构建消息
                messages = [{"role": "user", "content": prompt}]
                
                # 调用LLM
                response = await asyncio.wait_for(
                    self.llm_client.call_llm(
                        messages=messages,
                        agent_name=config.agent_name,
                        temperature=config.temperature,
                        max_tokens=config.max_tokens
                    ),
                    timeout=config.timeout
                )
                
                content = response.get("content", "").strip()
                if content:
                    return content
                else:
                    self.logger.warning(f"LLM返回空内容，尝试 {attempt + 1}/{config.max_retries + 1}")
                    
            except asyncio.TimeoutError:
                last_exception = "LLM调用超时"
                self.logger.warning(f"LLM调用超时，尝试 {attempt + 1}/{config.max_retries + 1}")
            except Exception as e:
                last_exception = str(e)
                self.logger.warning(f"LLM调用失败: {e}，尝试 {attempt + 1}/{config.max_retries + 1}")
            
            # 如果不是最后一次尝试，等待一下再重试
            if attempt < config.max_retries:
                await asyncio.sleep(0.5 * (attempt + 1))  # 递增等待时间
        
        self.logger.error(f"LLM调用最终失败，已重试 {config.max_retries} 次，最后错误: {last_exception}")
        return None
    
    def _generate_cache_key(self, prompt: str, config: LLMCallConfig) -> str:
        """
        根据提示词和配置生成唯一的缓存键。

        Args:
            prompt (str): 提示词。
            config (LLMCallConfig): LLM调用配置。

        Returns:
            str: MD5哈希值作为缓存键。
        """
        import hashlib
        # 将影响生成结果的关键信息组合成一个字符串
        key_data = f"{prompt}_{config.agent_name}_{config.temperature}_{config.max_tokens}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_from_cache(self, cache_key: str) -> Optional[str]:
        """从缓存获取"""
        if cache_key in self._cache:
            cached_data = self._cache[cache_key]
            if time.time() < cached_data["expires_at"]:
                return cached_data["response"]
            else:
                # 缓存过期，删除
                del self._cache[cache_key]
        return None
    
    def _save_to_cache(self, cache_key: str, response: str, ttl: int):
        """保存到缓存"""
        self._cache[cache_key] = {
            "response": response,
            "expires_at": time.time() + ttl
        }
        
        # 简单的缓存清理（保持缓存大小在合理范围内）
        if len(self._cache) > 1000:
            # 删除最旧的一半缓存项
            sorted_items = sorted(self._cache.items(), key=lambda x: x[1]["expires_at"])
            for key, _ in sorted_items[:500]:
                del self._cache[key]
    
    def _update_response_time_stats(self, response_time: float):
        """更新响应时间统计"""
        current_avg = self.stats["average_response_time"]
        total_calls = self.stats["total_calls"]
        
        # 计算新的平均响应时间
        self.stats["average_response_time"] = (
            (current_avg * (total_calls - 1) + response_time) / total_calls
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        total_calls = self.stats["total_calls"]
        return {
            **self.stats,
            "success_rate": self.stats["successful_calls"] / max(total_calls, 1),
            "failure_rate": self.stats["failed_calls"] / max(total_calls, 1),
            "cache_hit_rate": self.stats["cache_hits"] / max(total_calls, 1),
            "cache_size": len(self._cache)
        }
    
    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        self.logger.info("已清空动态回复生成器缓存")
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "cache_hits": 0,
            "average_response_time": 0.0,
            "quality_distribution": {
                "excellent": 0,
                "good": 0,
                "acceptable": 0,
                "poor": 0
            }
        }
        self.logger.info("已重置动态回复生成器统计信息")

    def _post_process_markdown_response(self, text: str) -> str:
        """
        对LLM生成的Markdown文本进行智能后处理，以优化格式，便于前端渲染。

        主要处理：
        1. 智能段落分隔 - 根据内容类型调整分段策略
        2. 中文文本优化 - 针对中文阅读习惯优化分段
        3. 列表和标题处理 - 确保结构化内容的正确格式
        4. 清理多余空行和空白字符
        """
        if not text:
            return ""

        # 检测内容类型，采用不同的处理策略
        content_type = self._detect_content_type(text)

        if content_type == "structured":
            return self._process_structured_content(text)
        elif content_type == "conversational":
            return self._process_conversational_content(text)
        else:
            return self._process_general_content(text)

    def _detect_content_type(self, text: str) -> str:
        """
        检测文本内容类型，用于选择合适的处理策略

        Returns:
            str: "structured" | "conversational" | "general"
        """
        # 检测结构化内容特征
        structured_indicators = [
            r'^#+\s',  # 标题
            r'^\s*[-*+]\s',  # 无序列表
            r'^\s*\d+\.\s',  # 有序列表
            r'\*\*.*?\*\*',  # 粗体
            r'```',  # 代码块
        ]

        structured_count = 0
        for pattern in structured_indicators:
            if re.search(pattern, text, re.MULTILINE):
                structured_count += 1

        # 如果包含多个结构化元素，认为是结构化内容
        if structured_count >= 2:
            return "structured"

        # 检测对话式内容特征
        conversational_indicators = [
            r'[？?]',  # 问号
            r'您|你',  # 称呼
            r'比如|例如',  # 举例
            r'建议|推荐',  # 建议性语言
        ]

        conversational_count = 0
        for pattern in conversational_indicators:
            if re.search(pattern, text):
                conversational_count += 1

        if conversational_count >= 2:
            return "conversational"

        return "general"

    def _process_structured_content(self, text: str) -> str:
        """处理结构化内容（包含标题、列表等）"""
        lines = text.split('\n')
        processed_lines = []
        prev_line_type = None

        for line in lines:
            stripped_line = line.strip()

            if not stripped_line:
                continue

            current_line_type = self._get_line_type(stripped_line)

            # 根据行类型决定是否添加空行
            if self._should_add_spacing(prev_line_type, current_line_type):
                processed_lines.append("")

            processed_lines.append(stripped_line)
            prev_line_type = current_line_type

        return "\n".join(processed_lines)

    def _process_conversational_content(self, text: str) -> str:
        """处理对话式内容（问答、建议等）"""
        # 按句子分组，每组不超过3句话形成一个段落
        sentences = self._split_chinese_sentences(text)
        paragraphs = []
        current_paragraph = []

        for sentence in sentences:
            current_paragraph.append(sentence)

            # 每3句话或遇到问号时结束一个段落
            if len(current_paragraph) >= 3 or sentence.strip().endswith(('？', '?')):
                paragraphs.append(''.join(current_paragraph).strip())
                current_paragraph = []

        # 处理剩余句子
        if current_paragraph:
            paragraphs.append(''.join(current_paragraph).strip())

        # 段落间用空行分隔
        return '\n\n'.join(paragraphs)

    def _process_general_content(self, text: str) -> str:
        """处理一般内容（原有逻辑的改进版）"""
        lines = text.split('\n')
        processed_lines = []
        prev_line_empty = False

        for line in lines:
            stripped_line = line.strip()

            if stripped_line:
                # 如果当前行有内容且前一行是空行，保持一个空行分隔
                if prev_line_empty and processed_lines:
                    processed_lines.append("")
                processed_lines.append(stripped_line)
                prev_line_empty = False
            else:
                prev_line_empty = True

        return "\n".join(processed_lines)

    def _get_line_type(self, line: str) -> str:
        """
        识别行的类型

        Returns:
            str: "title" | "list" | "bold" | "code" | "text"
        """
        line = line.strip()

        if re.match(r'^#+\s', line):
            return "title"
        elif re.match(r'^\s*[-*+]\s', line):
            return "list"
        elif re.match(r'^\s*\d+\.\s', line):
            return "list"
        elif re.search(r'\*\*.*?\*\*', line):
            return "bold"
        elif line.startswith('```') or line.endswith('```'):
            return "code"
        else:
            return "text"

    def _should_add_spacing(self, prev_type: Optional[str], current_type: str) -> bool:
        """
        判断是否应该在两行之间添加空行

        Args:
            prev_type: 前一行的类型
            current_type: 当前行的类型

        Returns:
            bool: 是否需要添加空行
        """
        if not prev_type:
            return False

        # 标题前后需要空行
        if current_type == "title" or prev_type == "title":
            return True

        # 列表和文本之间需要空行
        if (prev_type == "text" and current_type == "list") or \
           (prev_type == "list" and current_type == "text"):
            return True

        # 代码块前后需要空行
        if current_type == "code" or prev_type == "code":
            return True

        return False

    def _split_chinese_sentences(self, text: str) -> List[str]:
        """
        智能分割中文句子，综合考虑了标点、长度和常见分隔符。
        
        这是一个核心的文本处理函数，用于将长文本切分为更易于处理的句子单元。
        处理逻辑：
        1. 优先使用中文句末标点（。！？；）进行分割。
        2. 如果标点分割效果不佳，尝试使用逗号或空格分割，并限制每段长度。
        3. 如果仍然效果不佳，作为最后的手段，按字符数进行强制分割。
        4. 最后进行去重和清理，确保输出结果的质量。

        Args:
            text: 输入文本

        Returns:
            List[str]: 句子列表
        """
        # 中文句子结束标点
        sentence_endings = r'[。！？；]'

        # 分割句子，保留标点符号
        sentences = re.split(f'({sentence_endings})', text)

        # 重新组合句子和标点
        result = []
        for i in range(0, len(sentences), 2):
            if i < len(sentences):
                sentence = sentences[i].strip()
                if i + 1 < len(sentences):
                    sentence += sentences[i + 1]
                if sentence:
                    result.append(sentence)

        # 如果没有找到中文标点，按长度分割
        if len(result) <= 1 and text.strip():
            # 按逗号或空格分割，每段不超过50个字符
            parts = re.split(r'[，,\s]+', text)
            current_sentence = ""
            result = []  # 重置result列表

            for part in parts:
                part = part.strip()
                if not part:
                    continue

                # 如果当前句子加上新部分超过50个字符，且当前句子不为空，则结束当前句子
                if len(current_sentence + part) > 50 and current_sentence:
                    result.append(current_sentence.strip())
                    current_sentence = part
                else:
                    if current_sentence:
                        current_sentence += f"，{part}"
                    else:
                        current_sentence = part

            if current_sentence:
                result.append(current_sentence.strip())

            # 如果仍然只有一个结果且文本很长，强制按字符数分割
            if len(result) <= 1 and len(text) > 50:
                result = []
                words = text.split('，')
                current_sentence = ""

                for word in words:
                    word = word.strip()
                    if not word:
                        continue

                    if len(current_sentence + word) > 30 and current_sentence:
                        result.append(current_sentence.strip())
                        current_sentence = word
                    else:
                        if current_sentence:
                            current_sentence += f"，{word}"
                        else:
                            current_sentence = word

                if current_sentence:
                    result.append(current_sentence.strip())

        # 去重并过滤空字符串
        final_result = []
        seen = set()
        for item in result:
            item = item.strip()
            if item and item not in seen:
                final_result.append(item)
                seen.add(item)

        return final_result if final_result else [text.strip()]

    async def _get_conversation_history(self, session_id: str, user_id: str = "", max_turns: int = 15) -> str:
        """
        获取格式化的历史会话信息

        Args:
            session_id: 会话ID
            max_turns: 最大对话轮数

        Returns:
            str: 格式化的历史会话信息
        """
        try:
            if not self.message_manager or not session_id or not user_id:
                from backend.config.unified_config_loader import get_unified_config
                return get_unified_config().get_message_template(
                    'formatting.history.empty',
                    default="暂无历史信息（这是新的对话）"
                )

            # 使用统一的历史服务获取格式化的对话历史
            history_service = get_history_service(self.message_manager)
            return await history_service.get_conversation_history(
                session_id,
                user_id,
                HistoryConfig(
                    max_turns=max_turns,
                    max_message_length=200,
                    format_type=HistoryFormat.TEMPLATE
                )
            )

        except Exception as e:
            self.logger.warning(f"获取历史会话信息失败: {str(e)}")
            try:
                from backend.config.unified_config_loader import get_unified_config
                return get_unified_config().get_message_template(
                    'formatting.history.empty',
                    default="暂无历史信息（这是新的对话）"
                )
            except Exception as config_error:
                self.logger.warning(f"获取配置模板也失败: {config_error}")
                return "暂无历史信息（这是新的对话）"


class DynamicReplyFactory:
    """动态回复生成工厂类 - 提供便捷的预配置生成方法"""

    def __init__(self, generator: DynamicReplyGenerator):
        """
        初始化工厂类。

        Args:
            generator (DynamicReplyGenerator): 一个配置好的动态回复生成器实例。
        """
        self.generator = generator
        self.logger = logging.getLogger(__name__)
        # 导入配置管理器
        from backend.config.unified_config_loader import get_unified_config
        self.config_manager = get_unified_config()

    def _get_agent_config(self, agent_name: str) -> LLMCallConfig:
        """
        从配置系统获取Agent配置
        
        Args:
            agent_name: Agent名称
            
        Returns:
            LLMCallConfig: LLM调用配置
        """
        # 获取场景参数
        scenario_params = self.config_manager.get_scenario_params(agent_name)
        
        # 创建配置对象，使用配置值，同时提供默认值作为备份
        return LLMCallConfig(
            agent_name=agent_name,
            temperature=scenario_params.get("temperature", 0.7),
            max_tokens=scenario_params.get("max_tokens", 200),
            timeout=scenario_params.get("timeout", 30),
            max_retries=scenario_params.get("max_retries", 2),
            enable_cache=scenario_params.get("enable_cache", False),
            cache_ttl=scenario_params.get("cache_ttl", 300)
        )

    async def generate_greeting_reply(
        self,
        prompt_instruction: str,
        user_message: str = "",
        session_id: str = "",
        emotion: str = "neutral"  # 问候通常是中性情绪
    ) -> str:
        """生成问候回复"""
        context = GenerationContext(
            prompt_instruction=prompt_instruction,
            user_message=user_message,
            session_id=session_id,
            emotion=emotion
        )

        # 使用统一的配置获取方法
        config = self._get_agent_config("greeting_generator")

        return await self.generator.generate_reply(
            context=context,
            config=config,
            strategy=PromptStrategy.SIMPLE,
            fallback_message="您好！很高兴为您服务。请问有什么可以帮您？"
        )

    async def generate_empathy_reply(
        self,
        prompt_instruction: str,
        user_message: str,
        session_id: str = "",
        emotion: str = "negative"  # 共情回复通常对应负面情绪
    ) -> str:
        """生成共情回复"""
        context = GenerationContext(
            prompt_instruction=prompt_instruction,
            user_message=user_message,
            session_id=session_id,
            emotion=emotion
        )

        # 使用统一的配置获取方法
        config = self._get_agent_config("empathy_generator")

        return await self.generator.generate_reply(
            context=context,
            config=config,
            strategy=PromptStrategy.WITH_USER_INPUT,
            fallback_message="我理解您的感受。请问有什么我可以帮助您的吗？"
        )

    async def generate_clarification_reply(
        self,
        prompt_instruction: str,
        user_message: str,
        session_id: str = "",
        user_id: str = "",
        enhance_with_context: bool = False
    ) -> str:
        """
        生成澄清回复

        Args:
            prompt_instruction: 提示指令
            user_message: 用户消息
            session_id: 会话ID
            user_id: 用户ID（可选）
            enhance_with_context: 是否使用上下文增强（默认False保持向后兼容）

        Returns:
            str: 澄清回复
        """
        # 如果启用上下文增强，则增强prompt_instruction
        if enhance_with_context and user_id:
            try:
                context_info = await self._get_session_context(session_id, user_id)
                prompt_instruction = self._enhance_prompt_with_context(
                    prompt_instruction, user_message, context_info, "neutral"
                )
            except Exception as e:
                self.logger.warning(f"上下文增强失败，使用原始prompt: {e}")

        context = GenerationContext(
            prompt_instruction=prompt_instruction,
            user_message=user_message,
            session_id=session_id
        )

        # 使用统一的配置获取方法
        config = self._get_agent_config("clarification_generator")

        return await self.generator.generate_reply(
            context=context,
            config=config,
            strategy=PromptStrategy.WITH_USER_INPUT,
            fallback_message=get_unified_config().get_config_value("message_templates.clarification.question_unclear")
        )

    async def generate_apology_reply(
        self,
        prompt_instruction: str,
        user_message: str,
        session_id: str = ""
    ) -> str:
        """生成道歉回复"""
        context = GenerationContext(
            prompt_instruction=prompt_instruction,
            user_message=user_message,
            session_id=session_id
        )

        # 使用统一的配置获取方法
        config = self._get_agent_config("apology_generator")

        return await self.generator.generate_reply(
            context=context,
            config=config,
            strategy=PromptStrategy.WITH_USER_INPUT,
            fallback_message=get_unified_config().get_config_value("message_templates.clarification.document_dissatisfaction")
        )

    async def generate_intelligent_question(
        self,
        name: str,
        description: str,
        required: bool = False,
        examples: str = "",
        project_type: str = "",
        progress_info: str = "",
        session_id: str = "",
        user_id: str = "",
        conversation_history: str = None
    ) -> str:
        """
        [已弃用] 使用question_generation.md生成智能问题

        注意：此方法已弃用，请使用 generate_optimized_question 替代，
        它合并了问题生成和优化功能，性能更好。
        """
        # 内部调用新的优化方法以确保性能和一致性
        return await self.generate_optimized_question(
            name=name,
            description=description,
            required=required,
            examples=examples,
            project_type=project_type,
            progress_info=progress_info,
            user_input="",  # 旧方法没有user_input参数
            session_id=session_id,
            user_id=user_id,
            emotion="neutral",  # 旧方法没有emotion参数，使用默认值
            conversation_history=conversation_history
        )

    async def generate_question_polish(
        self,
        user_input: str,
        base_question: str,
        session_id: str = "",
        user_id: str = "",
        conversation_history: str = None
    ) -> str:
        """
        [已弃用] 生成润色后的问题

        注意：此方法已弃用，请使用 generate_optimized_question 替代，
        它合并了问题生成和优化功能，性能更好。
        """
        # 问题优化功能已经集成到 generate_optimized_question 中
        # 这里直接返回基础问题，因为新的优化方法已经一步到位
        # 如果需要真正的优化，建议直接使用 generate_optimized_question
        return base_question

    async def generate_optimized_question(
        self,
        name: str,
        description: str,
        required: bool = False,
        examples: str = "",
        project_type: str = "",
        progress_info: str = "",
        user_input: str = "",
        session_id: str = "",
        user_id: str = "",
        emotion: str = "neutral",  # 新增情绪参数
        conversation_history: str = None
    ) -> str:
        """使用优化模板一步生成最终问题（合并生成+优化）"""
        # 如果没有提供历史记录，自动获取
        if conversation_history is None:
            conversation_history = await self.generator._get_conversation_history(session_id, user_id)

        context = GenerationContext(
            prompt_instruction="",  # 将通过模板设置
            user_message=user_input,
            session_id=session_id,
            emotion=emotion,  # 传递情绪信息
            template_name="optimized_question_generation",
            template_variables={
                "name": name,
                "description": description,
                "required": required,
                "examples": examples,
                "project_type": project_type,
                "progress_info": progress_info,
                "user_input": user_input,
                "emotion": emotion,  # 在模板变量中也包含情绪
                "conversation_history": conversation_history
            }
        )

        # 使用optimized_question_generation的专用配置
        config = self._get_agent_config("optimized_question_generation")

        return await self.generator.generate_reply(
            context=context,
            config=config,
            strategy=PromptStrategy.TEMPLATE_BASED,
            fallback_message="请详细描述您的需求。"
        )

    async def generate_clarification_question(
        self,
        focus_point_name: str,
        focus_point_description: str,
        user_answer: str,
        session_id: str = "",
        user_id: str = "",
        conversation_history: str = None
    ) -> str:
        """生成澄清问题"""
        # 如果没有提供历史记录，自动获取
        if conversation_history is None:
            conversation_history = await self._get_conversation_history(session_id, user_id)
            
        context = GenerationContext(
            prompt_instruction="",  # 将通过模板设置
            user_message=user_answer,
            session_id=session_id,
            template_name="clarification_question",
            template_variables={
                "focus_point_name": focus_point_name,
                "focus_point_description": focus_point_description,
                "conversation_history": conversation_history,
                "user_answer": user_answer
            }
        )

        # 使用统一的配置获取方法
        config = self._get_agent_config("clarification_generator")

        return await self.generator.generate_reply(
            context=context,
            config=config,
            strategy=PromptStrategy.TEMPLATE_BASED,
            fallback_message=f"关于「{focus_point_name}」，您能提供更详细一些的信息吗？"
        )

    async def generate_dynamic_reply(
        self,
        prompt_instruction: str,
        user_message: str = "",
        session_id: str = "",
        user_id: str = "",
        emotion: str = "neutral",  # 添加情绪参数
        **kwargs
    ) -> str:
        """
        通用动态回复生成方法

        Args:
            prompt_instruction: 提示指令
            user_message: 用户消息
            session_id: 会话ID
            user_id: 用户ID
            emotion: 用户情绪状态
            **kwargs: 其他参数

        Returns:
            str: 生成的回复
        """
        # 获取增强的上下文信息
        enhanced_context = await self._build_enhanced_context(
            prompt_instruction, user_message, session_id, user_id, emotion, **kwargs
        )

        context = GenerationContext(
            prompt_instruction=enhanced_context,
            user_message=user_message,
            session_id=session_id,
            emotion=emotion
        )

        # 使用默认配置
        config = self._get_agent_config("default_generator")

        return await self.generator.generate_reply(
            context=context,
            config=config,
            strategy=PromptStrategy.WITH_USER_INPUT,
            fallback_message=get_unified_config().get_config_value("message_templates.clarification.cannot_generate_suitable")
        )

    async def _build_enhanced_context(
        self,
        prompt_instruction: str,
        user_message: str,
        session_id: str,
        user_id: str,
        emotion: str,
        **kwargs
    ) -> str:
        """
        构建增强的上下文信息

        Args:
            prompt_instruction: 原始提示指令
            user_message: 用户消息
            session_id: 会话ID
            user_id: 用户ID
            emotion: 用户情绪
            **kwargs: 其他参数

        Returns:
            str: 增强后的提示指令
        """
        try:
            # 获取会话上下文信息
            context_info = await self._get_session_context(session_id, user_id)

            # 构建增强的提示指令
            enhanced_prompt = self._enhance_prompt_with_context(
                prompt_instruction, user_message, context_info, emotion
            )

            return enhanced_prompt

        except Exception as e:
            self.logger.warning(f"构建增强上下文失败: {e}, 使用原始提示指令")
            return prompt_instruction

    async def _get_session_context(self, session_id: str, user_id: str) -> Dict[str, Any]:
        """
        获取会话上下文信息

        Args:
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            Dict[str, Any]: 会话上下文信息
        """
        context_info = {
            "project_type": "未知项目",
            "domain": None,
            "category": None,
            "current_focus_point": None,
            "collected_info": "基础信息",
            "conversation_history": []
        }

        try:
            # 获取数据库管理器
            from backend.data.db.database_manager import DatabaseManager
            db_manager = DatabaseManager()

            # 获取会话状态信息
            session_state = await self._get_session_state(db_manager, session_id, user_id)
            if session_state:
                context_info.update(session_state)

            # 获取当前关注点信息
            focus_point_info = await self._get_current_focus_point(db_manager, session_id, user_id)
            if focus_point_info:
                context_info["current_focus_point"] = focus_point_info

            # 获取对话历史
            conversation_history = await self._get_recent_conversation(db_manager, session_id, user_id)
            if conversation_history:
                context_info["conversation_history"] = conversation_history

        except Exception as e:
            self.logger.warning(f"获取会话上下文失败: {e}")

        return context_info

    async def _get_session_state(self, db_manager, session_id: str, user_id: str) -> Dict[str, Any]:
        """获取会话状态信息"""
        try:
            # 获取会话状态
            query = """
            SELECT domain, category, current_state
            FROM conversation_states
            WHERE conversation_id = ? AND user_id = ?
            ORDER BY created_at DESC LIMIT 1
            """
            result = db_manager.execute_query(query, (session_id, user_id))

            if result:
                state = result[0]
                # 获取领域和类别名称
                domain_name = await self._get_domain_name(db_manager, state.get('domain'))
                category_name = await self._get_category_name(db_manager, state.get('category'))

                return {
                    "domain": state.get('domain'),
                    "domain_name": domain_name,
                    "category": state.get('category'),
                    "category_name": category_name,
                    "current_state": state.get('current_state'),
                    "project_type": category_name or "未知项目"
                }
        except Exception as e:
            self.logger.warning(f"获取会话状态失败: {e}")

        return {}

    async def _get_current_focus_point(self, db_manager, session_id: str, user_id: str) -> Dict[str, Any]:
        """获取当前关注点信息"""
        try:
            # 获取正在处理的关注点
            query = """
            SELECT fps.focus_id, fpd.name, fpd.description, fpd.example
            FROM focus_point_states fps
            JOIN focus_point_definitions fpd ON fps.focus_id = fpd.focus_id
            WHERE fps.conversation_id = ? AND fps.user_id = ? AND fps.status = 'processing'
            ORDER BY fps.updated_at DESC LIMIT 1
            """
            result = db_manager.execute_query(query, (session_id, user_id))

            if result:
                focus_point = result[0]
                return {
                    "focus_id": focus_point.get('focus_id'),
                    "name": focus_point.get('name'),
                    "description": focus_point.get('description'),
                    "example": focus_point.get('example')
                }
        except Exception as e:
            self.logger.warning(f"获取当前关注点失败: {e}")

        return {}

    async def _get_recent_conversation(self, db_manager, session_id: str, user_id: str, limit: int = 5) -> List[Dict[str, Any]]:
        """获取最近的对话历史"""
        try:
            query = """
            SELECT role, content, timestamp
            FROM conversation_history
            WHERE conversation_id = ? AND user_id = ?
            ORDER BY timestamp DESC LIMIT ?
            """
            result = db_manager.execute_query(query, (session_id, user_id, limit * 2))

            if result:
                # 反转顺序，使其按时间正序排列
                conversations = []
                for row in reversed(result):
                    conversations.append({
                        "role": row.get('role'),
                        "content": row.get('content'),
                        "timestamp": row.get('timestamp')
                    })
                return conversations[-limit:]  # 只返回最近的几条
        except Exception as e:
            self.logger.warning(f"获取对话历史失败: {e}")

        return []

    async def _get_domain_name(self, db_manager, domain_id: str) -> str:
        """获取领域名称"""
        if not domain_id:
            return None
        try:
            query = "SELECT name FROM domains WHERE domain_id = ?"
            result = db_manager.execute_query(query, (domain_id,))
            if result:
                return result[0].get('name')
        except Exception as e:
            self.logger.warning(f"获取领域名称失败: {e}")
        return None

    async def _get_category_name(self, db_manager, category_id: str) -> str:
        """获取类别名称"""
        if not category_id:
            return None
        try:
            query = "SELECT name FROM categories WHERE category_id = ?"
            result = db_manager.execute_query(query, (category_id,))
            if result:
                return result[0].get('name')
        except Exception as e:
            self.logger.warning(f"获取类别名称失败: {e}")
        return None

    def _enhance_prompt_with_context(
        self,
        original_prompt: str,
        user_message: str,
        context_info: Dict[str, Any],
        emotion: str
    ) -> str:
        """
        使用上下文信息增强提示指令

        Args:
            original_prompt: 原始提示指令
            user_message: 用户消息
            context_info: 上下文信息
            emotion: 用户情绪

        Returns:
            str: 增强后的提示指令
        """
        # 构建项目信息
        project_type = context_info.get("project_type", "未知项目")
        domain_name = context_info.get("domain_name", "")
        category_name = context_info.get("category_name", "")
        current_state = context_info.get("current_state", "初期")

        # 构建当前关注点信息
        focus_point = context_info.get("current_focus_point", {})
        focus_point_info = ""
        if focus_point:
            focus_point_name = focus_point.get("name", "")
            focus_point_desc = focus_point.get("description", "")
            focus_point_example = focus_point.get("example", "")

            focus_point_info = f"""
当前正在收集关注点：{focus_point_name}
关注点描述：{focus_point_desc}
示例：{focus_point_example}
"""

        # 构建对话历史摘要
        conversation_summary = self._build_conversation_summary(context_info.get("conversation_history", []))

        # 构建增强的提示指令
        enhanced_prompt = f"""
用户正在进行{project_type}的需求收集，当前处于{current_state}阶段。
项目领域：{domain_name}
项目类别：{category_name}

{focus_point_info}

最近对话摘要：
{conversation_summary}

用户现在询问："{user_message}"

请你作为专业的需求分析师：
1. 针对用户的具体问题，结合项目背景和当前关注点提供专业、实用的回答
2. 如果用户询问的是当前关注点相关的选项说明，请基于该关注点的具体内容进行解释
3. 回答要具体、有价值，避免空泛的表述
4. 回答后自然地引导用户确认或补充相关信息，继续完善需求
5. 保持专业而友好的语调

用户情绪状态：{emotion}
请根据用户情绪调整回复语调。

{original_prompt}
"""

        return enhanced_prompt.strip()

    def _build_conversation_summary(self, conversation_history: List[Dict[str, Any]]) -> str:
        """构建对话历史摘要"""
        if not conversation_history:
            return "暂无对话历史"

        summary_parts = []
        for conv in conversation_history[-3:]:  # 只取最近3条
            role = conv.get("role", "")
            content = conv.get("content", "")
            if content:
                # 截断过长的内容
                truncated_content = content[:100] + "..." if len(content) > 100 else content
                summary_parts.append(f"{role}: {truncated_content}")

        return "\n".join(summary_parts) if summary_parts else "暂无对话历史"

    async def generate_context_aware_clarification_reply(
        self,
        user_message: str,
        session_id: str = "",
        user_id: str = "",
        emotion: str = "neutral",
        **kwargs
    ) -> str:
        """
        生成上下文感知的澄清回复 - 专门处理用户的澄清请求

        Args:
            user_message: 用户消息
            session_id: 会话ID
            user_id: 用户ID
            emotion: 用户情绪
            **kwargs: 其他参数

        Returns:
            str: 澄清回复
        """
        try:
            # 获取上下文信息
            context_info = await self._get_session_context(session_id, user_id)

            # 构建澄清专用的提示指令
            clarification_prompt = self._build_clarification_prompt(user_message, context_info, emotion)

            context = GenerationContext(
                prompt_instruction=clarification_prompt,
                user_message=user_message,
                session_id=session_id,
                emotion=emotion
            )

            # 使用澄清生成器配置
            config = self._get_agent_config("clarification_generator")

            return await self.generator.generate_reply(
                context=context,
                config=config,
                strategy=PromptStrategy.WITH_USER_INPUT,
                fallback_message="请您详细说明一下您的问题，我会尽力为您解答。"
            )

        except Exception as e:
            self.logger.error(f"生成澄清回复失败: {e}")
            return "请您详细说明一下您的问题，我会尽力为您解答。"

    def _build_clarification_prompt(
        self,
        user_message: str,
        context_info: Dict[str, Any],
        emotion: str
    ) -> str:
        """
        构建澄清专用的提示指令

        Args:
            user_message: 用户消息
            context_info: 上下文信息
            emotion: 用户情绪

        Returns:
            str: 澄清提示指令
        """
        project_type = context_info.get("project_type", "项目")
        focus_point = context_info.get("current_focus_point", {})

        # 如果有当前关注点，构建相关的澄清信息
        focus_clarification = ""
        if focus_point:
            focus_name = focus_point.get("name", "")
            focus_desc = focus_point.get("description", "")
            focus_example = focus_point.get("example", "")

            # 特殊处理艺术风格相关的澄清
            if "风格" in focus_name or "style" in focus_name.lower():
                focus_clarification = f"""
当前正在收集：{focus_name}
具体说明：{focus_desc}

如果用户询问风格选项的特点，请基于{project_type}的特点来解释相关风格，而不是通用的设计概念。
例如，如果是插画设计项目，应该解释插画风格（如手绘水彩、扁平风、MBE风格等），而不是网页设计风格。

参考示例：{focus_example}
"""
            else:
                focus_clarification = f"""
当前正在收集：{focus_name}
具体说明：{focus_desc}
参考示例：{focus_example}
"""

        clarification_prompt = f"""
你是一个专业的需求分析师，用户正在进行{project_type}的需求收集。

{focus_clarification}

用户询问："{user_message}"

请你：
1. 基于当前项目类型和正在收集的关注点，提供准确、相关的解释
2. 如果用户询问的是选项说明，请确保解释的内容与当前项目领域相匹配
3. 解释要清晰、具体，避免泛泛而谈
4. 解释完后，引导用户做出选择或提供更多信息
5. 保持专业而友好的语调

用户情绪：{emotion}
请根据用户情绪调整回复语调。
"""

        return clarification_prompt.strip()

    async def generate_domain_guidance(
        self,
        prompt_instruction: str,
        user_input: str,
        domains_text: str = "",
        session_id: str = "",
        user_id: str = ""
    ) -> str:
        """
        生成领域引导问题

        Args:
            prompt_instruction: 决策引擎提供的指令
            user_input: 用户输入
            domains_text: 格式化的领域信息文本
            session_id: 会话ID

        Returns:
            str: 生成的领域引导问题
        """
        # 获取历史会话信息
        conversation_history = await self.generator._get_conversation_history(session_id, user_id)

        # 构建上下文，使用模板策略
        context = GenerationContext(
            prompt_instruction=prompt_instruction,
            user_message=user_input,
            session_id=session_id,
            template_name="domain_guidance",
            template_variables={
                "user_input": user_input,
                "domains_text": domains_text,
                "prompt_instruction": prompt_instruction,
                "conversation_history": conversation_history
            }
        )

        # 使用统一的配置获取方法
        config = self._get_agent_config("domain_guidance_generator")

        # 统一调用 generate_reply，享受缓存、重试、验证等所有高级功能
        return await self.generator.generate_reply(
            context=context,
            config=config,
            strategy=PromptStrategy.TEMPLATE_BASED,
            fallback_message="请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。"
        )
