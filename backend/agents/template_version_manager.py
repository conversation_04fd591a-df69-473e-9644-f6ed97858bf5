# -*- coding: utf-8 -*-
"""
消息模板版本管理器 - 支持版本控制、A/B测试和效果追踪
版本: v1.0
作者: AI Assistant
创建时间: 2025-06-20

功能:
1. 消息模板版本控制和历史管理
2. A/B测试支持和流量分配
3. 模板效果追踪和分析
4. 模板回滚和发布管理
5. 多语言版本支持
"""

import logging
import json
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum
from datetime import datetime
from backend.config.unified_config_loader import get_unified_config
from dataclasses import dataclass
from backend.data.db.database_manager import DatabaseManager


class TemplateStatus(Enum):
    """模板状态枚举"""
    DRAFT = "draft"                 # 草稿
    TESTING = "testing"             # 测试中
    ACTIVE = "active"               # 激活
    DEPRECATED = "deprecated"       # 已弃用
    ARCHIVED = "archived"           # 已归档





@dataclass
class TemplateVersion:
    """模板版本数据类"""
    template_id: str
    version: str
    content: str
    variables: List[str]
    category: str
    language: str
    status: TemplateStatus
    created_by: str
    created_at: datetime
    updated_at: datetime
    description: str = ""
    tags: List[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}








class TemplateVersionManager:
    """消息模板版本管理器"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        """
        初始化模板版本管理器

        Args:
            db_manager: 数据库管理器实例
        """
        self.logger = logging.getLogger(__name__)
        from ..config import settings
        self.db_manager = db_manager or DatabaseManager(str(settings.DATABASE_PATH))
        self.config = get_unified_config()

        # 内存缓存
        self._template_cache = {}

        self._metrics_cache = {}

        self.logger.info("模板版本管理器初始化完成")

    async def initialize_database(self):
        """异步初始化数据库表"""
        await self._initialize_database()
    
    async def _initialize_database(self):
        """初始化数据库表"""
        try:
            # 使用事务来确保所有表都创建成功
            queries = [
                # 创建模板版本表
                ("""
                    CREATE TABLE IF NOT EXISTS template_versions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        template_id TEXT NOT NULL,
                        version TEXT NOT NULL,
                        content TEXT NOT NULL,
                        variables TEXT,
                        category TEXT NOT NULL,
                        language TEXT DEFAULT 'zh-CN',
                        status TEXT NOT NULL,
                        created_by TEXT NOT NULL,
                        created_at TIMESTAMP NOT NULL,
                        updated_at TIMESTAMP NOT NULL,
                        description TEXT,
                        tags TEXT,
                        metadata TEXT,
                        UNIQUE(template_id, version)
                    )
                """, None),

                

                # 创建模板使用指标表
                ("""
                    CREATE TABLE IF NOT EXISTS template_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        template_id TEXT NOT NULL,
                        version TEXT NOT NULL,
                        usage_count INTEGER DEFAULT 0,
                        success_count INTEGER DEFAULT 0,
                        error_count INTEGER DEFAULT 0,
                        user_satisfaction_score REAL DEFAULT 0.0,
                        avg_response_time REAL DEFAULT 0.0,
                        last_used TIMESTAMP,
                        date_recorded DATE NOT NULL,
                        UNIQUE(template_id, version, date_recorded)
                    )
                """, None),

                
            ]

            # 使用事务执行所有创建表的SQL
            success = await self.db_manager.execute_transaction(queries)

            if success:
                self.logger.info("模板版本管理数据库表初始化完成")
            else:
                raise Exception("数据库表创建事务失败")

        except Exception as e:
            self.logger.error(f"初始化数据库表失败: {e}", exc_info=True)
            raise
    
    async def create_template_version(
        self,
        template_id: str,
        content: str,
        variables: List[str] = None,
        category: str = "general",
        language: str = "zh-CN",
        created_by: str = "system",
        description: str = "",
        tags: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> TemplateVersion:
        """
        创建新的模板版本
        
        Args:
            template_id: 模板ID
            content: 模板内容
            variables: 变量列表
            category: 模板类别
            language: 语言
            created_by: 创建者
            description: 描述
            tags: 标签
            metadata: 元数据
            
        Returns:
            TemplateVersion: 创建的模板版本
        """
        try:
            # 生成新版本号
            latest_version = await self._get_latest_version(template_id)
            new_version = self._increment_version(latest_version)
            
            # 创建模板版本对象
            template_version = TemplateVersion(
                template_id=template_id,
                version=new_version,
                content=content,
                variables=variables or [],
                category=category,
                language=language,
                status=TemplateStatus.DRAFT,
                created_by=created_by,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                description=description,
                tags=tags or [],
                metadata=metadata or {}
            )
            
            # 保存到数据库
            await self._save_template_version(template_version)
            
            # 更新缓存
            cache_key = f"{template_id}_{new_version}"
            self._template_cache[cache_key] = template_version
            
            self.logger.info(f"创建模板版本成功: {template_id} v{new_version}")
            return template_version
            
        except Exception as e:
            self.logger.error(f"创建模板版本失败: {e}", exc_info=True)
            raise
    
    async def get_template_version(
        self,
        template_id: str,
        version: str = None,
        language: str = "zh-CN"
    ) -> Optional[TemplateVersion]:
        """
        获取指定版本的模板
        
        Args:
            template_id: 模板ID
            version: 版本号，如果为None则获取最新激活版本
            language: 语言
            
        Returns:
            TemplateVersion: 模板版本对象
        """
        try:
            # 如果没有指定版本，获取最新激活版本
            if version is None:
                version = await self._get_active_version(template_id, language)
                if not version:
                    return None
            
            # 检查缓存
            cache_key = f"{template_id}_{version}"
            if cache_key in self._template_cache:
                return self._template_cache[cache_key]
            
            # 从数据库查询
            query = self.config.get_config_value("database.queries.template_versions.get_by_id_version")
            result = await self.db_manager.get_record(query, (template_id, version, language))
            
            if result:
                template_version = self._row_to_template_version(result)
                self._template_cache[cache_key] = template_version
                return template_version
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取模板版本失败: {e}", exc_info=True)
            return None
    
    async def activate_template_version(
        self,
        template_id: str,
        version: str,
        language: str = "zh-CN"
    ) -> bool:
        """
        激活指定版本的模板
        
        Args:
            template_id: 模板ID
            version: 版本号
            language: 语言
            
        Returns:
            bool: 是否成功
        """
        try:
            # 先将同一模板的其他版本设为deprecated
            update_old_sql = self.config.get_config_value("database.queries.template_versions.update_status_deprecated")
            await self.db_manager.execute_update(
                update_old_sql,
                (TemplateStatus.DEPRECATED.value, datetime.now(), template_id, language, TemplateStatus.ACTIVE.value)
            )
            
            # 激活指定版本
            update_new_sql = self.config.get_config_value("database.queries.template_versions.update_status_active")
            await self.db_manager.execute_update(
                update_new_sql,
                (TemplateStatus.ACTIVE.value, datetime.now(), template_id, version, language)
            )
            
            # 清除相关缓存
            self._clear_template_cache(template_id)
            
            self.logger.info(f"激活模板版本成功: {template_id} v{version}")
            return True
            
        except Exception as e:
            self.logger.error(f"激活模板版本失败: {e}", exc_info=True)
            return False
    
    
    
    async def get_template_for_user(
        self,
        template_id: str,
        user_id: str,
        language: str = "zh-CN"
    ) -> Tuple[Optional[TemplateVersion], Optional[str]]:
        """
        为用户获取模板（考虑A/B测试）
        
        Args:
            template_id: 模板ID
            user_id: 用户ID
            language: 语言
            
        Returns:
            Tuple[TemplateVersion, test_id]: 模板版本和测试ID（如果参与A/B测试）
        """
        try:
            # 使用正常的激活版本
                template_version = await self.get_template_version(template_id, None, language)
                return template_version, None
                
        except Exception as e:
            self.logger.error(f"获取用户模板失败: {e}", exc_info=True)
            # 返回默认版本
            template_version = await self.get_template_version(template_id, None, language)
            return template_version, None
    
    
    
    

    

    async def list_template_versions(
        self,
        template_id: str = None,
        status: TemplateStatus = None,
        language: str = None
    ) -> List[Dict[str, Any]]:
        """
        列出模板版本

        Args:
            template_id: 模板ID过滤
            status: 状态过滤
            language: 语言过滤

        Returns:
            List[Dict]: 模板版本列表
        """
        try:
            conditions = []
            params = []

            if template_id:
                conditions.append("template_id = ?")
                params.append(template_id)

            if status:
                conditions.append("status = ?")
                params.append(status.value)

            if language:
                conditions.append("language = ?")
                params.append(language)

            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

            query = f"""
                SELECT template_id, version, category, language, status,
                       created_by, created_at, updated_at, description
                FROM template_versions
                {where_clause}
                ORDER BY template_id, created_at DESC
            """

            results = await self.db_manager.execute_query(query, params)

            versions = []
            for result in results:
                versions.append({
                    "template_id": result.get("template_id"),
                    "version": result.get("version"),
                    "category": result.get("category"),
                    "language": result.get("language"),
                    "status": result.get("status"),
                    "created_by": result.get("created_by"),
                    "created_at": result.get("created_at"),
                    "updated_at": result.get("updated_at"),
                    "description": result.get("description")
                })

            return versions

        except Exception as e:
            self.logger.error(f"列出模板版本失败: {e}", exc_info=True)
            return []

    # 私有辅助方法
    async def _get_latest_version(self, template_id: str) -> str:
        """获取最新版本号"""
        query = self.config.get_config_value("database.queries.template_versions.get_latest_version")
        result = await self.db_manager.get_record(query, (template_id,))
        return result["version"] if result else "0.0"

    def _increment_version(self, current_version: str) -> str:
        """递增版本号"""
        try:
            parts = current_version.split('.')
            if len(parts) >= 2:
                major, minor = int(parts[0]), int(parts[1])
                return f"{major}.{minor + 1}"
            else:
                return "1.0"
        except:
            return "1.0"

    async def _get_active_version(self, template_id: str, language: str) -> Optional[str]:
        """获取激活版本号"""
        query = """
            SELECT version FROM template_versions
            WHERE template_id = ? AND language = ? AND status = ?
            ORDER BY created_at DESC
            LIMIT 1
        """
        result = await self.db_manager.get_record(query, (template_id, language, TemplateStatus.ACTIVE.value))
        return result["version"] if result else None

    async def _save_template_version(self, template_version: TemplateVersion):
        """保存模板版本到数据库"""
        query = """
            INSERT INTO template_versions (
                template_id, version, content, variables, category, language,
                status, created_by, created_at, updated_at, description, tags, metadata
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            template_version.template_id,
            template_version.version,
            template_version.content,
            json.dumps(template_version.variables),
            template_version.category,
            template_version.language,
            template_version.status.value,
            template_version.created_by,
            template_version.created_at,
            template_version.updated_at,
            template_version.description,
            json.dumps(template_version.tags),
            json.dumps(template_version.metadata)
        )
        await self.db_manager.execute_update(query, params)

    

    

    

    

    

    

    def _row_to_template_version(self, row) -> TemplateVersion:
        """将数据库行转换为TemplateVersion对象"""
        return TemplateVersion(
            template_id=row["template_id"],
            version=row["version"],
            content=row["content"],
            variables=json.loads(row["variables"]) if row["variables"] else [],
            category=row["category"],
            language=row["language"],
            status=TemplateStatus(row["status"]),
            created_by=row["created_by"],
            created_at=datetime.fromisoformat(row["created_at"]),
            updated_at=datetime.fromisoformat(row["updated_at"]),
            description=row["description"] or "",
            tags=json.loads(row["tags"]) if row["tags"] else [],
            metadata=json.loads(row["metadata"]) if row["metadata"] else {}
        )

    

    def _clear_template_cache(self, template_id: str):
        """清除模板缓存"""
        keys_to_remove = [key for key in self._template_cache.keys() if key.startswith(f"{template_id}_")]
        for key in keys_to_remove:
            del self._template_cache[key]

    

    
