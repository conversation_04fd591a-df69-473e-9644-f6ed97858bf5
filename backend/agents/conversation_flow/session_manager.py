"""
会话管理器模块

负责管理：
- 会话初始化和清理
- 异步组件管理
- 会话状态持久化
- 清理和资源释放
"""

import logging
import asyncio
from typing import Any, Dict, List



class SessionManager:
    """会话管理器"""
    
    def __init__(self, db_manager, session_context_manager, history_service):
        self.db_manager = db_manager
        self.session_context_manager = session_context_manager
        self.history_service = history_service
        self.logger = logging.getLogger(__name__)
        
        # 异步组件初始化标记
        self._async_components_initialized = False
        self._initialization_lock = asyncio.Lock()

    async def initialize_async_components(self):
        """初始化异步组件"""
        async with self._initialization_lock:
            if self._async_components_initialized:
                return
            
            try:
                self.logger.info("开始初始化异步组件...")
                
                # 初始化数据库连接
                await self.db_manager.initialize_async()
                
                # 初始化会话上下文管理器
                if hasattr(self.session_context_manager, 'initialize_async'):
                    await self.session_context_manager.initialize_async()
                
                # 初始化历史服务
                if hasattr(self.history_service, 'initialize_async'):
                    await self.history_service.initialize_async()
                
                self._async_components_initialized = True
                self.logger.info("异步组件初始化完成")
                
            except Exception as e:
                self.logger.error(f"异步组件初始化失败: {e}")
                raise

    async def cleanup_session(self, session_id: str, user_id: str):
        """清理会话资源"""
        try:
            self.logger.info(f"开始清理会话资源: {session_id}")
            
            # 清理会话上下文
            if hasattr(self.session_context_manager, 'cleanup_session'):
                await self.session_context_manager.cleanup_session(session_id, user_id)
            
            # 清理历史服务中的会话数据
            if hasattr(self.history_service, 'cleanup_session'):
                await self.history_service.cleanup_session(session_id)
            
            self.logger.info(f"会话资源清理完成: {session_id}")
            
        except Exception as e:
            self.logger.error(f"会话资源清理失败: {e}")

    async def save_session_state(self, session_id: str, user_id: str, session_context):
        """保存会话状态"""
        try:
            await self.session_context_manager.save_session_context(session_context)
            self.logger.debug(f"会话状态已保存: {session_id}")
        except Exception as e:
            self.logger.error(f"保存会话状态失败: {e}")

    async def load_session_state(self, session_id: str, user_id: str):
        """加载会话状态"""
        try:
            session_context = await self.session_context_manager.load_session_context(session_id, user_id)
            self.logger.debug(f"会话状态已加载: {session_id}")
            return session_context
        except Exception as e:
            self.logger.error(f"加载会话状态失败: {e}")
            return None

    async def create_new_session(self, session_id: str, user_id: str, initial_data: Dict[str, Any] = None):
        """创建新会话"""
        try:
            self.logger.info(f"创建新会话: {session_id} for user: {user_id}")
            
            # 创建会话上下文
            session_context = await self.session_context_manager.create_session_context(
                session_id, user_id, initial_data or {}
            )
            
            # 初始化会话历史
            await self.history_service.initialize_session(session_id)
            
            self.logger.info(f"新会话创建完成: {session_id}")
            return session_context
            
        except Exception as e:
            self.logger.error(f"创建新会话失败: {e}")
            raise

    async def session_exists(self, session_id: str, user_id: str) -> bool:
        """检查会话是否存在"""
        try:
            return await self.session_context_manager.session_exists(session_id, user_id)
        except Exception as e:
            self.logger.error(f"检查会话存在性失败: {e}")
            return False

    async def get_session_summary(self, session_id: str, user_id: str) -> Dict[str, Any]:
        """获取会话摘要"""
        try:
            session_context = await self.load_session_state(session_id, user_id)
            if not session_context:
                return {}
            
            # 获取消息数量
            message_count = await self.history_service.get_message_count(session_id)
            
            return {
                "session_id": session_id,
                "user_id": user_id,
                "current_state": session_context.current_state.name if session_context.current_state else "unknown",
                "current_domain": session_context.current_domain,
                "current_category": session_context.current_category,
                "message_count": message_count,
                "created_at": getattr(session_context, 'created_at', None),
                "updated_at": getattr(session_context, 'updated_at', None)
            }
            
        except Exception as e:
            self.logger.error(f"获取会话摘要失败: {e}")
            return {}

    async def cleanup_expired_sessions(self, max_age_hours: int = 24):
        """清理过期会话"""
        try:
            self.logger.info(f"开始清理超过{max_age_hours}小时的过期会话")
            
            # 这里应该实现具体的过期会话清理逻辑
            # 可以根据实际需求添加更详细的实现
            
            self.logger.info("过期会话清理完成")
            
        except Exception as e:
            self.logger.error(f"清理过期会话失败: {e}")

    async def get_active_sessions(self, user_id: str = None) -> List[Dict[str, Any]]:
        """获取活跃会话列表"""
        try:
            # 这里应该实现获取活跃会话的逻辑
            # 可以根据实际需求添加更详细的实现
            return []
            
        except Exception as e:
            self.logger.error(f"获取活跃会话失败: {e}")
            return []