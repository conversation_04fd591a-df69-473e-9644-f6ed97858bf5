"""
对话流程核心模块

包含主要的AutoGenConversationFlowAgent类和核心业务逻辑
"""

# 导入必要的模块和库
from typing import Any, Dict, List, Optional
import sys
import os
import asyncio

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入项目内部模块
from ..base import AutoGenBaseAgent
from ..conversation_flow_reply_mixin import ConversationFlowReplyMixin
from ..conversation_flow_message_mixin import ConversationFlowMessageMixin
from ..session_context import SessionContextManager
from ..document_generator import DocumentGenerator
from ..knowledge_base import KnowledgeBaseAgent
from backend.data.db.database_manager import DatabaseManager
from backend.data.db.document_manager import DocumentManager
from backend.data.db.focus_point_manager import FocusPointManager
from backend.data.db.message_manager import MessageManager
from backend.data.db.summary_manager import SummaryManager
from backend.config import settings
from backend.services.conversation_history_service import get_history_service

# 导入新的模块组件
from .state_manager import StateManager
from .message_processor import MessageProcessor
from .session_manager import SessionManager


class AutoGenConversationFlowAgent(AutoGenBaseAgent, ConversationFlowReplyMixin, ConversationFlowMessageMixin):
    """
    AutoGen对话流程管理Agent - 重构版本
    
    核心功能：
    - 对话状态管理：跟踪和管理对话的各个阶段
    - 消息处理：接收用户消息，进行意图识别和决策，生成回复
    - 组件协调：协调意图识别、决策引擎、文档生成等各个组件
    - 数据管理：管理会话数据、消息历史、关注点等信息
    - 错误处理：提供完善的错误处理和回退机制
    """
    
    def __init__(self,
                 llm_client=None,
                 information_extractor_agent=None,
                 document_generator_agent=None,
                 review_and_refine_agent=None,
                 domain_classifier_agent=None,
                 knowledge_base_agent=None,
                 category_classifier_agent=None,
                 db_path=None,
                 system_message=None,
                 is_termination_msg=None,
                 max_consecutive_auto_reply=None,
                 human_input_mode=None,
                 code_execution_config=None,
                 llm_config=None,
                 default_auto_reply=None,
                 intent_decision_engine=None,
                 reply_manager=None,
                 reply_factory=None,
                 integrated_reply_system=None,
                 version_manager=None
                 ):
        
        # 初始化意图决策一体化引擎 - 使用加速版
        if intent_decision_engine is None:
            from backend.agents.simplified_decision_engine import get_simplified_decision_engine
            intent_decision_engine = get_simplified_decision_engine()
            intent_decision_engine.enable_acceleration_mode()
        self.intent_decision_engine = intent_decision_engine

        # 构建 AutoGen ConversableAgent 的配置参数字典
        conversable_agent_kwargs = {
            'system_message': system_message,
            'is_termination_msg': is_termination_msg,
            'max_consecutive_auto_reply': max_consecutive_auto_reply,
            'human_input_mode': human_input_mode,
            'code_execution_config': code_execution_config,
            'llm_config': llm_config,
            'default_auto_reply': default_auto_reply,
        }

        conversable_agent_kwargs['name'] = "AutoGenConversationFlow"
        code_execution_config = code_execution_config or {}
        code_execution_config['use_docker'] = False
        conversable_agent_kwargs['code_execution_config'] = code_execution_config

        super().__init__(**conversable_agent_kwargs)

        # 初始化无状态的共享组件
        self.llm_client = llm_client
        self.llm_config = llm_config

        # 初始化数据库管理器
        self.db_path = db_path or str(settings.DATABASE_PATH)
        self.db_manager = DatabaseManager(self.db_path)
        self.document_manager = DocumentManager(self.db_manager)
        self.focus_point_manager = FocusPointManager(self.db_manager)
        self.message_manager = MessageManager(self.db_manager)
        self.summary_manager = SummaryManager(self.db_manager)

        # 初始化会话上下文管理器
        self.session_context_manager = SessionContextManager(self.db_manager)
        
        # 初始化状态管理器
        self.state_manager = StateManager(self.db_manager, self.focus_point_manager)

        # 初始化统一的对话历史服务
        self.history_service = get_history_service(self.message_manager)

        # 初始化其他组件
        self.information_extractor_agent = information_extractor_agent
        self.document_generator_agent = document_generator_agent or DocumentGenerator(
            llm_client=llm_client,
            agent_name="document_generator",
            db_manager=self.db_manager
        )
        self.review_and_refine_agent = review_and_refine_agent
        self.domain_classifier_agent = domain_classifier_agent
        self.category_classifier_agent = category_classifier_agent
        self.knowledge_base_agent = knowledge_base_agent or KnowledgeBaseAgent()

        # 初始化会话管理器
        self.session_manager = SessionManager(self.db_manager, self.session_context_manager, self.history_service)
        
        # 动态加载决策引擎和回复系统
        self._initialize_action_executor()
        
        # 初始化消息处理器（需要在action_executor之后）
        self.message_processor = MessageProcessor(
            self.intent_decision_engine,
            self.message_manager,
            self.session_context_manager,
            self.action_executor,
            self.knowledge_base_agent
        )

    def _initialize_action_executor(self):
        """初始化动作执行器"""
        from backend.handlers.action_executor import ActionExecutor
        self.action_executor = ActionExecutor(self)
    
    # 状态管理方法委托给state_manager
    async def initialize_focus_points(self, session_id: str, user_id: str, focus_points: List[Dict[str, Any]]) -> bool:
        return await self.state_manager.initialize_focus_points(session_id, user_id, focus_points)
    
    async def update_focus_point_status(self, session_id: str, user_id: str, point_id: str, status: str, value: Optional[str] = None) -> bool:
        return await self.state_manager.update_focus_point_status(session_id, user_id, point_id, status, value)
    
    async def get_next_pending_point(self, session_id: str, user_id: str, focus_points: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        return await self.state_manager.get_next_pending_point(session_id, user_id, focus_points)
    
    async def get_processing_point(self, session_id: str, user_id: str) -> Optional[str]:
        return await self.state_manager.get_processing_point(session_id, user_id)
    
    async def clear_all_processing_status(self, session_id: str, user_id: str) -> None:
        return await self.state_manager.clear_all_processing_status(session_id, user_id)
    
    async def set_point_processing(self, session_id: str, user_id: str, point_id: str) -> bool:
        return await self.state_manager.set_point_processing(session_id, user_id, point_id)
    
    def get_focus_point_status(self, point_id: str) -> Dict[str, Any]:
        return self.state_manager.get_focus_point_status(point_id)
    
    async def reset_focus_points(self, session_id: str, user_id: str) -> None:
        return await self.state_manager.reset_focus_points(session_id, user_id)

    # 安全执行方法
    async def _safe_execute(self, operation_name: str, operation_func, default_return: Any = None, log_error: bool = True) -> Any:
        """
        安全执行方法，捕获异常并记录日志
        
        Args:
            operation_name: 操作名称，用于日志记录
            operation_func: 要执行的操作函数
            default_return: 操作失败时的默认返回值
            log_error: 是否记录错误日志
        
        Returns:
            Any: 操作结果或默认返回值
        """
        try:
            if asyncio.iscoroutinefunction(operation_func):
                return await operation_func()
            else:
                return operation_func()
        except Exception as e:
            if log_error:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Operation {operation_name} failed: {e}")
            return default_return

    # 消息处理方法委托给message_processor
    async def process_message(self, message_data: Dict[str, Any], session_context=None, **kwargs) -> Dict[str, Any]:
        """
        处理用户消息 - 委托给MessageProcessor
        """
        return await self.message_processor.process_message(message_data, session_context, **kwargs)
    
    # 会话管理方法委托给session_manager
    async def initialize_async_components(self):
        """初始化异步组件"""
        return await self.session_manager.initialize_async_components()
    
    async def cleanup_session(self, session_id: str, user_id: str):
        """清理会话资源"""
        return await self.session_manager.cleanup_session(session_id, user_id)