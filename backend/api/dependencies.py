"""
API依赖注入模块

此模块负责提供FastAPI应用所需的所有依赖项。
通过将组件创建和获取逻辑集中在这里，我们实现了：
1.  **关注点分离**：API端点专注于业务逻辑，而依赖项的创建和管理则由此模块负责。
2.  **资源复用**：通过依赖注入，全局共享的资源（如数据库连接池、无状态Agent）
    只被初始化一次，并在所有请求之间共享。
3.  **可测试性**：在测试中，可以轻松地用模拟（Mock）对象替换真实的依赖项。
"""

from fastapi import Header, HTTPException, status, Request, Depends

# 导入配置和管理器

# 导入Agent工厂
from backend.agents.factory import AgentFactory

# 导入所有Agent和Manager的定义
from backend.data.db.database_manager import DatabaseManager
from backend.agents.llm_service import AutoGenLLMServiceAgent

# --- 核心依赖项 ---

def get_current_user_id(x_session_id: str | None = Header(None, alias="X-Session-ID")) -> str:
    """
    从请求头中获取并验证 X-Session-ID。
    这是我们信任的、由API网关注入的用户唯一标识。
    """
    if x_session_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid request: Session ID is missing.",
        )
    return x_session_id

def get_agent_factory(request: Request) -> AgentFactory:
    """
    依赖项：获取在应用启动时初始化的Agent工厂实例。
    """
    return request.app.state.agent_factory

# --- 无状态组件依赖项 (通过Agent工厂获取) ---

def get_db_manager(agent_factory: AgentFactory = Depends(get_agent_factory)) -> DatabaseManager:
    """获取数据库管理器实例。"""
    return agent_factory.container.get_service("database_manager")

def get_llm_service_agent(agent_factory: AgentFactory = Depends(get_agent_factory)) -> AutoGenLLMServiceAgent:
    """获取LLM服务Agent实例。"""
    return agent_factory.container.get_service("llm_service")

# --- 有状态组件依赖项 (按请求创建的工厂) ---

def get_conversation_flow_agent_factory(
    agent_factory: AgentFactory = Depends(get_agent_factory)
) -> AgentFactory:
    """
    获取ConversationFlowAgent工厂。

    返回AgentFactory实例，用于在API端点中按需创建ConversationFlowAgent。
    这确保每个用户会话都有独立的Agent实例，避免数据混乱。
    """
    return agent_factory