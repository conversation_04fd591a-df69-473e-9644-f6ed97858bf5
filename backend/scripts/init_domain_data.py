#!/usr/bin/env python3
"""
初始化领域数据脚本
为domains和categories表添加基础数据
"""

import sqlite3
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

# 直接使用数据库路径
DATABASE_PATH = os.path.join(project_root, "backend/data/aidatabase.db")

def init_domain_data():
    """初始化领域和类别数据"""
    
    # 基础领域数据
    domains_data = [
        ('web_development', 'Web开发', '网站、Web应用程序开发相关需求'),
        ('mobile_development', '移动开发', '手机APP、移动应用开发相关需求'),
        ('desktop_software', '桌面软件', '桌面应用程序、客户端软件开发需求'),
        ('data_analysis', '数据分析', '数据处理、分析、可视化相关需求'),
        ('ai_ml', '人工智能', 'AI、机器学习、深度学习相关需求'),
        ('game_development', '游戏开发', '游戏、娱乐软件开发需求'),
        ('system_software', '系统软件', '操作系统、驱动程序、系统工具需求'),
        ('database', '数据库', '数据库设计、管理、优化相关需求'),
        ('network_security', '网络安全', '网络安全、信息安全相关需求'),
        ('design_creative', '设计创意', '平面设计、UI/UX设计、创意内容需求'),
        ('business_management', '商业管理', '企业管理、业务流程、ERP相关需求'),
        ('education_training', '教育培训', '在线教育、培训系统、学习平台需求'),
        ('general', '通用领域', '通用或未分类的软件开发需求')
    ]
    
    # 类别数据（每个领域的子分类）
    categories_data = [
        # Web开发类别
        ('web_frontend', 'web_development', '前端开发', '前端界面、用户体验开发'),
        ('web_backend', 'web_development', '后端开发', '服务器端、API、数据库开发'),
        ('web_fullstack', 'web_development', '全栈开发', '前后端一体化开发'),
        ('web_ecommerce', 'web_development', '电商网站', '在线商城、购物网站开发'),
        
        # 移动开发类别
        ('mobile_ios', 'mobile_development', 'iOS开发', 'iPhone、iPad应用开发'),
        ('mobile_android', 'mobile_development', 'Android开发', 'Android手机应用开发'),
        ('mobile_hybrid', 'mobile_development', '跨平台开发', '混合开发、React Native、Flutter'),
        
        # 数据分析类别
        ('data_visualization', 'data_analysis', '数据可视化', '图表、报表、仪表板开发'),
        ('data_processing', 'data_analysis', '数据处理', '数据清洗、转换、ETL流程'),
        ('data_mining', 'data_analysis', '数据挖掘', '数据挖掘、模式识别、预测分析'),
        
        # AI/ML类别
        ('ai_nlp', 'ai_ml', '自然语言处理', 'NLP、文本分析、语言模型'),
        ('ai_computer_vision', 'ai_ml', '计算机视觉', '图像识别、视频分析、CV应用'),
        ('ai_recommendation', 'ai_ml', '推荐系统', '个性化推荐、智能匹配系统'),
        
        # 设计创意类别
        ('design_graphic', 'design_creative', '平面设计', '海报、Logo、宣传材料设计'),
        ('design_ui_ux', 'design_creative', 'UI/UX设计', '用户界面、用户体验设计'),
        ('design_multimedia', 'design_creative', '多媒体设计', '视频、动画、交互媒体'),
        
        # 通用类别
        ('general_software', 'general', '通用软件', '通用软件开发需求'),
        ('general_consultation', 'general', '技术咨询', '技术咨询、方案设计'),
    ]
    
    try:
        with sqlite3.connect(DATABASE_PATH) as conn:
            cursor = conn.cursor()
            
            print("开始初始化领域数据...")
            
            # 插入领域数据
            print("插入领域数据...")
            cursor.executemany(
                "INSERT OR REPLACE INTO domains (domain_id, name, description) VALUES (?, ?, ?)",
                domains_data
            )
            
            # 插入类别数据
            print("插入类别数据...")
            cursor.executemany(
                "INSERT OR REPLACE INTO categories (category_id, domain_id, name, description) VALUES (?, ?, ?, ?)",
                categories_data
            )
            
            conn.commit()
            
            # 验证数据
            cursor.execute("SELECT COUNT(*) FROM domains")
            domain_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM categories")
            category_count = cursor.fetchone()[0]
            
            print(f"✅ 领域数据初始化完成!")
            print(f"   - 插入 {domain_count} 个领域")
            print(f"   - 插入 {category_count} 个类别")
            
            # 显示部分数据
            print("\n📋 领域列表:")
            cursor.execute("SELECT domain_id, name, description FROM domains ORDER BY name")
            for domain_id, name, desc in cursor.fetchall():
                print(f"   - {name} ({domain_id}): {desc}")
            
            return True
            
    except sqlite3.Error as e:
        print(f"❌ 数据库错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 初始化领域数据")
    print(f"数据库路径: {DATABASE_PATH}")
    
    if not os.path.exists(DATABASE_PATH):
        print(f"❌ 数据库文件不存在: {DATABASE_PATH}")
        print("请先运行 create_tables.sql 创建数据库表")
        sys.exit(1)
    
    success = init_domain_data()
    sys.exit(0 if success else 1)
