CREATE TABLE sqlite_sequence(name,seq);
CREATE TABLE IF NOT EXISTS "category_recognition_results" (
  result_id integer PRIMARY KEY AUTOINCREMENT,
  conversation_id integer NOT NULL,
  category_id integer,
  confidence real,
  FOREIGN KEY (category_id) REFERENCES "categories" (category_id)  ON DELETE NO ACTION ON UPDATE NO ACTION
);;
CREATE TABLE IF NOT EXISTS "domains" (
  domain_id text PRIMARY KEY,
  name text UNIQUE NOT NULL,
  description text NOT NULL
);
CREATE TABLE IF NOT EXISTS "categories" (
    category_id TEXT,
    domain_id TEXT,
    name TEXT,
    description TEXT
);
CREATE TABLE sqlite_stat1(tbl,idx,stat);
CREATE TABLE IF NOT EXISTS "messages" (
  message_id integer PRIMARY KEY AUTOINCREMENT,
  conversation_id integer NOT NULL,
  user_id TEXT,
  sender_type text NOT NULL,
  content text NOT NULL,
  focus_id text,
  message_type text,
  created_at text DEFAULT(datetime('now')),
  FOR<PERSON><PERSON>N KEY (conversation_id) REFERENCES "conversations" (conversation_id)  ON DELETE CASCADE ON UPDATE NO ACTION
);;
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_user_id ON messages(user_id);
CREATE INDEX idx_messages_focus_id ON messages(focus_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE TABLE IF NOT EXISTS "documents" (
    document_id TEXT PRIMARY KEY,
    conversation_id INTEGER NOT NULL REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    user_id TEXT,
    -- 关联会话
    version INTEGER NOT NULL DEFAULT 1,
    -- 版本号
    content TEXT NOT NULL,
    -- Markdown 格式文档
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'confirmed', 'rejected')),
    -- 状态：草稿、已确认、已拒绝
    feedback TEXT,
    -- 用户反馈
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    CONSTRAINT unique_user_conversation_version UNIQUE (user_id, conversation_id, version) -- 确保每个用户的每个会话的版本唯一
);
CREATE INDEX idx_documents_user_id ON documents(user_id);
CREATE TABLE IF NOT EXISTS "focus_point_definitions" (
  focus_id text PRIMARY KEY,
  category_id text,
  name text NOT NULL,
  description text NOT NULL,
  priority text NOT NULL,
  example text,
  required integer NOT NULL DEFAULT(0)
);
CREATE TABLE IF NOT EXISTS "conversation_summaries" (
  conversation_id text,
  user_id TEXT,
  summary_json text NOT NULL,
  updated_at text DEFAULT(datetime('now')),
  PRIMARY KEY (conversation_id, user_id),
  FOREIGN KEY (conversation_id) REFERENCES "conversations" (conversation_id)  ON DELETE CASCADE ON UPDATE NO ACTION
);
CREATE TABLE IF NOT EXISTS "conversations" (
  conversation_id text PRIMARY KEY,
  user_id text,
  domain_id text,
  category_id text,
  status text NOT NULL DEFAULT('active'),
  created_at text DEFAULT(datetime('now')),
  updated_at text DEFAULT(datetime('now')),
  last_activity_at text DEFAULT(datetime('now')),
  metadata text,
  FOREIGN KEY (category_id) REFERENCES "categories" (category_id)  ON DELETE NO ACTION ON UPDATE NO ACTION,
  FOREIGN KEY (domain_id) REFERENCES "domains" (domain_id)  ON DELETE NO ACTION ON UPDATE NO ACTION
);
CREATE INDEX idx_conversations_user_id ON conversations (user_id);
CREATE TABLE IF NOT EXISTS "concern_point_coverage" (
  coverage_id integer PRIMARY KEY AUTOINCREMENT,
  conversation_id integer NOT NULL,
  user_id TEXT,
  focus_id text NOT NULL,
  status text DEFAULT('pending'),
  attempts integer DEFAULT(0),
  is_covered integer DEFAULT(0),
  extracted_info text,
  updated_at text DEFAULT(datetime('now')),
  additional_data text,
  FOREIGN KEY (conversation_id) REFERENCES "conversations" (conversation_id)  ON DELETE CASCADE ON UPDATE NO ACTION
);
CREATE INDEX idx_concern_point_coverage_conversation_id ON concern_point_coverage (conversation_id);
CREATE INDEX idx_concern_point_coverage_focus_id ON concern_point_coverage (focus_id);
CREATE INDEX idx_concern_point_coverage_status ON concern_point_coverage (status);
CREATE INDEX idx_concern_point_coverage_user_id ON concern_point_coverage(user_id);
CREATE TABLE requirements (requirement_id INTEGER PRIMARY KEY AUTOINCREMENT, conversation_id INTEGER NOT NULL REFERENCES conversations(conversation_id) ON DELETE CASCADE, user_id TEXT, concern_point_id INTEGER REFERENCES concern_points(concern_point_id), content TEXT NOT NULL);
CREATE INDEX idx_requirements_user_id ON requirements(user_id);