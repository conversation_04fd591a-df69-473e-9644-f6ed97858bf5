-- 迁移脚本：修复documents表的唯一约束问题
-- 将 UNIQUE (conversation_id, version) 改为 UNIQUE (user_id, conversation_id, version)

BEGIN TRANSACTION;

-- 1. 创建新的documents表结构
CREATE TABLE IF NOT EXISTS "documents_new" (
    document_id TEXT PRIMARY KEY,
    conversation_id INTEGER NOT NULL REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    user_id TEXT,
    -- 关联会话
    version INTEGER NOT NULL DEFAULT 1,
    -- 版本号
    content TEXT NOT NULL,
    -- Markdown 格式文档
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'confirmed', 'rejected')),
    -- 状态：草稿、已确认、已拒绝
    feedback TEXT,
    -- 用户反馈
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    CONSTRAINT unique_user_conversation_version UNIQUE (user_id, conversation_id, version) -- 确保每个用户的每个会话的版本唯一
);

-- 2. 复制现有数据到新表
INSERT INTO documents_new 
SELECT document_id, conversation_id, user_id, version, content, status, feedback, created_at, updated_at
FROM documents;

-- 3. 删除旧表
DROP TABLE documents;

-- 4. 重命名新表
ALTER TABLE documents_new RENAME TO documents;

-- 5. 重新创建索引
CREATE INDEX idx_documents_user_id ON documents(user_id);

COMMIT;
