# Backend Scripts 说明文档

## 概述

本目录包含系统的数据库管理和初始化脚本，用于数据库表创建、数据初始化和数据库迁移等操作。

## 脚本列表

### 1. create_tables.sql
**用途**: 数据库表结构创建脚本  
**功能**: 创建系统所需的所有数据库表和索引  
**使用场景**: 
- 首次部署系统时创建数据库结构
- 开发环境初始化
- 测试环境搭建

**包含的表**:
- `conversations` - 对话会话表
- `messages` - 消息记录表
- `documents` - 文档存储表
- `domains` - 领域定义表
- `categories` - 分类定义表
- `focus_point_definitions` - 关注点定义表
- `concern_point_coverage` - 关注点覆盖情况表
- `conversation_summaries` - 对话摘要表
- `category_recognition_results` - 分类识别结果表
- `requirements` - 需求记录表

**使用方法**:
```bash
# 使用SQLite命令行工具执行
sqlite3 backend/data/aidatabase.db < backend/scripts/create_tables.sql

# 或使用Python
import sqlite3
conn = sqlite3.connect('backend/data/aidatabase.db')
with open('backend/scripts/create_tables.sql', 'r') as f:
    conn.executescript(f.read())
conn.close()
```

### 2. init_domain_data.py
**用途**: 初始化领域和分类数据  
**功能**: 向domains和categories表插入基础数据  
**使用场景**:
- 系统首次部署后初始化基础数据
- 重置或更新领域分类数据
- 开发测试环境数据准备

**包含的数据**:
- **13个基础领域**: Web开发、移动开发、桌面软件、数据分析、人工智能、游戏开发、系统软件、数据库、网络安全、设计创意、商业管理、教育培训、通用领域
- **对应的分类数据**: 每个领域下的具体分类

**使用方法**:
```bash
# 直接运行脚本
python backend/scripts/init_domain_data.py

# 或在项目根目录运行
cd /path/to/project
python -m backend.scripts.init_domain_data
```

**注意事项**:
- 脚本会检查数据库文件是否存在
- 使用 `INSERT OR REPLACE` 语句，可以安全重复执行
- 执行后会显示插入的数据统计和验证信息

### 3. migrate_documents_constraint.sql
**用途**: 数据库迁移脚本 - 修复documents表约束  
**功能**: 修复documents表的唯一约束问题  
**使用场景**:
- 从旧版本数据库升级到新版本
- 修复多用户环境下的数据约束问题

**具体操作**:
- 将原有的 `UNIQUE (conversation_id, version)` 约束
- 修改为 `UNIQUE (user_id, conversation_id, version)` 约束
- 确保多用户环境下数据隔离的正确性

**使用方法**:
```bash
# 使用SQLite命令行工具执行
sqlite3 backend/data/aidatabase.db < backend/scripts/migrate_documents_constraint.sql

# 建议在执行前备份数据库
cp backend/data/aidatabase.db backend/data/aidatabase.db.backup
```

**注意事项**:
- ⚠️ 这是一个破坏性操作，执行前请务必备份数据库
- 脚本使用事务确保操作的原子性
- 如果迁移失败，事务会自动回滚

## 使用顺序

对于全新部署，建议按以下顺序执行脚本：

1. **创建数据库表结构**:
   ```bash
   sqlite3 backend/data/aidatabase.db < backend/scripts/create_tables.sql
   ```

2. **初始化基础数据**:
   ```bash
   python backend/scripts/init_domain_data.py
   ```

3. **如需迁移（仅在升级时）**:
   ```bash
   sqlite3 backend/data/aidatabase.db < backend/scripts/migrate_documents_constraint.sql
   ```

## 安全注意事项

### 备份策略
- 执行任何数据库脚本前，请先备份数据库文件
- 建议使用时间戳命名备份文件：
  ```bash
  cp backend/data/aidatabase.db backend/data/aidatabase.db.$(date +%Y%m%d_%H%M%S)
  ```

### 权限检查
- 确保脚本有足够的权限访问数据库文件
- 检查数据库文件的读写权限

### 环境验证
- 在生产环境执行前，先在测试环境验证脚本
- 检查脚本执行后的数据完整性

## 故障排除

### 常见问题

1. **数据库文件不存在**
   ```
   错误: 数据库文件不存在
   解决: 确保数据库文件路径正确，或先创建空数据库文件
   ```

2. **权限不足**
   ```
   错误: Permission denied
   解决: 检查文件权限，确保有读写权限
   ```

3. **表已存在**
   ```
   错误: table already exists
   解决: 脚本使用 CREATE TABLE IF NOT EXISTS，正常情况下不会出现此错误
   ```

4. **数据重复**
   ```
   错误: UNIQUE constraint failed
   解决: init_domain_data.py 使用 INSERT OR REPLACE，可以安全重复执行
   ```

### 验证方法

执行脚本后，可以使用以下SQL验证数据：

```sql
-- 检查表是否创建成功
.tables

-- 检查领域数据
SELECT COUNT(*) FROM domains;
SELECT * FROM domains LIMIT 5;

-- 检查分类数据
SELECT COUNT(*) FROM categories;
SELECT * FROM categories LIMIT 5;

-- 检查表结构
.schema documents
```

## 维护建议

1. **定期备份**: 建立定期数据库备份机制
2. **版本控制**: 所有脚本变更都应纳入版本控制
3. **测试验证**: 新脚本应在测试环境充分验证
4. **文档更新**: 脚本变更时及时更新此文档

---

**最后更新**: 2025年7月30日  
**维护者**: 系统开发团队
