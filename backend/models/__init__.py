#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模型模块

该模块包含系统中所有数据模型的定义，主要用于API请求/响应和数据验证。
"""

# 导入管理后台相关的数据模型
from .admin import (
    # 枚举类型
    AdminRole,
    AdminStatus,
    UserStatus,
    
    # 认证相关模型
    AdminLoginRequest,
    AdminLoginResponse,
    AdminUserInfo,
    TokenRefreshResponse,
    
    # 用户管理相关模型
    UserInfo,
    UserListRequest,
    UserListResponse,
    UserStatusUpdateRequest,
    ConversationInfo,
    
    # 数据统计相关模型
    StatsOverviewResponse,
    TrendDataPoint,
    TrendsResponse,
    DomainStats,
    DomainStatsResponse,
    
    # 系统监控相关模型
    SystemStatus,
    LogEntry,
    LogListRequest,
    LogListResponse,
    
    # 配置管理相关模型
    ConfigItem,
    ConfigListResponse,
    ConfigUpdateRequest,
    AIModelConfig,
    AIModelsConfigResponse,
    
    # 操作日志相关模型
    OperationLog,
    OperationLogRequest,
    OperationLogResponse,
    CreateOperationLogRequest,
    
    # 通用响应模型
    ApiResponse,
    ErrorResponse,
)

# 导出所有模型
__all__ = [
    # 枚举类型
    "AdminRole",
    "AdminStatus", 
    "UserStatus",
    
    # 认证相关模型
    "AdminLoginRequest",
    "AdminLoginResponse",
    "AdminUserInfo",
    "TokenRefreshResponse",
    
    # 用户管理相关模型
    "UserInfo",
    "UserListRequest",
    "UserListResponse",
    "UserStatusUpdateRequest",
    "ConversationInfo",
    
    # 数据统计相关模型
    "StatsOverviewResponse",
    "TrendDataPoint",
    "TrendsResponse",
    "DomainStats",
    "DomainStatsResponse",
    
    # 系统监控相关模型
    "SystemStatus",
    "LogEntry",
    "LogListRequest",
    "LogListResponse",
    
    # 配置管理相关模型
    "ConfigItem",
    "ConfigListResponse",
    "ConfigUpdateRequest",
    "AIModelConfig",
    "AIModelsConfigResponse",
    
    # 操作日志相关模型
    "OperationLog",
    "OperationLogRequest",
    "OperationLogResponse",
    "CreateOperationLogRequest",
    
    # 通用响应模型
    "ApiResponse",
    "ErrorResponse",
]
