"""
后台管理系统数据模型
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class AdminRole(str, Enum):
    """管理员角色枚举"""
    SUPER_ADMIN = "super_admin"
    SYSTEM_ADMIN = "system_admin"
    DATA_ANALYST = "data_analyst"
    CUSTOMER_SERVICE = "customer_service"


class AdminStatus(str, Enum):
    """管理员状态枚举"""
    ACTIVE = "active"
    DISABLED = "disabled"
    DELETED = "deleted"


class UserStatus(str, Enum):
    """用户状态枚举"""
    ACTIVE = "active"
    DISABLED = "disabled"
    DELETED = "deleted"


# ============================================================================
# 认证相关模型
# ============================================================================

class AdminLoginRequest(BaseModel):
    """管理员登录请求"""
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=6, max_length=100)


class AdminLoginResponse(BaseModel):
    """管理员登录响应"""
    token: str
    user: 'AdminUserInfo'
    expires_in: int


class AdminUserInfo(BaseModel):
    """管理员用户信息"""
    id: int
    username: str
    role: AdminRole
    email: Optional[str] = None
    created_at: datetime
    last_login: Optional[datetime] = None
    status: AdminStatus


class TokenRefreshResponse(BaseModel):
    """Token刷新响应"""
    token: str
    expires_in: int


# ============================================================================
# 用户管理相关模型
# ============================================================================

class UserInfo(BaseModel):
    """用户信息"""
    id: str
    created_at: datetime
    last_active: Optional[datetime] = None
    status: UserStatus
    conversation_count: int = 0
    document_count: int = 0


class UserListRequest(BaseModel):
    """用户列表请求"""
    page: int = Field(1, ge=1)
    limit: int = Field(20, ge=1, le=100)
    search: Optional[str] = None
    status: Optional[UserStatus] = None


class UserListResponse(BaseModel):
    """用户列表响应"""
    users: List[UserInfo]
    total: int
    page: int
    limit: int


class UserStatusUpdateRequest(BaseModel):
    """用户状态更新请求"""
    status: UserStatus
    reason: Optional[str] = None


class ConversationInfo(BaseModel):
    """对话信息"""
    id: str
    user_id: str
    created_at: datetime
    updated_at: datetime
    status: str
    message_count: int
    domain: Optional[str] = None


# ============================================================================
# 数据统计相关模型
# ============================================================================

class StatsOverviewResponse(BaseModel):
    """统计概览响应"""
    active_users: int
    total_conversations: int
    total_documents: int
    avg_response_time: float
    error_rate: float
    period_comparison: Dict[str, str]


class TrendDataPoint(BaseModel):
    """趋势数据点"""
    timestamp: datetime
    value: float


class TrendsResponse(BaseModel):
    """趋势数据响应"""
    metric: str
    period: str
    data: List[TrendDataPoint]


class DomainStats(BaseModel):
    """领域统计"""
    domain: str
    count: int
    percentage: float


class DomainStatsResponse(BaseModel):
    """领域统计响应"""
    domains: List[DomainStats]
    total: int


# ============================================================================
# 系统监控相关模型
# ============================================================================

class SystemStatus(BaseModel):
    """系统状态"""
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    database_status: str
    api_response_time: float
    active_connections: int
    uptime: int


class LogEntry(BaseModel):
    """日志条目"""
    id: int
    timestamp: datetime
    level: str
    message: str
    module: Optional[str] = None
    user_id: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class LogListRequest(BaseModel):
    """日志列表请求"""
    page: int = Field(1, ge=1)
    limit: int = Field(50, ge=1, le=200)
    level: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    search: Optional[str] = None


class LogListResponse(BaseModel):
    """日志列表响应"""
    logs: List[LogEntry]
    total: int
    page: int
    limit: int


# ============================================================================
# 配置管理相关模型
# ============================================================================

class ConfigItem(BaseModel):
    """配置项"""
    key: str
    value: str
    type: str
    description: Optional[str] = None
    category: str
    updated_at: datetime
    updated_by: Optional[int] = None


class ConfigListResponse(BaseModel):
    """配置列表响应"""
    configs: List[ConfigItem]


class ConfigUpdateRequest(BaseModel):
    """配置更新请求"""
    value: str
    description: Optional[str] = None


class AIModelConfig(BaseModel):
    """AI模型配置"""
    api_key: str
    base_url: str
    enabled: bool = True
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None


class AIModelsConfigResponse(BaseModel):
    """AI模型配置响应"""
    deepseek: Optional[AIModelConfig] = None
    doubao: Optional[AIModelConfig] = None
    qwen: Optional[AIModelConfig] = None
    openai: Optional[AIModelConfig] = None


# ============================================================================
# 操作日志相关模型
# ============================================================================

class OperationLog(BaseModel):
    """操作日志"""
    id: int
    admin_id: int
    admin_username: str
    action: str
    resource: Optional[str] = None
    details: Optional[str] = None
    ip_address: Optional[str] = None
    created_at: datetime


class OperationLogRequest(BaseModel):
    """操作日志请求"""
    page: int = Field(1, ge=1)
    limit: int = Field(50, ge=1, le=200)
    admin_id: Optional[int] = None
    action: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


class OperationLogResponse(BaseModel):
    """操作日志响应"""
    logs: List[OperationLog]
    total: int
    page: int
    limit: int


class CreateOperationLogRequest(BaseModel):
    """创建操作日志请求"""
    action: str
    resource: Optional[str] = None
    details: Optional[str] = None


# ============================================================================
# 通用响应模型
# ============================================================================

class ApiResponse(BaseModel):
    """API通用响应"""
    success: bool
    message: Optional[str] = None
    data: Optional[Any] = None


class ErrorResponse(BaseModel):
    """错误响应"""
    success: bool = False
    error_code: int
    message: str
    details: Optional[Dict[str, Any]] = None


# 更新前向引用
AdminLoginResponse.model_rebuild()
