# 信息提取模板 (V8 - 简洁版)

## 任务描述
你是一位经验丰富的需求分析师。请结合**已知信息摘要**和**对话历史**，从**用户的最新输入**中提取、更新或补充与关注点相关的信息，并**智能评估**信息完整度。
**同时考虑对话上下文和信息的不确定性**。**请务必只返回JSON格式的内容，不得包含任何其他文字、注释、代码块标记或换行。**

## 关注点列表
{focus_points_str}

## 对话历史 (Conversation History)
{{conversation_history}}

## 用户输入 (Latest User Input)
{{user_input}}

## 提取原则与输出要求

### ⚠️ **关键原则：关联性检查**

**只有当用户输入与当前关注点完全无关时，才返回空数组 `extracted_points: []`**

#### 什么是"完全无关"：
- 用户输入与关注点及其业务背景没有任何联系
- 例如：关注点是"预算多少"，用户说"今天天气很好"

#### 什么应该提取：
- **直接回答**：用户直接回答关注点问题
- **相关信息**：虽然不直接回答，但对关注点有参考价值的信息
- **背景信息**：能够影响关注点决策的用户特征、偏好等

### 基本提取原则
1. **专注关联**：仅提取与关注点相关的信息。
2. **用户优先**：新旧信息冲突时，优先采纳用户的最新描述。
3. **常识验证**：在提取信息前，必须对用户输入进行常识验证，发现明显错误时需要标记并降低完整度。
4. **智能完整度评估**：作为专业需求分析师，请智能评估每个关注点的完整度。

### ⚠️ **严禁的错误行为**
1. **禁止强制关联**：不得从完全无关的输入中强行提取信息
2. **禁止过度推理**：不得进行不合理的推理和联想

## 完整度评估原则

### 简单三步流程

#### 第一步：关联性检查
- 如果用户输入与关注点完全无关（如天气与预算），返回 `{"extracted_points": []}`
- 否则，继续提取相关信息

#### 第二步：常识验证
- 发现明显错误时，在value中标注并降低completeness
- 例如："预算-10万 (常识错误：预算不能为负数)", completeness: 0.2

#### 第三步：完整度评估

**固定档位评分标准**：

- **1.0分**：信息完整明确，足以做决策
  - 明确的委托决策："听你们的"、"你们决定"
  - 明确的否定回答："不需要"、"没有特别的"
  - 明确的确认回答："就这样就行"、"没问题"
  - 直接明确的回答，信息充分

- **0.8分**：信息基本完整，核心内容明确
  - 回答了关注点的主要内容
  - 信息具体且有用，但可能缺少细节

- **0.6分**：寻求建议/方案型
  - 用户希望获得专业建议或多个选项
  - 用户保留最终决策权，需要系统提供建议
  - 例如："给我一些建议"、"你推荐什么"

- **0.4分**：有一定信息，但不够明确
  - 回答包含有用信息但比较模糊
  - 需要进一步澄清或补充

- **0.2分**：信息模糊或不确定
  - 回答含糊其辞，包含大量不确定词汇
  - 信息量少且质量不高

- **0.0分**：几乎没有有效信息
  - 纯粹的敷衍回答
  - 完全没有涉及关注点内容

### 重要提醒
- 简洁明确的回答比冗长模糊的描述更有价值
- 明确的否定（"不需要"）也是完整的信息
- 结合对话历史进行评估

## 输出格式

### ⚠️ **输出前最终检查**
在输出结果前，请再次确认：
1. **关联性检查**：提取的信息是否与当前关注点有关联？
2. **合理性检查**：提取结果是否合理且符合常识？
3. **价值判断**：提取的信息是否对当前关注点有帮助？
4. **如有疑问**：只有当信息完全无关时，才返回空数组 `[]`

输出必须为以下JSON结构，extracted_points 数组只包含从用户输入中实际能提取到信息的关注点：

{
    "extracted_points": [
        {
            "name": "关注点名称",
            "value": "更新或补充后的具体信息",
            "completeness": 0.8
        }
    ]
}

## 注意事项
- 确保提取信息准确反映用户描述
- 完整度评分应基于智能分析，不是机械评分
- 注意关注点之间可能存在的信息关联
- 用简体中文回答

## 完整度评估示例

**1.0分示例**：
- "听你们专业的" → completeness: 1.0（委托决策）
- "不需要" → completeness: 1.0（明确否定）
- "预算大概10万左右" → completeness: 1.0（直接明确回答）

**0.8分示例**：
- "我喜欢简约风格" → completeness: 0.8（基本完整）
- "主要是企业用户使用" → completeness: 0.8（核心信息明确）

**0.6分示例**：
- "给我一些建议" → completeness: 0.6（寻求建议）
- "你推荐什么风格" → completeness: 0.6（寻求方案）

**0.4分示例**：
- "目标用户是25-35岁白领"（对于"给什么平台设计"） → completeness: 0.4（相关但不够明确）
- "用户群体比较年轻"（对于"设计风格"） → completeness: 0.4（有信息但模糊）

**0.2分示例**：
- "可能需要吧" → completeness: 0.2（模糊不确定）
- "我不太清楚" → completeness: 0.2（信息量少）

**完全无关示例**：
- 关注点："预算多少"，用户回答："今天天气很好" → 返回 `{"extracted_points": []}`
