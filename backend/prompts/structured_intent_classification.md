# 结构化意图分类模板 (版本 v1.0)

## ！！！严格输出格式要求！！！
⚠️ 只允许输出唯一一行纯 JSON 对象，禁止包含：
  • 任何额外文本、说明、解释、注释
  • 代码块标记（如 ```json）
  • 多余的空格、换行、标点
  • 额外字段、顺序错误

✅ 唯一可接受格式：
{
  "is_composite": true,
  "intents": [
    {
      "intent": "business_requirement",
      "confidence": 0.92,
      "text_span": "这个app需要用户注册功能",
      "reason": "用户描述具体的业务功能需求"
    },
    {
      "intent": "search_knowledge_base",
      "confidence": 0.95,
      "text_span": "你们平台支持哪些支付方式？",
      "reason": "询问平台标准服务信息"
    }
  ],
  "primary_intent": "business_requirement",
  "analysis": "用户先描述业务需求，然后询问平台能力，是典型的复合意图"
}

## 任务说明
你是一个专业的意图分析专家。请分析用户输入，判断其包含的所有意图。

## 当前会话状态
{current_state}

## 用户输入
{user_input}

## 关键词提示（仅供参考）
{keyword_hints}

## 对话历史
{full_conversation}

## 🔥 分析步骤（必须严格遵循）

### 步骤1：意图数量判断
- **单一意图**：用户表达只有一个明确目标
  * 示例："我想设计一个Logo" → 只有设计需求
  * 示例："你好" → 只有问候
  * 示例："我不知道这个项目需要多少钱，能给建议吗？" → 只有业务咨询（不是知识库查询）

- **复合意图**：用户在同一句话中表达多个不同目标
  * 示例："我要开发一个网站，你们平台支持哪些技术栈？" → 业务需求 + 知识库查询
  * 示例："帮我设计海报，顺便问一下价格是多少？" → 业务需求 + 知识库查询

### 步骤2：文本片段定位
为每个识别出的意图标注对应的文本片段：
- 精确定位表达该意图的文本部分
- 避免重复或遗漏关键信息
- 如果整句话都表达同一意图，可以使用完整句子

### 步骤3：置信度评估
根据以下标准评估每个意图的置信度：
- **0.9-1.0**：意图表达非常明确，关键词清晰
- **0.7-0.9**：意图表达较为明确，有一定推理成分
- **0.5-0.7**：意图表达模糊，需要较多推理
- **0.3-0.5**：意图表达很模糊，推理成分较大
- **0.0-0.3**：几乎无法确定意图

## 🚨 状态感知规则（优先级最高）

### 当current_state为"COLLECTING_INFO"时：
⚠️ **强制规则：除非明确例外，否则必须识别为process_answer**

**必须识别为process_answer的情况**：
- 用户提供具体信息："120cm×240cm"、"3天内"、"1万元"
- 用户询问选项差异："这些价格有什么不同"、"有什么区别"
- 用户表达偏好："我喜欢第一个"、"我选择A方案"
- 用户提供描述性回答："科技园会议中心"、"夏季新品发布会"
- 用户咨询预算建议："我不知道需要多少钱，能给建议吗？"

**例外情况（不识别为process_answer）**：
- 明确重新开始："新聊天"、"重新开始"、"新需求" → restart
- 询问问题含义："这个问题是什么意思"、"你在问什么" → request_clarification
- 完全无关闲聊："今天天气怎么样"、"你好吗" → general_chat

### 当current_state为"IDLE"时：
- 优先识别business_requirement：用户描述新的业务需求
- 正常应用所有意图识别规则

### 当current_state为"DOCUMENTING"时：
- 优先识别文档相关操作：confirm、modify等

## 意图类型定义

### 核心业务意图
- **business_requirement**: 用户有具体业务需求，需要咨询、报价或服务
  * 示例："我想开发一个电商网站"、"需要设计Logo"、"策划营销活动"
  
- **search_knowledge_base**: 用户想了解平台标准信息、价格、服务内容
  * 示例："你们的收费标准是什么？"、"价格表在哪里？"、"支持哪些技术？"

### 流程控制意图
- **process_answer**: 处理用户在信息收集阶段的回答
- **restart**: 用户要求重新开始或重置对话
- **confirm**: 用户确认或同意某个操作
- **modify**: 用户要求修改某些内容

### 交互意图
- **greeting**: 用户的问候或打招呼
- **request_clarification**: 用户请求澄清或解释
- **general_chat**: 用户进行一般性对话或闲聊
- **feedback**: 用户提供反馈或评价

## 🎯 关键判断原则

### 价格相关表达的区分
- **业务咨询**："我不知道需要多少钱，能给建议吗？" → business_requirement
- **知识库查询**："你们的收费标准是什么？" → search_knowledge_base
- **复合意图**："我要做网站，你们收费多少？" → business_requirement + search_knowledge_base

### 复合意图的识别标准
- **真正的复合意图**：表达两个独立的目标
  * "我要做X，你们支持Y吗？"
  * "帮我设计A，顺便问一下B"
  
- **单一意图的多种表述**：同一个目标的不同描述
  * "我想做网站，需要电商功能" → 只是business_requirement的详细描述
  * "我不知道价格，能给建议吗？" → 只是business_requirement的咨询表达

### 上下文依赖的处理
- 结合对话历史理解当前输入
- 考虑当前状态对意图的影响
- 区分新需求 vs 对现有需求的补充

## 输出字段说明

```json
{
  "is_composite": "布尔值，是否为复合意图",
  "intents": [
    {
      "intent": "意图名称，必须从定义的意图类型中选择",
      "confidence": "0.0-1.0之间的数值，表示识别置信度",
      "text_span": "对应的文本片段，精确定位意图表达",
      "reason": "判断理由，简要说明为什么这样分类"
    }
  ],
  "primary_intent": "主要意图，复合意图时选择最重要的一个",
  "analysis": "整体分析说明，解释意图识别的逻辑和上下文考虑"
}
```

## 示例分析

### 示例1：单一业务需求
**输入**："我想设计一个公司Logo"
**输出**：
```json
{
  "is_composite": false,
  "intents": [
    {
      "intent": "business_requirement",
      "confidence": 0.95,
      "text_span": "我想设计一个公司Logo",
      "reason": "用户明确表达设计需求"
    }
  ],
  "primary_intent": "business_requirement",
  "analysis": "用户有明确的设计业务需求，意图单一且清晰"
}
```

### 示例2：复合意图
**输入**："这个app需要用户注册功能。你们平台支持哪些支付方式？"
**输出**：
```json
{
  "is_composite": true,
  "intents": [
    {
      "intent": "business_requirement",
      "confidence": 0.92,
      "text_span": "这个app需要用户注册功能",
      "reason": "用户描述具体的业务功能需求"
    },
    {
      "intent": "search_knowledge_base",
      "confidence": 0.95,
      "text_span": "你们平台支持哪些支付方式？",
      "reason": "询问平台标准服务信息"
    }
  ],
  "primary_intent": "business_requirement",
  "analysis": "用户先描述业务需求，然后询问平台能力，是典型的复合意图"
}
```

### 示例3：价格咨询（单一意图）
**输入**："我对设计这张海报具体需要多少钱没有概念，您能给我建议吗？"
**输出**：
```json
{
  "is_composite": false,
  "intents": [
    {
      "intent": "business_requirement",
      "confidence": 0.88,
      "text_span": "我对设计这张海报具体需要多少钱没有概念，您能给我建议吗？",
      "reason": "用户在业务需求背景下咨询预算建议，属于业务咨询而非标准价格查询"
    }
  ],
  "primary_intent": "business_requirement",
  "analysis": "用户针对具体项目咨询预算建议，是业务需求的一部分，不是查询标准价格"
}
```

### 示例4：知识库查询
**输入**："你们的收费标准是什么？价格表在哪里可以看到？"
**输出**：
```json
{
  "is_composite": false,
  "intents": [
    {
      "intent": "search_knowledge_base",
      "confidence": 0.96,
      "text_span": "你们的收费标准是什么？价格表在哪里可以看到？",
      "reason": "用户询问平台标准价格信息和文档位置"
    }
  ],
  "primary_intent": "search_knowledge_base",
  "analysis": "用户查询标准价格信息，虽然有两个问句但都指向同一个目标：了解价格信息"
}
```

### 示例5：COLLECTING_INFO状态下的回答
**输入**："我不太确定，大概3-5万的预算吧"
**当前状态**："COLLECTING_INFO"
**输出**：
```json
{
  "is_composite": false,
  "intents": [
    {
      "intent": "process_answer",
      "confidence": 0.92,
      "text_span": "我不太确定，大概3-5万的预算吧",
      "reason": "在信息收集状态下，用户提供预算信息作为问题回答"
    }
  ],
  "primary_intent": "process_answer",
  "analysis": "用户在信息收集阶段提供预算范围，符合process_answer的状态感知规则"
}
```

### 示例6：边界情况 - 模糊的价格表达
**输入**："具体需要多少钱没有概念，您能给我建议吗？"
**输出**：
```json
{
  "is_composite": false,
  "intents": [
    {
      "intent": "business_requirement",
      "confidence": 0.75,
      "text_span": "具体需要多少钱没有概念，您能给我建议吗？",
      "reason": "用户寻求预算建议，虽然缺乏具体项目描述，但意图是业务咨询"
    }
  ],
  "primary_intent": "business_requirement",
  "analysis": "用户表达对项目成本的不确定性并寻求建议，属于业务咨询范畴"
}
```

## 🔧 特殊情况处理指南

### 1. 模糊表达的处理
- 当用户表达不够明确时，优先选择最可能的意图
- 降低置信度以反映不确定性
- 在analysis中说明推理过程

### 2. 上下文依赖的处理
- 结合对话历史和当前状态
- 如果上下文提供了关键信息，在reason中说明
- 状态感知规则优先于一般规则

### 3. 关键词提示的使用
- 关键词提示仅供参考，以语义分析为准
- 如果关键词提示与语义分析冲突，优先语义分析
- 在analysis中可以提及关键词提示的参考价值

### 4. 置信度校准
- 考虑表达的明确性、上下文的完整性
- 复合意图中各个意图的置信度可以不同
- 主要意图通常应该有较高的置信度
