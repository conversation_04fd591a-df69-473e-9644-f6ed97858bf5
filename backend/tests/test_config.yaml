# 业务逻辑回归测试配置文件
# 用于配置测试参数、阈值和验证标准

# 测试环境配置
test_environment:
  database_path: "backend/data/test_aidatabase.db"
  log_level: "DEBUG"
  timeout_seconds: 30
  max_concurrent_tests: 10

# 业务逻辑验证标准
validation_thresholds:
  # 状态管理
  state_transition_success_rate: 100.0    # 状态转换成功率必须100%
  state_consistency_rate: 100.0           # 状态一致性必须100%
  
  # 关注点管理
  focus_point_consistency_rate: 100.0     # 关注点一致性必须100%
  p0_collection_success_rate: 100.0       # P0关注点采集成功率必须100%
  p1_retry_compliance_rate: 100.0         # P1重试机制合规率必须100%
  
  # 文档确认
  document_confirmation_accuracy: 99.5    # 文档确认准确率≥99.5%
  keyword_priority_compliance: 100.0     # 关键词优先级合规率必须100%
  
  # 会话隔离
  session_isolation_integrity: 100.0      # 会话隔离完整性必须100%
  concurrent_safety_rate: 100.0          # 并发安全率必须100%
  
  # 错误处理
  error_recovery_success_rate: 95.0       # 错误恢复成功率≥95%
  data_consistency_rate: 100.0           # 数据一致性必须100%

# 测试用例配置
test_cases:
  # 状态转换测试
  state_transitions:
    - name: "idle_to_processing"
      initial_state: "IDLE"
      trigger: "user_input"
      expected_state: "PROCESSING_INTENT"
      
    - name: "processing_to_collecting"
      initial_state: "PROCESSING_INTENT"
      trigger: "intent_recognized"
      expected_state: "COLLECTING_INFO"
      
    - name: "collecting_to_documenting"
      initial_state: "COLLECTING_INFO"
      trigger: "focus_points_completed"
      expected_state: "DOCUMENTING"
      
    - name: "documenting_to_idle"
      initial_state: "DOCUMENTING"
      trigger: "document_confirmed"
      expected_state: "IDLE"

  # 关注点优先级测试
  focus_point_priorities:
    - priority: "P0"
      required: true
      max_attempts: null  # 无限重试
      skip_allowed: false
      
    - priority: "P1"
      required: true
      max_attempts: 3
      skip_allowed: true  # 3次后可跳过
      
    - priority: "P2"
      required: false
      max_attempts: 1
      skip_allowed: true  # 可直接跳过

  # 文档确认关键词测试
  document_keywords:
    confirmation_keywords:
      - text: "确认"
        priority: 0
        expected_intent: "confirm"
        
      - text: "没问题"
        priority: 0
        expected_intent: "confirm"
        
      - text: "ok"
        priority: 0
        expected_intent: "confirm"
        
    negation_keywords:
      - text: "修改"
        priority: 10
        expected_intent: "modify"
        
      - text: "不对"
        priority: 10
        expected_intent: "modify"
        
      - text: "调整"
        priority: 10
        expected_intent: "modify"
        
    mixed_keywords:
      - text: "好的，但是需要修改"
        expected_intent: "modify"  # 否定词优先级更高
        
      - text: "确认，不过要调整一下"
        expected_intent: "modify"  # 否定词优先级更高

  # 状态限制测试
  state_restrictions:
    - intent: "confirm"
      allowed_states: ["DOCUMENTING"]
      test_cases:
        - current_state: "IDLE"
          input: "确认"
          expected_result: "invalid"
          
        - current_state: "DOCUMENTING"
          input: "确认"
          expected_result: "valid"
          
    - intent: "modify"
      allowed_states: ["DOCUMENTING"]
      test_cases:
        - current_state: "COLLECTING_INFO"
          input: "修改"
          expected_result: "invalid"
          
        - current_state: "DOCUMENTING"
          input: "修改"
          expected_result: "valid"

# 性能基准测试
performance_benchmarks:
  keyword_matching_time: 0.001    # < 1ms
  semantic_matching_time: 0.010   # < 10ms
  llm_fallback_time: 0.500        # < 500ms
  state_transition_time: 0.005    # < 5ms
  focus_point_update_time: 0.010  # < 10ms
  document_generation_time: 2.000 # < 2000ms

# 边界条件测试
boundary_conditions:
  session_management:
    - condition: "empty_input"
      test_data: ""
      expected_behavior: "handle_gracefully"
      
    - condition: "very_long_input"
      test_data: "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
      expected_behavior: "truncate_or_reject"
      
    - condition: "special_characters"
      test_data: "!@#$%^&*()_+"
      expected_behavior: "handle_gracefully"
      
  focus_point_processing:
    - condition: "null_input"
      test_data: null
      expected_behavior: "handle_gracefully"
      
    - condition: "concurrent_updates"
      test_data: "multiple_simultaneous_updates"
      expected_behavior: "maintain_consistency"
      
  document_confirmation:
    - condition: "ambiguous_input"
      test_data: "可能需要确认或者修改"
      expected_behavior: "request_clarification"
      
    - condition: "no_keywords"
      test_data: "这是一个没有关键词的输入"
      expected_behavior: "fallback_to_llm"

# 回归测试场景
regression_scenarios:
  critical_paths:
    - name: "complete_requirement_collection_flow"
      description: "完整的需求采集流程"
      steps:
        - "用户输入需求"
        - "系统识别意图"
        - "进入信息收集"
        - "采集关注点"
        - "生成文档"
        - "用户确认"
        - "完成流程"
        
    - name: "document_modification_flow"
      description: "文档修改流程"
      steps:
        - "生成初始文档"
        - "用户提出修改"
        - "系统识别修改意图"
        - "执行文档修改"
        - "用户确认修改"
        
    - name: "session_recovery_flow"
      description: "会话恢复流程"
      steps:
        - "创建会话"
        - "进行部分操作"
        - "模拟中断"
        - "恢复会话"
        - "验证状态完整性"

# 测试报告配置
reporting:
  output_format: "json"
  include_performance_metrics: true
  include_detailed_logs: true
  generate_html_report: true
  report_directory: "tests/reports"
  
# 失败处理配置
failure_handling:
  stop_on_first_failure: false
  retry_failed_tests: true
  max_retries: 3
  generate_failure_report: true
