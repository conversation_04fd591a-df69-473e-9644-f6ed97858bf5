"""
FastAPI 应用入口测试
"""
import time
from fastapi.testclient import TestClient
import pytest
from unittest.mock import MagicMock, AsyncMock
from backend.api.main import app, sessions, cleanup_expired_sessions
from backend.agents.conversation_flow import ConversationFlowAgent
# UserInteractionAgent已删除，使用ConversationFlowAgent替代

@pytest.fixture
def test_client():
    """创建测试客户端"""
    return TestClient(app)

@pytest.fixture
def mock_conversation_agent():
    """模拟对话流程Agent"""
    agent = MagicMock(spec=ConversationFlowAgent)
    agent.process_message = AsyncMock(return_value="测试响应")
    return agent

@pytest.fixture
def mock_user_agent():
    """模拟用户交互Agent - 使用ConversationFlowAgent替代"""
    agent = MagicMock(spec=ConversationFlowAgent)
    agent.process_message = AsyncMock(return_value="正在处理您的请求。\n您好！很高兴为您服务。")
    agent.get_state.return_value = {
        "domain_result": {"domain": "测试领域", "confidence": 0.9},
        "category_result": {"category": "测试类别", "confidence": 0.8},
        "focus_points_status": [{"id": "fp1", "description": "测试关注点", "status": "pending"}],
        "last_activity": time.time()
    }
    return agent

def verify_chat_response_schema(data):
    """验证聊天响应的数据结构"""
    assert "response" in data
    assert "session_id" in data
    assert "state" in data
    assert isinstance(data["state"], dict)
    
    state = data["state"]
    if "domain_result" in state:
        assert isinstance(state["domain_result"], dict)
        assert "domain" in state["domain_result"]
        assert "confidence" in state["domain_result"]
    
    if "category_result" in state:
        assert isinstance(state["category_result"], dict)
        assert "category" in state["category_result"]
        assert "confidence" in state["category_result"]
    
    if "focus_points_status" in state:
        assert isinstance(state["focus_points_status"], list)

@pytest.fixture(autouse=True)
async def cleanup_test_sessions():
    """每个测试后清理测试会话"""
    yield
    # 清理以"test_"开头的会话
    test_sessions = [sid for sid in sessions.keys() if isinstance(sid, str) and 
                    (sid.startswith("test_") or sid.startswith("session_"))]
    for session_id in test_sessions:
        if session_id in sessions:
            del sessions[session_id]
            
@pytest.fixture
def mock_session_state():
    """提供模拟的会话状态"""
    return {
        "current_state": "COLLECTING",
        "conversation_state": {
            "domain": "测试领域",
            "category": "测试类别",
            "collection_data": {}
        },
        "domain_result": {
            "domain": "测试领域",
            "confidence": 0.9
        },
        "category_result": {
            "category": "测试类别",
            "confidence": 0.8
        },
        "focus_points_status": [],
        "last_activity": time.time()
    }

def test_health_check(test_client):
    """测试健康检查接口"""
    response = test_client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "version" in data
    assert "timestamp" in data

def test_list_sessions(test_client):
    """测试会话列表接口"""
    response = test_client.get("/sessions")
    assert response.status_code == 200
    data = response.json()
    assert "sessions" in data
    assert isinstance(data["sessions"], list)
    assert "count" in data
    assert isinstance(data["count"], int)

async def test_chat_new_session(test_client, mock_conversation_agent, monkeypatch):
    """测试新会话聊天请求"""
    # 模拟sessions字典
    sessions = {}
    monkeypatch.setattr("autogen_backend.main.sessions", sessions)
    
    response = test_client.post(
        "/chat",
        json={
            "message": "你好",
            "session_id": "test_session"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["response"] == "正在处理您的请求。\n您好！很高兴为您服务。"
    assert data["session_id"] == "test_session"
    assert "domain_result" in data
    assert "category_result" in data
    assert "focus_points_status" in data

async def test_chat_existing_session(test_client, mock_user_agent, monkeypatch):
    """测试已存在会话的聊天请求"""
    # 模拟已存在的会话
    mock_sessions = {"existing_session": mock_user_agent}
    monkeypatch.setattr("autogen_backend.main.sessions", mock_sessions)
    
    response = test_client.post(
        "/chat",
        json={
            "message": "继续",
            "session_id": "existing_session"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["response"] == "正在处理您的请求。\n您好！很高兴为您服务。"
    assert "domain_result" in data["state"]
    assert data["state"]["domain_result"]["domain"] == "测试领域"
    assert data["session_id"] == "existing_session"
    
    # 验证状态
    state = data["state"]
    assert state["domain_result"]["domain"] == "测试领域"
    assert state["category_result"]["category"] == "测试类别"

async def test_chat_error_handling(test_client, mock_user_agent, monkeypatch):
    """测试错误处理"""
    # 模拟抛出异常的Agent
    mock_user_agent.process_message.side_effect = Exception("测试错误")
    sessions = {"error_session": mock_user_agent}
    monkeypatch.setattr("autogen_backend.main.sessions", sessions)
    
    response = test_client.post(
        "/chat",
        json={
            "message": "触发错误",
            "session_id": "error_session"
        }
    )
    
    assert response.status_code == 500
    data = response.json()
    assert "detail" in data
    assert "测试错误" in data["detail"]

async def test_chat_invalid_request(test_client):
    """测试无效的请求格式"""
    response = test_client.post(
        "/chat",
        json={
            "invalid": "request"
        }
    )
    
    assert response.status_code == 422  # Validation Error

@pytest.mark.asyncio
async def test_chat_error_handling(test_client, mock_user_agent, monkeypatch):
    """测试聊天接口错误处理"""
    def mock_process_message(*args, **kwargs):
        raise ValueError("测试验证错误")
    
    # 模拟ConversationFlowAgent（替代UserInteractionAgent）
    monkeypatch.setitem(sessions, "test_session", mock_user_agent)
    mock_user_agent.process_message.side_effect = mock_process_message
    
    response = test_client.post("/chat", 
        json={"message": "测试消息", "session_id": "test_session"}
    )
    
    assert response.status_code == 400
    assert "测试验证错误" in response.json()["detail"]
    
@pytest.mark.asyncio
async def test_chat_session_management(test_client, mock_user_agent):
    """测试会话管理功能"""
    # 测试新会话创建
    response = test_client.post("/chat", 
        json={"message": "新会话消息"}
    )
    assert response.status_code == 200
    assert "session_" in response.json()["session_id"]
    
    # 测试使用已存在会话
    session_id = response.json()["session_id"]
    response = test_client.post("/chat",
        json={"message": "后续消息", "session_id": session_id}
    )
    assert response.status_code == 200
    assert response.json()["session_id"] == session_id

@pytest.mark.asyncio
async def test_session_cleanup(test_client, mock_user_agent, monkeypatch):
    """测试会话清理功能"""
    # 创建一个模拟的sessions字典
    mock_sessions = {}
    monkeypatch.setattr("autogen_backend.main.sessions", mock_sessions)
    
    # 创建一个过期会话
    old_session_id = f"session_{time.time() - 25*3600}"  # 25小时前
    old_agent = MagicMock(spec=ConversationFlowAgent)
    old_agent.get_state.return_value = {"last_activity": time.time() - 25*3600}
    mock_sessions[old_session_id] = old_agent
    
    # 运行清理
    await cleanup_expired_sessions()
    
    # 验证过期会话已被删除
    assert old_session_id not in mock_sessions
    
    # 创建新会话并设置为不过期
    response = test_client.post("/chat", json={"message": "测试消息"})
    assert response.status_code == 200
    new_session_id = response.json()["session_id"]
    
    # 为新会话创建mock agent
    new_agent = MagicMock(spec=ConversationFlowAgent)
    new_agent.get_state.return_value = {"last_activity": time.time()}
    mock_sessions[new_session_id] = new_agent
    
    # 再次运行清理
    await cleanup_expired_sessions()
    # 验证新会话没有被清理
    assert new_session_id in mock_sessions

@pytest.mark.asyncio
async def test_health_check_detailed(test_client):
    """测试健康检查端点的详细信息"""
    response = test_client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "健康"
    assert "timestamp" in data
    assert isinstance(data["timestamp"], str)
    assert data["message"] == "系统正常运行"

@pytest.mark.asyncio
async def test_delete_session(test_client, mock_user_agent):
    """测试删除会话"""
    # 先创建一个会话
    sessions["test_delete"] = mock_user_agent
    
    # 测试删除会话
    response = test_client.delete("/sessions/test_delete")
    assert response.status_code == 200
    assert response.json()["message"] == "会话已删除"
    assert "test_delete" not in sessions
    
    # 测试删除不存在的会话
    response = test_client.delete("/sessions/nonexistent")
    assert response.status_code == 404

@pytest.mark.asyncio
async def test_chat_state_persistence(test_client, mock_user_agent, monkeypatch):
    """测试会话状态持久化"""
    # 模拟已存在的会话
    sessions["state_test"] = mock_user_agent
    mock_user_agent.get_state.return_value = {
        "domain_result": {"domain": "测试领域", "confidence": 0.9},
        "category_result": {"category": "测试类别", "confidence": 0.8},
        "focus_points_status": [{"id": "fp1", "status": "pending"}],
        "last_activity": time.time()
    }
    
    # 发送消息
    response = test_client.post(
        "/chat",
        json={
            "message": "测试状态",
            "session_id": "state_test"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["state"]["domain_result"]["domain"] == "测试领域"
    assert data["state"]["category_result"]["category"] == "测试类别"
    assert len(data["state"]["focus_points_status"]) > 0
    
@pytest.mark.asyncio
async def test_chat_with_context(test_client, mock_user_agent, monkeypatch):
    """测试带上下文的聊天"""
    # 设置会话
    sessions["context_test"] = mock_user_agent
    context = {"key": "value"}
    
    # 测试带上下文的请求
    response = test_client.post(
        "/chat",
        json={
            "message": "带上下文的消息",
            "session_id": "context_test",
            "context": context
        }
    )
    
    assert response.status_code == 200
    mock_user_agent.process_message.assert_called_once_with("带上下文的消息", context)

@pytest.mark.asyncio
async def test_chat_full_conversation_flow(test_client, mock_user_agent, mock_conversation_agent, monkeypatch):
    """测试完整的对话流程"""
    # 模拟完整的对话流程状态
    mock_user_agent.get_state.return_value = {
        "current_state": "COLLECTING",
        "conversation_state": {
            "domain": "软件开发",
            "category": "Web开发",
            "collection_data": {
                "focus_points": [
                    {"id": "fp1", "description": "技术栈", "status": "pending"},
                    {"id": "fp2", "description": "功能需求", "status": "pending"}
                ]
            }
        },
        "domain_result": {"domain": "软件开发", "confidence": 0.9},
        "category_result": {"category": "Web开发", "confidence": 0.8},
        "focus_points_status": [
            {"id": "fp1", "description": "技术栈", "status": "pending"},
            {"id": "fp2", "description": "功能需求", "status": "pending"}
        ],
        "last_activity": time.time()
    }

    # 设置会话
    sessions["flow_test"] = mock_user_agent
    
    # 第一条消息 - 初始需求描述
    response = test_client.post(
        "/chat",
        json={
            "message": "我需要开发一个企业网站",
            "session_id": "flow_test"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["domain_result"]["domain"] == "软件开发"
    assert data["category_result"]["category"] == "Web开发"
    
    # 第二条消息 - 回答关注点问题
    mock_user_agent.get_state.return_value["focus_points_status"][0]["status"] = "confirmed"
    response = test_client.post(
        "/chat",
        json={
            "message": "使用React和Node.js技术栈",
            "session_id": "flow_test"
        }
    )
    assert response.status_code == 200
    assert response.json()["state"]["focus_points_status"][0]["status"] == "confirmed"

@pytest.mark.asyncio
async def test_chat_error_recovery(test_client, mock_user_agent, monkeypatch):
    """测试错误恢复机制"""
    sessions["error_test"] = mock_user_agent
    
    # 第一次调用抛出异常
    mock_user_agent.process_message.side_effect = [
        Exception("网络错误"),
        "恢复后的响应"  # 第二次调用返回正常响应
    ]
    
    # 首次请求触发错误
    response = test_client.post(
        "/chat",
        json={
            "message": "测试消息",
            "session_id": "error_test"
        }
    )
    assert response.status_code == 500
    
    # 重试请求应该成功
    mock_user_agent.process_message.side_effect = None
    mock_user_agent.process_message.return_value = "恢复后的响应"
    
    response = test_client.post(
        "/chat",
        json={
            "message": "重试消息",
            "session_id": "error_test"
        }
    )
    assert response.status_code == 200
    assert response.json()["response"] == "恢复后的响应"

@pytest.mark.asyncio
async def test_session_auto_creation(test_client, monkeypatch):
    """测试会话自动创建"""
    # 创建一个模拟的sessions字典
    mock_sessions = {}
    monkeypatch.setattr("autogen_backend.main.sessions", mock_sessions)
    
    response = test_client.post(
        "/chat",
        json={
            "message": "新对话"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "session_" in data["session_id"]
    session_id = data["session_id"]
    
    # 验证会话已创建
    assert session_id in mock_sessions
    # ConversationFlowAgent类型检查已通过实际response验证
    assert "state" in data
    assert isinstance(data["state"], dict)

@pytest.mark.asyncio
async def test_chat_empty_message(test_client):
    """测试空消息处理"""
    response = test_client.post(
        "/chat",
        json={
            "message": "",
            "session_id": "test_session"
        }
    )
    assert response.status_code == 422  # 验证错误

@pytest.mark.asyncio
async def test_chat_long_message(test_client, mock_user_agent, monkeypatch):
    """测试超长消息处理"""
    sessions["long_msg_test"] = mock_user_agent
    
    # 生成一个超长消息
    long_message = "测试" * 1000
    
    response = test_client.post(
        "/chat",
        json={
            "message": long_message,
            "session_id": "long_msg_test"
        }
    )
    assert response.status_code == 200  # 应该能正常处理长消息

@pytest.mark.asyncio
async def test_chat_special_characters(test_client, mock_user_agent, monkeypatch):
    """测试特殊字符处理"""
    sessions["special_char_test"] = mock_user_agent
    
    special_chars = """!@#$%^&*()_+-={}[]|\\:;"'<>?,./~`"""
    response = test_client.post(
        "/chat",
        json={
            "message": special_chars,
            "session_id": "special_char_test"
        }
    )
    assert response.status_code == 200  # 应该能正常处理特殊字符

@pytest.mark.asyncio
async def test_concurrent_requests(test_client, mock_user_agent, monkeypatch):
    """测试并发请求处理"""
    sessions["concurrent_test"] = mock_user_agent
    
    import asyncio
    
    async def make_request():
        response = test_client.post(
            "/chat",
            json={
                "message": "并发测试",
                "session_id": "concurrent_test"
            }
        )
        return response.status_code
    
    # 同时发送多个请求
    tasks = [make_request() for _ in range(5)]
    results = await asyncio.gather(*tasks)
    
    # 所有请求都应该成功
    assert all(status == 200 for status in results)

@pytest.mark.asyncio
async def test_session_cleanup_edge_cases(test_client):
    """测试会话清理的边界情况"""
    # 测试清理刚创建的会话
    just_created_id = f"session_{time.time()}"
    sessions[just_created_id] = MagicMock()
    sessions[just_created_id].get_state.return_value = {"last_activity": time.time()}
    
    await cleanup_expired_sessions(max_age_hours=24)
    assert just_created_id in sessions  # 新会话不应被清理
    
    # 测试清理边界时间的会话
    border_time_id = f"session_{time.time() - 23.9 * 3600}"  # 23.9小时前
    sessions[border_time_id] = MagicMock()
    sessions[border_time_id].get_state.return_value = {"last_activity": time.time() - 23.9 * 3600}
    
    await cleanup_expired_sessions(max_age_hours=24)
    assert border_time_id in sessions  # 边界时间的会话不应被清理
