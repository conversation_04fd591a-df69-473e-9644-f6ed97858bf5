{"sessions": [{"user_id": "test_user_001", "session_id": "test_session_d7bafee7", "current_state": "DOCUMENTING", "created_at": "2025-07-11T09:31:28.340474", "last_activity": "2025-07-11T09:47:28.340474", "domain_id": "domain_3", "category_id": null}, {"user_id": "test_user_002", "session_id": "test_session_45146557", "current_state": "DOCUMENTING", "created_at": "2025-07-11T13:31:28.340499", "last_activity": "2025-07-11T14:30:28.340499", "domain_id": "domain_2", "category_id": "category_4"}, {"user_id": "test_user_003", "session_id": "test_session_9ba561ba", "current_state": "IDLE", "created_at": "2025-07-10T23:31:28.340509", "last_activity": "2025-07-11T00:08:28.340509", "domain_id": "domain_5", "category_id": "category_9"}, {"user_id": "test_user_004", "session_id": "test_session_4fb484f7", "current_state": "COLLECTING_INFO", "created_at": "2025-07-11T11:31:28.340528", "last_activity": "2025-07-11T12:21:28.340528", "domain_id": "domain_4", "category_id": "category_6"}, {"user_id": "test_user_005", "session_id": "test_session_18c6d9c7", "current_state": "COLLECTING_INFO", "created_at": "2025-07-11T10:31:28.340536", "last_activity": "2025-07-11T10:49:28.340536", "domain_id": null, "category_id": "category_10"}, {"user_id": "test_user_006", "session_id": "test_session_8d32bb7c", "current_state": "PROCESSING_INTENT", "created_at": "2025-07-11T04:31:28.340543", "last_activity": "2025-07-11T05:00:28.340543", "domain_id": null, "category_id": "category_3"}, {"user_id": "test_user_007", "session_id": "test_session_93bb51ee", "current_state": "COLLECTING_INFO", "created_at": "2025-07-10T18:31:28.340550", "last_activity": "2025-07-10T19:04:28.340550", "domain_id": "domain_3", "category_id": "category_9"}, {"user_id": "test_user_008", "session_id": "test_session_aef7320c", "current_state": "DOCUMENTING", "created_at": "2025-07-10T17:31:28.340559", "last_activity": "2025-07-10T18:06:28.340559", "domain_id": "domain_2", "category_id": "category_7"}, {"user_id": "test_user_009", "session_id": "test_session_bea63ca6", "current_state": "IDLE", "created_at": "2025-07-11T08:31:28.340568", "last_activity": "2025-07-11T08:49:28.340568", "domain_id": "domain_2", "category_id": "category_9"}, {"user_id": "test_user_010", "session_id": "test_session_380aaf17", "current_state": "PROCESSING_INTENT", "created_at": "2025-07-11T04:31:28.340575", "last_activity": "2025-07-11T04:48:28.340575", "domain_id": null, "category_id": null}, {"user_id": "test_user_011", "session_id": "test_session_3254b532", "current_state": "IDLE", "created_at": "2025-07-11T01:31:28.340581", "last_activity": "2025-07-11T01:35:28.340581", "domain_id": "domain_4", "category_id": "category_8"}, {"user_id": "test_user_012", "session_id": "test_session_a3b19548", "current_state": "COLLECTING_INFO", "created_at": "2025-07-11T02:31:28.340587", "last_activity": "2025-07-11T03:24:28.340587", "domain_id": "domain_4", "category_id": null}, {"user_id": "test_user_013", "session_id": "test_session_ce6a8de9", "current_state": "IDLE", "created_at": "2025-07-11T02:31:28.340593", "last_activity": "2025-07-11T02:45:28.340593", "domain_id": null, "category_id": "category_7"}, {"user_id": "test_user_014", "session_id": "test_session_e971c47b", "current_state": "PROCESSING_INTENT", "created_at": "2025-07-11T02:31:28.340599", "last_activity": "2025-07-11T02:47:28.340599", "domain_id": "domain_1", "category_id": null}, {"user_id": "test_user_015", "session_id": "test_session_1e385276", "current_state": "IDLE", "created_at": "2025-07-11T06:31:28.340605", "last_activity": "2025-07-11T07:00:28.340605", "domain_id": null, "category_id": "category_9"}, {"user_id": "test_user_016", "session_id": "test_session_7c0bc4ae", "current_state": "PROCESSING_INTENT", "created_at": "2025-07-10T15:31:28.340611", "last_activity": "2025-07-10T15:56:28.340611", "domain_id": null, "category_id": "category_5"}, {"user_id": "test_user_017", "session_id": "test_session_7523bb80", "current_state": "COLLECTING_INFO", "created_at": "2025-07-11T02:31:28.340617", "last_activity": "2025-07-11T03:27:28.340617", "domain_id": null, "category_id": null}, {"user_id": "test_user_018", "session_id": "test_session_00877df9", "current_state": "COLLECTING_INFO", "created_at": "2025-07-11T02:31:28.340623", "last_activity": "2025-07-11T03:01:28.340623", "domain_id": "domain_2", "category_id": "category_9"}, {"user_id": "test_user_019", "session_id": "test_session_bd9a6c6b", "current_state": "DOCUMENTING", "created_at": "2025-07-10T21:31:28.340629", "last_activity": "2025-07-10T22:17:28.340629", "domain_id": "domain_4", "category_id": null}, {"user_id": "test_user_020", "session_id": "test_session_c27ed150", "current_state": "DOCUMENTING", "created_at": "2025-07-11T10:31:28.340635", "last_activity": "2025-07-11T11:28:28.340635", "domain_id": null, "category_id": "category_2"}], "focus_points": [{"id": "fp_test_session_d7bafee7_1", "name": "核心功能需求_1", "priority": "P0", "status": "pending", "attempts": 0, "max_attempts": null, "created_at": "2025-07-11T09:31:28.340474", "updated_at": "2025-07-11T09:47:28.340474"}, {"id": "fp_test_session_d7bafee7_2", "name": "可选特性_2", "priority": "P2", "status": "skipped", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-11T09:31:28.340474", "updated_at": "2025-07-11T09:47:28.340474"}, {"id": "fp_test_session_d7bafee7_3", "name": "技术架构要求_3", "priority": "P0", "status": "processing", "attempts": 1, "max_attempts": null, "created_at": "2025-07-11T09:31:28.340474", "updated_at": "2025-07-11T09:47:28.340474"}, {"id": "fp_test_session_d7bafee7_4", "name": "性能指标_4", "priority": "P1", "status": "pending", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T09:31:28.340474", "updated_at": "2025-07-11T09:47:28.340474"}, {"id": "fp_test_session_45146557_1", "name": "用户体验要求_1", "priority": "P1", "status": "skipped", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-11T13:31:28.340499", "updated_at": "2025-07-11T14:30:28.340499"}, {"id": "fp_test_session_45146557_2", "name": "技术架构要求_2", "priority": "P0", "status": "processing", "attempts": 1, "max_attempts": null, "created_at": "2025-07-11T13:31:28.340499", "updated_at": "2025-07-11T14:30:28.340499"}, {"id": "fp_test_session_45146557_3", "name": "扩展功能_3", "priority": "P2", "status": "pending", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T13:31:28.340499", "updated_at": "2025-07-11T14:30:28.340499"}, {"id": "fp_test_session_45146557_4", "name": "技术架构要求_4", "priority": "P0", "status": "skipped", "attempts": 0, "max_attempts": null, "created_at": "2025-07-11T13:31:28.340499", "updated_at": "2025-07-11T14:30:28.340499"}, {"id": "fp_test_session_9ba561ba_1", "name": "技术架构要求_1", "priority": "P0", "status": "skipped", "attempts": 0, "max_attempts": null, "created_at": "2025-07-10T23:31:28.340509", "updated_at": "2025-07-11T00:08:28.340509"}, {"id": "fp_test_session_9ba561ba_2", "name": "扩展功能_2", "priority": "P2", "status": "processing", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-10T23:31:28.340509", "updated_at": "2025-07-11T00:08:28.340509"}, {"id": "fp_test_session_9ba561ba_3", "name": "用户体验要求_3", "priority": "P1", "status": "completed", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-10T23:31:28.340509", "updated_at": "2025-07-11T00:08:28.340509"}, {"id": "fp_test_session_9ba561ba_4", "name": "核心功能需求_4", "priority": "P0", "status": "processing", "attempts": 2, "max_attempts": null, "created_at": "2025-07-10T23:31:28.340509", "updated_at": "2025-07-11T00:08:28.340509"}, {"id": "fp_test_session_4fb484f7_1", "name": "性能指标_1", "priority": "P1", "status": "pending", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-11T11:31:28.340528", "updated_at": "2025-07-11T12:21:28.340528"}, {"id": "fp_test_session_4fb484f7_2", "name": "可选特性_2", "priority": "P2", "status": "processing", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-11T11:31:28.340528", "updated_at": "2025-07-11T12:21:28.340528"}, {"id": "fp_test_session_4fb484f7_3", "name": "核心功能需求_3", "priority": "P0", "status": "completed", "attempts": 2, "max_attempts": null, "created_at": "2025-07-11T11:31:28.340528", "updated_at": "2025-07-11T12:21:28.340528"}, {"id": "fp_test_session_4fb484f7_4", "name": "可选特性_4", "priority": "P2", "status": "pending", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-11T11:31:28.340528", "updated_at": "2025-07-11T12:21:28.340528"}, {"id": "fp_test_session_4fb484f7_5", "name": "技术架构要求_5", "priority": "P0", "status": "pending", "attempts": 0, "max_attempts": null, "created_at": "2025-07-11T11:31:28.340528", "updated_at": "2025-07-11T12:21:28.340528"}, {"id": "fp_test_session_18c6d9c7_1", "name": "技术架构要求_1", "priority": "P0", "status": "processing", "attempts": 3, "max_attempts": null, "created_at": "2025-07-11T10:31:28.340536", "updated_at": "2025-07-11T10:49:28.340536"}, {"id": "fp_test_session_18c6d9c7_2", "name": "可选特性_2", "priority": "P2", "status": "skipped", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T10:31:28.340536", "updated_at": "2025-07-11T10:49:28.340536"}, {"id": "fp_test_session_18c6d9c7_3", "name": "扩展功能_3", "priority": "P2", "status": "processing", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-11T10:31:28.340536", "updated_at": "2025-07-11T10:49:28.340536"}, {"id": "fp_test_session_18c6d9c7_4", "name": "扩展功能_4", "priority": "P2", "status": "skipped", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T10:31:28.340536", "updated_at": "2025-07-11T10:49:28.340536"}, {"id": "fp_test_session_18c6d9c7_5", "name": "技术架构要求_5", "priority": "P0", "status": "pending", "attempts": 2, "max_attempts": null, "created_at": "2025-07-11T10:31:28.340536", "updated_at": "2025-07-11T10:49:28.340536"}, {"id": "fp_test_session_18c6d9c7_6", "name": "扩展功能_6", "priority": "P2", "status": "processing", "attempts": 2, "max_attempts": 3, "created_at": "2025-07-11T10:31:28.340536", "updated_at": "2025-07-11T10:49:28.340536"}, {"id": "fp_test_session_8d32bb7c_1", "name": "扩展功能_1", "priority": "P2", "status": "skipped", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-11T04:31:28.340543", "updated_at": "2025-07-11T05:00:28.340543"}, {"id": "fp_test_session_8d32bb7c_2", "name": "扩展功能_2", "priority": "P2", "status": "pending", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-11T04:31:28.340543", "updated_at": "2025-07-11T05:00:28.340543"}, {"id": "fp_test_session_8d32bb7c_3", "name": "用户体验要求_3", "priority": "P1", "status": "processing", "attempts": 2, "max_attempts": 3, "created_at": "2025-07-11T04:31:28.340543", "updated_at": "2025-07-11T05:00:28.340543"}, {"id": "fp_test_session_8d32bb7c_4", "name": "扩展功能_4", "priority": "P2", "status": "skipped", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T04:31:28.340543", "updated_at": "2025-07-11T05:00:28.340543"}, {"id": "fp_test_session_8d32bb7c_5", "name": "技术架构要求_5", "priority": "P0", "status": "pending", "attempts": 3, "max_attempts": null, "created_at": "2025-07-11T04:31:28.340543", "updated_at": "2025-07-11T05:00:28.340543"}, {"id": "fp_test_session_93bb51ee_1", "name": "核心功能需求_1", "priority": "P0", "status": "skipped", "attempts": 0, "max_attempts": null, "created_at": "2025-07-10T18:31:28.340550", "updated_at": "2025-07-10T19:04:28.340550"}, {"id": "fp_test_session_93bb51ee_2", "name": "技术架构要求_2", "priority": "P0", "status": "processing", "attempts": 1, "max_attempts": null, "created_at": "2025-07-10T18:31:28.340550", "updated_at": "2025-07-10T19:04:28.340550"}, {"id": "fp_test_session_93bb51ee_3", "name": "核心功能需求_3", "priority": "P0", "status": "completed", "attempts": 1, "max_attempts": null, "created_at": "2025-07-10T18:31:28.340550", "updated_at": "2025-07-10T19:04:28.340550"}, {"id": "fp_test_session_93bb51ee_4", "name": "可选特性_4", "priority": "P2", "status": "skipped", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-10T18:31:28.340550", "updated_at": "2025-07-10T19:04:28.340550"}, {"id": "fp_test_session_93bb51ee_5", "name": "技术架构要求_5", "priority": "P0", "status": "processing", "attempts": 3, "max_attempts": null, "created_at": "2025-07-10T18:31:28.340550", "updated_at": "2025-07-10T19:04:28.340550"}, {"id": "fp_test_session_93bb51ee_6", "name": "用户体验要求_6", "priority": "P1", "status": "completed", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-10T18:31:28.340550", "updated_at": "2025-07-10T19:04:28.340550"}, {"id": "fp_test_session_aef7320c_1", "name": "技术架构要求_1", "priority": "P0", "status": "skipped", "attempts": 0, "max_attempts": null, "created_at": "2025-07-10T17:31:28.340559", "updated_at": "2025-07-10T18:06:28.340559"}, {"id": "fp_test_session_aef7320c_2", "name": "核心功能需求_2", "priority": "P0", "status": "skipped", "attempts": 2, "max_attempts": null, "created_at": "2025-07-10T17:31:28.340559", "updated_at": "2025-07-10T18:06:28.340559"}, {"id": "fp_test_session_aef7320c_3", "name": "用户体验要求_3", "priority": "P1", "status": "completed", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-10T17:31:28.340559", "updated_at": "2025-07-10T18:06:28.340559"}, {"id": "fp_test_session_aef7320c_4", "name": "扩展功能_4", "priority": "P2", "status": "completed", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-10T17:31:28.340559", "updated_at": "2025-07-10T18:06:28.340559"}, {"id": "fp_test_session_aef7320c_5", "name": "性能指标_5", "priority": "P1", "status": "skipped", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-10T17:31:28.340559", "updated_at": "2025-07-10T18:06:28.340559"}, {"id": "fp_test_session_bea63ca6_1", "name": "用户体验要求_1", "priority": "P1", "status": "skipped", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-11T08:31:28.340568", "updated_at": "2025-07-11T08:49:28.340568"}, {"id": "fp_test_session_bea63ca6_2", "name": "可选特性_2", "priority": "P2", "status": "pending", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-11T08:31:28.340568", "updated_at": "2025-07-11T08:49:28.340568"}, {"id": "fp_test_session_bea63ca6_3", "name": "技术架构要求_3", "priority": "P0", "status": "completed", "attempts": 3, "max_attempts": null, "created_at": "2025-07-11T08:31:28.340568", "updated_at": "2025-07-11T08:49:28.340568"}, {"id": "fp_test_session_bea63ca6_4", "name": "核心功能需求_4", "priority": "P0", "status": "completed", "attempts": 3, "max_attempts": null, "created_at": "2025-07-11T08:31:28.340568", "updated_at": "2025-07-11T08:49:28.340568"}, {"id": "fp_test_session_bea63ca6_5", "name": "扩展功能_5", "priority": "P2", "status": "skipped", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-11T08:31:28.340568", "updated_at": "2025-07-11T08:49:28.340568"}, {"id": "fp_test_session_bea63ca6_6", "name": "扩展功能_6", "priority": "P2", "status": "skipped", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-11T08:31:28.340568", "updated_at": "2025-07-11T08:49:28.340568"}, {"id": "fp_test_session_380aaf17_1", "name": "性能指标_1", "priority": "P1", "status": "pending", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T04:31:28.340575", "updated_at": "2025-07-11T04:48:28.340575"}, {"id": "fp_test_session_380aaf17_2", "name": "可选特性_2", "priority": "P2", "status": "skipped", "attempts": 2, "max_attempts": 3, "created_at": "2025-07-11T04:31:28.340575", "updated_at": "2025-07-11T04:48:28.340575"}, {"id": "fp_test_session_380aaf17_3", "name": "技术架构要求_3", "priority": "P0", "status": "pending", "attempts": 1, "max_attempts": null, "created_at": "2025-07-11T04:31:28.340575", "updated_at": "2025-07-11T04:48:28.340575"}, {"id": "fp_test_session_380aaf17_4", "name": "技术架构要求_4", "priority": "P0", "status": "skipped", "attempts": 2, "max_attempts": null, "created_at": "2025-07-11T04:31:28.340575", "updated_at": "2025-07-11T04:48:28.340575"}, {"id": "fp_test_session_380aaf17_5", "name": "扩展功能_5", "priority": "P2", "status": "processing", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-11T04:31:28.340575", "updated_at": "2025-07-11T04:48:28.340575"}, {"id": "fp_test_session_3254b532_1", "name": "技术架构要求_1", "priority": "P0", "status": "skipped", "attempts": 2, "max_attempts": null, "created_at": "2025-07-11T01:31:28.340581", "updated_at": "2025-07-11T01:35:28.340581"}, {"id": "fp_test_session_3254b532_2", "name": "可选特性_2", "priority": "P2", "status": "pending", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-11T01:31:28.340581", "updated_at": "2025-07-11T01:35:28.340581"}, {"id": "fp_test_session_3254b532_3", "name": "可选特性_3", "priority": "P2", "status": "completed", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T01:31:28.340581", "updated_at": "2025-07-11T01:35:28.340581"}, {"id": "fp_test_session_3254b532_4", "name": "性能指标_4", "priority": "P1", "status": "processing", "attempts": 2, "max_attempts": 3, "created_at": "2025-07-11T01:31:28.340581", "updated_at": "2025-07-11T01:35:28.340581"}, {"id": "fp_test_session_a3b19548_1", "name": "扩展功能_1", "priority": "P2", "status": "pending", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340587", "updated_at": "2025-07-11T03:24:28.340587"}, {"id": "fp_test_session_a3b19548_2", "name": "性能指标_2", "priority": "P1", "status": "processing", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340587", "updated_at": "2025-07-11T03:24:28.340587"}, {"id": "fp_test_session_a3b19548_3", "name": "扩展功能_3", "priority": "P2", "status": "completed", "attempts": 2, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340587", "updated_at": "2025-07-11T03:24:28.340587"}, {"id": "fp_test_session_a3b19548_4", "name": "扩展功能_4", "priority": "P2", "status": "pending", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340587", "updated_at": "2025-07-11T03:24:28.340587"}, {"id": "fp_test_session_a3b19548_5", "name": "用户体验要求_5", "priority": "P1", "status": "pending", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340587", "updated_at": "2025-07-11T03:24:28.340587"}, {"id": "fp_test_session_a3b19548_6", "name": "技术架构要求_6", "priority": "P0", "status": "skipped", "attempts": 1, "max_attempts": null, "created_at": "2025-07-11T02:31:28.340587", "updated_at": "2025-07-11T03:24:28.340587"}, {"id": "fp_test_session_ce6a8de9_1", "name": "性能指标_1", "priority": "P1", "status": "processing", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340593", "updated_at": "2025-07-11T02:45:28.340593"}, {"id": "fp_test_session_ce6a8de9_2", "name": "核心功能需求_2", "priority": "P0", "status": "skipped", "attempts": 2, "max_attempts": null, "created_at": "2025-07-11T02:31:28.340593", "updated_at": "2025-07-11T02:45:28.340593"}, {"id": "fp_test_session_ce6a8de9_3", "name": "可选特性_3", "priority": "P2", "status": "skipped", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340593", "updated_at": "2025-07-11T02:45:28.340593"}, {"id": "fp_test_session_ce6a8de9_4", "name": "用户体验要求_4", "priority": "P1", "status": "completed", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340593", "updated_at": "2025-07-11T02:45:28.340593"}, {"id": "fp_test_session_ce6a8de9_5", "name": "性能指标_5", "priority": "P1", "status": "completed", "attempts": 2, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340593", "updated_at": "2025-07-11T02:45:28.340593"}, {"id": "fp_test_session_ce6a8de9_6", "name": "性能指标_6", "priority": "P1", "status": "processing", "attempts": 2, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340593", "updated_at": "2025-07-11T02:45:28.340593"}, {"id": "fp_test_session_e971c47b_1", "name": "用户体验要求_1", "priority": "P1", "status": "pending", "attempts": 2, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340599", "updated_at": "2025-07-11T02:47:28.340599"}, {"id": "fp_test_session_e971c47b_2", "name": "用户体验要求_2", "priority": "P1", "status": "processing", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340599", "updated_at": "2025-07-11T02:47:28.340599"}, {"id": "fp_test_session_e971c47b_3", "name": "用户体验要求_3", "priority": "P1", "status": "skipped", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340599", "updated_at": "2025-07-11T02:47:28.340599"}, {"id": "fp_test_session_e971c47b_4", "name": "核心功能需求_4", "priority": "P0", "status": "processing", "attempts": 3, "max_attempts": null, "created_at": "2025-07-11T02:31:28.340599", "updated_at": "2025-07-11T02:47:28.340599"}, {"id": "fp_test_session_e971c47b_5", "name": "可选特性_5", "priority": "P2", "status": "pending", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340599", "updated_at": "2025-07-11T02:47:28.340599"}, {"id": "fp_test_session_e971c47b_6", "name": "技术架构要求_6", "priority": "P0", "status": "completed", "attempts": 2, "max_attempts": null, "created_at": "2025-07-11T02:31:28.340599", "updated_at": "2025-07-11T02:47:28.340599"}, {"id": "fp_test_session_1e385276_1", "name": "用户体验要求_1", "priority": "P1", "status": "skipped", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-11T06:31:28.340605", "updated_at": "2025-07-11T07:00:28.340605"}, {"id": "fp_test_session_1e385276_2", "name": "技术架构要求_2", "priority": "P0", "status": "skipped", "attempts": 0, "max_attempts": null, "created_at": "2025-07-11T06:31:28.340605", "updated_at": "2025-07-11T07:00:28.340605"}, {"id": "fp_test_session_1e385276_3", "name": "扩展功能_3", "priority": "P2", "status": "processing", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T06:31:28.340605", "updated_at": "2025-07-11T07:00:28.340605"}, {"id": "fp_test_session_7c0bc4ae_1", "name": "技术架构要求_1", "priority": "P0", "status": "completed", "attempts": 2, "max_attempts": null, "created_at": "2025-07-10T15:31:28.340611", "updated_at": "2025-07-10T15:56:28.340611"}, {"id": "fp_test_session_7c0bc4ae_2", "name": "核心功能需求_2", "priority": "P0", "status": "completed", "attempts": 1, "max_attempts": null, "created_at": "2025-07-10T15:31:28.340611", "updated_at": "2025-07-10T15:56:28.340611"}, {"id": "fp_test_session_7c0bc4ae_3", "name": "用户体验要求_3", "priority": "P1", "status": "skipped", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-10T15:31:28.340611", "updated_at": "2025-07-10T15:56:28.340611"}, {"id": "fp_test_session_7c0bc4ae_4", "name": "核心功能需求_4", "priority": "P0", "status": "completed", "attempts": 3, "max_attempts": null, "created_at": "2025-07-10T15:31:28.340611", "updated_at": "2025-07-10T15:56:28.340611"}, {"id": "fp_test_session_7c0bc4ae_5", "name": "性能指标_5", "priority": "P1", "status": "processing", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-10T15:31:28.340611", "updated_at": "2025-07-10T15:56:28.340611"}, {"id": "fp_test_session_7c0bc4ae_6", "name": "技术架构要求_6", "priority": "P0", "status": "completed", "attempts": 0, "max_attempts": null, "created_at": "2025-07-10T15:31:28.340611", "updated_at": "2025-07-10T15:56:28.340611"}, {"id": "fp_test_session_7523bb80_1", "name": "用户体验要求_1", "priority": "P1", "status": "pending", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340617", "updated_at": "2025-07-11T03:27:28.340617"}, {"id": "fp_test_session_7523bb80_2", "name": "核心功能需求_2", "priority": "P0", "status": "completed", "attempts": 2, "max_attempts": null, "created_at": "2025-07-11T02:31:28.340617", "updated_at": "2025-07-11T03:27:28.340617"}, {"id": "fp_test_session_7523bb80_3", "name": "性能指标_3", "priority": "P1", "status": "processing", "attempts": 2, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340617", "updated_at": "2025-07-11T03:27:28.340617"}, {"id": "fp_test_session_7523bb80_4", "name": "可选特性_4", "priority": "P2", "status": "pending", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340617", "updated_at": "2025-07-11T03:27:28.340617"}, {"id": "fp_test_session_7523bb80_5", "name": "可选特性_5", "priority": "P2", "status": "pending", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340617", "updated_at": "2025-07-11T03:27:28.340617"}, {"id": "fp_test_session_7523bb80_6", "name": "用户体验要求_6", "priority": "P1", "status": "processing", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340617", "updated_at": "2025-07-11T03:27:28.340617"}, {"id": "fp_test_session_00877df9_1", "name": "可选特性_1", "priority": "P2", "status": "skipped", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340623", "updated_at": "2025-07-11T03:01:28.340623"}, {"id": "fp_test_session_00877df9_2", "name": "扩展功能_2", "priority": "P2", "status": "pending", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340623", "updated_at": "2025-07-11T03:01:28.340623"}, {"id": "fp_test_session_00877df9_3", "name": "扩展功能_3", "priority": "P2", "status": "pending", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340623", "updated_at": "2025-07-11T03:01:28.340623"}, {"id": "fp_test_session_00877df9_4", "name": "扩展功能_4", "priority": "P2", "status": "skipped", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-11T02:31:28.340623", "updated_at": "2025-07-11T03:01:28.340623"}, {"id": "fp_test_session_bd9a6c6b_1", "name": "用户体验要求_1", "priority": "P1", "status": "processing", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-10T21:31:28.340629", "updated_at": "2025-07-10T22:17:28.340629"}, {"id": "fp_test_session_bd9a6c6b_2", "name": "技术架构要求_2", "priority": "P0", "status": "processing", "attempts": 0, "max_attempts": null, "created_at": "2025-07-10T21:31:28.340629", "updated_at": "2025-07-10T22:17:28.340629"}, {"id": "fp_test_session_bd9a6c6b_3", "name": "性能指标_3", "priority": "P1", "status": "completed", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-10T21:31:28.340629", "updated_at": "2025-07-10T22:17:28.340629"}, {"id": "fp_test_session_bd9a6c6b_4", "name": "用户体验要求_4", "priority": "P1", "status": "skipped", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-10T21:31:28.340629", "updated_at": "2025-07-10T22:17:28.340629"}, {"id": "fp_test_session_bd9a6c6b_5", "name": "技术架构要求_5", "priority": "P0", "status": "completed", "attempts": 3, "max_attempts": null, "created_at": "2025-07-10T21:31:28.340629", "updated_at": "2025-07-10T22:17:28.340629"}, {"id": "fp_test_session_c27ed150_1", "name": "用户体验要求_1", "priority": "P1", "status": "completed", "attempts": 0, "max_attempts": 3, "created_at": "2025-07-11T10:31:28.340635", "updated_at": "2025-07-11T11:28:28.340635"}, {"id": "fp_test_session_c27ed150_2", "name": "用户体验要求_2", "priority": "P1", "status": "skipped", "attempts": 3, "max_attempts": 3, "created_at": "2025-07-11T10:31:28.340635", "updated_at": "2025-07-11T11:28:28.340635"}, {"id": "fp_test_session_c27ed150_3", "name": "技术架构要求_3", "priority": "P0", "status": "completed", "attempts": 2, "max_attempts": null, "created_at": "2025-07-11T10:31:28.340635", "updated_at": "2025-07-11T11:28:28.340635"}, {"id": "fp_test_session_c27ed150_4", "name": "性能指标_4", "priority": "P1", "status": "pending", "attempts": 1, "max_attempts": 3, "created_at": "2025-07-11T10:31:28.340635", "updated_at": "2025-07-11T11:28:28.340635"}], "documents": [{"id": "doc_8a1e307b", "session_id": "test_session_d7bafee7", "user_id": "test_user_001", "content": "# 项目需求文档\n\n## 核心功能\n- 功能1\n- 功能2\n\n## 技术要求\n- 要求1\n- 要求2", "status": "rejected", "version": 1, "created_at": "2025-07-11T09:47:28.340474", "updated_at": "2025-07-11T09:47:28.340474"}, {"id": "doc_f80eb870", "session_id": "test_session_45146557", "user_id": "test_user_002", "content": "# 用户需求分析\n\n## 用户画像\n目标用户群体\n\n## 功能需求\n核心功能列表", "status": "rejected", "version": 1, "created_at": "2025-07-11T14:30:28.340499", "updated_at": "2025-07-11T14:30:28.340499"}, {"id": "doc_ac22299f", "session_id": "test_session_9ba561ba", "user_id": "test_user_003", "content": "# 项目需求文档\n\n## 核心功能\n- 功能1\n- 功能2\n\n## 技术要求\n- 要求1\n- 要求2", "status": "draft", "version": 3, "created_at": "2025-07-11T00:08:28.340509", "updated_at": "2025-07-11T00:08:28.340509"}, {"id": "doc_0afb6b40", "session_id": "test_session_aef7320c", "user_id": "test_user_008", "content": "# 项目需求文档\n\n## 核心功能\n- 功能1\n- 功能2\n\n## 技术要求\n- 要求1\n- 要求2", "status": "rejected", "version": 2, "created_at": "2025-07-10T18:06:28.340559", "updated_at": "2025-07-10T18:06:28.340559"}, {"id": "doc_33865f4e", "session_id": "test_session_bea63ca6", "user_id": "test_user_009", "content": "# 项目需求文档\n\n## 核心功能\n- 功能1\n- 功能2\n\n## 技术要求\n- 要求1\n- 要求2", "status": "draft", "version": 3, "created_at": "2025-07-11T08:49:28.340568", "updated_at": "2025-07-11T08:49:28.340568"}], "user_inputs": [{"id": "input_001", "session_id": "test_session_93bb51ee", "user_id": "test_user_007", "message": "hi", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340830"}, {"id": "input_002", "session_id": "test_session_c27ed150", "user_id": "test_user_020", "message": "确认", "expected_intent": "confirm", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340837"}, {"id": "input_003", "session_id": "test_session_ce6a8de9", "user_id": "test_user_013", "message": "hello", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340841"}, {"id": "input_004", "session_id": "test_session_8d32bb7c", "user_id": "test_user_006", "message": "重置", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340844"}, {"id": "input_005", "session_id": "test_session_18c6d9c7", "user_id": "test_user_005", "message": "新聊天", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340847"}, {"id": "input_006", "session_id": "test_session_ce6a8de9", "user_id": "test_user_013", "message": "下午好", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340868"}, {"id": "input_007", "session_id": "test_session_93bb51ee", "user_id": "test_user_007", "message": "重置", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340872"}, {"id": "input_008", "session_id": "test_session_00877df9", "user_id": "test_user_018", "message": "新聊天", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340878"}, {"id": "input_009", "session_id": "test_session_aef7320c", "user_id": "test_user_008", "message": "新聊天", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.340882"}, {"id": "input_010", "session_id": "test_session_e971c47b", "user_id": "test_user_014", "message": "hi", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.340886"}, {"id": "input_011", "session_id": "test_session_ce6a8de9", "user_id": "test_user_013", "message": "你好", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340889"}, {"id": "input_012", "session_id": "test_session_8d32bb7c", "user_id": "test_user_006", "message": "新聊天", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340892"}, {"id": "input_013", "session_id": "test_session_aef7320c", "user_id": "test_user_008", "message": "新聊天", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340895"}, {"id": "input_014", "session_id": "test_session_45146557", "user_id": "test_user_002", "message": "需要改", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340899"}, {"id": "input_015", "session_id": "test_session_bd9a6c6b", "user_id": "test_user_019", "message": "不对", "expected_intent": "modify", "expected_state_transition": "DOCUMENTING", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.340901"}, {"id": "input_016", "session_id": "test_session_e971c47b", "user_id": "test_user_014", "message": "下午好", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340904"}, {"id": "input_017", "session_id": "test_session_bea63ca6", "user_id": "test_user_009", "message": "hello", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.340908"}, {"id": "input_018", "session_id": "test_session_c27ed150", "user_id": "test_user_020", "message": "ok", "expected_intent": "confirm", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.340911"}, {"id": "input_019", "session_id": "test_session_3254b532", "user_id": "test_user_011", "message": "hi", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340914"}, {"id": "input_020", "session_id": "test_session_00877df9", "user_id": "test_user_018", "message": "早上好", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340917"}, {"id": "input_021", "session_id": "test_session_7c0bc4ae", "user_id": "test_user_016", "message": "重新开始", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.340920"}, {"id": "input_022", "session_id": "test_session_45146557", "user_id": "test_user_002", "message": "我想重新开始，先确认一下", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.340923"}, {"id": "input_023", "session_id": "test_session_7c0bc4ae", "user_id": "test_user_016", "message": "hi", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340927"}, {"id": "input_024", "session_id": "test_session_1e385276", "user_id": "test_user_015", "message": "重新开始", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340929"}, {"id": "input_025", "session_id": "test_session_8d32bb7c", "user_id": "test_user_006", "message": "hi", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340932"}, {"id": "input_026", "session_id": "test_session_7c0bc4ae", "user_id": "test_user_016", "message": "下午好", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340936"}, {"id": "input_027", "session_id": "test_session_ce6a8de9", "user_id": "test_user_013", "message": "hello", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340939"}, {"id": "input_028", "session_id": "test_session_7c0bc4ae", "user_id": "test_user_016", "message": "hello", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.340942"}, {"id": "input_029", "session_id": "test_session_bd9a6c6b", "user_id": "test_user_019", "message": "我想重新开始，先确认一下", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340945"}, {"id": "input_030", "session_id": "test_session_1e385276", "user_id": "test_user_015", "message": "你好", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.340948"}, {"id": "input_031", "session_id": "test_session_a3b19548", "user_id": "test_user_012", "message": "早上好", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340951"}, {"id": "input_032", "session_id": "test_session_e971c47b", "user_id": "test_user_014", "message": "你好", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.340956"}, {"id": "input_033", "session_id": "test_session_a3b19548", "user_id": "test_user_012", "message": "重新开始", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340958"}, {"id": "input_034", "session_id": "test_session_380aaf17", "user_id": "test_user_010", "message": "重新开始", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340961"}, {"id": "input_035", "session_id": "test_session_18c6d9c7", "user_id": "test_user_005", "message": "hello", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340965"}, {"id": "input_036", "session_id": "test_session_45146557", "user_id": "test_user_002", "message": "重置", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340967"}, {"id": "input_037", "session_id": "test_session_9ba561ba", "user_id": "test_user_003", "message": "下午好", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340971"}, {"id": "input_038", "session_id": "test_session_45146557", "user_id": "test_user_002", "message": "需要改", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340974"}, {"id": "input_039", "session_id": "test_session_3254b532", "user_id": "test_user_011", "message": "你好", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.340977"}, {"id": "input_040", "session_id": "test_session_c27ed150", "user_id": "test_user_020", "message": "调整", "expected_intent": "modify", "expected_state_transition": "DOCUMENTING", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340980"}, {"id": "input_041", "session_id": "test_session_9ba561ba", "user_id": "test_user_003", "message": "新聊天", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340982"}, {"id": "input_042", "session_id": "test_session_aef7320c", "user_id": "test_user_008", "message": "确认，不过要调整一下", "expected_intent": "modify", "expected_state_transition": "DOCUMENTING", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340985"}, {"id": "input_043", "session_id": "test_session_ce6a8de9", "user_id": "test_user_013", "message": "清空", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340988"}, {"id": "input_044", "session_id": "test_session_ce6a8de9", "user_id": "test_user_013", "message": "早上好", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.340991"}, {"id": "input_045", "session_id": "test_session_1e385276", "user_id": "test_user_015", "message": "重置", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340994"}, {"id": "input_046", "session_id": "test_session_45146557", "user_id": "test_user_002", "message": "重新开始", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.340997"}, {"id": "input_047", "session_id": "test_session_00877df9", "user_id": "test_user_018", "message": "重置", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.340999"}, {"id": "input_048", "session_id": "test_session_93bb51ee", "user_id": "test_user_007", "message": "重新开始", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341002"}, {"id": "input_049", "session_id": "test_session_c27ed150", "user_id": "test_user_020", "message": "重置", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341005"}, {"id": "input_050", "session_id": "test_session_a3b19548", "user_id": "test_user_012", "message": "hello", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341008"}, {"id": "input_051", "session_id": "test_session_d7bafee7", "user_id": "test_user_001", "message": "不对", "expected_intent": "modify", "expected_state_transition": "DOCUMENTING", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341010"}, {"id": "input_052", "session_id": "test_session_45146557", "user_id": "test_user_002", "message": "我想重新开始，先确认一下", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341013"}, {"id": "input_053", "session_id": "test_session_18c6d9c7", "user_id": "test_user_005", "message": "hi", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341018"}, {"id": "input_054", "session_id": "test_session_9ba561ba", "user_id": "test_user_003", "message": "重置", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341021"}, {"id": "input_055", "session_id": "test_session_bea63ca6", "user_id": "test_user_009", "message": "下午好", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341024"}, {"id": "input_056", "session_id": "test_session_93bb51ee", "user_id": "test_user_007", "message": "hi", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341027"}, {"id": "input_057", "session_id": "test_session_45146557", "user_id": "test_user_002", "message": "需要改", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341031"}, {"id": "input_058", "session_id": "test_session_380aaf17", "user_id": "test_user_010", "message": "新聊天", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341033"}, {"id": "input_059", "session_id": "test_session_1e385276", "user_id": "test_user_015", "message": "重新开始", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341036"}, {"id": "input_060", "session_id": "test_session_93bb51ee", "user_id": "test_user_007", "message": "重置", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341039"}, {"id": "input_061", "session_id": "test_session_c27ed150", "user_id": "test_user_020", "message": "我想重新开始，先确认一下", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341041"}, {"id": "input_062", "session_id": "test_session_00877df9", "user_id": "test_user_018", "message": "重置", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341044"}, {"id": "input_063", "session_id": "test_session_d7bafee7", "user_id": "test_user_001", "message": "好的，但是需要修改", "expected_intent": "modify", "expected_state_transition": "DOCUMENTING", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341047"}, {"id": "input_064", "session_id": "test_session_7c0bc4ae", "user_id": "test_user_016", "message": "hi", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341050"}, {"id": "input_065", "session_id": "test_session_00877df9", "user_id": "test_user_018", "message": "重置", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341053"}, {"id": "input_066", "session_id": "test_session_aef7320c", "user_id": "test_user_008", "message": "好的，但是需要修改", "expected_intent": "modify", "expected_state_transition": "DOCUMENTING", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341056"}, {"id": "input_067", "session_id": "test_session_c27ed150", "user_id": "test_user_020", "message": "ok", "expected_intent": "confirm", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341059"}, {"id": "input_068", "session_id": "test_session_9ba561ba", "user_id": "test_user_003", "message": "清空", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341062"}, {"id": "input_069", "session_id": "test_session_aef7320c", "user_id": "test_user_008", "message": "需要改", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341065"}, {"id": "input_070", "session_id": "test_session_bd9a6c6b", "user_id": "test_user_019", "message": "确认，不过要调整一下", "expected_intent": "modify", "expected_state_transition": "DOCUMENTING", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341068"}, {"id": "input_071", "session_id": "test_session_e971c47b", "user_id": "test_user_014", "message": "你好", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341071"}, {"id": "input_072", "session_id": "test_session_e971c47b", "user_id": "test_user_014", "message": "下午好", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341074"}, {"id": "input_073", "session_id": "test_session_d7bafee7", "user_id": "test_user_001", "message": "不对", "expected_intent": "modify", "expected_state_transition": "DOCUMENTING", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341077"}, {"id": "input_074", "session_id": "test_session_d7bafee7", "user_id": "test_user_001", "message": "新聊天", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341079"}, {"id": "input_075", "session_id": "test_session_4fb484f7", "user_id": "test_user_004", "message": "你好", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341083"}, {"id": "input_076", "session_id": "test_session_c27ed150", "user_id": "test_user_020", "message": "调整", "expected_intent": "modify", "expected_state_transition": "DOCUMENTING", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341085"}, {"id": "input_077", "session_id": "test_session_aef7320c", "user_id": "test_user_008", "message": "重置", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341088"}, {"id": "input_078", "session_id": "test_session_8d32bb7c", "user_id": "test_user_006", "message": "你好", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341091"}, {"id": "input_079", "session_id": "test_session_18c6d9c7", "user_id": "test_user_005", "message": "重置", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341094"}, {"id": "input_080", "session_id": "test_session_380aaf17", "user_id": "test_user_010", "message": "新聊天", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341097"}, {"id": "input_081", "session_id": "test_session_7523bb80", "user_id": "test_user_017", "message": "清空", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341100"}, {"id": "input_082", "session_id": "test_session_d7bafee7", "user_id": "test_user_001", "message": "但是", "expected_intent": "modify", "expected_state_transition": "DOCUMENTING", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341102"}, {"id": "input_083", "session_id": "test_session_93bb51ee", "user_id": "test_user_007", "message": "早上好", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341105"}, {"id": "input_084", "session_id": "test_session_bd9a6c6b", "user_id": "test_user_019", "message": "确认", "expected_intent": "confirm", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341108"}, {"id": "input_085", "session_id": "test_session_93bb51ee", "user_id": "test_user_007", "message": "新聊天", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341111"}, {"id": "input_086", "session_id": "test_session_93bb51ee", "user_id": "test_user_007", "message": "hello", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341114"}, {"id": "input_087", "session_id": "test_session_1e385276", "user_id": "test_user_015", "message": "重新开始", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341117"}, {"id": "input_088", "session_id": "test_session_7523bb80", "user_id": "test_user_017", "message": "hi", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341120"}, {"id": "input_089", "session_id": "test_session_d7bafee7", "user_id": "test_user_001", "message": "不对", "expected_intent": "modify", "expected_state_transition": "DOCUMENTING", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341123"}, {"id": "input_090", "session_id": "test_session_4fb484f7", "user_id": "test_user_004", "message": "清空", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341126"}, {"id": "input_091", "session_id": "test_session_45146557", "user_id": "test_user_002", "message": "不对", "expected_intent": "modify", "expected_state_transition": "DOCUMENTING", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341128"}, {"id": "input_092", "session_id": "test_session_4fb484f7", "user_id": "test_user_004", "message": "hi", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341132"}, {"id": "input_093", "session_id": "test_session_1e385276", "user_id": "test_user_015", "message": "重置", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "IDLE", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341134"}, {"id": "input_094", "session_id": "test_session_4fb484f7", "user_id": "test_user_004", "message": "新聊天", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "COLLECTING_INFO", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341137"}, {"id": "input_095", "session_id": "test_session_e971c47b", "user_id": "test_user_014", "message": "早上好", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341140"}, {"id": "input_096", "session_id": "test_session_d7bafee7", "user_id": "test_user_001", "message": "调整", "expected_intent": "modify", "expected_state_transition": "DOCUMENTING", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341143"}, {"id": "input_097", "session_id": "test_session_aef7320c", "user_id": "test_user_008", "message": "好的", "expected_intent": "confirm", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341146"}, {"id": "input_098", "session_id": "test_session_e971c47b", "user_id": "test_user_014", "message": "你好", "expected_intent": "greeting", "expected_state_transition": null, "context": {"current_state": "PROCESSING_INTENT", "has_document": false, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341149"}, {"id": "input_099", "session_id": "test_session_aef7320c", "user_id": "test_user_008", "message": "清空", "expected_intent": "unknown", "expected_state_transition": null, "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": false}, "timestamp": "2025-07-11T14:31:28.341152"}, {"id": "input_100", "session_id": "test_session_c27ed150", "user_id": "test_user_020", "message": "新聊天", "expected_intent": "restart", "expected_state_transition": "IDLE", "context": {"current_state": "DOCUMENTING", "has_document": true, "focus_points_completed": true}, "timestamp": "2025-07-11T14:31:28.341155"}], "state_transitions": [{"type": "normal_transition", "from_state": "IDLE", "to_state": "PROCESSING_INTENT", "trigger": "user_input_received", "expected_result": "success"}, {"type": "invalid_transition", "from_state": "IDLE", "to_state": "COLLECTING_INFO", "trigger": "user_input_received", "expected_result": "failure"}, {"type": "invalid_transition", "from_state": "IDLE", "to_state": "DOCUMENTING", "trigger": "user_input_received", "expected_result": "failure"}, {"type": "normal_transition", "from_state": "PROCESSING_INTENT", "to_state": "COLLECTING_INFO", "trigger": "intent_recognized", "expected_result": "success"}, {"type": "invalid_transition", "from_state": "PROCESSING_INTENT", "to_state": "IDLE", "trigger": "intent_recognized", "expected_result": "failure"}, {"type": "invalid_transition", "from_state": "PROCESSING_INTENT", "to_state": "DOCUMENTING", "trigger": "intent_recognized", "expected_result": "failure"}, {"type": "normal_transition", "from_state": "COLLECTING_INFO", "to_state": "DOCUMENTING", "trigger": "focus_points_completed", "expected_result": "success"}, {"type": "invalid_transition", "from_state": "COLLECTING_INFO", "to_state": "IDLE", "trigger": "focus_points_completed", "expected_result": "failure"}, {"type": "invalid_transition", "from_state": "COLLECTING_INFO", "to_state": "PROCESSING_INTENT", "trigger": "focus_points_completed", "expected_result": "failure"}, {"type": "normal_transition", "from_state": "DOCUMENTING", "to_state": "IDLE", "trigger": "document_confirmed", "expected_result": "success"}, {"type": "invalid_transition", "from_state": "DOCUMENTING", "to_state": "PROCESSING_INTENT", "trigger": "document_confirmed", "expected_result": "failure"}, {"type": "invalid_transition", "from_state": "DOCUMENTING", "to_state": "COLLECTING_INFO", "trigger": "document_confirmed", "expected_result": "failure"}], "edge_cases": [{"type": "empty_input", "input": "", "expected_behavior": "handle_gracefully", "context": {"state": "IDLE"}}, {"type": "very_long_input", "input": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "expected_behavior": "truncate_or_reject", "context": {"state": "COLLECTING_INFO"}}, {"type": "special_characters", "input": "!@#$%^&*()_+{}[]|\\:;\"'<>?,./", "expected_behavior": "handle_gracefully", "context": {"state": "IDLE"}}, {"type": "mixed_language", "input": "Hello 你好 こんにちは", "expected_behavior": "handle_gracefully", "context": {"state": "COLLECTING_INFO"}}, {"type": "state_inconsistency", "input": "确认文档", "expected_behavior": "recover_state", "context": {"memory_state": "DOCUMENTING", "database_state": "IDLE"}}, {"type": "concurrent_operations", "input": "multiple_simultaneous_requests", "expected_behavior": "maintain_consistency", "context": {"concurrent_users": 5, "same_session": true}}]}