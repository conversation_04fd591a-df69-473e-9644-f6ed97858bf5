#!/usr/bin/env python3
"""
配置完整性验证脚本
验证所有硬编码消除后的配置项是否正确加载
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from backend.config.unified_config_loader import get_unified_config
import traceback

def test_config_integrity():
    """测试配置完整性"""
    print("🔧 开始配置完整性验证...")
    
    try:
        config = get_unified_config()
        
        # 定义所有需要验证的配置项
        config_items = [
            # 错误消息模板
            "message_templates.error.technical_issue",
            "message_templates.error.session_error", 
            "message_templates.error.message_processing_error",
            "message_templates.error.request_processing_error",
            "message_templates.error.temporary_unavailable",
            "message_templates.error.answer_processing_error",
            "message_templates.error.unclear_understanding",
            "message_templates.error.requirement_clarification",
            "message_templates.error.rephrase_request",
            "message_templates.error.detailed_clarification_template",
            "message_templates.error.request_timeout",
            "message_templates.error.no_valid_response",
            "message_templates.error.processing_failure",
            "message_templates.error.knowledge_base_error",
            "message_templates.error.document_not_found",
            "message_templates.error.processing_failed",
            "message_templates.error.document_save_failed",
            "message_templates.error.internal_error",
            "message_templates.error.no_knowledge_found",
            "message_templates.error.system_problem",
            "message_templates.error.cannot_generate_reply",
            "message_templates.error.no_information",
            
            # 问候消息模板
            "message_templates.greeting.requirement_assistant",
            
            # 澄清消息模板
            "message_templates.clarification.not_understand",
            "message_templates.clarification.exception_occurred",
            
            # 关键词加速器模板
            "message_templates.keyword_accelerator.greeting",
            "message_templates.keyword_accelerator.confirm",
            "message_templates.keyword_accelerator.restart",
            "message_templates.keyword_accelerator.system_capability_query",
            "message_templates.keyword_accelerator.modify",
            "message_templates.keyword_accelerator.default_response",
            
            # 状态机模板
            "message_templates.state_machine.greeting_fallback",
            "message_templates.state_machine.greeting_default",
            "message_templates.state_machine.new_project_greeting",
            
            # 对话处理器模板
            "message_templates.conversation_handler.welcome_default",
            
            # 消息回复管理器模板
            "message_templates.message_reply_manager.unknown_action_fallback",
            
            # 对话流程回复混入模板
            "message_templates.conversation_flow_reply_mixin.general_problem",
            
            # 基础Agent模板
            "message_templates.base_agent.processing_with_error",
            
            # 能力策略模板
            "message_templates.capabilities_strategy.closing_guidance",
            
            # 情感支持策略模板
            "message_templates.emotional_support_strategy.anger_response_1",
            "message_templates.emotional_support_strategy.anxiety_response_1",
            "message_templates.emotional_support_strategy.confused_response_1",
            "message_templates.emotional_support_strategy.fallback_response",
            "message_templates.emotional_support_strategy.default_understanding",
            
            # 知识库处理器模板
            "message_templates.knowledge_base_handler.system_unavailable",
            
            # 文档处理器模板
            "message_templates.document_handler.confirmation_success",
            
            # 复合处理器模板
            "message_templates.composite_handler.continue_collecting",
            
            # 知识库策略模板
            "message_templates.knowledge_base_strategy.search_template",
            
            # 阈值配置
            "thresholds.keyword_match_threshold",
            "thresholds.strategy.requirement.keyword_match_multiplier",
            "thresholds.strategy.requirement.max_keyword_score",
        ]
        
        print(f"📋 验证 {len(config_items)} 个配置项...")
        
        missing_configs = []
        valid_configs = []
        
        for config_path in config_items:
            try:
                value = config.get_config_value(config_path)
                if value is not None:
                    valid_configs.append((config_path, value))
                    print(f"  ✓ {config_path}")
                else:
                    missing_configs.append(config_path)
                    print(f"  ❌ {config_path}: 配置缺失")
            except Exception as e:
                missing_configs.append(config_path)
                print(f"  ❌ {config_path}: 加载错误 - {e}")
        
        print(f"\n📊 验证结果:")
        print(f"  ✅ 有效配置: {len(valid_configs)}")
        print(f"  ❌ 缺失配置: {len(missing_configs)}")
        
        if missing_configs:
            print(f"\n⚠️  缺失的配置项:")
            for config in missing_configs:
                print(f"    - {config}")
            return False
        else:
            print(f"\n🎉 所有配置项验证通过!")
            return True
            
    except Exception as e:
        print(f"❌ 配置完整性验证失败: {e}")
        traceback.print_exc()
        return False

def test_template_formatting():
    """测试模板格式化功能"""
    print("\n🔧 开始模板格式化验证...")
    
    try:
        config = get_unified_config()
        
        # 测试需要格式化的模板
        format_tests = [
            ("message_templates.error.detailed_clarification_template", {"message_preview": "测试消息"}),
            ("message_templates.base_agent.processing_with_error", {"error_msg": "测试错误"}),
            ("message_templates.keyword_accelerator.default_response", {"intent": "测试意图"}),
            ("message_templates.knowledge_base_strategy.search_template", {"search_prompt": "测试搜索"}),
        ]
        
        print(f"📋 验证 {len(format_tests)} 个格式化模板...")
        
        failed_formats = []
        
        for template_path, format_args in format_tests:
            try:
                template = config.get_config_value(template_path)
                if template:
                    formatted = template.format(**format_args)
                    print(f"  ✓ {template_path}: 格式化成功")
                    print(f"    示例: {formatted[:50]}...")
                else:
                    failed_formats.append((template_path, "模板不存在"))
                    print(f"  ❌ {template_path}: 模板不存在")
            except Exception as e:
                failed_formats.append((template_path, str(e)))
                print(f"  ❌ {template_path}: 格式化失败 - {e}")
        
        print(f"\n📊 格式化验证结果:")
        print(f"  ✅ 成功格式化: {len(format_tests) - len(failed_formats)}")
        print(f"  ❌ 格式化失败: {len(failed_formats)}")
        
        if failed_formats:
            print(f"\n⚠️  格式化失败的模板:")
            for template, error in failed_formats:
                print(f"    - {template}: {error}")
            return False
        else:
            print(f"\n🎉 所有模板格式化验证通过!")
            return True
            
    except Exception as e:
        print(f"❌ 模板格式化验证失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始阶段4：配置完整性验证")
    print("=" * 60)
    
    # 执行验证
    config_ok = test_config_integrity()
    format_ok = test_template_formatting()
    
    print("\n" + "=" * 60)
    if config_ok and format_ok:
        print("🎉 阶段4配置完整性验证全部通过!")
        sys.exit(0)
    else:
        print("❌ 阶段4配置完整性验证存在问题，需要修复!")
        sys.exit(1)
