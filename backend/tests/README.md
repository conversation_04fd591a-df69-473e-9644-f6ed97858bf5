# 业务逻辑回归测试套件

这是一套完整的业务逻辑回归测试工具，用于确保架构简化改造不会破坏现有的业务逻辑。

## 📁 文件结构

```
tests/
├── README.md                           # 本文件
├── business_logic_regression_tests.py  # 核心测试套件
├── test_config.yaml                    # 测试配置文件
├── run_business_logic_tests.py         # 测试运行脚本
├── test_data_generator.py              # 测试数据生成器
├── generated_test_data.json            # 生成的测试数据
└── reports/                            # 测试报告目录
    ├── baseline_report.json            # 基准测试报告
    └── regression_report.json          # 回归测试报告
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install pytest pytest-asyncio pyyaml
```

### 2. 生成测试数据

```bash
# 生成所有类型的测试数据
python tests/test_data_generator.py --type all --count 20

# 只生成特定类型的数据
python tests/test_data_generator.py --type sessions --count 10
python tests/test_data_generator.py --type user_inputs --count 50
```

### 3. 建立基准测试

在架构改造**之前**运行，建立基准数据：

```bash
python tests/run_business_logic_tests.py --mode baseline
```

### 4. 运行回归测试

在架构改造**之后**运行，验证业务逻辑完整性：

```bash
python tests/run_business_logic_tests.py --mode regression
```

### 5. 查看测试报告

```bash
# 查看详细的对比报告
python tests/run_business_logic_tests.py --mode compare
```

## 📊 测试覆盖范围

### 1. 会话状态管理测试
- ✅ 状态转换逻辑验证
- ✅ 状态持久化测试
- ✅ 状态恢复机制测试
- ✅ 无效状态转换拒绝测试

### 2. 关注点管理测试
- ✅ P0关注点强制采集验证
- ✅ P1关注点重试机制测试
- ✅ P2关注点可选跳过测试
- ✅ 单一processing状态约束测试

### 3. 文档确认流程测试
- ✅ 确认关键词识别测试
- ✅ 否定关键词优先级测试
- ✅ 状态限制验证测试
- ✅ 混合关键词处理测试

### 4. 用户会话隔离测试
- ✅ 会话数据隔离验证
- ✅ 并发操作安全测试
- ✅ 用户数据隔离测试

### 5. 错误处理和恢复测试
- ✅ 异常状态恢复测试
- ✅ 数据一致性验证
- ✅ 错误分类处理测试

### 6. 性能基准测试
- ✅ 关键词匹配性能测试
- ✅ 语义匹配性能测试
- ✅ LLM调用性能测试
- ✅ 状态转换性能测试

## ⚙️ 配置说明

### 测试配置文件 (`test_config.yaml`)

```yaml
# 业务逻辑验证标准
validation_thresholds:
  state_transition_success_rate: 100.0    # 状态转换成功率必须100%
  focus_point_consistency_rate: 100.0     # 关注点一致性必须100%
  document_confirmation_accuracy: 99.5    # 文档确认准确率≥99.5%
  session_isolation_integrity: 100.0      # 会话隔离完整性必须100%
  error_recovery_success_rate: 95.0       # 错误恢复成功率≥95%

# 性能基准测试
performance_benchmarks:
  keyword_matching_time: 0.001    # < 1ms
  semantic_matching_time: 0.010   # < 10ms
  llm_fallback_time: 0.500        # < 500ms
```

### 关键配置项说明

- **validation_thresholds**: 业务逻辑验证的成功率阈值
- **performance_benchmarks**: 性能测试的基准时间
- **test_cases**: 具体的测试用例配置
- **boundary_conditions**: 边界条件测试配置

## 🔧 使用方法

### 基本用法

```bash
# 1. 架构改造前 - 建立基准
python tests/run_business_logic_tests.py --mode baseline

# 2. 架构改造后 - 回归测试
python tests/run_business_logic_tests.py --mode regression

# 3. 查看对比结果
python tests/run_business_logic_tests.py --mode compare
```

### 高级用法

```bash
# 使用自定义配置文件
python tests/run_business_logic_tests.py --mode regression --config custom_config.yaml

# 生成特定类型的测试数据
python tests/test_data_generator.py --type focus_points --count 50

# 运行特定的测试类别
python -m pytest tests/business_logic_regression_tests.py::TestStateManagement -v

# 运行性能测试
python -m pytest tests/business_logic_regression_tests.py -k "performance" -v
```

## 📈 测试报告解读

### 基准测试报告
```json
{
  "test_run_id": "baseline_1703123456",
  "timestamp": "2023-12-21T10:30:45",
  "mode": "baseline",
  "total_tests": 45,
  "passed_tests": 45,
  "failed_tests": 0,
  "success_rate": 100.0,
  "total_duration": 2.34,
  "performance_metrics": {
    "keyword_matching_time": 0.0008,
    "semantic_matching_time": 0.0085,
    "llm_fallback_time": 0.456
  }
}
```

### 回归测试报告
```json
{
  "test_run_id": "regression_1703123789",
  "mode": "regression",
  "success_rate": 100.0,
  "business_logic_violations": [],
  "performance_comparison": {
    "keyword_matching_time": {
      "baseline": 0.0008,
      "regression": 0.0005,
      "improvement_percent": 37.5
    }
  }
}
```

### 关键指标说明

- **success_rate**: 测试成功率，必须≥基准值
- **business_logic_violations**: 业务逻辑违规列表，必须为空
- **performance_comparison**: 性能对比，显示改进百分比
- **recommendation**: 基于测试结果的建议

## ⚠️ 重要注意事项

### 1. 测试顺序
```bash
# 正确的测试流程
1. 架构改造前：运行 baseline 模式
2. 架构改造后：运行 regression 模式
3. 分析结果：运行 compare 模式
```

### 2. 失败处理
- 如果 `business_logic_violations` 不为空，**必须立即回退**
- 如果 `success_rate` 低于基准值，需要详细分析失败原因
- 如果性能没有改善，需要检查优化效果

### 3. 数据安全
- 测试使用独立的测试数据库
- 不会影响生产数据
- 测试数据可以重复生成

## 🛠️ 扩展测试

### 添加新的测试用例

1. 在 `test_config.yaml` 中添加配置：
```yaml
test_cases:
  custom_tests:
    - name: "my_custom_test"
      expected_behavior: "success"
```

2. 在 `business_logic_regression_tests.py` 中添加测试类：
```python
class TestCustomLogic:
    @pytest.mark.asyncio
    async def test_my_custom_logic(self):
        # 测试逻辑
        assert True
```

### 添加新的验证标准

在 `test_config.yaml` 中添加：
```yaml
validation_thresholds:
  my_custom_metric: 95.0  # 自定义指标阈值
```

## 📞 支持和帮助

如果在使用过程中遇到问题：

1. 检查配置文件格式是否正确
2. 确认测试数据已正确生成
3. 查看测试报告中的详细错误信息
4. 检查系统日志获取更多调试信息

## 🎯 最佳实践

1. **定期运行基准测试**：在每次重要变更前建立新的基准
2. **完整的测试覆盖**：确保所有业务逻辑都有对应的测试用例
3. **持续监控**：将测试集成到CI/CD流程中
4. **及时响应**：发现业务逻辑违规时立即处理

这套测试工具将确保您的架构简化改造既能获得性能提升，又能保持100%的业务逻辑完整性。
