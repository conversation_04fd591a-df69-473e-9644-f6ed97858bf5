"""
配置设置模块测试
"""
import os
from unittest.mock import patch
from backend.config.settings import get_required_env

def test_get_required_env():
    """测试获取必要的环境变量函数"""
    # 测试存在的环境变量
    with patch.dict(os.environ, {"TEST_VAR": "test_value"}):
        assert get_required_env("TEST_VAR") == "test_value"
    
    # 测试不存在的环境变量，使用默认值
    assert get_required_env("NON_EXISTENT_VAR", "default_value") == "default_value"
    
    # 测试不存在的环境变量，无默认值
    assert get_required_env("NON_EXISTENT_VAR") == ""

def test_unified_config_integration():
    """测试统一配置系统集成"""
    # 这个测试验证 settings.py 与统一配置系统的集成
    from backend.config.unified_config_loader import get_unified_config

    config = get_unified_config()

    # 验证统一配置系统可以正常加载
    assert config is not None

    # 验证基本配置结构存在
    assert config.get_config_value("app.name") is not None

def test_settings_basic_functionality():
    """测试 settings.py 基本功能"""
    # 测试日志配置相关功能
    from backend.config.settings import LOGS_DIR
    import os

    # 验证日志目录配置
    assert LOGS_DIR is not None
    assert isinstance(LOGS_DIR, str)

    # 验证日志目录存在或可创建
    if not os.path.exists(LOGS_DIR):
        try:
            os.makedirs(LOGS_DIR, exist_ok=True)
            assert os.path.exists(LOGS_DIR)
        except Exception:
            pass  # 在某些环境下可能无法创建目录
