#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息模板配置测试脚本

测试消息模板重构后的功能正确性，包括：
1. 配置文件加载测试
2. 模板访问测试
3. 变量替换测试
4. 回退机制测试
"""

import sys
import os
import pytest
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from backend.config.unified_config_loader import get_unified_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestMessageTemplates:
    """消息模板测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.config = get_unified_config()
    
    def test_config_loading(self):
        """测试配置文件加载"""
        assert self.config is not None
        message_templates = self.config.get_message_templates()
        assert message_templates is not None
        assert isinstance(message_templates, dict)
        logger.info(f"配置加载成功，包含 {len(message_templates)} 个模板分类")
    
    def test_greeting_templates(self):
        """测试问候模板"""
        # 测试基础问候
        basic_greeting = self.config.get_message_template("greeting.basic")
        assert basic_greeting is not None
        assert len(basic_greeting) > 0
        logger.info(f"基础问候模板: {basic_greeting[:50]}...")
        
        # 测试友好问候
        friendly_greeting = self.config.get_message_template("greeting.friendly")
        assert friendly_greeting is not None
        assert len(friendly_greeting) > 0
        logger.info(f"友好问候模板: {friendly_greeting[:50]}...")
        
        # 测试新增的问候模板
        ai_assistant = self.config.get_message_template("greeting.ai_assistant")
        assert ai_assistant is not None
        assert "AI助手" in ai_assistant
        logger.info(f"AI助手问候模板: {ai_assistant[:50]}...")
    
    def test_error_templates(self):
        """测试错误模板"""
        # 测试系统错误
        system_error = self.config.get_message_template("error.system")
        assert system_error is not None
        assert "系统" in system_error
        logger.info(f"系统错误模板: {system_error}")
        
        # 测试新增的错误模板
        message_processing = self.config.get_message_template("error.message_processing")
        assert message_processing is not None
        assert "处理消息" in message_processing
        logger.info(f"消息处理错误模板: {message_processing}")
        
        # 测试通用回退错误
        general_fallback = self.config.get_message_template("error.general_fallback")
        assert general_fallback is not None
        logger.info(f"通用回退错误模板: {general_fallback}")
    
    def test_clarification_templates(self):
        """测试澄清模板"""
        # 测试基础澄清请求
        request = self.config.get_message_template("clarification.request")
        assert request is not None
        assert "澄清" in request or "详细" in request
        logger.info(f"澄清请求模板: {request[:50]}...")
        
        # 测试新增的澄清模板
        need_more_info = self.config.get_message_template("clarification.need_more_info")
        assert need_more_info is not None
        assert "更多信息" in need_more_info
        logger.info(f"需要更多信息模板: {need_more_info[:50]}...")
    
    def test_fallback_templates(self):
        """测试回退模板"""
        # 测试通用回退
        general = self.config.get_message_template("fallback.general")
        assert general is not None
        logger.info(f"通用回退模板: {general}")
        
        # 测试紧急回退
        emergency = self.config.get_message_template("fallback.emergency")
        assert emergency is not None
        logger.info(f"紧急回退模板: {emergency}")
    
    def test_introduction_templates(self):
        """测试介绍模板"""
        # 测试完整介绍
        full = self.config.get_message_template("introduction.full")
        assert full is not None
        assert "需求分析" in full
        logger.info(f"完整介绍模板: {full[:50]}...")
        
        # 测试由己平台介绍
        youji_platform = self.config.get_message_template("introduction.youji_platform")
        assert youji_platform is not None
        assert "由己" in youji_platform
        logger.info(f"由己平台介绍模板: {youji_platform[:100]}...")
    
    def test_template_variable_replacement(self):
        """测试模板变量替换"""
        # 测试带变量的模板
        processing_error = self.config.get_message_template(
            "error.processing", 
            error_msg="测试错误"
        )
        assert processing_error is not None
        assert "测试错误" in processing_error
        logger.info(f"变量替换测试: {processing_error}")
    
    def test_missing_template_handling(self):
        """测试缺失模板处理"""
        # 测试不存在的模板
        missing = self.config.get_message_template("nonexistent.template")
        assert missing == ""  # 应该返回空字符串
        logger.info("缺失模板处理正确")
        
        # 测试带默认值的缺失模板
        missing_with_default = self.config.get_message_template(
            "nonexistent.template", 
            default="默认消息"
        )
        assert missing_with_default == "默认消息"
        logger.info("带默认值的缺失模板处理正确")


def run_template_validation():
    """运行模板验证"""
    logger.info("开始消息模板配置验证...")
    
    try:
        # 创建测试实例
        test_instance = TestMessageTemplates()
        test_instance.setup_method()
        
        # 运行所有测试
        test_methods = [
            test_instance.test_config_loading,
            test_instance.test_greeting_templates,
            test_instance.test_error_templates,
            test_instance.test_clarification_templates,
            test_instance.test_fallback_templates,
            test_instance.test_introduction_templates,
            test_instance.test_template_variable_replacement,
            test_instance.test_missing_template_handling
        ]
        
        passed = 0
        failed = 0
        
        for test_method in test_methods:
            try:
                test_method()
                logger.info(f"✅ {test_method.__name__} 通过")
                passed += 1
            except Exception as e:
                logger.error(f"❌ {test_method.__name__} 失败: {e}")
                failed += 1
        
        logger.info(f"\n测试结果: {passed} 通过, {failed} 失败")
        
        if failed == 0:
            logger.info("🎉 所有消息模板测试通过！")
            return True
        else:
            logger.error("⚠️ 部分测试失败，请检查配置")
            return False
            
    except Exception as e:
        logger.error(f"测试运行失败: {e}")
        return False


if __name__ == "__main__":
    success = run_template_validation()
    sys.exit(0 if success else 1)
