#!/usr/bin/env python3
"""
统一决策引擎核心框架测试

测试内容：
1. 基础组件初始化
2. 策略注册和管理
3. 上下文分析功能
4. 决策执行流程
5. 缓存机制
6. 冲突检测和解决
7. 性能监控
"""

import sys
import os
import asyncio
import time
from typing import List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.agents.decision_types import (
    DecisionStrategy, AnalyzedContext, DecisionResult, 
    create_decision_context, create_decision_result
)
from backend.agents.strategy_registry import StrategyRegistry, get_strategy_registry, reset_strategy_registry
from backend.agents.context_analyzer import ContextAnalyzer, get_context_analyzer, reset_context_analyzer
from backend.agents.unified_decision_engine import UnifiedDecisionEngine, get_unified_decision_engine, reset_unified_decision_engine
from backend.agents.unified_state_manager import ConversationState


# ==================== 测试策略实现 ====================

class TestGreetingStrategy(DecisionStrategy):
    """测试用问候策略"""
    
    @property
    def name(self) -> str:
        return "test_greeting_strategy"
    
    @property
    def supported_intents(self) -> List[str]:
        return ["greeting"]
    
    @property
    def priority(self) -> int:
        return 8
    
    async def can_handle(self, context: AnalyzedContext) -> bool:
        return context.intent == "greeting"
    
    async def execute(self, context: AnalyzedContext) -> DecisionResult:
        return create_decision_result(
            action="send_greeting",
            confidence=0.9,
            intent=context.intent,
            emotion=context.emotion,
            response_template="greeting.welcome",
            next_state=ConversationState.IDLE
        )


class TestBusinessStrategy(DecisionStrategy):
    """测试用业务需求策略"""
    
    @property
    def name(self) -> str:
        return "test_business_strategy"
    
    @property
    def supported_intents(self) -> List[str]:
        return ["business_requirement"]
    
    @property
    def priority(self) -> int:
        return 7
    
    async def can_handle(self, context: AnalyzedContext) -> bool:
        return context.intent == "business_requirement"
    
    async def execute(self, context: AnalyzedContext) -> DecisionResult:
        return create_decision_result(
            action="start_requirement_collection",
            confidence=0.85,
            intent=context.intent,
            emotion=context.emotion,
            response_template="requirement.start",
            next_state=ConversationState.COLLECTING_INFO
        )


class TestConflictStrategy(DecisionStrategy):
    """测试用冲突策略（与问候策略有相同优先级）"""
    
    @property
    def name(self) -> str:
        return "test_conflict_strategy"
    
    @property
    def supported_intents(self) -> List[str]:
        return ["greeting"]  # 与问候策略重叠
    
    @property
    def priority(self) -> int:
        return 8  # 与问候策略相同优先级
    
    async def can_handle(self, context: AnalyzedContext) -> bool:
        return context.intent == "greeting"
    
    async def execute(self, context: AnalyzedContext) -> DecisionResult:
        return create_decision_result(
            action="alternative_greeting",
            confidence=0.7,
            intent=context.intent,
            emotion=context.emotion,
            response_template="greeting.alternative"
        )


# ==================== 测试类 ====================

class UnifiedDecisionEngineTest:
    """统一决策引擎测试类"""
    
    def __init__(self):
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
        
        # 重置全局实例
        reset_strategy_registry()
        reset_context_analyzer()
        reset_unified_decision_engine()
    
    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """记录测试结果"""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            status = "✅ PASS"
        else:
            status = "❌ FAIL"
        
        result = f"{status} {test_name}"
        if message:
            result += f": {message}"
        
        print(result)
        self.test_results.append({
            "name": test_name,
            "passed": passed,
            "message": message
        })
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始统一决策引擎核心框架测试")
        print("=" * 60)
        
        # 1. 基础组件测试
        await self.test_basic_components()
        
        # 2. 策略注册测试
        await self.test_strategy_registration()
        
        # 3. 上下文分析测试
        await self.test_context_analysis()
        
        # 4. 决策执行测试
        await self.test_decision_execution()
        
        # 5. 缓存机制测试
        await self.test_caching_mechanism()
        
        # 6. 冲突处理测试
        await self.test_conflict_resolution()
        
        # 7. 性能监控测试
        await self.test_performance_monitoring()
        
        # 8. 向后兼容测试
        await self.test_backward_compatibility()
        
        # 输出测试总结
        self.print_test_summary()
    
    async def test_basic_components(self):
        """测试基础组件初始化"""
        print("\n📋 1. 基础组件测试")
        
        try:
            # 测试策略注册中心
            registry = get_strategy_registry()
            self.log_test("策略注册中心初始化", registry is not None)
            
            # 测试上下文分析器
            analyzer = get_context_analyzer()
            self.log_test("上下文分析器初始化", analyzer is not None)
            
            # 测试统一决策引擎
            engine = get_unified_decision_engine()
            self.log_test("统一决策引擎初始化", engine is not None)
            
            # 测试组件关联
            self.log_test("组件关联正确", 
                         engine.strategy_registry is registry and 
                         engine.context_analyzer is analyzer)
            
        except Exception as e:
            self.log_test("基础组件初始化", False, str(e))
    
    async def test_strategy_registration(self):
        """测试策略注册功能"""
        print("\n📋 2. 策略注册测试")
        
        try:
            engine = get_unified_decision_engine()
            
            # 注册测试策略
            greeting_strategy = TestGreetingStrategy()
            business_strategy = TestBusinessStrategy()
            
            engine.register_strategy(greeting_strategy)
            engine.register_strategy(business_strategy)
            
            # 验证注册结果
            registered = engine.get_registered_strategies()
            self.log_test("策略注册成功", len(registered) == 2)
            
            # 验证支持的意图
            intents = engine.get_supported_intents()
            expected_intents = {"greeting", "business_requirement"}
            self.log_test("意图支持正确", intents == expected_intents)
            
            # 测试策略注销
            success = engine.unregister_strategy("test_greeting_strategy")
            self.log_test("策略注销成功", success)
            
            registered_after = engine.get_registered_strategies()
            self.log_test("注销后策略数量正确", len(registered_after) == 1)
            
            # 重新注册以供后续测试使用
            engine.register_strategy(greeting_strategy)
            
        except Exception as e:
            self.log_test("策略注册功能", False, str(e))
    
    async def test_context_analysis(self):
        """测试上下文分析功能"""
        print("\n📋 3. 上下文分析测试")
        
        try:
            analyzer = get_context_analyzer()
            
            # 测试意图识别
            intent1 = await analyzer.analyze_intent("你好")
            self.log_test("问候意图识别", intent1 == "greeting")
            
            intent2 = await analyzer.analyze_intent("我想制作一个网站")
            self.log_test("业务需求意图识别", intent2 == "business_requirement")
            
            # 测试情感分析
            emotion1 = await analyzer.analyze_emotion("我很开心")
            self.log_test("积极情感识别", emotion1 == "positive")
            
            emotion2 = await analyzer.analyze_emotion("我很沮丧")
            self.log_test("消极情感识别", emotion2 == "negative")
            
            # 测试实体提取
            entities = await analyzer.extract_entities("我想制作一个网站，预算5000元")
            self.log_test("实体提取功能", 
                         "project_type" in entities and "numbers" in entities)
            
            # 测试完整上下文分析
            context = create_decision_context("test_session", "test_user", "你好，我想制作网站")
            analyzed = await analyzer.analyze(context)
            
            self.log_test("完整上下文分析", 
                         analyzed.intent in ["greeting", "business_requirement"] and
                         analyzed.confidence > 0)
            
        except Exception as e:
            self.log_test("上下文分析功能", False, str(e))
    
    async def test_decision_execution(self):
        """测试决策执行流程"""
        print("\n📋 4. 决策执行测试")
        
        try:
            engine = get_unified_decision_engine()
            
            # 测试问候决策
            context1 = create_decision_context("test_session", "test_user", "你好")
            result1 = await engine.make_decision(context1)
            
            self.log_test("问候决策执行", 
                         result1.action == "send_greeting" and 
                         result1.intent == "greeting")
            
            # 测试业务需求决策
            context2 = create_decision_context("test_session", "test_user", "我想制作网站")
            result2 = await engine.make_decision(context2)
            
            self.log_test("业务需求决策执行",
                         result2.action == "start_requirement_collection" and
                         result2.intent == "business_requirement")
            
            # 测试未知意图处理
            context3 = create_decision_context("test_session", "test_user", "xyz123abc")
            result3 = await engine.make_decision(context3)
            
            self.log_test("未知意图处理", result3.action is not None)
            
            # 验证执行时间记录
            self.log_test("执行时间记录", result1.execution_time > 0)
            
        except Exception as e:
            self.log_test("决策执行流程", False, str(e))
    
    async def test_caching_mechanism(self):
        """测试缓存机制"""
        print("\n📋 5. 缓存机制测试")
        
        try:
            engine = get_unified_decision_engine()
            
            # 清空缓存
            engine.clear_cache()
            
            # 第一次决策
            context = create_decision_context("cache_test", "test_user", "你好")
            start_time = time.time()
            result1 = await engine.make_decision(context)
            first_time = time.time() - start_time
            
            # 第二次相同决策（应该使用缓存）
            start_time = time.time()
            result2 = await engine.make_decision(context)
            second_time = time.time() - start_time
            
            # 验证缓存效果
            self.log_test("缓存命中", 
                         result1.action == result2.action and 
                         second_time < first_time)
            
            # 验证性能指标
            metrics = engine.get_performance_metrics()
            self.log_test("缓存指标记录", 
                         metrics["cache_hits"] > 0 and 
                         metrics["cache_misses"] > 0)
            
        except Exception as e:
            self.log_test("缓存机制", False, str(e))
    
    async def test_conflict_resolution(self):
        """测试冲突检测和解决"""
        print("\n📋 6. 冲突处理测试")

        try:
            engine = get_unified_decision_engine()

            # 清空缓存以确保不使用缓存结果
            engine.clear_cache()

            # 注册冲突策略
            conflict_strategy = TestConflictStrategy()
            engine.register_strategy(conflict_strategy)

            # 触发冲突场景（使用不同的session_id避免缓存）
            context = create_decision_context("conflict_test_unique", "test_user", "你好，请问候我")
            result = await engine.make_decision(context)

            # 验证冲突解决
            self.log_test("冲突检测和解决",
                         result.action in ["send_greeting", "alternative_greeting"])

            # 验证冲突历史记录
            conflict_history = engine.get_conflict_history()
            self.log_test("冲突历史记录", len(conflict_history) > 0)

            # 清理冲突策略
            engine.unregister_strategy("test_conflict_strategy")

        except Exception as e:
            self.log_test("冲突处理", False, str(e))
    
    async def test_performance_monitoring(self):
        """测试性能监控功能"""
        print("\n📋 7. 性能监控测试")
        
        try:
            engine = get_unified_decision_engine()
            
            # 执行多次决策
            for i in range(5):
                context = create_decision_context(f"perf_test_{i}", "test_user", f"测试消息{i}")
                await engine.make_decision(context)
            
            # 验证性能指标
            metrics = engine.get_performance_metrics()
            self.log_test("决策计数正确", metrics["total_decisions"] >= 5)
            self.log_test("平均决策时间记录", metrics["average_decision_time"] > 0)
            
            # 验证决策历史
            history = engine.get_decision_history(limit=10)
            self.log_test("决策历史记录", len(history) >= 5)
            
            # 验证历史记录格式
            if history:
                record = history[0]
                required_fields = ["session_id", "action", "intent", "confidence", "execution_time"]
                has_all_fields = all(field in record for field in required_fields)
                self.log_test("历史记录格式正确", has_all_fields)
            
        except Exception as e:
            self.log_test("性能监控", False, str(e))
    
    async def test_backward_compatibility(self):
        """测试向后兼容性"""
        print("\n📋 8. 向后兼容测试")
        
        try:
            engine = get_unified_decision_engine()
            
            # 测试旧版本analyze方法
            legacy_result = await engine.analyze("你好", [{"session_id": "legacy_test"}])
            
            # 验证返回格式
            required_keys = ["decision", "intent", "emotion", "confidence"]
            has_required_keys = all(key in legacy_result for key in required_keys)
            self.log_test("向后兼容格式", has_required_keys)
            
            # 验证decision子结构
            if "decision" in legacy_result:
                decision = legacy_result["decision"]
                decision_keys = ["action", "priority", "response_template"]
                has_decision_keys = all(key in decision for key in decision_keys)
                self.log_test("决策结构兼容", has_decision_keys)
            
        except Exception as e:
            self.log_test("向后兼容性", False, str(e))
    
    def print_test_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 测试总结")
        print("=" * 60)
        
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        print(f"总测试数: {self.total_tests}")
        print(f"通过测试: {self.passed_tests}")
        print(f"失败测试: {self.total_tests - self.passed_tests}")
        print(f"成功率: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("🎉 测试结果: 优秀！核心框架工作正常")
        elif success_rate >= 70:
            print("✅ 测试结果: 良好，有少量问题需要修复")
        else:
            print("⚠️ 测试结果: 需要重点关注，存在较多问题")
        
        # 显示失败的测试
        failed_tests = [t for t in self.test_results if not t["passed"]]
        if failed_tests:
            print(f"\n❌ 失败的测试 ({len(failed_tests)}个):")
            for test in failed_tests:
                print(f"  - {test['name']}: {test['message']}")


# ==================== 主函数 ====================

async def main():
    """主测试函数"""
    tester = UnifiedDecisionEngineTest()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
