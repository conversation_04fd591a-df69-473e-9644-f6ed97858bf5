#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阈值参数配置测试脚本

测试阈值参数重构后的功能正确性，包括：
1. 配置文件加载测试
2. 阈值参数访问测试
3. 默认值回退测试
4. 重构模块功能测试
"""

import sys
import os
import pytest
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from backend.config.unified_config_loader import get_unified_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestThresholdParameters:
    """阈值参数测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.config = get_unified_config()
    
    def test_config_loading(self):
        """测试配置文件加载"""
        assert self.config is not None
        # 测试配置对象是否正常工作
        test_threshold = self.config.get_threshold("confidence.default", 0.7)
        assert test_threshold is not None
        logger.info(f"阈值配置加载成功，测试阈值: {test_threshold}")
    
    def test_confidence_thresholds(self):
        """测试置信度阈值"""
        # 测试基础置信度阈值
        default_confidence = self.config.get_threshold("confidence.default", 0.7)
        assert isinstance(default_confidence, (int, float))
        assert 0.0 <= default_confidence <= 1.0
        logger.info(f"默认置信度阈值: {default_confidence}")
        
        # 测试高置信度阈值
        high_confidence = self.config.get_threshold("confidence.high", 0.8)
        assert isinstance(high_confidence, (int, float))
        assert 0.0 <= high_confidence <= 1.0
        assert high_confidence >= default_confidence
        logger.info(f"高置信度阈值: {high_confidence}")
        
        # 测试特定场景置信度阈值
        decision_engine = self.config.get_threshold("confidence.decision_engine", 0.7)
        assert isinstance(decision_engine, (int, float))
        assert 0.0 <= decision_engine <= 1.0
        logger.info(f"决策引擎置信度阈值: {decision_engine}")
    
    def test_performance_thresholds(self):
        """测试性能相关阈值"""
        # 测试超时时间
        default_timeout = self.config.get_threshold("performance.timeout.default", 5)
        assert isinstance(default_timeout, (int, float))
        assert default_timeout > 0
        logger.info(f"默认超时时间: {default_timeout}秒")
        
        # 测试重试次数
        default_retry = self.config.get_threshold("performance.retry.default", 3)
        assert isinstance(default_retry, int)
        assert default_retry > 0
        logger.info(f"默认重试次数: {default_retry}次")
        
        # 测试LLM服务超时
        llm_timeout = self.config.get_threshold("performance.timeout.llm_service", 30)
        assert isinstance(llm_timeout, (int, float))
        assert llm_timeout >= default_timeout
        logger.info(f"LLM服务超时时间: {llm_timeout}秒")
    
    def test_limits_thresholds(self):
        """测试数量限制阈值"""
        # 测试默认最大项目数
        max_items = self.config.get_threshold("limits.default_max_items", 10)
        assert isinstance(max_items, int)
        assert max_items > 0
        logger.info(f"默认最大项目数: {max_items}")
        
        # 测试最大结果数
        max_results = self.config.get_threshold("limits.max_results", 50)
        assert isinstance(max_results, int)
        assert max_results >= max_items
        logger.info(f"最大结果数: {max_results}")
        
        # 测试最大并发数
        max_concurrent = self.config.get_threshold("limits.max_concurrent", 5)
        assert isinstance(max_concurrent, int)
        assert max_concurrent > 0
        logger.info(f"最大并发数: {max_concurrent}")
    
    def test_quality_thresholds(self):
        """测试质量控制阈值"""
        # 测试相似度阈值
        similarity = self.config.get_threshold("quality.similarity_threshold", 0.3)
        assert isinstance(similarity, (int, float))
        assert 0.0 <= similarity <= 1.0
        logger.info(f"相似度阈值: {similarity}")
        
        # 测试完整性阈值
        completeness = self.config.get_threshold("quality.completeness_threshold", 0.8)
        assert isinstance(completeness, (int, float))
        assert 0.0 <= completeness <= 1.0
        logger.info(f"完整性阈值: {completeness}")
    
    def test_security_thresholds(self):
        """测试安全相关阈值"""
        # 测试最大登录尝试次数
        max_attempts = self.config.get_threshold("security.max_login_attempts", 5)
        assert isinstance(max_attempts, int)
        assert max_attempts > 0
        logger.info(f"最大登录尝试次数: {max_attempts}")
        
        # 测试会话超时时间
        session_timeout = self.config.get_threshold("security.session_timeout", 1800)
        assert isinstance(session_timeout, int)
        assert session_timeout > 0
        logger.info(f"会话超时时间: {session_timeout}秒")
    
    def test_business_thresholds(self):
        """测试业务逻辑阈值"""
        # 测试需求完成度阈值
        requirement_completion = self.config.get_threshold("business.requirement_completion_threshold", 0.8)
        assert isinstance(requirement_completion, (int, float))
        assert 0.0 <= requirement_completion <= 1.0
        logger.info(f"需求完成度阈值: {requirement_completion}")
        
        # 测试文档质量阈值
        document_quality = self.config.get_threshold("business.document_quality_threshold", 0.8)
        assert isinstance(document_quality, (int, float))
        assert 0.0 <= document_quality <= 1.0
        logger.info(f"文档质量阈值: {document_quality}")
    
    def test_threshold_fallback(self):
        """测试阈值回退机制"""
        # 测试不存在的阈值
        missing_threshold = self.config.get_threshold("nonexistent.threshold", 0.5)
        assert missing_threshold == 0.5
        logger.info("缺失阈值回退机制正常")
        
        # 测试无默认值的不存在阈值
        missing_no_default = self.config.get_threshold("nonexistent.threshold")
        assert missing_no_default is None
        logger.info("无默认值的缺失阈值处理正常")
    
    def test_threshold_consistency(self):
        """测试阈值一致性"""
        # 测试置信度阈值的逻辑关系
        minimum = self.config.get_threshold("confidence.minimum", 0.0)
        low = self.config.get_threshold("confidence.low", 0.6)
        default = self.config.get_threshold("confidence.default", 0.7)
        high = self.config.get_threshold("confidence.high", 0.8)
        very_high = self.config.get_threshold("confidence.very_high", 0.9)
        
        assert minimum <= low <= default <= high <= very_high
        logger.info("置信度阈值逻辑关系正确")
        
        # 测试超时时间的逻辑关系
        short = self.config.get_threshold("performance.timeout.short", 3)
        default_timeout = self.config.get_threshold("performance.timeout.default", 5)
        medium = self.config.get_threshold("performance.timeout.medium", 10)
        long = self.config.get_threshold("performance.timeout.long", 30)
        
        assert short <= default_timeout <= medium <= long
        logger.info("超时时间阈值逻辑关系正确")


def run_threshold_validation():
    """运行阈值参数验证"""
    logger.info("开始阈值参数配置验证...")
    
    try:
        # 创建测试实例
        test_instance = TestThresholdParameters()
        test_instance.setup_method()
        
        # 运行所有测试
        test_methods = [
            test_instance.test_config_loading,
            test_instance.test_confidence_thresholds,
            test_instance.test_performance_thresholds,
            test_instance.test_limits_thresholds,
            test_instance.test_quality_thresholds,
            test_instance.test_security_thresholds,
            test_instance.test_business_thresholds,
            test_instance.test_threshold_fallback,
            test_instance.test_threshold_consistency
        ]
        
        passed = 0
        failed = 0
        
        for test_method in test_methods:
            try:
                test_method()
                logger.info(f"✅ {test_method.__name__} 通过")
                passed += 1
            except Exception as e:
                logger.error(f"❌ {test_method.__name__} 失败: {e}")
                failed += 1
        
        logger.info(f"\n测试结果: {passed} 通过, {failed} 失败")
        
        if failed == 0:
            logger.info("🎉 所有阈值参数测试通过！")
            return True
        else:
            logger.error("⚠️ 部分测试失败，请检查配置")
            return False
            
    except Exception as e:
        logger.error(f"测试运行失败: {e}")
        return False


if __name__ == "__main__":
    success = run_threshold_validation()
    sys.exit(0 if success else 1)
