#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
冗余代码清理验证测试

验证清理工作后系统的核心功能是否正常
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CleanupVerificationTest:
    """清理验证测试类"""
    
    def test_unified_config_loading(self):
        """测试统一配置加载"""
        logger.info("测试统一配置加载...")
        
        try:
            from backend.config.unified_config_loader import get_unified_config
            config = get_unified_config()
            
            # 测试基本配置访问
            assert config is not None
            
            # 测试阈值参数访问
            confidence = config.get_threshold("confidence.default", 0.7)
            assert isinstance(confidence, (int, float))
            
            # 测试消息模板访问
            template = config.get_message_template("system.welcome", default="欢迎")
            assert isinstance(template, str)
            
            logger.info("✅ 统一配置加载正常")
            return True
            
        except Exception as e:
            logger.error(f"❌ 统一配置加载失败: {e}")
            return False
    
    def test_decision_engine_factory(self):
        """测试决策引擎工厂"""
        logger.info("测试决策引擎工厂...")
        
        try:
            from backend.agents.decision_engine_factory import (
                get_decision_engine_factory,
                DecisionEngineType,
                get_recommended_decision_engine
            )
            
            # 测试工厂创建
            factory = get_decision_engine_factory()
            assert factory is not None
            
            # 测试推荐引擎获取
            engine = get_recommended_decision_engine()
            assert engine is not None
            
            # 测试引擎信息
            info = factory.get_engine_info()
            assert isinstance(info, dict)
            assert "default_type" in info
            
            logger.info("✅ 决策引擎工厂正常")
            return True
            
        except Exception as e:
            logger.error(f"❌ 决策引擎工厂失败: {e}")
            return False
    
    def test_simplified_decision_engine(self):
        """测试简化决策引擎"""
        logger.info("测试简化决策引擎...")
        
        try:
            from backend.agents.simplified_decision_engine import get_simplified_decision_engine
            
            # 创建引擎实例
            engine = get_simplified_decision_engine()
            assert engine is not None
            
            # 测试基本属性
            assert hasattr(engine, 'config')
            assert hasattr(engine, 'logger')
            
            logger.info("✅ 简化决策引擎正常")
            return True
            
        except Exception as e:
            logger.error(f"❌ 简化决策引擎失败: {e}")
            return False
    
    def test_configuration_service(self):
        """测试配置服务"""
        logger.info("测试配置服务...")
        
        try:
            from backend.config.service import ConfigurationService
            
            # 创建服务实例
            service = ConfigurationService()
            assert service is not None
            
            # 测试缓存的配置对象
            assert hasattr(service, '_unified_config')
            assert service._unified_config is not None
            
            # 测试LLM配置获取
            llm_config = service.get_llm_config("conversation")
            assert isinstance(llm_config, dict)
            
            logger.info("✅ 配置服务正常")
            return True
            
        except Exception as e:
            logger.error(f"❌ 配置服务失败: {e}")
            return False
    
    def test_conversation_history_service(self):
        """测试对话历史服务"""
        logger.info("测试对话历史服务...")
        
        try:
            from backend.services.conversation_history_service import HistoryConfig
            
            # 测试配置类
            config = HistoryConfig.from_unified_config()
            assert config is not None
            assert isinstance(config.max_turns, int)
            assert isinstance(config.max_message_length, int)
            
            logger.info("✅ 对话历史服务正常")
            return True
            
        except Exception as e:
            logger.error(f"❌ 对话历史服务失败: {e}")
            return False
    
    def test_safety_manager(self):
        """测试安全管理器"""
        logger.info("测试安全管理器...")
        
        try:
            from backend.utils.safety_manager import SafetyManager
            
            # 创建安全管理器实例
            manager = SafetyManager()
            assert manager is not None
            
            # 测试缓存的配置对象
            assert hasattr(manager, 'unified_config')
            assert manager.unified_config is not None
            
            logger.info("✅ 安全管理器正常")
            return True
            
        except Exception as e:
            logger.error(f"❌ 安全管理器失败: {e}")
            return False
    
    def test_import_cleanup(self):
        """测试导入清理效果"""
        logger.info("测试导入清理效果...")
        
        try:
            # 测试清理后的导入
            from backend.agents.context_analyzer import ContextAnalyzer
            from backend.agents.decision_monitor import DecisionMonitor
            from backend.agents.conversation_state_machine import ConversationStateMachine
            
            # 这些模块应该能正常导入
            assert ContextAnalyzer is not None
            assert DecisionMonitor is not None
            assert ConversationStateMachine is not None
            
            logger.info("✅ 导入清理效果正常")
            return True
            
        except Exception as e:
            logger.error(f"❌ 导入清理效果异常: {e}")
            return False


def run_verification_tests():
    """运行验证测试"""
    logger.info("开始冗余代码清理验证测试...")
    
    test_instance = CleanupVerificationTest()
    
    test_methods = [
        test_instance.test_unified_config_loading,
        test_instance.test_decision_engine_factory,
        test_instance.test_simplified_decision_engine,
        test_instance.test_configuration_service,
        test_instance.test_conversation_history_service,
        test_instance.test_safety_manager,
        test_instance.test_import_cleanup
    ]
    
    passed = 0
    failed = 0
    
    for test_method in test_methods:
        try:
            if test_method():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"❌ {test_method.__name__} 异常: {e}")
            failed += 1
    
    logger.info(f"\n验证测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        logger.info("🎉 所有验证测试通过！冗余代码清理成功！")
        return True
    else:
        logger.error("⚠️ 部分验证测试失败，请检查清理代码")
        return False


if __name__ == "__main__":
    success = run_verification_tests()
    sys.exit(0 if success else 1)
