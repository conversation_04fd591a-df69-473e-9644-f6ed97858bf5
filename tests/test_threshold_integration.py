#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阈值参数集成测试

测试重构后的模块是否能正确使用阈值参数配置
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from backend.config.unified_config_loader import get_unified_config
from backend.agents.simplified_decision_engine import SimplifiedDecisionEngine
from backend.config.service import ConfigurationService
from backend.utils.safety_manager import SafetyManager
from backend.utils.intent_manager import IntentManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestThresholdIntegration:
    """阈值参数集成测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.config = get_unified_config()
    
    def test_decision_engine_thresholds(self):
        """测试决策引擎使用阈值"""
        logger.info("测试决策引擎阈值使用...")
        
        try:
            # 创建决策引擎实例
            engine = SimplifiedDecisionEngine()
            
            # 测试决策引擎是否能正常工作
            assert engine is not None
            logger.info("✅ 决策引擎创建成功")
            
            # 验证决策引擎使用了配置中的阈值
            # 这里我们通过检查配置是否正确加载来验证
            confidence_threshold = self.config.get_threshold("confidence.high", 0.8)
            assert confidence_threshold == 0.8
            logger.info(f"✅ 决策引擎置信度阈值配置正确: {confidence_threshold}")
            
        except Exception as e:
            logger.error(f"决策引擎阈值测试失败: {e}")
            raise
    
    def test_config_service_thresholds(self):
        """测试配置服务使用阈值"""
        logger.info("测试配置服务阈值使用...")
        
        try:
            # 创建配置服务实例
            service = ConfigurationService()
            
            # 测试获取LLM配置
            llm_config = service.get_llm_config("conversation")
            assert llm_config is not None
            assert "temperature" in llm_config
            assert "max_tokens" in llm_config
            logger.info(f"✅ LLM配置获取成功: temperature={llm_config.get('temperature')}")
            
            # 验证配置使用了阈值参数
            temperature = llm_config.get("temperature")
            expected_temp = self.config.get_threshold("confidence.default", 0.7)
            # 温度值应该在合理范围内
            assert 0.0 <= temperature <= 2.0
            logger.info(f"✅ 配置服务阈值使用正确: {temperature}")
            
        except Exception as e:
            logger.error(f"配置服务阈值测试失败: {e}")
            raise
    
    def test_safety_manager_thresholds(self):
        """测试安全管理器使用阈值"""
        logger.info("测试安全管理器阈值使用...")
        
        try:
            # 创建安全管理器实例
            safety_manager = SafetyManager()
            
            # 测试安全管理器是否正常初始化
            assert safety_manager is not None
            assert safety_manager.config is not None
            logger.info("✅ 安全管理器创建成功")
            
            # 验证安全配置使用了阈值参数
            error_thresholds = safety_manager.config.get("error_thresholds", {})
            assert "max_consecutive_errors" in error_thresholds
            
            max_errors = error_thresholds["max_consecutive_errors"]
            expected_max = self.config.get_threshold("security.max_login_attempts", 5)
            assert max_errors == expected_max
            logger.info(f"✅ 安全管理器阈值配置正确: max_errors={max_errors}")
            
        except Exception as e:
            logger.error(f"安全管理器阈值测试失败: {e}")
            raise
    
    def test_intent_manager_thresholds(self):
        """测试意图管理器使用阈值"""
        logger.info("测试意图管理器阈值使用...")
        
        try:
            # 创建意图管理器实例
            intent_manager = IntentManager()
            
            # 测试意图管理器是否正常初始化
            assert intent_manager is not None
            logger.info("✅ 意图管理器创建成功")
            
            # 测试置信度阈值获取
            confidence_thresholds = intent_manager.get_confidence_thresholds()
            assert isinstance(confidence_thresholds, dict)
            assert "high" in confidence_thresholds
            assert "medium" in confidence_thresholds
            assert "low" in confidence_thresholds
            
            # 验证阈值使用了配置参数
            high_threshold = confidence_thresholds["high"]
            expected_high = self.config.get_threshold("confidence.high", 0.8)
            assert high_threshold == expected_high
            logger.info(f"✅ 意图管理器置信度阈值正确: high={high_threshold}")
            
        except Exception as e:
            logger.error(f"意图管理器阈值测试失败: {e}")
            raise
    
    def test_threshold_consistency_across_modules(self):
        """测试模块间阈值一致性"""
        logger.info("测试模块间阈值一致性...")
        
        try:
            # 获取各模块使用的相同阈值参数
            default_confidence = self.config.get_threshold("confidence.default", 0.7)
            high_confidence = self.config.get_threshold("confidence.high", 0.8)
            default_timeout = self.config.get_threshold("performance.timeout.default", 5)
            default_retry = self.config.get_threshold("performance.retry.default", 3)
            
            # 验证阈值的合理性
            assert 0.0 <= default_confidence <= 1.0
            assert 0.0 <= high_confidence <= 1.0
            assert high_confidence >= default_confidence
            assert default_timeout > 0
            assert default_retry > 0
            
            logger.info(f"✅ 阈值一致性检查通过:")
            logger.info(f"   默认置信度: {default_confidence}")
            logger.info(f"   高置信度: {high_confidence}")
            logger.info(f"   默认超时: {default_timeout}秒")
            logger.info(f"   默认重试: {default_retry}次")
            
        except Exception as e:
            logger.error(f"阈值一致性测试失败: {e}")
            raise
    
    def test_threshold_performance_impact(self):
        """测试阈值参数对性能的影响"""
        logger.info("测试阈值参数性能影响...")
        
        try:
            import time
            
            # 测试配置加载性能
            start_time = time.time()
            for _ in range(100):
                self.config.get_threshold("confidence.default", 0.7)
            end_time = time.time()
            
            avg_time = (end_time - start_time) / 100
            assert avg_time < 0.001  # 平均每次调用应该小于1毫秒
            
            logger.info(f"✅ 阈值参数访问性能良好: 平均{avg_time*1000:.3f}ms/次")
            
        except Exception as e:
            logger.error(f"性能测试失败: {e}")
            raise
    
    def test_threshold_error_handling(self):
        """测试阈值参数错误处理"""
        logger.info("测试阈值参数错误处理...")
        
        try:
            # 测试不存在的阈值参数
            missing_threshold = self.config.get_threshold("nonexistent.parameter", 42)
            assert missing_threshold == 42
            
            # 测试无默认值的不存在参数
            missing_no_default = self.config.get_threshold("nonexistent.parameter")
            assert missing_no_default is None
            
            logger.info("✅ 阈值参数错误处理正确")
            
        except Exception as e:
            logger.error(f"错误处理测试失败: {e}")
            raise


def run_integration_tests():
    """运行集成测试"""
    logger.info("开始阈值参数集成测试...")
    
    try:
        # 创建测试实例
        test_instance = TestThresholdIntegration()
        test_instance.setup_method()
        
        # 运行所有测试
        test_methods = [
            test_instance.test_decision_engine_thresholds,
            test_instance.test_config_service_thresholds,
            test_instance.test_safety_manager_thresholds,
            test_instance.test_intent_manager_thresholds,
            test_instance.test_threshold_consistency_across_modules,
            test_instance.test_threshold_performance_impact,
            test_instance.test_threshold_error_handling
        ]
        
        passed = 0
        failed = 0
        
        for test_method in test_methods:
            try:
                test_method()
                logger.info(f"✅ {test_method.__name__} 通过")
                passed += 1
            except Exception as e:
                logger.error(f"❌ {test_method.__name__} 失败: {e}")
                failed += 1
        
        logger.info(f"\n集成测试结果: {passed} 通过, {failed} 失败")
        
        if failed == 0:
            logger.info("🎉 所有集成测试通过！阈值参数重构成功！")
            return True
        else:
            logger.error("⚠️ 部分集成测试失败，请检查重构代码")
            return False
            
    except Exception as e:
        logger.error(f"集成测试运行失败: {e}")
        return False


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
