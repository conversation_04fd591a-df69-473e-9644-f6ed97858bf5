"""
统一决策引擎完整功能验证测试

测试所有业务场景和性能指标，确保系统完整性
"""

import asyncio
import time
import pytest
from typing import Dict, Any

from backend.agents.unified_decision_engine import get_unified_decision_engine
from backend.agents.decision_engine_adapter import get_decision_engine_adapter
from backend.agents.decision_types import create_decision_context
from backend.agents.unified_state_manager import ConversationState


class TestUnifiedDecisionEngineComplete:
    """统一决策引擎完整功能测试"""
    
    @pytest.fixture
    def engine(self):
        """获取统一决策引擎实例"""
        return get_unified_decision_engine()
    
    @pytest.fixture
    def adapter(self):
        """获取决策引擎适配器实例"""
        return get_decision_engine_adapter(use_unified_engine=True)
    
    def create_test_context(self, message: str, state: ConversationState = ConversationState.IDLE):
        """创建测试上下文"""
        return create_decision_context(
            session_id='test_session',
            user_id='test_user',
            message=message,
            current_state=state
        )
    
    @pytest.mark.asyncio
    async def test_greeting_scenarios(self, engine):
        """测试问候场景"""
        greeting_messages = [
            "你好", "hi", "hello", "早上好", "晚上好",
            "您好", "嗨", "哈喽", "good morning"
        ]
        
        for message in greeting_messages:
            context = self.create_test_context(message)
            result = await engine.make_decision(context)
            
            assert result.intent == "greeting"
            assert result.strategy_name == "greeting_strategy"
            assert "greeting" in result.action.lower()
            assert result.confidence > 0.8
    
    @pytest.mark.asyncio
    async def test_requirement_scenarios(self, engine):
        """测试需求收集场景"""
        requirement_messages = [
            "我想制作一个网站",
            "需要开发一个APP",
            "想要设计一个logo",
            "帮我写一份商业计划书",
            "我要做电商平台"
        ]
        
        for message in requirement_messages:
            context = self.create_test_context(message)
            result = await engine.make_decision(context)
            
            assert result.intent == "business_requirement"
            assert result.strategy_name == "requirement_strategy"
            assert result.confidence > 0.7
    
    @pytest.mark.asyncio
    async def test_knowledge_base_scenarios(self, engine):
        """测试知识库查询场景"""
        knowledge_messages = [
            "如何注册账号？",
            "什么是云计算？",
            "怎么使用这个功能？",
            "价格是多少？",
            "有什么功能？"
        ]
        
        for message in knowledge_messages:
            context = self.create_test_context(message)
            result = await engine.make_decision(context)
            
            assert result.intent == "search_knowledge_base"
            assert result.strategy_name == "knowledge_base_strategy"
            assert result.confidence > 0.6
    
    @pytest.mark.asyncio
    async def test_capabilities_scenarios(self, engine):
        """测试能力介绍场景"""
        capability_messages = [
            "你能做什么？",
            "有什么功能？",
            "你会什么？",
            "能帮我什么？",
            "介绍一下你的能力"
        ]
        
        for message in capability_messages:
            context = self.create_test_context(message)
            result = await engine.make_decision(context)
            
            assert result.intent == "ask_capabilities"
            assert result.strategy_name == "capabilities_strategy"
            assert result.confidence > 0.7
    
    @pytest.mark.asyncio
    async def test_emotional_support_scenarios(self, engine):
        """测试情感支持场景"""
        emotional_messages = [
            "我很沮丧",
            "感觉很累",
            "太开心了",
            "我很愤怒",
            "心情不好"
        ]
        
        for message in emotional_messages:
            context = self.create_test_context(message)
            result = await engine.make_decision(context)
            
            assert result.intent == "emotional_support"
            assert result.strategy_name == "emotional_support_strategy"
            assert result.confidence > 0.5
    
    @pytest.mark.asyncio
    async def test_fallback_scenarios(self, engine):
        """测试回退场景"""
        fallback_messages = [
            "xyz123abc",
            "随机文本",
            "不知道说什么",
            "测试消息",
            "blah blah"
        ]
        
        for message in fallback_messages:
            context = self.create_test_context(message)
            result = await engine.make_decision(context)
            
            assert result.strategy_name == "fallback_strategy"
            assert result.confidence > 0.0
    
    @pytest.mark.asyncio
    async def test_performance_metrics(self, engine):
        """测试性能指标"""
        test_messages = [
            "你好", "我想制作网站", "如何注册？", 
            "你能做什么？", "我很开心", "随机文本"
        ]
        
        start_time = time.time()
        
        for message in test_messages:
            context = self.create_test_context(message)
            result = await engine.make_decision(context)
            assert result is not None
        
        total_time = time.time() - start_time
        avg_time = total_time / len(test_messages)
        
        # 性能要求：平均响应时间 < 200ms
        assert avg_time < 0.2, f"平均响应时间过长: {avg_time:.3f}s"
        
        # 获取性能统计
        stats = engine.get_performance_stats()
        assert stats is not None
        assert "overall" in stats
        assert stats["overall"]["total_requests"] >= len(test_messages)
    
    @pytest.mark.asyncio
    async def test_caching_effectiveness(self, engine):
        """测试缓存效果"""
        message = "你好"
        context = self.create_test_context(message)
        
        # 第一次调用
        start_time = time.time()
        result1 = await engine.make_decision(context)
        first_call_time = time.time() - start_time
        
        # 第二次调用（应该命中缓存）
        start_time = time.time()
        result2 = await engine.make_decision(context)
        second_call_time = time.time() - start_time
        
        # 验证结果一致性
        assert result1.action == result2.action
        assert result1.intent == result2.intent
        assert result1.strategy_name == result2.strategy_name
        
        # 验证缓存效果（第二次调用应该更快）
        assert second_call_time <= first_call_time
        
        # 检查缓存统计
        stats = engine.get_performance_stats()
        if "cache" in stats:
            assert stats["cache"]["total_requests"] >= 2
            assert stats["cache"]["cache_hits"] >= 1
    
    @pytest.mark.asyncio
    async def test_adapter_compatibility(self, adapter):
        """测试适配器兼容性"""
        context = [{
            'session_id': 'test_session',
            'user_id': 'test_user',
            'current_state': 'IDLE',
            'conversation_history': []
        }]
        
        test_messages = [
            "你好", "我想制作网站", "如何注册？"
        ]
        
        for message in test_messages:
            result = await adapter.analyze(message, context)
            
            # 验证返回格式兼容性
            assert "decision" in result
            assert "intent" in result
            assert "emotion" in result
            assert "confidence" in result
            assert "metadata" in result
            
            # 验证决策字段
            decision = result["decision"]
            assert "action" in decision
            assert "priority" in decision
            assert "response_template" in decision
            assert "next_state" in decision
    
    @pytest.mark.asyncio
    async def test_state_transitions(self, engine):
        """测试状态转换"""
        # 测试不同状态下的决策
        states_to_test = [
            ConversationState.IDLE,
            ConversationState.COLLECTING_REQUIREMENTS,
            ConversationState.DOCUMENTING,
            ConversationState.WAITING_CONFIRMATION
        ]
        
        for state in states_to_test:
            context = self.create_test_context("你好", state)
            result = await engine.make_decision(context)
            
            assert result is not None
            assert result.next_state is not None
    
    @pytest.mark.asyncio
    async def test_error_handling(self, engine):
        """测试错误处理"""
        # 测试空消息
        context = self.create_test_context("")
        result = await engine.make_decision(context)
        assert result is not None
        
        # 测试超长消息
        long_message = "测试" * 1000
        context = self.create_test_context(long_message)
        result = await engine.make_decision(context)
        assert result is not None
        
        # 测试特殊字符
        special_message = "!@#$%^&*()_+{}|:<>?[]\\;'\",./"
        context = self.create_test_context(special_message)
        result = await engine.make_decision(context)
        assert result is not None
    
    def test_monitoring_data(self, engine):
        """测试监控数据"""
        # 获取性能统计
        stats = engine.get_performance_stats()
        assert isinstance(stats, dict)
        
        # 获取错误记录
        errors = engine.get_recent_errors()
        assert isinstance(errors, list)
        
        # 获取性能趋势
        trends = engine.get_performance_trends()
        assert isinstance(trends, dict)
        assert "timestamps" in trends
        assert "response_times" in trends
        assert "success_rates" in trends


async def run_complete_test():
    """运行完整测试"""
    print("🧪 开始统一决策引擎完整功能验证...")
    
    test_instance = TestUnifiedDecisionEngineComplete()
    engine = get_unified_decision_engine()
    adapter = get_decision_engine_adapter(use_unified_engine=True)
    
    # 运行所有测试
    test_methods = [
        ("问候场景", test_instance.test_greeting_scenarios(engine)),
        ("需求收集场景", test_instance.test_requirement_scenarios(engine)),
        ("知识库查询场景", test_instance.test_knowledge_base_scenarios(engine)),
        ("能力介绍场景", test_instance.test_capabilities_scenarios(engine)),
        ("情感支持场景", test_instance.test_emotional_support_scenarios(engine)),
        ("回退场景", test_instance.test_fallback_scenarios(engine)),
        ("性能指标", test_instance.test_performance_metrics(engine)),
        ("缓存效果", test_instance.test_caching_effectiveness(engine)),
        ("适配器兼容性", test_instance.test_adapter_compatibility(adapter)),
        ("状态转换", test_instance.test_state_transitions(engine)),
        ("错误处理", test_instance.test_error_handling(engine))
    ]
    
    passed_tests = 0
    failed_tests = 0
    
    for test_name, test_coro in test_methods:
        try:
            await test_coro
            print(f"✅ {test_name}: 通过")
            passed_tests += 1
        except Exception as e:
            print(f"❌ {test_name}: 失败 - {e}")
            failed_tests += 1
    
    # 运行同步测试
    try:
        test_instance.test_monitoring_data(engine)
        print("✅ 监控数据: 通过")
        passed_tests += 1
    except Exception as e:
        print(f"❌ 监控数据: 失败 - {e}")
        failed_tests += 1
    
    # 输出最终统计
    total_tests = passed_tests + failed_tests
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"\n📊 测试结果统计:")
    print(f"  总测试数: {total_tests}")
    print(f"  通过: {passed_tests}")
    print(f"  失败: {failed_tests}")
    print(f"  成功率: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("\n🎉 统一决策引擎功能验证完成！系统运行正常。")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查。")
    
    return success_rate >= 90


if __name__ == "__main__":
    asyncio.run(run_complete_test())
