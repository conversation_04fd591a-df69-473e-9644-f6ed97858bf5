#!/usr/bin/env python3
"""
结构化意图分类测试脚本
测试新的结构化意图分类LLM对各种意图表达的识别能力，特别是价格相关表达和复合意图
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from backend.agents.simplified_decision_engine import SimplifiedDecisionEngine
from backend.config.unified_config_loader import get_unified_config


class StructuredIntentTester:
    """结构化意图分类测试器"""
    
    def __init__(self):
        self.config = get_unified_config()
        self.decision_engine = None
        
    async def initialize(self):
        """初始化决策引擎"""
        try:
            self.decision_engine = SimplifiedDecisionEngine()
            print("✅ 决策引擎初始化成功")
        except Exception as e:
            print(f"❌ 决策引擎初始化失败: {e}")
            return False
        return True
    
    def get_test_cases(self):
        """获取测试用例"""
        return [
            {
                "name": "原始问题",
                "input": "我对设计这张海报具体需要多少钱没有概念，您能给我建议吗？",
                "expected_intent": "business_requirement",
                "context": [{"current_state": "IDLE"}]
            },
            {
                "name": "简化版本",
                "input": "具体需要多少钱没有概念，您能给我建议吗？",
                "expected_intent": "business_requirement", 
                "context": [{"current_state": "IDLE"}]
            },
            {
                "name": "预算咨询",
                "input": "这个项目大概需要多少预算？",
                "expected_intent": "business_requirement",
                "context": [{"current_state": "IDLE"}]
            },
            {
                "name": "成本询问",
                "input": "开发这样的app费用大概是多少？",
                "expected_intent": "business_requirement",
                "context": [{"current_state": "IDLE"}]
            },
            {
                "name": "明确知识库查询",
                "input": "你们的收费标准是什么？",
                "expected_intent": "search_knowledge_base",
                "context": [{"current_state": "IDLE"}]
            },
            {
                "name": "价格表查询",
                "input": "价格表在哪里可以看到？",
                "expected_intent": "search_knowledge_base",
                "context": [{"current_state": "IDLE"}]
            },
            {
                "name": "套餐查询",
                "input": "有什么套餐可以选择？",
                "expected_intent": "search_knowledge_base",
                "context": [{"current_state": "IDLE"}]
            },
            {
                "name": "边界情况1",
                "input": "你们平台设计海报多少钱？",
                "expected_intent": "search_knowledge_base",  # 这个可能会有争议
                "context": [{"current_state": "IDLE"}]
            },
            {
                "name": "边界情况2",
                "input": "价格是多少？",
                "expected_intent": "search_knowledge_base",
                "context": [{"current_state": "IDLE"}]
            },
            {
                "name": "收集状态下的价格咨询",
                "input": "我不知道这个需要多少成本",
                "expected_intent": "process_answer",
                "context": [{"current_state": "COLLECTING_INFO"}]
            },
            {
                "name": "复合意图测试1",
                "input": "我要开发一个网站，你们平台支持哪些技术栈？",
                "expected_intent": "business_requirement",  # 主要意图
                "context": [{"current_state": "IDLE"}]
            },
            {
                "name": "复合意图测试2",
                "input": "帮我设计海报，顺便问一下价格表在哪里？",
                "expected_intent": "business_requirement",  # 主要意图
                "context": [{"current_state": "IDLE"}]
            },
            {
                "name": "单一意图误判测试",
                "input": "我想做网站，需要电商功能",
                "expected_intent": "business_requirement",  # 应该是单一意图
                "context": [{"current_state": "IDLE"}]
            }
        ]
    
    async def test_single_case(self, test_case):
        """测试单个用例"""
        try:
            print(f"\n🧪 测试用例: {test_case['name']}")
            print(f"📝 输入: {test_case['input']}")
            print(f"🎯 期望意图: {test_case['expected_intent']}")
            print(f"🔄 状态: {test_case['context'][0]['current_state']}")
            
            # 执行意图识别
            result = await self.decision_engine.analyze(
                test_case['input'], 
                test_case['context']
            )
            
            # 提取结果
            actual_intent = result.get('intent', 'unknown')
            confidence = result.get('confidence', 0.0)
            emotion = result.get('emotion', 'unknown')
            metadata = result.get('metadata', {})
            
            # 判断是否成功
            success = actual_intent == test_case['expected_intent']
            
            print(f"📊 实际意图: {actual_intent}")
            print(f"📈 置信度: {confidence:.2f}")
            print(f"😊 情绪: {emotion}")
            print(f"{'✅ 通过' if success else '❌ 失败'}")
            
            if not success:
                print(f"⚠️  期望 {test_case['expected_intent']}, 实际 {actual_intent}")
            
            # 显示处理方法
            processing_method = metadata.get('processing_method', 'unknown')
            print(f"🔧 处理方法: {processing_method}")
            
            return {
                'test_name': test_case['name'],
                'input': test_case['input'],
                'expected_intent': test_case['expected_intent'],
                'actual_intent': actual_intent,
                'confidence': confidence,
                'emotion': emotion,
                'success': success,
                'processing_method': processing_method,
                'full_result': result
            }
            
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            return {
                'test_name': test_case['name'],
                'input': test_case['input'],
                'error': str(e),
                'success': False
            }
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始价格意图识别测试")
        print("=" * 60)
        
        if not await self.initialize():
            return
        
        test_cases = self.get_test_cases()
        results = []
        
        for test_case in test_cases:
            result = await self.test_single_case(test_case)
            results.append(result)
        
        # 统计结果
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r.get('success', False))
        success_rate = successful_tests / total_tests * 100 if total_tests > 0 else 0
        
        print("\n" + "=" * 60)
        print("📊 测试结果统计")
        print(f"总测试数: {total_tests}")
        print(f"成功数: {successful_tests}")
        print(f"失败数: {total_tests - successful_tests}")
        print(f"成功率: {success_rate:.1f}%")
        
        # 分析失败的测试
        failed_tests = [r for r in results if not r.get('success', False)]
        if failed_tests:
            print("\n❌ 失败的测试用例:")
            for test in failed_tests:
                print(f"  - {test['test_name']}: 期望 {test.get('expected_intent', 'N/A')}, 实际 {test.get('actual_intent', 'N/A')}")
        
        # 保存详细结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"tests/reports/price_intent_test_{timestamp}.json"
        
        try:
            os.makedirs("tests/reports", exist_ok=True)
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'summary': {
                        'total_tests': total_tests,
                        'successful_tests': successful_tests,
                        'success_rate': success_rate
                    },
                    'results': results
                }, f, ensure_ascii=False, indent=2)
            print(f"\n📄 详细报告已保存到: {report_file}")
        except Exception as e:
            print(f"⚠️  保存报告失败: {e}")
        
        return results


async def main():
    """主函数"""
    tester = StructuredIntentTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
