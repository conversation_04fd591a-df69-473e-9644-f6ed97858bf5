{"test_run_info": {"timestamp": "2025-08-09T16:51:21.435460", "total_execution_time": 0.1981513500213623, "test_session_id": "test_session_1754729481", "test_user_id": "test_user_1754729481"}, "summary": {"total_test_suites": 5, "total_tests": 15, "total_passed": 3, "total_failed": 12, "success_rate": 20.0}, "suite_results": {"knowledge_base_query": {"suite_name": "knowledge_base_query", "description": "知识库查询功能测试", "test_results": ["TestResult(test_name='basic_faq_query', test_type='knowledge_base_query', input_data={'query': '如何注册开发者账号？', 'role_filter': 'developer'}, expected_output={'expected_keywords': ['注册', '开发者', '账号'], 'min_confidence': 0.7}, actual_output={'error': \"RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'\", 'available': False}, success=False, error_message=\"执行错误: RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'\", execution_time=0.16643500328063965, timestamp='2025-08-09T16:51:21.403867')", "TestResult(test_name='feature_explanation_query', test_type='knowledge_base_query', input_data={'query': '需求采集系统有哪些功能？', 'role_filter': 'company'}, expected_output={'expected_keywords': ['需求采集', '功能'], 'min_confidence': 0.6}, actual_output={'error': \"RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'\", 'available': False}, success=False, error_message=\"执行错误: RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'\", execution_time=7.987022399902344e-05, timestamp='2025-08-09T16:51:21.403957')", "TestResult(test_name='tutorial_query', test_type='knowledge_base_query', input_data={'query': '如何使用API接口？', 'category_filter': 'api'}, expected_output={'expected_keywords': ['API', '接口', '使用'], 'min_confidence': 0.7}, actual_output={'error': \"RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'\", 'available': False}, success=False, error_message=\"执行错误: RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'\", execution_time=7.295608520507812e-05, timestamp='2025-08-09T16:51:21.404035')", "TestResult(test_name='no_result_query', test_type='knowledge_base_query', input_data={'query': '火星上的天气如何？'}, expected_output={'expected_fallback': True, 'min_confidence': 0.0}, actual_output={'error': \"RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'\", 'available': False}, success=False, error_message=\"执行错误: RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'\", execution_time=0.00021791458129882812, timestamp='2025-08-09T16:51:21.404263')"], "summary": {"total_tests": 4, "passed": 0, "failed": 4, "execution_time": 0.1668381690979004}}, "focus_points_flow": {"suite_name": "focus_points_flow", "description": "关注点流程完整性测试", "test_results": ["TestResult(test_name='app_development_flow', test_type='focus_points_flow', input_data={'message': '我想开发一个电商App', 'expected_domain': 'LY_005', 'expected_category': 'LB_022'}, expected_output={'min_coverage': 0.8}, actual_output={'error': \"'AgentFactory' object has no attribute 'create_conversation_flow_agent'\", 'message': '我想开发一个电商App'}, success=False, error_message=\"执行错误: 'AgentFactory' object has no attribute 'create_conversation_flow_agent'\", execution_time=4.1961669921875e-05, timestamp='2025-08-09T16:51:21.404443')", "TestResult(test_name='website_development_flow', test_type='focus_points_flow', input_data={'message': '我需要制作一个企业官网', 'expected_domain': 'LY_005', 'expected_category': 'LB_021'}, expected_output={'min_coverage': 0.7}, actual_output={'error': \"'AgentFactory' object has no attribute 'create_conversation_flow_agent'\", 'message': '我需要制作一个企业官网'}, success=False, error_message=\"执行错误: 'AgentFactory' object has no attribute 'create_conversation_flow_agent'\", execution_time=4.00543212890625e-05, timestamp='2025-08-09T16:51:21.404486')"], "summary": {"total_tests": 2, "passed": 0, "failed": 2, "execution_time": 9.083747863769531e-05}}, "composite_intent_recognition": {"suite_name": "composite_intent_recognition", "description": "复合意图识别准确性测试", "test_results": ["TestResult(test_name='business_requirement_intent', test_type='composite_intent_recognition', input_data={'message': '我想开发一个在线教育平台'}, expected_output={'expected_intent': 'business_requirement', 'expected_sub_intent': 'software_development', 'min_confidence': 0.8}, actual_output={'error': \"'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'\", 'message': '我想开发一个在线教育平台'}, success=False, error_message=\"执行错误: 'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'\", execution_time=0.008319854736328125, timestamp='2025-08-09T16:51:21.413018')", "TestResult(test_name='knowledge_query_intent', test_type='composite_intent_recognition', input_data={'message': '系统支持哪些编程语言？'}, expected_output={'expected_intent': 'knowledge_query', 'min_confidence': 0.7}, actual_output={'error': \"'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'\", 'message': '系统支持哪些编程语言？'}, success=False, error_message=\"执行错误: 'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'\", execution_time=0.0074460506439208984, timestamp='2025-08-09T16:51:21.420476')", "TestResult(test_name='greeting_intent', test_type='composite_intent_recognition', input_data={'message': '你好，我是新用户'}, expected_output={'expected_intent': 'greeting', 'min_confidence': 0.9}, actual_output={'error': \"'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'\", 'message': '你好，我是新用户'}, success=False, error_message=\"执行错误: 'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'\", execution_time=0.0071828365325927734, timestamp='2025-08-09T16:51:21.427669')", "TestResult(test_name='mixed_intent', test_type='composite_intent_recognition', input_data={'message': '你好，我想了解一下如何开发移动应用，系统能帮我做需求分析吗？'}, expected_output={'min_confidence': 0.6}, actual_output={'error': \"'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'\", 'message': '你好，我想了解一下如何开发移动应用，系统能帮我做需求分析吗？'}, success=False, error_message=\"执行错误: 'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'\", execution_time=0.007234096527099609, timestamp='2025-08-09T16:51:21.434910')"], "summary": {"total_tests": 4, "passed": 0, "failed": 4, "execution_time": 0.03022003173828125}}, "decision_strategy": {"suite_name": "decision_strategy", "description": "决策策略正确性测试", "test_results": ["TestResult(test_name='threshold_boundary_test', test_type='decision_strategy', input_data={'confidence_values': [0.5, 0.7, 0.8, 0.9, 1.0], 'threshold_key': 'confidence.default'}, expected_output={}, actual_output={'threshold_tests': {'0.5': 'low_confidence_handling', '0.7': 'normal_processing', '0.8': 'high_confidence_processing', '0.9': 'very_high_confidence_processing', '1.0': 'very_high_confidence_processing'}}, success=True, error_message=None, execution_time=5.507469177246094e-05, timestamp='2025-08-09T16:51:21.435147')", "TestResult(test_name='business_rule_consistency', test_type='decision_strategy', input_data={'scenarios': [{'domain': 'LY_005', 'category': 'LB_022', 'expected_action': 'start_requirement_collection'}, {'domain': 'LY_001', 'category': 'LB_001', 'expected_action': 'start_requirement_collection'}, {'domain': 'NULL', 'category': 'NULL', 'expected_action': 'request_clarification'}]}, expected_output={}, actual_output={'business_rule_tests': [{'scenario': {'domain': 'LY_005', 'category': 'LB_022', 'expected_action': 'start_requirement_collection'}, 'decision': 'start_requirement_collection'}, {'scenario': {'domain': 'LY_001', 'category': 'LB_001', 'expected_action': 'start_requirement_collection'}, 'decision': 'start_requirement_collection'}, {'scenario': {'domain': 'NULL', 'category': 'NULL', 'expected_action': 'request_clarification'}, 'decision': 'request_clarification'}]}, success=True, error_message=None, execution_time=4.100799560546875e-05, timestamp='2025-08-09T16:51:21.435192')", "TestResult(test_name='fallback_mechanism_test', test_type='decision_strategy', input_data={'error_scenarios': ['llm_service_timeout', 'database_connection_error', 'invalid_configuration']}, expected_output={}, actual_output={'fallback_tests': [{'error_scenario': 'llm_service_timeout', 'fallback_action': 'use_cached_response'}, {'error_scenario': 'database_connection_error', 'fallback_action': 'use_default_template'}, {'error_scenario': 'invalid_configuration', 'fallback_action': 'graceful_degradation'}]}, success=True, error_message=None, execution_time=3.504753112792969e-05, timestamp='2025-08-09T16:51:21.435230')"], "summary": {"total_tests": 3, "passed": 3, "failed": 0, "execution_time": 0.00014209747314453125}}, "end_to_end_integration": {"suite_name": "end_to_end_integration", "description": "端到端业务流程集成测试", "test_results": ["TestResult(test_name='complete_requirement_collection_flow', test_type='end_to_end_integration', input_data={'conversation_flow': ['我想开发一个社交媒体应用', '主要功能包括用户注册、发布动态、私信聊天', '目标用户是18-35岁的年轻人', '预算大概50万，希望6个月内完成', '使用React Native开发，支持iOS和Android']}, expected_output={'expected_outcomes': {'domain_classification': 'LY_005', 'category_classification': 'LB_022', 'focus_points_covered': 5, 'document_generated': True, 'final_state': 'DOCUMENTING'}}, actual_output={'error': \"'AgentFactory' object has no attribute 'create_conversation_flow_agent'\", 'conversation_flow': ['我想开发一个社交媒体应用', '主要功能包括用户注册、发布动态、私信聊天', '目标用户是18-35岁的年轻人', '预算大概50万，希望6个月内完成', '使用React Native开发，支持iOS和Android']}, success=False, error_message=\"执行错误: 'AgentFactory' object has no attribute 'create_conversation_flow_agent'\", execution_time=4.100799560546875e-05, timestamp='2025-08-09T16:51:21.435380')", "TestResult(test_name='knowledge_base_to_requirement_transition', test_type='end_to_end_integration', input_data={'conversation_flow': ['系统支持哪些开发框架？', '我想用React开发一个电商网站', '需要支持在线支付和库存管理']}, expected_output={'expected_outcomes': {'initial_intent': 'knowledge_query', 'transition_intent': 'business_requirement', 'final_state': 'COLLECTING_INFO'}}, actual_output={'error': \"'AgentFactory' object has no attribute 'create_conversation_flow_agent'\", 'conversation_flow': ['系统支持哪些开发框架？', '我想用React开发一个电商网站', '需要支持在线支付和库存管理']}, success=False, error_message=\"执行错误: 'AgentFactory' object has no attribute 'create_conversation_flow_agent'\", execution_time=3.7670135498046875e-05, timestamp='2025-08-09T16:51:21.435420')"], "summary": {"total_tests": 2, "passed": 0, "failed": 2, "execution_time": 8.296966552734375e-05}}}, "detailed_results": [{"test_name": "basic_faq_query", "test_type": "knowledge_base_query", "input_data": {"query": "如何注册开发者账号？", "role_filter": "developer"}, "expected_output": {"expected_keywords": ["注册", "开发者", "账号"], "min_confidence": 0.7}, "actual_output": {"error": "RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'", "available": false}, "success": false, "error_message": "执行错误: RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'", "execution_time": 0.16643500328063965, "timestamp": "2025-08-09T16:51:21.403867"}, {"test_name": "feature_explanation_query", "test_type": "knowledge_base_query", "input_data": {"query": "需求采集系统有哪些功能？", "role_filter": "company"}, "expected_output": {"expected_keywords": ["需求采集", "功能"], "min_confidence": 0.6}, "actual_output": {"error": "RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'", "available": false}, "success": false, "error_message": "执行错误: RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'", "execution_time": 7.987022399902344e-05, "timestamp": "2025-08-09T16:51:21.403957"}, {"test_name": "tutorial_query", "test_type": "knowledge_base_query", "input_data": {"query": "如何使用API接口？", "category_filter": "api"}, "expected_output": {"expected_keywords": ["API", "接口", "使用"], "min_confidence": 0.7}, "actual_output": {"error": "RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'", "available": false}, "success": false, "error_message": "执行错误: RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'", "execution_time": 7.295608520507812e-05, "timestamp": "2025-08-09T16:51:21.404035"}, {"test_name": "no_result_query", "test_type": "knowledge_base_query", "input_data": {"query": "火星上的天气如何？"}, "expected_output": {"expected_fallback": true, "min_confidence": 0.0}, "actual_output": {"error": "RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'", "available": false}, "success": false, "error_message": "执行错误: RAGKnowledgeBaseAgent.query() got an unexpected keyword argument 'query'", "execution_time": 0.00021791458129882812, "timestamp": "2025-08-09T16:51:21.404263"}, {"test_name": "app_development_flow", "test_type": "focus_points_flow", "input_data": {"message": "我想开发一个电商App", "expected_domain": "LY_005", "expected_category": "LB_022"}, "expected_output": {"min_coverage": 0.8}, "actual_output": {"error": "'AgentFactory' object has no attribute 'create_conversation_flow_agent'", "message": "我想开发一个电商App"}, "success": false, "error_message": "执行错误: 'AgentFactory' object has no attribute 'create_conversation_flow_agent'", "execution_time": 4.1961669921875e-05, "timestamp": "2025-08-09T16:51:21.404443"}, {"test_name": "website_development_flow", "test_type": "focus_points_flow", "input_data": {"message": "我需要制作一个企业官网", "expected_domain": "LY_005", "expected_category": "LB_021"}, "expected_output": {"min_coverage": 0.7}, "actual_output": {"error": "'AgentFactory' object has no attribute 'create_conversation_flow_agent'", "message": "我需要制作一个企业官网"}, "success": false, "error_message": "执行错误: 'AgentFactory' object has no attribute 'create_conversation_flow_agent'", "execution_time": 4.00543212890625e-05, "timestamp": "2025-08-09T16:51:21.404486"}, {"test_name": "business_requirement_intent", "test_type": "composite_intent_recognition", "input_data": {"message": "我想开发一个在线教育平台"}, "expected_output": {"expected_intent": "business_requirement", "expected_sub_intent": "software_development", "min_confidence": 0.8}, "actual_output": {"error": "'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'", "message": "我想开发一个在线教育平台"}, "success": false, "error_message": "执行错误: 'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'", "execution_time": 0.008319854736328125, "timestamp": "2025-08-09T16:51:21.413018"}, {"test_name": "knowledge_query_intent", "test_type": "composite_intent_recognition", "input_data": {"message": "系统支持哪些编程语言？"}, "expected_output": {"expected_intent": "knowledge_query", "min_confidence": 0.7}, "actual_output": {"error": "'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'", "message": "系统支持哪些编程语言？"}, "success": false, "error_message": "执行错误: 'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'", "execution_time": 0.0074460506439208984, "timestamp": "2025-08-09T16:51:21.420476"}, {"test_name": "greeting_intent", "test_type": "composite_intent_recognition", "input_data": {"message": "你好，我是新用户"}, "expected_output": {"expected_intent": "greeting", "min_confidence": 0.9}, "actual_output": {"error": "'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'", "message": "你好，我是新用户"}, "success": false, "error_message": "执行错误: 'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'", "execution_time": 0.0071828365325927734, "timestamp": "2025-08-09T16:51:21.427669"}, {"test_name": "mixed_intent", "test_type": "composite_intent_recognition", "input_data": {"message": "你好，我想了解一下如何开发移动应用，系统能帮我做需求分析吗？"}, "expected_output": {"min_confidence": 0.6}, "actual_output": {"error": "'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'", "message": "你好，我想了解一下如何开发移动应用，系统能帮我做需求分析吗？"}, "success": false, "error_message": "执行错误: 'SimplifiedDecisionEngine' object has no attribute 'recognize_intent'", "execution_time": 0.007234096527099609, "timestamp": "2025-08-09T16:51:21.434910"}, {"test_name": "threshold_boundary_test", "test_type": "decision_strategy", "input_data": {"confidence_values": [0.5, 0.7, 0.8, 0.9, 1.0], "threshold_key": "confidence.default"}, "expected_output": {}, "actual_output": {"threshold_tests": {"0.5": "low_confidence_handling", "0.7": "normal_processing", "0.8": "high_confidence_processing", "0.9": "very_high_confidence_processing", "1.0": "very_high_confidence_processing"}}, "success": true, "error_message": null, "execution_time": 5.507469177246094e-05, "timestamp": "2025-08-09T16:51:21.435147"}, {"test_name": "business_rule_consistency", "test_type": "decision_strategy", "input_data": {"scenarios": [{"domain": "LY_005", "category": "LB_022", "expected_action": "start_requirement_collection"}, {"domain": "LY_001", "category": "LB_001", "expected_action": "start_requirement_collection"}, {"domain": "NULL", "category": "NULL", "expected_action": "request_clarification"}]}, "expected_output": {}, "actual_output": {"business_rule_tests": [{"scenario": {"domain": "LY_005", "category": "LB_022", "expected_action": "start_requirement_collection"}, "decision": "start_requirement_collection"}, {"scenario": {"domain": "LY_001", "category": "LB_001", "expected_action": "start_requirement_collection"}, "decision": "start_requirement_collection"}, {"scenario": {"domain": "NULL", "category": "NULL", "expected_action": "request_clarification"}, "decision": "request_clarification"}]}, "success": true, "error_message": null, "execution_time": 4.100799560546875e-05, "timestamp": "2025-08-09T16:51:21.435192"}, {"test_name": "fallback_mechanism_test", "test_type": "decision_strategy", "input_data": {"error_scenarios": ["llm_service_timeout", "database_connection_error", "invalid_configuration"]}, "expected_output": {}, "actual_output": {"fallback_tests": [{"error_scenario": "llm_service_timeout", "fallback_action": "use_cached_response"}, {"error_scenario": "database_connection_error", "fallback_action": "use_default_template"}, {"error_scenario": "invalid_configuration", "fallback_action": "graceful_degradation"}]}, "success": true, "error_message": null, "execution_time": 3.504753112792969e-05, "timestamp": "2025-08-09T16:51:21.435230"}, {"test_name": "complete_requirement_collection_flow", "test_type": "end_to_end_integration", "input_data": {"conversation_flow": ["我想开发一个社交媒体应用", "主要功能包括用户注册、发布动态、私信聊天", "目标用户是18-35岁的年轻人", "预算大概50万，希望6个月内完成", "使用React Native开发，支持iOS和Android"]}, "expected_output": {"expected_outcomes": {"domain_classification": "LY_005", "category_classification": "LB_022", "focus_points_covered": 5, "document_generated": true, "final_state": "DOCUMENTING"}}, "actual_output": {"error": "'AgentFactory' object has no attribute 'create_conversation_flow_agent'", "conversation_flow": ["我想开发一个社交媒体应用", "主要功能包括用户注册、发布动态、私信聊天", "目标用户是18-35岁的年轻人", "预算大概50万，希望6个月内完成", "使用React Native开发，支持iOS和Android"]}, "success": false, "error_message": "执行错误: 'AgentFactory' object has no attribute 'create_conversation_flow_agent'", "execution_time": 4.100799560546875e-05, "timestamp": "2025-08-09T16:51:21.435380"}, {"test_name": "knowledge_base_to_requirement_transition", "test_type": "end_to_end_integration", "input_data": {"conversation_flow": ["系统支持哪些开发框架？", "我想用React开发一个电商网站", "需要支持在线支付和库存管理"]}, "expected_output": {"expected_outcomes": {"initial_intent": "knowledge_query", "transition_intent": "business_requirement", "final_state": "COLLECTING_INFO"}}, "actual_output": {"error": "'AgentFactory' object has no attribute 'create_conversation_flow_agent'", "conversation_flow": ["系统支持哪些开发框架？", "我想用React开发一个电商网站", "需要支持在线支付和库存管理"]}, "success": false, "error_message": "执行错误: 'AgentFactory' object has no attribute 'create_conversation_flow_agent'", "execution_time": 3.7670135498046875e-05, "timestamp": "2025-08-09T16:51:21.435420"}]}