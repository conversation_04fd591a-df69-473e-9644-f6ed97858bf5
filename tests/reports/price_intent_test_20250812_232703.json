{"timestamp": "2025-08-12T23:27:03.718549", "summary": {"total_tests": 10, "successful_tests": 6, "success_rate": 60.0}, "results": [{"test_name": "原始问题", "input": "我对设计这张海报具体需要多少钱没有概念，您能给我建议吗？", "expected_intent": "business_requirement", "actual_intent": "business_requirement", "confidence": 0.8, "emotion": "neutral", "success": true, "processing_method": "unknown", "full_result": {"decision": {"action": "start_requirement_collection", "priority": 1, "response_template": "requirement_collection.start", "next_state": "COLLECTING_INFO"}, "intent": "business_requirement", "emotion": "neutral", "confidence": 0.8, "metadata": {"intent": "business_requirement", "current_state": "IDLE", "rule_matched": true, "context": {"current_state": "IDLE"}}}}, {"test_name": "简化版本", "input": "具体需要多少钱没有概念，您能给我建议吗？", "expected_intent": "business_requirement", "actual_intent": "general_chat", "confidence": 0.0, "emotion": "neutral", "success": false, "processing_method": "unknown", "full_result": {"decision": {"action": "respond_to_general_chat", "priority": 1, "response_template": "general.chat", "next_state": "IDLE"}, "intent": "general_chat", "emotion": "neutral", "confidence": 0.0, "metadata": {"intent": "general_chat", "current_state": "IDLE", "rule_matched": true, "context": {"current_state": "IDLE"}}}}, {"test_name": "预算咨询", "input": "这个项目大概需要多少预算？", "expected_intent": "business_requirement", "actual_intent": "general_chat", "confidence": 0.0, "emotion": "neutral", "success": false, "processing_method": "unknown", "full_result": {"decision": {"action": "respond_to_general_chat", "priority": 1, "response_template": "general.chat", "next_state": "IDLE"}, "intent": "general_chat", "emotion": "neutral", "confidence": 0.0, "metadata": {"intent": "general_chat", "current_state": "IDLE", "rule_matched": true, "context": {"current_state": "IDLE"}}}}, {"test_name": "成本询问", "input": "开发这样的app费用大概是多少？", "expected_intent": "business_requirement", "actual_intent": "business_requirement", "confidence": 0.8, "emotion": "neutral", "success": true, "processing_method": "unknown", "full_result": {"decision": {"action": "start_requirement_collection", "priority": 1, "response_template": "requirement_collection.start", "next_state": "COLLECTING_INFO"}, "intent": "business_requirement", "emotion": "neutral", "confidence": 0.8, "metadata": {"intent": "business_requirement", "current_state": "IDLE", "rule_matched": true, "context": {"current_state": "IDLE"}}}}, {"test_name": "明确知识库查询", "input": "你们的收费标准是什么？", "expected_intent": "search_knowledge_base", "actual_intent": "search_knowledge_base", "confidence": 0.8, "emotion": "neutral", "success": true, "processing_method": "unknown", "full_result": {"decision": {"action": "search_knowledge_base", "priority": 2, "response_template": "knowledge_base.search", "next_state": "IDLE"}, "intent": "search_knowledge_base", "emotion": "neutral", "confidence": 0.8, "metadata": {"intent": "search_knowledge_base", "current_state": "IDLE", "rule_matched": true, "context": {"current_state": "IDLE"}}}}, {"test_name": "价格表查询", "input": "价格表在哪里可以看到？", "expected_intent": "search_knowledge_base", "actual_intent": "search_knowledge_base", "confidence": 0.8, "emotion": "neutral", "success": true, "processing_method": "unknown", "full_result": {"decision": {"action": "search_knowledge_base", "priority": 2, "response_template": "knowledge_base.search", "next_state": "IDLE"}, "intent": "search_knowledge_base", "emotion": "neutral", "confidence": 0.8, "metadata": {"intent": "search_knowledge_base", "current_state": "IDLE", "rule_matched": true, "context": {"current_state": "IDLE"}}}}, {"test_name": "套餐查询", "input": "有什么套餐可以选择？", "expected_intent": "search_knowledge_base", "actual_intent": "search_knowledge_base", "confidence": 0.8, "emotion": "neutral", "success": true, "processing_method": "unknown", "full_result": {"decision": {"action": "search_knowledge_base", "priority": 2, "response_template": "knowledge_base.search", "next_state": "IDLE"}, "intent": "search_knowledge_base", "emotion": "neutral", "confidence": 0.8, "metadata": {"intent": "search_knowledge_base", "current_state": "IDLE", "rule_matched": true, "context": {"current_state": "IDLE"}}}}, {"test_name": "边界情况1", "input": "你们平台设计海报多少钱？", "expected_intent": "search_knowledge_base", "actual_intent": "business_requirement", "confidence": 0.8, "emotion": "neutral", "success": false, "processing_method": "unknown", "full_result": {"decision": {"action": "start_requirement_collection", "priority": 1, "response_template": "requirement_collection.start", "next_state": "COLLECTING_INFO"}, "intent": "business_requirement", "emotion": "neutral", "confidence": 0.8, "metadata": {"intent": "business_requirement", "current_state": "IDLE", "rule_matched": true, "context": {"current_state": "IDLE"}}}}, {"test_name": "边界情况2", "input": "价格是多少？", "expected_intent": "search_knowledge_base", "actual_intent": "search_knowledge_base", "confidence": 0.8, "emotion": "neutral", "success": true, "processing_method": "unknown", "full_result": {"decision": {"action": "search_knowledge_base", "priority": 2, "response_template": "knowledge_base.search", "next_state": "IDLE"}, "intent": "search_knowledge_base", "emotion": "neutral", "confidence": 0.8, "metadata": {"intent": "search_knowledge_base", "current_state": "IDLE", "rule_matched": true, "context": {"current_state": "IDLE"}}}}, {"test_name": "收集状态下的价格咨询", "input": "我不知道这个需要多少成本", "expected_intent": "process_answer", "actual_intent": "business_requirement", "confidence": 0.8, "emotion": "neutral", "success": false, "processing_method": "unknown", "full_result": {"decision": {"action": "process_answer_and_ask_next", "priority": 0, "response_template": "requirement_collection.continue", "next_state": "COLLECTING_INFO"}, "intent": "business_requirement", "emotion": "neutral", "confidence": 0.8, "metadata": {"intent": "business_requirement", "current_state": "COLLECTING_INFO", "rule_matched": false, "context": {"current_state": "COLLECTING_INFO"}}}}]}