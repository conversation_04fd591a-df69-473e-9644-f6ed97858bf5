#!/usr/bin/env python3
"""
综合业务流程测试套件
版本: v1.0
作者: AI Assistant
创建时间: 2025-08-09

功能:
1. 知识库查询功能测试
2. 关注点流程完整性测试
3. 复合意图识别准确性测试
4. 决策策略正确性测试
5. 端到端业务流程集成测试

设计原则:
- 可重复执行的回归测试
- 独立的验证工具
- 详细的输入输出报告
- 业务逻辑一致性验证
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from backend.config.unified_config_loader import get_unified_config
from backend.agents.factory import AgentFactory
from backend.agents.conversation_flow.core_refactored import AutoGenConversationFlowAgent
from backend.agents.simplified_decision_engine import SimplifiedDecisionEngine
from backend.data.db.database_manager import DatabaseManager


@dataclass
class TestResult:
    """测试结果数据结构"""
    test_name: str
    test_type: str
    input_data: Dict[str, Any]
    expected_output: Optional[Dict[str, Any]]
    actual_output: Dict[str, Any]
    success: bool
    error_message: Optional[str]
    execution_time: float
    timestamp: str


@dataclass
class TestSuite:
    """测试套件数据结构"""
    suite_name: str
    description: str
    test_cases: List[Dict[str, Any]]
    setup_required: bool = False
    teardown_required: bool = False


class ComprehensiveBusinessTester:
    """综合业务流程测试器"""

    def __init__(self):
        """初始化测试器"""
        self.logger = self._setup_logger()
        self.config = get_unified_config()
        self.agent_factory = None
        self.test_results: List[TestResult] = []
        self.test_session_id = f"test_session_{int(time.time())}"
        self.test_user_id = f"test_user_{int(time.time())}"

        # 测试数据目录
        self.test_data_dir = Path(__file__).parent / "test_data"
        self.reports_dir = Path(__file__).parent / "reports"

        # 确保目录存在
        self.test_data_dir.mkdir(exist_ok=True)
        self.reports_dir.mkdir(exist_ok=True)

        self.logger.info("综合业务流程测试器初始化完成")

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger("ComprehensiveBusinessTester")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    async def initialize(self):
        """初始化测试环境"""
        try:
            self.logger.info("正在初始化测试环境...")

            # 初始化Agent工厂
            self.agent_factory = AgentFactory()
            # AgentFactory 会自动进行延迟初始化，不需要显式调用 initialize

            self.logger.info("测试环境初始化完成")
            return True

        except Exception as e:
            self.logger.error(f"测试环境初始化失败: {str(e)}")
            return False

    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试套件"""
        self.logger.info("开始执行综合业务流程测试")
        start_time = time.time()

        # 初始化测试环境
        if not await self.initialize():
            return {"error": "测试环境初始化失败"}

        # 定义测试套件
        test_suites = [
            self._get_knowledge_base_test_suite(),
            self._get_focus_points_test_suite(),
            self._get_composite_intent_test_suite(),
            self._get_decision_strategy_test_suite(),
            self._get_end_to_end_test_suite()
        ]

        # 执行所有测试套件
        suite_results = {}
        for suite in test_suites:
            self.logger.info(f"执行测试套件: {suite.suite_name}")
            suite_result = await self._run_test_suite(suite)
            suite_results[suite.suite_name] = suite_result

        # 生成综合报告
        total_time = time.time() - start_time
        report = await self._generate_comprehensive_report(suite_results, total_time)

        self.logger.info(f"所有测试完成，总耗时: {total_time:.2f}秒")
        return report

    def _get_knowledge_base_test_suite(self) -> TestSuite:
        """获取知识库查询测试套件"""
        return TestSuite(
            suite_name="knowledge_base_query",
            description="知识库查询功能测试",
            test_cases=[
                {
                    "name": "basic_faq_query",
                    "description": "基础FAQ查询测试",
                    "input": {
                        "query": "如何注册开发者账号？",
                        "role_filter": "developer"
                    },
                    "expected_keywords": ["注册", "开发者", "账号"],
                    "min_confidence": 0.7
                },
                {
                    "name": "feature_explanation_query",
                    "description": "功能说明查询测试",
                    "input": {
                        "query": "需求采集系统有哪些功能？",
                        "role_filter": "company"
                    },
                    "expected_keywords": ["需求采集", "功能"],
                    "min_confidence": 0.6
                },
                {
                    "name": "tutorial_query",
                    "description": "教程查询测试",
                    "input": {
                        "query": "如何使用API接口？",
                        "category_filter": "api"
                    },
                    "expected_keywords": ["API", "接口", "使用"],
                    "min_confidence": 0.7
                },
                {
                    "name": "no_result_query",
                    "description": "无结果查询测试",
                    "input": {
                        "query": "火星上的天气如何？"
                    },
                    "expected_fallback": True,
                    "min_confidence": 0.0
                }
            ]
        )

    def _get_focus_points_test_suite(self) -> TestSuite:
        """获取关注点流程测试套件"""
        return TestSuite(
            suite_name="focus_points_flow",
            description="关注点流程完整性测试",
            test_cases=[
                {
                    "name": "app_development_flow",
                    "description": "App开发需求采集流程",
                    "input": {
                        "message": "我想开发一个电商App",
                        "expected_domain": "LY_005",
                        "expected_category": "LB_022"
                    },
                    "expected_focus_points": [
                        "想做什么App",
                        "需要哪些功能",
                        "目标用户群体",
                        "预算和时间",
                        "技术要求"
                    ],
                    "min_coverage": 0.8
                },
                {
                    "name": "website_development_flow",
                    "description": "网站开发需求采集流程",
                    "input": {
                        "message": "我需要制作一个企业官网",
                        "expected_domain": "LY_005",
                        "expected_category": "LB_021"
                    },
                    "expected_focus_points": [
                        "网站类型和目的",
                        "功能需求",
                        "设计风格",
                        "预算范围",
                        "上线时间"
                    ],
                    "min_coverage": 0.7
                }
            ]
        )

    def _get_composite_intent_test_suite(self) -> TestSuite:
        """获取复合意图识别测试套件"""
        return TestSuite(
            suite_name="composite_intent_recognition",
            description="复合意图识别准确性测试",
            test_cases=[
                {
                    "name": "business_requirement_intent",
                    "description": "业务需求意图识别",
                    "input": {
                        "message": "我想开发一个在线教育平台"
                    },
                    "expected_intent": "business_requirement",
                    "expected_sub_intent": "software_development",
                    "min_confidence": 0.8
                },
                {
                    "name": "knowledge_query_intent",
                    "description": "知识库查询意图识别",
                    "input": {
                        "message": "系统支持哪些编程语言？"
                    },
                    "expected_intent": "knowledge_query",
                    "min_confidence": 0.7
                },
                {
                    "name": "greeting_intent",
                    "description": "问候意图识别",
                    "input": {
                        "message": "你好，我是新用户"
                    },
                    "expected_intent": "greeting",
                    "min_confidence": 0.9
                },
                {
                    "name": "mixed_intent",
                    "description": "混合意图识别",
                    "input": {
                        "message": "你好，我想了解一下如何开发移动应用，系统能帮我做需求分析吗？"
                    },
                    "expected_intents": ["greeting", "knowledge_query", "business_requirement"],
                    "primary_intent": "business_requirement",
                    "min_confidence": 0.6
                }
            ]
        )

    def _get_decision_strategy_test_suite(self) -> TestSuite:
        """获取决策策略测试套件"""
        return TestSuite(
            suite_name="decision_strategy",
            description="决策策略正确性测试",
            test_cases=[
                {
                    "name": "threshold_boundary_test",
                    "description": "阈值边界测试",
                    "input": {
                        "confidence_values": [0.5, 0.7, 0.8, 0.9, 1.0],
                        "threshold_key": "confidence.default"
                    },
                    "expected_decisions": {
                        0.5: "low_confidence_handling",
                        0.7: "normal_processing",
                        0.8: "high_confidence_processing",
                        0.9: "very_high_confidence_processing",
                        1.0: "perfect_confidence_processing"
                    }
                },
                {
                    "name": "business_rule_consistency",
                    "description": "业务规则一致性测试",
                    "input": {
                        "scenarios": [
                            {"domain": "LY_005", "category": "LB_022", "expected_action": "start_requirement_collection"},
                            {"domain": "LY_001", "category": "LB_001", "expected_action": "start_requirement_collection"},
                            {"domain": "NULL", "category": "NULL", "expected_action": "request_clarification"}
                        ]
                    }
                },
                {
                    "name": "fallback_mechanism_test",
                    "description": "回退机制测试",
                    "input": {
                        "error_scenarios": [
                            "llm_service_timeout",
                            "database_connection_error",
                            "invalid_configuration"
                        ]
                    },
                    "expected_fallback_actions": [
                        "use_cached_response",
                        "use_default_template",
                        "graceful_degradation"
                    ]
                }
            ]
        )

    def _get_end_to_end_test_suite(self) -> TestSuite:
        """获取端到端集成测试套件"""
        return TestSuite(
            suite_name="end_to_end_integration",
            description="端到端业务流程集成测试",
            test_cases=[
                {
                    "name": "complete_requirement_collection_flow",
                    "description": "完整需求采集流程测试",
                    "input": {
                        "conversation_flow": [
                            "我想开发一个社交媒体应用",
                            "主要功能包括用户注册、发布动态、私信聊天",
                            "目标用户是18-35岁的年轻人",
                            "预算大概50万，希望6个月内完成",
                            "使用React Native开发，支持iOS和Android"
                        ]
                    },
                    "expected_outcomes": {
                        "domain_classification": "LY_005",
                        "category_classification": "LB_022",
                        "focus_points_covered": 5,
                        "document_generated": True,
                        "final_state": "DOCUMENTING"
                    }
                },
                {
                    "name": "knowledge_base_to_requirement_transition",
                    "description": "知识库查询到需求采集转换测试",
                    "input": {
                        "conversation_flow": [
                            "系统支持哪些开发框架？",
                            "我想用React开发一个电商网站",
                            "需要支持在线支付和库存管理"
                        ]
                    },
                    "expected_outcomes": {
                        "initial_intent": "knowledge_query",
                        "transition_intent": "business_requirement",
                        "final_state": "COLLECTING_INFO"
                    }
                }
            ]
        )

    async def _run_test_suite(self, suite: TestSuite) -> Dict[str, Any]:
        """执行单个测试套件"""
        self.logger.info(f"开始执行测试套件: {suite.suite_name}")

        suite_start_time = time.time()
        suite_results = {
            "suite_name": suite.suite_name,
            "description": suite.description,
            "test_results": [],
            "summary": {
                "total_tests": len(suite.test_cases),
                "passed": 0,
                "failed": 0,
                "execution_time": 0.0
            }
        }

        # 执行套件中的每个测试用例
        for test_case in suite.test_cases:
            try:
                result = await self._run_single_test(suite.suite_name, test_case)
                suite_results["test_results"].append(result)

                if result.success:
                    suite_results["summary"]["passed"] += 1
                else:
                    suite_results["summary"]["failed"] += 1

            except Exception as e:
                self.logger.error(f"测试用例 {test_case['name']} 执行失败: {str(e)}")
                error_result = TestResult(
                    test_name=test_case["name"],
                    test_type=suite.suite_name,
                    input_data=test_case.get("input", {}),
                    expected_output=None,
                    actual_output={"error": str(e)},
                    success=False,
                    error_message=str(e),
                    execution_time=0.0,
                    timestamp=datetime.now().isoformat()
                )
                suite_results["test_results"].append(error_result)
                suite_results["summary"]["failed"] += 1

        suite_results["summary"]["execution_time"] = time.time() - suite_start_time
        self.logger.info(f"测试套件 {suite.suite_name} 完成: {suite_results['summary']['passed']}/{suite_results['summary']['total_tests']} 通过")

        return suite_results

    async def _run_single_test(self, suite_type: str, test_case: Dict[str, Any]) -> TestResult:
        """执行单个测试用例"""
        test_start_time = time.time()
        test_name = test_case["name"]

        self.logger.info(f"执行测试: {test_name}")

        try:
            # 根据测试套件类型选择执行方法
            if suite_type == "knowledge_base_query":
                actual_output = await self._test_knowledge_base_query(test_case)
            elif suite_type == "focus_points_flow":
                actual_output = await self._test_focus_points_flow(test_case)
            elif suite_type == "composite_intent_recognition":
                actual_output = await self._test_composite_intent_recognition(test_case)
            elif suite_type == "decision_strategy":
                actual_output = await self._test_decision_strategy(test_case)
            elif suite_type == "end_to_end_integration":
                actual_output = await self._test_end_to_end_integration(test_case)
            else:
                raise ValueError(f"未知的测试套件类型: {suite_type}")

            # 验证测试结果
            success, error_message = self._validate_test_result(test_case, actual_output)

            result = TestResult(
                test_name=test_name,
                test_type=suite_type,
                input_data=test_case.get("input", {}),
                expected_output=self._extract_expected_output(test_case),
                actual_output=actual_output,
                success=success,
                error_message=error_message,
                execution_time=time.time() - test_start_time,
                timestamp=datetime.now().isoformat()
            )

            self.test_results.append(result)
            return result

        except Exception as e:
            self.logger.error(f"测试 {test_name} 执行异常: {str(e)}")
            return TestResult(
                test_name=test_name,
                test_type=suite_type,
                input_data=test_case.get("input", {}),
                expected_output=None,
                actual_output={"error": str(e)},
                success=False,
                error_message=str(e),
                execution_time=time.time() - test_start_time,
                timestamp=datetime.now().isoformat()
            )

    async def _test_knowledge_base_query(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """测试知识库查询功能"""
        input_data = test_case["input"]

        try:
            # 获取知识库代理
            kb_agent = self.agent_factory.get_rag_knowledge_base_agent()
            if not kb_agent:
                return {"error": "知识库代理未初始化", "available": False}

            # 执行查询
            query_result = await kb_agent.query(
                query=input_data["query"],
                context={
                    "session_id": self.test_session_id,
                    "user_id": self.test_user_id
                },
                filters={
                    "role_filter": input_data.get("role_filter"),
                    "category_filter": input_data.get("category_filter")
                }
            )

            return {
                "query": input_data["query"],
                "response": query_result.get("answer", ""),
                "sources": query_result.get("sources", []),
                "processing_info": query_result.get("processing_info", {}),
                "success": query_result.get("success", False),
                "error": query_result.get("error")
            }

        except Exception as e:
            return {"error": str(e), "available": False}

    async def _test_focus_points_flow(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """测试关注点流程"""
        input_data = test_case["input"]

        try:
            # 创建会话流程代理
            conversation_agent = self.agent_factory.create_conversation_flow_agent(
                session_id=self.test_session_id,
                user_id=self.test_user_id
            )

            # 处理用户消息
            response = await conversation_agent.process_message(input_data["message"])

            # 获取会话状态
            session_context = await conversation_agent.get_session_context()

            # 获取关注点状态
            focus_points_status = await conversation_agent.get_focus_points_status()

            return {
                "message": input_data["message"],
                "response": response,
                "domain_result": session_context.get("current_domain"),
                "category_result": session_context.get("current_category"),
                "focus_points_status": focus_points_status,
                "session_state": session_context.get("current_state"),
                "coverage_info": self._calculate_coverage_info(focus_points_status)
            }

        except Exception as e:
            return {"error": str(e), "message": input_data["message"]}

    async def _test_composite_intent_recognition(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """测试复合意图识别"""
        input_data = test_case["input"]

        try:
            # 获取决策引擎
            decision_engine = SimplifiedDecisionEngine()

            # 执行意图识别
            intent_result = await decision_engine.recognize_intent(input_data["message"])

            return {
                "message": input_data["message"],
                "recognized_intent": intent_result.get("intent"),
                "sub_intent": intent_result.get("sub_intent"),
                "confidence": intent_result.get("confidence", 0.0),
                "emotion": intent_result.get("emotion", "neutral"),
                "entities": intent_result.get("entities", {}),
                "processing_method": intent_result.get("processing_method", "unknown")
            }

        except Exception as e:
            return {"error": str(e), "message": input_data["message"]}

    async def _test_decision_strategy(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """测试决策策略"""
        input_data = test_case["input"]

        try:
            results = {}

            # 阈值边界测试
            if "confidence_values" in input_data:
                threshold_results = {}
                for confidence in input_data["confidence_values"]:
                    decision = self._simulate_confidence_decision(confidence)
                    threshold_results[str(confidence)] = decision
                results["threshold_tests"] = threshold_results

            # 业务规则一致性测试
            if "scenarios" in input_data:
                rule_results = []
                for scenario in input_data["scenarios"]:
                    decision = self._simulate_business_rule_decision(scenario)
                    rule_results.append({
                        "scenario": scenario,
                        "decision": decision
                    })
                results["business_rule_tests"] = rule_results

            # 回退机制测试
            if "error_scenarios" in input_data:
                fallback_results = []
                for error_scenario in input_data["error_scenarios"]:
                    fallback_action = self._simulate_fallback_mechanism(error_scenario)
                    fallback_results.append({
                        "error_scenario": error_scenario,
                        "fallback_action": fallback_action
                    })
                results["fallback_tests"] = fallback_results

            return results

        except Exception as e:
            return {"error": str(e), "input": input_data}

    async def _test_end_to_end_integration(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """测试端到端集成流程"""
        input_data = test_case["input"]
        conversation_flow = input_data["conversation_flow"]

        try:
            # 创建会话流程代理
            conversation_agent = self.agent_factory.create_conversation_flow_agent(
                session_id=self.test_session_id,
                user_id=self.test_user_id
            )

            conversation_history = []
            final_state = None

            # 逐步处理对话流程
            for i, message in enumerate(conversation_flow):
                self.logger.info(f"处理对话步骤 {i+1}: {message}")

                response = await conversation_agent.process_message(message)
                session_context = await conversation_agent.get_session_context()

                conversation_history.append({
                    "step": i + 1,
                    "user_message": message,
                    "ai_response": response,
                    "session_state": session_context.get("current_state"),
                    "domain": session_context.get("current_domain"),
                    "category": session_context.get("current_category")
                })

                final_state = session_context.get("current_state")

            # 获取最终的关注点状态
            focus_points_status = await conversation_agent.get_focus_points_status()

            return {
                "conversation_flow": conversation_flow,
                "conversation_history": conversation_history,
                "final_state": final_state,
                "focus_points_status": focus_points_status,
                "total_steps": len(conversation_flow),
                "coverage_info": self._calculate_coverage_info(focus_points_status)
            }

        except Exception as e:
            return {"error": str(e), "conversation_flow": conversation_flow}

    def _calculate_coverage_info(self, focus_points_status: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算关注点覆盖信息"""
        if not focus_points_status:
            return {"total": 0, "completed": 0, "coverage_rate": 0.0}

        total_points = len(focus_points_status)
        completed_points = sum(1 for fp in focus_points_status if fp.get("status") == "completed")
        coverage_rate = completed_points / total_points if total_points > 0 else 0.0

        return {
            "total": total_points,
            "completed": completed_points,
            "coverage_rate": coverage_rate,
            "details": focus_points_status
        }

    def _simulate_confidence_decision(self, confidence: float) -> str:
        """模拟置信度决策"""
        # 基于配置的阈值进行决策
        default_threshold = self.config.get_threshold("confidence.default", 0.7)
        high_threshold = self.config.get_threshold("confidence.high", 0.8)
        very_high_threshold = self.config.get_threshold("confidence.very_high", 0.9)

        if confidence >= very_high_threshold:
            return "very_high_confidence_processing"
        elif confidence >= high_threshold:
            return "high_confidence_processing"
        elif confidence >= default_threshold:
            return "normal_processing"
        else:
            return "low_confidence_handling"

    def _simulate_business_rule_decision(self, scenario: Dict[str, Any]) -> str:
        """模拟业务规则决策"""
        domain = scenario.get("domain")
        category = scenario.get("category")

        # 基于业务规则配置进行决策
        if domain and domain != "NULL" and category and category != "NULL":
            return "start_requirement_collection"
        else:
            return "request_clarification"

    def _simulate_fallback_mechanism(self, error_scenario: str) -> str:
        """模拟回退机制"""
        fallback_mapping = {
            "llm_service_timeout": "use_cached_response",
            "database_connection_error": "use_default_template",
            "invalid_configuration": "graceful_degradation"
        }
        return fallback_mapping.get(error_scenario, "unknown_fallback")

    def _validate_test_result(self, test_case: Dict[str, Any], actual_output: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """验证测试结果"""
        try:
            # 检查是否有错误
            if "error" in actual_output:
                return False, f"执行错误: {actual_output['error']}"

            # 根据测试用例类型进行特定验证
            test_name = test_case["name"]

            if "knowledge_base" in test_name:
                return self._validate_knowledge_base_result(test_case, actual_output)
            elif "focus_points" in test_name or "flow" in test_name:
                return self._validate_focus_points_result(test_case, actual_output)
            elif "intent" in test_name:
                return self._validate_intent_result(test_case, actual_output)
            elif "decision" in test_name or "strategy" in test_name:
                return self._validate_decision_result(test_case, actual_output)
            elif "end_to_end" in test_name:
                return self._validate_end_to_end_result(test_case, actual_output)
            else:
                return True, None  # 默认通过

        except Exception as e:
            return False, f"验证过程出错: {str(e)}"

    def _validate_knowledge_base_result(self, test_case: Dict[str, Any], actual_output: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """验证知识库查询结果"""
        # 检查是否有预期的关键词
        expected_keywords = test_case.get("expected_keywords", [])
        response = actual_output.get("response", "").lower()

        if expected_keywords:
            missing_keywords = [kw for kw in expected_keywords if kw.lower() not in response]
            if missing_keywords:
                return False, f"响应中缺少预期关键词: {missing_keywords}"

        # 检查是否是预期的回退情况
        if test_case.get("expected_fallback", False):
            if actual_output.get("success", True):
                return False, "预期应该回退，但查询成功了"
        else:
            if not actual_output.get("success", False):
                return False, "查询失败，但预期应该成功"

        return True, None

    def _validate_focus_points_result(self, test_case: Dict[str, Any], actual_output: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """验证关注点流程结果"""
        input_data = test_case["input"]

        # 验证领域分类
        expected_domain = input_data.get("expected_domain")
        if expected_domain:
            actual_domain = actual_output.get("domain_result")
            if actual_domain != expected_domain:
                return False, f"领域分类不匹配: 预期 {expected_domain}, 实际 {actual_domain}"

        # 验证类别分类
        expected_category = input_data.get("expected_category")
        if expected_category:
            actual_category = actual_output.get("category_result")
            if actual_category != expected_category:
                return False, f"类别分类不匹配: 预期 {expected_category}, 实际 {actual_category}"

        # 验证覆盖率
        min_coverage = test_case.get("min_coverage", 0.0)
        coverage_info = actual_output.get("coverage_info", {})
        actual_coverage = coverage_info.get("coverage_rate", 0.0)

        if actual_coverage < min_coverage:
            return False, f"覆盖率不足: 预期 >= {min_coverage}, 实际 {actual_coverage}"

        return True, None

    def _validate_intent_result(self, test_case: Dict[str, Any], actual_output: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """验证意图识别结果"""
        # 验证主要意图
        expected_intent = test_case.get("expected_intent")
        if expected_intent:
            actual_intent = actual_output.get("recognized_intent")
            if actual_intent != expected_intent:
                return False, f"意图识别不匹配: 预期 {expected_intent}, 实际 {actual_intent}"

        # 验证子意图
        expected_sub_intent = test_case.get("expected_sub_intent")
        if expected_sub_intent:
            actual_sub_intent = actual_output.get("sub_intent")
            if actual_sub_intent != expected_sub_intent:
                return False, f"子意图识别不匹配: 预期 {expected_sub_intent}, 实际 {actual_sub_intent}"

        # 验证置信度
        min_confidence = test_case.get("min_confidence", 0.0)
        actual_confidence = actual_output.get("confidence", 0.0)

        if actual_confidence < min_confidence:
            return False, f"置信度不足: 预期 >= {min_confidence}, 实际 {actual_confidence}"

        return True, None

    def _validate_decision_result(self, test_case: Dict[str, Any], actual_output: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """验证决策策略结果"""
        input_data = test_case["input"]

        # 验证阈值决策
        if "expected_decisions" in test_case:
            expected_decisions = test_case["expected_decisions"]
            threshold_tests = actual_output.get("threshold_tests", {})

            for confidence_str, expected_decision in expected_decisions.items():
                actual_decision = threshold_tests.get(confidence_str)
                if actual_decision != expected_decision:
                    return False, f"阈值决策不匹配 (置信度 {confidence_str}): 预期 {expected_decision}, 实际 {actual_decision}"

        # 验证业务规则
        if "scenarios" in input_data:
            business_rule_tests = actual_output.get("business_rule_tests", [])
            for i, scenario in enumerate(input_data["scenarios"]):
                expected_action = scenario.get("expected_action")
                if i < len(business_rule_tests):
                    actual_decision = business_rule_tests[i].get("decision")
                    if actual_decision != expected_action:
                        return False, f"业务规则决策不匹配: 预期 {expected_action}, 实际 {actual_decision}"

        return True, None

    def _validate_end_to_end_result(self, test_case: Dict[str, Any], actual_output: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """验证端到端测试结果"""
        expected_outcomes = test_case.get("expected_outcomes", {})

        # 验证最终状态
        expected_final_state = expected_outcomes.get("final_state")
        if expected_final_state:
            actual_final_state = actual_output.get("final_state")
            if actual_final_state != expected_final_state:
                return False, f"最终状态不匹配: 预期 {expected_final_state}, 实际 {actual_final_state}"

        # 验证关注点覆盖数量
        expected_focus_points = expected_outcomes.get("focus_points_covered")
        if expected_focus_points:
            coverage_info = actual_output.get("coverage_info", {})
            actual_completed = coverage_info.get("completed", 0)
            if actual_completed < expected_focus_points:
                return False, f"关注点覆盖不足: 预期 >= {expected_focus_points}, 实际 {actual_completed}"

        return True, None

    def _extract_expected_output(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """提取预期输出"""
        expected = {}

        # 提取各种预期值
        for key in ["expected_intent", "expected_sub_intent", "expected_domain", "expected_category",
                   "expected_keywords", "expected_fallback", "min_confidence", "min_coverage"]:
            if key in test_case:
                expected[key] = test_case[key]

        if "expected_outcomes" in test_case:
            expected["expected_outcomes"] = test_case["expected_outcomes"]

        return expected

    async def _generate_comprehensive_report(self, suite_results: Dict[str, Any], total_time: float) -> Dict[str, Any]:
        """生成综合测试报告"""
        timestamp = datetime.now().isoformat()

        # 计算总体统计
        total_tests = sum(suite["summary"]["total_tests"] for suite in suite_results.values())
        total_passed = sum(suite["summary"]["passed"] for suite in suite_results.values())
        total_failed = sum(suite["summary"]["failed"] for suite in suite_results.values())

        # 生成报告
        report = {
            "test_run_info": {
                "timestamp": timestamp,
                "total_execution_time": total_time,
                "test_session_id": self.test_session_id,
                "test_user_id": self.test_user_id
            },
            "summary": {
                "total_test_suites": len(suite_results),
                "total_tests": total_tests,
                "total_passed": total_passed,
                "total_failed": total_failed,
                "success_rate": (total_passed / total_tests * 100) if total_tests > 0 else 0.0
            },
            "suite_results": suite_results,
            "detailed_results": [asdict(result) for result in self.test_results]
        }

        # 保存报告到文件
        report_file = self.reports_dir / f"comprehensive_test_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)

        # 生成简化的控制台报告
        self._print_console_report(report)

        self.logger.info(f"详细报告已保存到: {report_file}")
        return report

    def _print_console_report(self, report: Dict[str, Any]):
        """打印控制台报告"""
        print("\n" + "="*80)
        print("📊 综合业务流程测试报告")
        print("="*80)

        summary = report["summary"]
        print(f"🕒 执行时间: {report['test_run_info']['total_execution_time']:.2f}秒")
        print(f"📋 测试套件: {summary['total_test_suites']} 个")
        print(f"🧪 测试用例: {summary['total_tests']} 个")
        print(f"✅ 通过: {summary['total_passed']} 个")
        print(f"❌ 失败: {summary['total_failed']} 个")
        print(f"📈 成功率: {summary['success_rate']:.1f}%")

        print("\n📋 各测试套件详情:")
        print("-" * 80)

        for suite_name, suite_result in report["suite_results"].items():
            suite_summary = suite_result["summary"]
            status_icon = "✅" if suite_summary["failed"] == 0 else "❌"
            print(f"{status_icon} {suite_result['description']}")
            print(f"   通过: {suite_summary['passed']}/{suite_summary['total_tests']} "
                  f"({suite_summary['passed']/suite_summary['total_tests']*100:.1f}%) "
                  f"耗时: {suite_summary['execution_time']:.2f}s")

            # 显示失败的测试
            failed_tests = [test for test in suite_result["test_results"] if not test.success]
            if failed_tests:
                print("   失败的测试:")
                for test in failed_tests:
                    print(f"     - {test.test_name}: {test.error_message}")

        print("\n" + "="*80)


# 主函数和命令行接口
async def main():
    """主函数"""
    print("🚀 启动综合业务流程测试...")

    tester = ComprehensiveBusinessTester()

    try:
        # 运行所有测试
        report = await tester.run_all_tests()

        if "error" in report:
            print(f"❌ 测试执行失败: {report['error']}")
            return 1

        # 检查是否有失败的测试
        if report["summary"]["total_failed"] > 0:
            print(f"\n⚠️  有 {report['summary']['total_failed']} 个测试失败")
            return 1
        else:
            print(f"\n🎉 所有 {report['summary']['total_tests']} 个测试都通过了！")
            return 0

    except Exception as e:
        print(f"❌ 测试执行异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)