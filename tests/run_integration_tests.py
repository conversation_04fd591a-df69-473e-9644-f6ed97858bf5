#!/usr/bin/env python3
"""
集成测试运行脚本

功能：
1. 运行完整的集成测试套件
2. 生成详细的测试报告
3. 性能基准对比分析
4. 业务逻辑完整性验证
5. 小范围用户场景模拟

使用方法：
python run_integration_tests.py [--verbose] [--output-file report.json]
"""

import asyncio
import argparse
import json
import sys
import os
from datetime import datetime
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from backend.testing.integration_test_framework import integration_test_framework
    print("✅ 成功导入集成测试框架")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def setup_logging(verbose: bool = False):
    """设置日志"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def print_test_summary(report: dict):
    """打印测试摘要"""
    print("\n" + "="*80)
    print("🎯 集成测试结果摘要")
    print("="*80)
    
    summary = report["test_summary"]
    print(f"📊 总体结果:")
    print(f"   测试场景总数: {summary['total_scenarios']}")
    print(f"   成功场景数: {summary['successful_scenarios']}")
    print(f"   成功率: {summary['success_rate']:.1f}%")
    print(f"   平均处理时间: {summary['avg_processing_time']:.2f}ms")
    
    # 场景详细结果
    print(f"\n📋 场景详细结果:")
    for result in report["scenario_results"]:
        status = "✅" if result["success"] else "❌"
        print(f"   {status} {result['scenario_name']}: {result['processing_time']:.2f}ms")
        if not result["success"] and result.get("error_message"):
            print(f"      错误: {result['error_message']}")
    
    # 性能数据
    print(f"\n⚡ 性能分析:")
    for perf_data in report["performance_data"]:
        if perf_data["test_type"] == "performance_comparison":
            metrics = perf_data["metrics"]
            print(f"   总请求数: {metrics['total_requests']}")
            print(f"   加速请求数: {metrics['accelerated_requests']}")
            print(f"   复杂保护请求数: {metrics['complex_protected_requests']}")
            print(f"   加速率: {metrics['acceleration_rate']:.1f}%")
            print(f"   平均响应时间: {metrics['avg_response_time']:.2f}ms")
            print(f"   P95响应时间: {metrics['p95_response_time']:.2f}ms")
            print(f"   P99响应时间: {metrics['p99_response_time']:.2f}ms")
        
        elif perf_data["test_type"] == "concurrency_test":
            print(f"   并发测试: {perf_data['successful_requests']}/{perf_data['total_requests']} 成功")
            print(f"   并发成功率: {perf_data['success_rate']:.1f}%")
            print(f"   并发平均时间: {perf_data['avg_processing_time']:.2f}ms")
            print(f"   并发最大时间: {perf_data['max_processing_time']:.2f}ms")
    
    # 业务逻辑验证
    print(f"\n🛡️ 业务逻辑验证:")
    for perf_data in report["performance_data"]:
        if perf_data["test_type"] == "business_logic_validation":
            for result in perf_data["results"]:
                status = "✅" if result["success"] else "❌"
                print(f"   {status} {result['scenario']}: {result['description']}")
    
    # 改进建议
    print(f"\n💡 改进建议:")
    for recommendation in report["recommendations"]:
        print(f"   • {recommendation}")


def print_performance_comparison(report: dict):
    """打印性能对比分析"""
    print("\n" + "="*80)
    print("📈 性能对比分析")
    print("="*80)
    
    # 查找性能对比数据
    perf_data = None
    for data in report["performance_data"]:
        if data["test_type"] == "performance_comparison":
            perf_data = data
            break
    
    if not perf_data:
        print("❌ 未找到性能对比数据")
        return
    
    metrics = perf_data["metrics"]
    raw_times = perf_data["raw_times"]
    
    print(f"🚀 新系统性能表现:")
    print(f"   总请求数: {metrics['total_requests']}")
    print(f"   加速处理数: {metrics['accelerated_requests']} ({metrics['acceleration_rate']:.1f}%)")
    print(f"   复杂保护数: {metrics['complex_protected_requests']}")
    print(f"   平均响应时间: {metrics['avg_response_time']:.2f}ms")
    print(f"   P95响应时间: {metrics['p95_response_time']:.2f}ms")
    print(f"   P99响应时间: {metrics['p99_response_time']:.2f}ms")
    print(f"   错误率: {metrics['error_rate']:.1f}%")
    print(f"   业务逻辑成功率: {metrics['business_logic_success_rate']:.1f}%")
    
    # 响应时间分布
    print(f"\n📊 响应时间分布:")
    fast_requests = sum(1 for t in raw_times if t < 10)
    medium_requests = sum(1 for t in raw_times if 10 <= t < 100)
    slow_requests = sum(1 for t in raw_times if t >= 100)
    
    print(f"   极快 (<10ms): {fast_requests} ({fast_requests/len(raw_times)*100:.1f}%)")
    print(f"   快速 (10-100ms): {medium_requests} ({medium_requests/len(raw_times)*100:.1f}%)")
    print(f"   较慢 (>=100ms): {slow_requests} ({slow_requests/len(raw_times)*100:.1f}%)")
    
    # 性能提升估算
    print(f"\n🎯 性能提升估算:")
    # 假设原系统平均响应时间为500ms
    original_avg_time = 500.0
    improvement_factor = original_avg_time / metrics['avg_response_time']
    improvement_percentage = (1 - metrics['avg_response_time'] / original_avg_time) * 100
    
    print(f"   原系统估算平均时间: {original_avg_time:.0f}ms")
    print(f"   新系统平均时间: {metrics['avg_response_time']:.2f}ms")
    print(f"   性能提升倍数: {improvement_factor:.1f}x")
    print(f"   响应时间减少: {improvement_percentage:.1f}%")


def print_business_logic_analysis(report: dict):
    """打印业务逻辑分析"""
    print("\n" + "="*80)
    print("🛡️ 业务逻辑完整性分析")
    print("="*80)
    
    # 查找业务逻辑验证数据
    business_data = None
    for data in report["performance_data"]:
        if data["test_type"] == "business_logic_validation":
            business_data = data
            break
    
    if not business_data:
        print("❌ 未找到业务逻辑验证数据")
        return
    
    results = business_data["results"]
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r["success"])
    
    print(f"📊 业务逻辑验证结果:")
    print(f"   总验证项: {total_tests}")
    print(f"   通过验证: {successful_tests}")
    print(f"   验证成功率: {successful_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细验证结果:")
    for result in results:
        status = "✅" if result["success"] else "❌"
        print(f"   {status} {result['scenario']}")
        print(f"      描述: {result['description']}")
        if not result["success"] and "error" in result:
            print(f"      错误: {result['error']}")
    
    # 业务逻辑保护评估
    print(f"\n🔒 业务逻辑保护评估:")
    if successful_tests == total_tests:
        print("   ✅ 所有业务逻辑验证通过，系统保护完善")
    elif successful_tests >= total_tests * 0.8:
        print("   ⚠️ 大部分业务逻辑验证通过，少数问题需要修复")
    else:
        print("   ❌ 业务逻辑验证失败较多，需要重点关注")


def save_detailed_report(report: dict, output_file: str):
    """保存详细报告"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"\n💾 详细报告已保存到: {output_file}")
    except Exception as e:
        print(f"\n❌ 保存报告失败: {e}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行集成测试")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--output-file", "-o", default="integration_test_report.json", help="输出文件")
    parser.add_argument("--quick", "-q", action="store_true", help="快速测试模式")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    
    print("🚀 开始运行集成测试...")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 运行综合测试
        start_time = datetime.now()
        report = await integration_test_framework.run_comprehensive_test()
        end_time = datetime.now()
        
        # 添加测试执行信息
        report["test_execution"] = {
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "duration_seconds": (end_time - start_time).total_seconds(),
            "test_mode": "quick" if args.quick else "comprehensive"
        }
        
        # 打印结果
        print_test_summary(report)
        print_performance_comparison(report)
        print_business_logic_analysis(report)
        
        # 保存详细报告
        save_detailed_report(report, args.output_file)
        
        # 总结
        print("\n" + "="*80)
        print("🎉 集成测试完成")
        print("="*80)
        print(f"⏱️ 总耗时: {(end_time - start_time).total_seconds():.1f}秒")
        
        # 根据测试结果确定退出码
        success_rate = report["test_summary"]["success_rate"]
        if success_rate >= 90:
            print("✅ 测试结果优秀，系统表现良好")
            return 0
        elif success_rate >= 70:
            print("⚠️ 测试结果良好，有少数问题需要关注")
            return 0
        else:
            print("❌ 测试结果不理想，需要重点修复")
            return 1
            
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        import traceback
        if args.verbose:
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    # 运行测试
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
