#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息模板集成测试

测试重构后的模块是否能正确使用消息模板配置
"""

import sys
import os
import logging
import asyncio

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from backend.agents.strategies.greeting_strategy import GreetingStrategy
from backend.agents.decision_types import AnalyzedContext
from backend.handlers.general_request_handler import GeneralRequestHandler
from backend.handlers.base_action_handler import ActionContext
from backend.config.unified_config_loader import get_unified_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestMessageTemplateIntegration:
    """消息模板集成测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.config = get_unified_config()
    
    def test_greeting_strategy_templates(self):
        """测试问候策略使用模板"""
        logger.info("测试问候策略模板使用...")

        # 创建问候策略实例
        strategy = GreetingStrategy()

        # 创建简单的测试上下文对象
        class SimpleContext:
            def __init__(self):
                self.message = "你好"
                self.intent = "greeting"
                self.emotion = "neutral"
                self.confidence = 0.9
                self.entities = []
                self.keywords = ["你好"]

        context = SimpleContext()

        # 测试选择问候回复
        response = strategy._select_greeting_response(context)
        assert response is not None
        assert len(response) > 0
        logger.info(f"问候策略回复: {response[:50]}...")

        # 验证回复来自配置而非硬编码
        basic_template = self.config.get_message_template("greeting.basic")
        assert response == basic_template
        logger.info("✅ 问候策略正确使用配置模板")
    
    def test_general_request_handler_templates(self):
        """测试通用请求处理器使用模板"""
        logger.info("测试通用请求处理器模板使用...")

        # 创建处理器实例
        handler = GeneralRequestHandler()

        # 创建简单的测试上下文对象
        class SimpleActionContext:
            def __init__(self):
                self.action = "general_request"
                self.message = "我需要帮助"
                self.session_id = "test_session"
                self.user_id = "test_user"

        context = SimpleActionContext()

        # 测试回退回复
        fallback_reply = handler._get_fallback_reply(context)
        assert fallback_reply is not None
        assert len(fallback_reply) > 0
        logger.info(f"回退回复: {fallback_reply}")

        # 验证回复来自配置
        expected_template = self.config.get_message_template("clarification.need_more_info")
        assert fallback_reply == expected_template
        logger.info("✅ 通用请求处理器正确使用配置模板")
    
    def test_error_template_consistency(self):
        """测试错误模板一致性"""
        logger.info("测试错误模板一致性...")
        
        # 测试各种错误模板
        error_templates = [
            "error.system",
            "error.processing",
            "error.message_processing",
            "error.request_processing",
            "error.general_fallback",
            "error.emergency_fallback"
        ]
        
        for template_key in error_templates:
            template = self.config.get_message_template(template_key)
            assert template is not None, f"模板 {template_key} 不存在"
            assert len(template) > 0, f"模板 {template_key} 为空"
            # 检查模板内容是否包含错误相关词汇
            template_lower = template.lower()
            error_words = ["抱歉", "错误", "问题", "失败", "遇到"]
            has_error_word = any(word in template_lower for word in error_words)
            if not has_error_word:
                logger.warning(f"模板 {template_key} 可能不包含错误相关词汇: {template}")
            logger.info(f"✅ {template_key}: {template[:30]}...")
        
        logger.info("✅ 所有错误模板一致性检查通过")
    
    def test_template_fallback_mechanism(self):
        """测试模板回退机制"""
        logger.info("测试模板回退机制...")
        
        # 测试不存在的模板
        missing_template = self.config.get_message_template("nonexistent.template")
        assert missing_template == ""
        
        # 测试带默认值的不存在模板
        missing_with_default = self.config.get_message_template(
            "nonexistent.template", 
            default="默认回退消息"
        )
        assert missing_with_default == "默认回退消息"
        
        logger.info("✅ 模板回退机制正常工作")
    
    def test_template_variable_replacement(self):
        """测试模板变量替换功能"""
        logger.info("测试模板变量替换功能...")
        
        # 测试带变量的模板
        template_with_vars = self.config.get_message_template(
            "error.processing",
            error_msg="网络连接超时"
        )
        
        assert "网络连接超时" in template_with_vars
        logger.info(f"变量替换结果: {template_with_vars}")
        
        logger.info("✅ 模板变量替换功能正常")


def run_integration_tests():
    """运行集成测试"""
    logger.info("开始消息模板集成测试...")
    
    try:
        # 创建测试实例
        test_instance = TestMessageTemplateIntegration()
        test_instance.setup_method()
        
        # 运行所有测试
        test_methods = [
            test_instance.test_greeting_strategy_templates,
            test_instance.test_general_request_handler_templates,
            test_instance.test_error_template_consistency,
            test_instance.test_template_fallback_mechanism,
            test_instance.test_template_variable_replacement
        ]
        
        passed = 0
        failed = 0
        
        for test_method in test_methods:
            try:
                test_method()
                logger.info(f"✅ {test_method.__name__} 通过")
                passed += 1
            except Exception as e:
                logger.error(f"❌ {test_method.__name__} 失败: {e}")
                failed += 1
        
        logger.info(f"\n集成测试结果: {passed} 通过, {failed} 失败")
        
        if failed == 0:
            logger.info("🎉 所有集成测试通过！消息模板重构成功！")
            return True
        else:
            logger.error("⚠️ 部分集成测试失败，请检查重构代码")
            return False
            
    except Exception as e:
        logger.error(f"集成测试运行失败: {e}")
        return False


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
