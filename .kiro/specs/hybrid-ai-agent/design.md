# 设计文档

## 概述

本设计文档描述了知识库问答与需求采集混合型AI代理系统的技术架构。该系统以现有需求采集功能为核心，通过配置开关的方式可选地集成RAG（检索增强生成）知识库问答功能。设计原则是确保现有需求采集功能完全不受影响，知识库功能作为增强特性可以安全地启用或禁用。

## 架构设计

### 整体架构

```mermaid
graph TB
    A[用户输入] --> B[配置检查]
    B --> C{知识库功能启用?}

    C -->|否| D[原有两层匹配机制]
    C -->|是| E[增强两层匹配机制]

    D --> F[Layer1: 关键词匹配]
    D --> H[Layer2: LLM意图识别]

    E --> I[Layer1: 关键词匹配+知识库关键词]
    E --> K[Layer2: LLM意图识别+知识库意图]

    F --> L{需求采集路由}
    H --> L

    I --> M{混合意图路由}
    K --> M
    
    L -->|需求采集| N[现有需求采集流程]
    L -->|其他| O[澄清对话]
    
    M -->|知识库查询| P[RAG知识库代理]
    M -->|需求采集| N
    M -->|其他| O
    
    P --> Q[ChromaDB向量检索]
    P --> R[LLM生成回答]
    
    N --> S[领域分类]
    N --> T[类别选择]
    N --> U[关注点跟踪]
    
    Q --> V[用户响应]
    R --> V
    S --> V
    T --> V
    U --> V
    O --> V
```

### 核心组件

#### 1. 配置管理器

**组件名称：** `KnowledgeBaseConfigManager`

**职责：**
- 管理知识库功能的启用/禁用状态
- 提供运行时配置检查
- 支持热配置更新（无需重启）

**接口设计：**
```python
class KnowledgeBaseConfigManager:
    def __init__(self):
        self.enabled = False  # 默认禁用
        
    def is_knowledge_base_enabled(self) -> bool:
        # 检查知识库功能是否启用
        pass
    
    def enable_knowledge_base(self) -> bool:
        # 启用知识库功能
        pass
    
    def disable_knowledge_base(self) -> bool:
        # 禁用知识库功能，确保安全回退
        pass
```

#### 2. 增强型意图识别层

**组件名称：** `HybridIntentRecognitionEngine`

**职责：**
- 继承现有的`AcceleratedIntentDecisionEngine`，保持完全兼容
- 仅在配置启用时增加知识库查询意图识别
- 配置禁用时行为与原系统完全一致

**接口设计：**
```python
class HybridIntentRecognitionEngine(AcceleratedIntentDecisionEngine):
    def __init__(self, llm_service, config_manager, **kwargs):
        super().__init__(llm_service, **kwargs)
        self.config_manager = config_manager
        
    async def analyze(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        # 首先检查配置
        if not self.config_manager.is_knowledge_base_enabled():
            # 配置禁用时，完全使用原有逻辑
            return await super().analyze(message, context)
        
        # 配置启用时，扩展意图识别
        return await self._analyze_with_knowledge_base(message, context)
    
    def _is_knowledge_base_query(self, message: str, context: Dict[str, Any]) -> bool:
        # 判断是否为知识库查询（仅在启用时调用）
        pass
```

#### 2. RAG知识库代理

**组件名称：** `RAGKnowledgeBaseAgent`

**职责：**
- 基于现有的`KnowledgeBaseAgent`进行扩展
- 实现向量检索和生成式回答
- 集成ChromaDB向量数据库
- 提供上下文感知的知识库问答

**接口设计：**
```python
class RAGKnowledgeBaseAgent:
    def __init__(self, chroma_client, llm_service, embedding_model):
        pass
    
    async def query(self, question: str, context: Dict[str, Any]) -> Dict[str, Any]:
        # 执行RAG查询流程
        pass
    
    def _retrieve_relevant_docs(self, question: str, top_k: int = 5) -> List[Dict]:
        # 向量检索相关文档
        pass
    
    def _generate_answer(self, question: str, docs: List[Dict]) -> str:
        # 基于检索文档生成答案
        pass
```

#### 3. 混合对话路由器

**组件名称：** `HybridConversationRouter`

**职责：**
- 根据意图识别结果路由到相应的处理器
- 管理知识库问答和需求采集之间的切换
- 维护对话上下文和状态

**接口设计：**
```python
class HybridConversationRouter:
    def __init__(self, rag_agent, requirement_flow, intent_engine):
        pass
    
    async def route_message(self, message: str, session_context: Dict) -> Dict[str, Any]:
        # 路由消息到相应的处理器
        pass
    
    def _handle_mode_switch(self, from_mode: str, to_mode: str, context: Dict) -> Dict:
        # 处理模式切换
        pass
```

#### 4. 知识库管理器

**组件名称：** `KnowledgeBaseManager`

**职责：**
- 管理知识库文档的摄入和更新
- 处理文档分块和向量化
- 维护ChromaDB集合

**接口设计：**
```python
class KnowledgeBaseManager:
    def __init__(self, chroma_client, embedding_model):
        pass
    
    def ingest_documents(self, documents: List[Dict]) -> bool:
        # 摄入新文档
        pass
    
    def update_document(self, doc_id: str, content: str) -> bool:
        # 更新现有文档
        pass
    
    def _chunk_document(self, content: str) -> List[str]:
        # 文档分块
        pass
```

## 数据流设计

### 1. 知识库摄入流程

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant KBM as KnowledgeBaseManager
    participant Chunker as 文档分块器
    participant Embedder as 嵌入模型
    participant ChromaDB as ChromaDB
    
    Admin->>KBM: 上传文档
    KBM->>Chunker: 分块处理
    Chunker->>KBM: 返回文档块
    KBM->>Embedder: 生成向量嵌入
    Embedder->>KBM: 返回向量
    KBM->>ChromaDB: 存储文档和向量
    ChromaDB->>KBM: 确认存储
    KBM->>Admin: 摄入完成
```

### 2. 用户查询处理流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Router as HybridConversationRouter
    participant Intent as HybridIntentRecognitionEngine
    participant RAG as RAGKnowledgeBaseAgent
    participant Req as RequirementCollectionFlow
    participant ChromaDB as ChromaDB
    participant LLM as LLM服务
    
    User->>Router: 用户输入
    Router->>Intent: 意图识别
    
    alt 知识库查询
        Intent->>Router: 知识库查询意图
        Router->>RAG: 处理查询
        RAG->>ChromaDB: 向量检索
        ChromaDB->>RAG: 相关文档
        RAG->>LLM: 生成答案
        LLM->>RAG: 生成结果
        RAG->>Router: 知识库回答
    else 需求采集
        Intent->>Router: 需求采集意图
        Router->>Req: 处理需求
        Req->>Router: 需求采集回答
    else 模糊意图
        Intent->>Router: 模糊意图
        Router->>User: 澄清问题
    end
    
    Router->>User: 最终回答
```

## 技术选型

### 向量数据库
- **选择：** ChromaDB
- **理由：** 
  - 项目中已有ChromaDB基础设施
  - 支持中文嵌入模型（moka-ai/m3e-base）
  - 轻量级，易于部署和维护
  - 支持元数据过滤和混合搜索

### 文本分块策略
- **策略：** 递归字符分块 + 语义分块
- **参数：**
  - 块大小：500-800字符
  - 重叠：100字符
  - 分隔符：段落、句子、标点符号

### 嵌入模型
- **选择：** moka-ai/m3e-base
- **理由：**
  - 专为中文优化
  - 项目中已有使用经验
  - 支持多语言（中英文混合）
  - 模型大小适中，推理速度快

### LLM集成
- **方式：** 复用现有LLM服务
- **模型：** 使用项目配置的DeepSeek等模型
- **集成点：** 通过现有的`AutoGenLLMServiceAgent`

## 接口定义

### 1. 知识库查询接口

```python
# 请求格式
{
    "query": "如何注册账号？",
    "context": {
        "session_id": "session_123",
        "user_id": "user_456",
        "current_mode": "knowledge_base"
    },
    "filters": {
        "role": "company",  # 可选：角色过滤
        "category": "account"  # 可选：类别过滤
    }
}

# 响应格式
{
    "success": true,
    "answer": "注册账号的步骤如下：...",
    "sources": [
        {
            "document": "company/register-employer-account.md",
            "relevance_score": 0.95,
            "snippet": "注册雇主账号需要..."
        }
    ],
    "processing_info": {
        "method": "rag_retrieval",
        "retrieval_time": 0.05,
        "generation_time": 0.8,
        "total_time": 0.85
    }
}
```

### 2. 模式切换接口

```python
# 内部接口，用于模式切换
{
    "from_mode": "knowledge_base",
    "to_mode": "requirement_collection",
    "context": {
        "session_id": "session_123",
        "conversation_history": [...],
        "preserved_state": {...}
    },
    "switch_reason": "user_intent_change"
}
```

## 错误处理

### 1. 知识库查询失败
- **场景：** 向量检索失败或无相关文档
- **处理：** 提供搜索建议，询问是否切换到需求采集模式

### 2. LLM生成失败
- **场景：** LLM服务不可用或生成异常
- **处理：** 返回检索到的原始文档片段，标注为"参考信息"

### 3. 模式切换失败
- **场景：** 上下文丢失或状态不一致
- **处理：** 重新初始化对话状态，询问用户当前需求

## 测试策略

### 1. 单元测试
- 意图识别准确性测试
- 向量检索相关性测试
- 文档分块质量测试
- RAG生成质量测试

### 2. 集成测试
- 端到端对话流程测试
- 模式切换功能测试
- 性能基准测试
- 并发处理测试

### 3. 用户验收测试
- 知识库问答准确性评估
- 用户体验一致性测试
- 响应时间满意度测试

## 配置管理与风险控制

### 1. 配置开关设计

```yaml
# backend/config/knowledge_base.yaml
knowledge_base:
  enabled: false  # 默认禁用，确保现有功能不受影响
  
  # 功能级别开关
  features:
    rag_query: false          # RAG查询功能
    intent_enhancement: false # 意图识别增强
    mode_switching: false     # 模式切换功能
  
  # 安全设置
  safety:
    fallback_to_requirement: true  # 失败时回退到需求采集
    max_retry_attempts: 3          # 最大重试次数
    timeout_seconds: 10            # 查询超时时间
  
  # 性能设置
  performance:
    cache_enabled: true
    max_concurrent_queries: 5
    vector_search_limit: 10
```

### 2. 渐进式启用策略

**阶段1：基础设施准备**
- 部署ChromaDB和嵌入模型
- 配置保持禁用状态
- 验证基础组件正常工作

**阶段2：知识库构建**
- 启用文档摄入功能
- 构建向量数据库
- 验证检索功能

**阶段3：功能测试**
- 启用RAG查询功能
- 小范围用户测试
- 监控系统稳定性

**阶段4：全面启用**
- 启用所有知识库功能
- 监控性能指标
- 收集用户反馈

### 3. 回退机制

```python
class SafetyManager:
    def __init__(self):
        self.error_count = 0
        self.max_errors = 5
        
    def handle_knowledge_base_error(self, error: Exception):
        self.error_count += 1
        
        if self.error_count >= self.max_errors:
            # 自动禁用知识库功能
            self.disable_knowledge_base_temporarily()
            
        # 回退到需求采集模式
        return self.fallback_to_requirement_collection()
```

## 部署策略

### 1. 零风险部署

**步骤1：代码部署**
- 部署新代码，配置保持禁用
- 验证现有功能正常工作
- 监控系统指标

**步骤2：基础设施部署**
- 部署ChromaDB实例
- 配置嵌入模型
- 验证连接正常

**步骤3：数据准备**
- 摄入知识库文档
- 构建向量索引
- 验证检索功能

**步骤4：功能启用**
- 逐步启用各项功能
- 实时监控系统状态
- 准备快速回退

### 2. 监控指标

```python
# 关键监控指标
monitoring_metrics = {
    "system_health": {
        "requirement_collection_success_rate": ">95%",
        "response_time_p95": "<2s",
        "error_rate": "<1%"
    },
    "knowledge_base": {
        "query_success_rate": ">90%",
        "retrieval_accuracy": ">80%",
        "generation_quality": ">85%"
    }
}
```

## 性能优化

### 1. 缓存策略
- 向量检索结果缓存
- 常见问题答案缓存
- 嵌入向量缓存

### 2. 异步处理
- 文档摄入异步化
- 向量计算异步化
- LLM调用异步化

### 3. 资源管理
- 连接池管理
- 内存使用优化
- 模型加载优化

### 4. 降级策略
- 知识库不可用时自动降级
- 保持需求采集功能正常
- 提供用户友好的错误提示