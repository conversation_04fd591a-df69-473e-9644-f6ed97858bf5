# Requirements Document

## Introduction

本需求文档描述了对现有混合AI代理系统进行简化重构的需求。当前系统在识别部分和状态管理部分存在过高的复杂度，导致频繁的bug修复和维护困难。通过四位一体的简化方案（去掉语义识别、采用状态机、优化决策引擎、简化配置文件），我们希望在保持核心业务逻辑的同时，大幅降低系统复杂度。

## Requirements

### Requirement 1

**User Story:** 作为系统维护者，我希望简化意图识别流程，以便减少识别错误和提高处理速度

#### Acceptance Criteria

1. WHEN 用户输入消息时 THEN 系统应该使用两层识别（关键词匹配 + LLM识别）而不是三层识别
2. WHEN 关键词匹配置信度大于0.9时 THEN 系统应该直接返回结果，跳过LLM调用
3. WHEN 关键词匹配失败或置信度不足时 THEN 系统应该回退到LLM智能识别
4. WHEN 处理简单指令时 THEN 系统响应时间应该小于100ms
5. WHEN 处理复杂查询时 THEN 系统应该保持原有的识别准确率

### Requirement 2

**User Story:** 作为系统架构师，我希望采用状态机模式管理对话状态，以便清晰地控制状态转换和业务流程

#### Acceptance Criteria

1. WHEN 系统启动时 THEN 应该初始化为IDLE状态
2. WHEN 用户开始需求描述时 THEN 系统应该从IDLE转换到COLLECTING状态
3. WHEN 需求收集完成时 THEN 系统应该从COLLECTING转换到DOCUMENTING状态
4. WHEN 文档确认完成时 THEN 系统应该从DOCUMENTING转换回IDLE状态
5. WHEN 用户请求重新开始时 THEN 系统应该从任何状态转换回IDLE状态
6. WHEN 状态转换发生时 THEN 系统应该记录转换日志用于调试

### Requirement 3

**User Story:** 作为业务分析师，我希望保留核心业务逻辑的差异化处理，以便不同类型的需求得到专业化的服务

#### Acceptance Criteria

1. WHEN 用户描述营销需求时 THEN 系统应该聚焦营销四要素（目标受众、营销渠道、预算范围、推广周期）
2. WHEN 用户描述软件开发需求时 THEN 系统应该聚焦技术要素（功能需求、技术架构、性能要求、项目约束）
3. WHEN 用户描述设计需求时 THEN 系统应该聚焦设计要素（设计风格、目标用户、应用场景、品牌要求）
4. WHEN 无法识别具体业务类型时 THEN 系统应该使用通用的需求收集流程
5. WHEN 用户表达焦虑情绪时 THEN 系统应该使用温和耐心的语气回应

### Requirement 4

**User Story:** 作为决策引擎开发者，我希望简化决策逻辑，以便减少配置错误和提高决策准确性

#### Acceptance Criteria

1. WHEN 进行决策时 THEN 系统应该只考虑状态和意图两个维度，不再使用子意图和情感的复杂组合
2. WHEN 在IDLE状态接收到business_requirement意图时 THEN 系统应该执行start_requirement_gathering动作
3. WHEN 在COLLECTING状态接收到任何输入时 THEN 系统应该默认当作回答处理，执行process_answer_and_continue动作
4. WHEN 在DOCUMENTING状态接收到非confirm/restart的输入时 THEN 系统应该默认当作修改请求处理
5. WHEN 决策规则匹配失败时 THEN 系统应该有明确的默认处理策略

### Requirement 5

**User Story:** 作为配置管理员，我希望大幅简化配置文件，以便降低维护成本和配置错误

#### Acceptance Criteria

1. WHEN 配置文件加载时 THEN 总行数应该少于200行（相比当前1000+行）
2. WHEN 添加新的业务类型时 THEN 只需要在business_rules部分添加配置，不需要修改多个地方
3. WHEN 修改关键词规则时 THEN 应该有高置信度和中置信度的明确分类
4. WHEN 配置文件格式错误时 THEN 系统应该提供清晰的错误提示
5. WHEN 系统启动时 THEN 应该验证配置文件的完整性和正确性

### Requirement 6

**User Story:** 作为系统运维人员，我希望新系统保持向后兼容，以便平滑迁移现有功能

#### Acceptance Criteria

1. WHEN 现有的API调用时 THEN 新系统应该返回兼容的响应格式
2. WHEN 现有的业务流程执行时 THEN 核心功能应该保持不变
3. WHEN 数据库查询时 THEN 应该兼容现有的数据结构
4. WHEN 日志记录时 THEN 应该保持现有的日志格式便于监控
5. WHEN 性能监控时 THEN 应该提供新旧系统的对比指标

### Requirement 7

**User Story:** 作为质量保证工程师，我希望新系统具有更好的可测试性和可观测性

#### Acceptance Criteria

1. WHEN 执行单元测试时 THEN 每个组件应该可以独立测试
2. WHEN 执行集成测试时 THEN 状态转换应该可以被验证
3. WHEN 系统运行时 THEN 应该提供详细的决策日志
4. WHEN 性能测试时 THEN 应该记录各层处理时间
5. WHEN 错误发生时 THEN 应该提供清晰的错误堆栈和上下文信息