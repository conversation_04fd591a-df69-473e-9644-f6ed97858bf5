# 后台管理系统需求文档

## 项目概述

基于现有智能需求采集系统，构建一个轻量级的后台管理系统，专注于管理LLM配置、场景映射和模板维护三个核心功能。该系统将提供直观的Web界面，帮助开发人员和运维人员高效地维护系统的核心配置。

## 需求列表

### 需求1：LLM配置管理功能

**用户故事：** 作为系统管理员，我希望能够通过Web界面管理LLM模型配置，以便于调整模型参数和连接设置而无需直接编辑配置文件。

#### 验收标准

1. WHEN 管理员访问LLM配置页面 THEN 系统应显示所有已配置的LLM模型列表
2. WHEN 管理员查看模型详情 THEN 系统应显示模型的API密钥、基础URL、超时设置、重试次数等参数
3. WHEN 管理员编辑模型配置 THEN 系统应提供表单界面修改temperature、max_tokens、timeout等参数
4. WHEN 管理员测试模型连接 THEN 系统应提供连接测试功能验证API密钥和网络连通性
5. WHEN 管理员保存配置更改 THEN 系统应验证配置的有效性并实时更新unified_config.yaml文件
6. WHEN 管理员添加新模型 THEN 系统应支持添加新的LLM提供商和模型配置
7. WHEN 配置出现错误 THEN 系统应提供详细的错误信息和修复建议

### 需求2：场景映射管理功能

**用户故事：** 作为系统管理员，我希望能够管理不同业务场景到LLM模型的映射关系，以便于优化不同任务的模型选择和性能。

#### 验收标准

1. WHEN 管理员访问场景映射页面 THEN 系统应显示所有场景及其对应的模型映射关系
2. WHEN 管理员查看场景详情 THEN 系统应显示场景名称、当前映射的模型、场景参数配置
3. WHEN 管理员修改场景映射 THEN 系统应提供下拉选择方式选择可用的LLM模型
4. WHEN 管理员编辑场景参数 THEN 系统应提供表单界面调整temperature、max_tokens、timeout等场景特定参数
5. WHEN 管理员保存映射更改 THEN 系统应验证映射的有效性并更新配置文件
6. WHEN 管理员测试场景配置 THEN 系统应提供场景测试功能验证配置是否正常工作
7. WHEN 场景配置冲突 THEN 系统应检测并提示配置冲突和解决建议

### 需求3：模板管理功能

**用户故事：** 作为系统管理员，我希望能够管理系统中的提示词模板和消息模板，以便于优化用户交互体验和系统响应质量。

#### 验收标准

1. WHEN 管理员访问模板管理页面 THEN 系统应分类显示提示词模板（backend/prompts/目录）和消息模板（unified_config.yaml中的message_templates）
2. WHEN 管理员查看提示词模板 THEN 系统应显示所有.md文件的列表，包含文件名和最后修改时间
3. WHEN 管理员编辑提示词模板 THEN 系统应提供Markdown编辑器界面，支持语法高亮和实时预览
4. WHEN 管理员查看消息模板 THEN 系统应以树形结构显示message_templates配置的层次结构
5. WHEN 管理员编辑消息模板 THEN 系统应提供文本编辑器，支持变量插入和格式化
6. WHEN 管理员保存提示词模板 THEN 系统应直接更新对应的.md文件
7. WHEN 管理员保存消息模板 THEN 系统应验证YAML语法并更新unified_config.yaml文件
8. WHEN 管理员修改模板后 THEN 系统应提示哪些服务需要重启以使更改生效