# 后台管理系统设计文档

## 概述

基于现有智能需求采集系统，设计一个轻量级的后台管理系统，专注于LLM配置、场景映射和模板管理三个核心功能。采用前后端分离架构，后端基于FastAPI提供RESTful API，前端使用React + TypeScript构建简洁的管理界面。系统直接操作现有的配置文件和模板文件，不引入额外的数据存储。

## 架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                 轻量级后台管理系统架构                        │
├─────────────────────────────────────────────────────────────┤
│  前端层 (React + TypeScript)                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ LLM配置管理  │ │ 场景映射管理  │ │ 模板管理     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  API层 (FastAPI)                                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ 配置API │ 场景API │ 模板API │ 测试API │ 文件操作API    │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层 (Service Layer)                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 配置服务     │ │ 场景服务     │ │ 模板服务     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  文件操作层 (File Access Layer)                            │
│  ┌─────────────┐ ┌─────────────┐                           │
│  │ YAML操作     │ │ Markdown操作 │                           │
│  └─────────────┘ └─────────────┘                           │
├─────────────────────────────────────────────────────────────┤
│  文件存储层 (File Storage)                                 │
│  ┌─────────────┐ ┌─────────────┐                           │
│  │unified_config│ │backend/prompts│                         │
│  │    .yaml     │ │    /*.md     │                         │
│  └─────────────┘ └─────────────┘                           │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈选择

**后端技术栈：**
- **FastAPI**: 轻量级异步Web框架
- **PyYAML**: YAML文件处理
- **Pydantic**: 数据验证和序列化
- **Uvicorn**: ASGI服务器

**前端技术栈：**
- **React 18**: 前端框架
- **TypeScript**: 类型安全
- **Ant Design**: UI组件库
- **React Query**: 数据获取和缓存
- **Monaco Editor**: 代码编辑器

## 组件设计

### 后端组件

#### 1. API路由层 (admin_api/)

```python
admin_api/
├── main.py              # FastAPI应用入口
├── routers/
│   ├── config.py        # LLM配置管理API
│   ├── scenario.py      # 场景映射管理API
│   ├── template.py      # 模板管理API
│   └── test.py          # 测试功能API
└── middleware/
    └── cors.py          # CORS中间件
```

#### 2. 服务层 (admin_services/)

```python
admin_services/
├── config_service.py    # 配置文件操作服务
├── scenario_service.py  # 场景映射服务
├── template_service.py  # 模板文件操作服务
└── test_service.py      # 测试功能服务
```

#### 3. 文件操作层 (admin_utils/)

```python
admin_utils/
├── yaml_handler.py      # YAML文件读写
├── file_handler.py      # 通用文件操作
└── validation.py        # 配置验证
```

### 前端组件

#### 1. 页面组件 (pages/)

```typescript
pages/
├── Dashboard/           # 主仪表板
├── ConfigManagement/    # LLM配置管理
├── ScenarioMapping/     # 场景映射管理
└── TemplateManagement/  # 模板管理
```

#### 2. 业务组件 (components/)

```typescript
components/
├── ConfigEditor/        # 配置编辑器
├── ScenarioMapper/      # 场景映射器
├── TemplateEditor/      # 模板编辑器
└── TestPanel/           # 测试面板
```

## 数据模型设计

### API数据模型

#### 1. LLM配置相关模型

```python
from pydantic import BaseModel
from typing import Dict, Any, Optional

class LLMModel(BaseModel):
    name: str
    provider: str
    api_key: str
    api_base: str
    model_name: str
    temperature: float
    max_tokens: int
    timeout: int
    max_retries: int

class LLMConfigUpdate(BaseModel):
    model_name: str
    config: Dict[str, Any]

class ConnectionTest(BaseModel):
    model_name: str
    test_prompt: str = "Hello, this is a test."
```

#### 2. 场景映射相关模型

```python
class ScenarioMapping(BaseModel):
    scenario_name: str
    model_name: str
    parameters: Dict[str, Any]

class ScenarioTest(BaseModel):
    scenario_name: str
    test_input: str
    expected_output: Optional[str] = None
```

#### 3. 模板相关模型

```python
class PromptTemplate(BaseModel):
    filename: str
    content: str
    last_modified: str

class MessageTemplate(BaseModel):
    path: str
    content: str
    template_type: str  # string, object, array

class TemplateTest(BaseModel):
    template_name: str
    template_type: str  # prompt, message
    test_data: Dict[str, Any]
```

## 接口设计

### RESTful API设计

#### 1. LLM配置管理API

```python
# GET /api/admin/config/llm - 获取所有LLM配置
# GET /api/admin/config/llm/{model_name} - 获取指定模型配置
# PUT /api/admin/config/llm/{model_name} - 更新模型配置
# POST /api/admin/config/llm - 添加新模型配置
# POST /api/admin/config/llm/{model_name}/test - 测试模型连接
# POST /api/admin/config/reload - 重新加载配置
```

#### 2. 场景映射管理API

```python
# GET /api/admin/scenario/mappings - 获取所有场景映射
# GET /api/admin/scenario/mappings/{scenario_name} - 获取指定场景映射
# PUT /api/admin/scenario/mappings/{scenario_name} - 更新场景映射
# POST /api/admin/scenario/{scenario_name}/test - 测试场景配置
# GET /api/admin/scenario/available-models - 获取可用模型列表
```

#### 3. 模板管理API

```python
# GET /api/admin/templates/prompts - 获取所有提示词模板
# GET /api/admin/templates/prompts/{filename} - 获取指定提示词模板
# PUT /api/admin/templates/prompts/{filename} - 更新提示词模板
# GET /api/admin/templates/messages - 获取所有消息模板
# GET /api/admin/templates/messages/{path} - 获取指定消息模板
# PUT /api/admin/templates/messages/{path} - 更新消息模板
# POST /api/admin/templates/test - 测试模板渲染
```

## 用户界面设计

### 1. 整体布局

```
┌─────────────────────────────────────────────────────────────┐
│ Header: 后台管理系统 + 系统状态指示器                        │
├─────────────────────────────────────────────────────────────┤
│ │                                                           │
│ │ Sidebar:                    Main Content Area            │
│ │ ├─ 概览                                                  │
│ │ ├─ LLM配置管理                                           │
│ │ ├─ 场景映射管理                                          │
│ │ └─ 模板管理                                              │
│ │    ├─ 提示词模板                                         │
│ │    └─ 消息模板                                           │
│ │                                                           │
└─────────────────────────────────────────────────────────────┘
```

### 2. 关键页面设计

#### LLM配置管理页面
- 模型列表卡片展示
- 配置参数表单编辑
- 连接测试按钮
- 配置验证状态显示

#### 场景映射管理页面
- 场景-模型映射表格
- 下拉选择模型
- 参数调整面板
- 场景测试功能

#### 模板管理页面
- 分类标签页（提示词/消息）
- Monaco编辑器
- 实时预览面板
- 模板测试工具

## 错误处理策略

### 1. 后端错误处理

```python
class AdminException(Exception):
    def __init__(self, message: str, code: str = "ADMIN_ERROR"):
        self.message = message
        self.code = code

class ConfigValidationError(AdminException):
    def __init__(self, message: str):
        super().__init__(message, "CONFIG_VALIDATION_ERROR")

class FileOperationError(AdminException):
    def __init__(self, message: str):
        super().__init__(message, "FILE_OPERATION_ERROR")

# 全局异常处理器
@app.exception_handler(AdminException)
async def admin_exception_handler(request: Request, exc: AdminException):
    return JSONResponse(
        status_code=400,
        content={
            "error": {
                "code": exc.code,
                "message": exc.message
            }
        }
    )
```

### 2. 前端错误处理

```typescript
// 统一错误处理
const handleApiError = (error: any) => {
  if (error.response?.data?.error) {
    message.error(error.response.data.error.message);
  } else {
    message.error('操作失败，请稍后重试');
  }
};

// React Query错误处理
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      onError: handleApiError,
    },
    mutations: {
      onError: handleApiError,
    },
  },
});
```

## 部署架构

### 开发环境

```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  admin-backend:
    build: ./admin-backend
    ports:
      - "8001:8000"
    volumes:
      - ./backend:/app/backend  # 挂载现有配置文件
    environment:
      - ENV=development
  
  admin-frontend:
    build: ./admin-frontend
    ports:
      - "3001:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8001
```

### 生产环境

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  admin-backend:
    image: admin-backend:latest
    ports:
      - "8001:8000"
    volumes:
      - ./backend/config:/app/backend/config
      - ./backend/prompts:/app/backend/prompts
    environment:
      - ENV=production
    restart: unless-stopped
  
  admin-frontend:
    image: admin-frontend:latest
    ports:
      - "3001:80"
    restart: unless-stopped
```

## 安全考虑

### 1. 文件操作安全
- 路径验证，防止目录遍历攻击
- 文件类型验证
- 备份机制，防止误操作

### 2. 配置安全
- API密钥脱敏显示
- 配置变更日志记录
- 回滚机制

### 3. 访问控制
- IP白名单限制（可选）
- 操作确认机制
- 危险操作警告

## 测试策略

### 1. 后端测试

```python
# 单元测试
class TestConfigService:
    def test_load_config(self):
        # 测试配置加载
        pass
    
    def test_update_config(self):
        # 测试配置更新
        pass

# 集成测试
class TestAdminAPI:
    def test_config_endpoints(self):
        # 测试配置管理API
        pass
```

### 2. 前端测试

```typescript
// 组件测试
describe('ConfigEditor', () => {
  it('should render config form correctly', () => {
    // 测试配置编辑器渲染
  });
  
  it('should handle form submission', () => {
    // 测试表单提交
  });
});
```

这个设计文档专注于三个核心功能，采用轻量级架构，直接操作现有文件，避免了复杂的数据库设计和权限管理，更适合当前的需求。