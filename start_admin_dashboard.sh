#!/bin/bash

# 智能需求采集系统 - 管理后台启动脚本
# 启动后端和前端服务

echo "🚀 启动智能需求采集系统 - 管理后台"
echo "=================================="

# 检查端口占用
check_port() {
    if lsof -i:$1 > /dev/null 2>&1; then
        echo "❌ 端口 $1 已被占用"
        return 1
    else
        echo "✅ 端口 $1 可用"
        return 0
    fi
}

# 启动后端服务
start_backend() {
    echo "📦 启动后端服务..."
    cd admin-backend
    
    # 加载环境变量
    if [ -f "../.env" ]; then
        echo "🔧 加载环境变量..."
        export $(grep -v '^#' ../.env | xargs)
    fi
    
    # 检查Python环境
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3 未安装"
        exit 1
    fi
    
    # 安装依赖
    if [ ! -f "requirements_installed" ]; then
        echo "📥 安装后端依赖..."
        pip3 install -r requirements.txt
        touch requirements_installed
    fi
    
    # 启动后端
    echo "🚀 启动 FastAPI 服务 (端口 8002)..."
    python3 main.py > ../logs/admin-backend.log 2>&1 &
    BACKEND_PID=$!
    echo "✅ 后端服务已启动 (PID: $BACKEND_PID)"
    cd ..
}

# 启动前端服务
start_frontend() {
    echo "🎨 启动前端服务..."
    cd admin-frontend
    
    # 检查Node.js环境
    if ! command -v npm &> /dev/null; then
        echo "❌ npm 未安装"
        exit 1
    fi
    
    # 启动前端
    echo "🚀 启动 React 服务 (端口 3001)..."
    npm start &
    FRONTEND_PID=$!
    echo "✅ 前端服务已启动 (PID: $FRONTEND_PID)"
    cd ..
}

# 检查端口
echo "🔍 检查端口占用..."
check_port 8002 || exit 1
check_port 3001 || exit 1

# 启动服务
start_backend
sleep 3
start_frontend

echo ""
echo "🎉 管理后台启动完成！"
echo "==================="
echo "📊 管理后台地址: http://localhost:3000"
echo "📚 API文档地址: http://localhost:8002/docs"
echo "🔍 健康检查: http://localhost:8002/health"
echo ""
echo "📋 使用说明:"
echo "1. LLM配置管理: 管理所有LLM模型配置"
echo "2. 场景映射管理: 配置场景到模型的映射关系"
echo "3. 模板管理: 编辑提示词和消息模板"
echo ""
echo "🛑 停止服务: 按 Ctrl+C 两次"

# 捕获中断信号
trap 'echo "🛑 正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0' INT

# 等待用户输入
wait
