---
type: "agent_requested"
description: "Example description"
---
# 协作沟通规则

## 🎯 核心协作原则

### 三大基础原则
- **"Stop and think"** - 复杂任务执行前先暂停思考
- **"Small steps, fast iterations"** - 小步快跑，快速迭代
- **"Documentation first"** - 文档优先

### 基本沟通原则
- 我需要先解释理解和建议方案，等待您确认后再执行
- 质量和稳定性比速度更重要
- 避免上下文丢失、任务细节遗漏和引入新错误

## 🚨 复杂任务执行规则

### 暂停回顾触发条件
- 当任务超过1小时或涉及5个以上文件修改时必须暂停回顾
- 每个步骤不超过30分钟且有明确验证标准

### 编码前必须明确
- 修改文件列表
- 验证检查项
- 风险点识别
- 回滚计划

## 📋 复杂项目实施协作模式

### 阶段管理流程
1. **阶段开始前** - 先说明具体目标和预期结果
2. **实施过程中** - 提供详细的代码修改和验证步骤
3. **阶段完成后** - 提供状态快照和下一步计划
4. **中断处理** - 提供快速恢复指南和当前状态总结

### 实施跟踪文档要求
建立实施跟踪文档，必须包含：
- 核心目标
- 当前问题
- 实施方案
- 检查点
- 回滚计划

### 上下文保持机制
- 使用代码注释标记
- 遵循Git提交规范
- 维护实施日志
- 每个阶段完成后记录状态快照便于中断恢复

## 🔄 协作流程检查点

### 任务启动阶段
- [ ] 需求理解确认 - 与用户确认理解正确
- [ ] 方案设计说明 - 详细说明实施方案
- [ ] 风险评估完成 - 识别潜在风险点
- [ ] 用户方案确认 - 等待用户确认后执行

### 实施执行阶段
- [ ] 小步骤推进 - 每步不超过30分钟
- [ ] 实时状态同步 - 及时报告进展状态
- [ ] 上下文维护 - 保持任务连续性
- [ ] 质量优先原则 - 稳定性重于速度

### 阶段完成确认
- [ ] 目标达成验证 - 确认阶段目标完成
- [ ] 状态快照记录 - 记录当前完成状态
- [ ] 下步计划准备 - 明确后续行动计划
- [ ] 用户确认反馈 - 获取用户确认或反馈

## 🚫 协作禁忌事项

### 严禁的协作行为
- ❌ 未经确认直接执行复杂变更
- ❌ 跳过风险评估和回滚计划
- ❌ 丢失任务上下文和执行状态
- ❌ 为了速度牺牲质量和稳定性
- ❌ 超过时间限制不暂停回顾
- ❌ 缺少状态快照和恢复指南