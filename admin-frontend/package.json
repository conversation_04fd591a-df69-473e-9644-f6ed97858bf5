{"name": "admin-frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^6.0.0", "@monaco-editor/react": "^4.7.0", "@tanstack/react-query": "^5.83.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "antd": "^5.26.6", "axios": "^1.11.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/node": "^24.1.0"}}