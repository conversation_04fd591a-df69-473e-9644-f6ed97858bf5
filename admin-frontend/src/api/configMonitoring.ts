/**
 * 配置监控API客户端
 */

import { request } from '../utils/request';

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface HealthSummary {
  overall_status: string;
  health_score: number;
  last_check: string;
  quick_stats: {
    total_configs: number;
    valid_configs: number;
    missing_configs: number;
    processed_files: number;
    eliminated_hardcodes: number;
  };
  status_color: string;
}

export interface ConfigIntegrityResult {
  check_time: string;
  total_configs: number;
  valid_configs: number;
  missing_configs: string[];
  invalid_configs: Array<{
    config: string;
    error: string;
  }>;
  health_score: number;
  status: string;
}

export interface HardcodeScanResult {
  scan_time: string;
  scanned_files: number;
  potential_hardcodes: Array<{
    file: string;
    line: number;
    content: string;
    context: string;
  }>;
  risk_level: string;
  status: string;
}

export interface DashboardData {
  last_update: string;
  config_health: {
    status: string;
    health_score: number;
    total_configs: number;
    valid_configs: number;
    missing_configs: number;
    invalid_configs: number;
  };
  hardcode_scan: {
    status: string;
    risk_level: string;
    scanned_files: number;
    potential_issues: number;
  };
  project_stats: {
    processed_files: number;
    eliminated_hardcodes: number;
    hardcode_reduction_rate: string;
    overall_progress: string;
  };
}

export interface ProjectStats {
  project_name: string;
  current_phase: string;
  overall_progress: string;
  statistics: {
    total_phases: number;
    completed_phases: number;
    processed_files: number;
    eliminated_hardcodes: number;
    high_risk_eliminated: number;
    medium_risk_eliminated: number;
    hardcode_reduction_rate: string;
    config_templates: number;
    threshold_configs: number;
  };
  milestones: Array<{
    phase: string;
    status: string;
    description: string;
  }>;
}

export const configMonitoringApi = {
  /**
   * 获取监控仪表板数据
   */
  getDashboard: (): Promise<ApiResponse<DashboardData>> => {
    return request.get('/api/admin/config-monitoring/dashboard');
  },

  /**
   * 检查配置完整性
   */
  checkConfigIntegrity: (): Promise<ApiResponse<ConfigIntegrityResult>> => {
    return request.get('/api/admin/config-monitoring/config-integrity');
  },

  /**
   * 扫描硬编码回归
   */
  scanHardcodeRegression: (): Promise<ApiResponse<HardcodeScanResult>> => {
    return request.get('/api/admin/config-monitoring/hardcode-scan');
  },

  /**
   * 运行完整检查
   */
  runFullCheck: (): Promise<ApiResponse<{ check_time: string }>> => {
    return request.post('/api/admin/config-monitoring/run-full-check');
  },

  /**
   * 获取健康度摘要
   */
  getHealthSummary: (): Promise<ApiResponse<HealthSummary>> => {
    return request.get('/api/admin/config-monitoring/health-summary');
  },

  /**
   * 获取项目统计信息
   */
  getProjectStats: (): Promise<ApiResponse<ProjectStats>> => {
    return request.get('/api/admin/config-monitoring/project-stats');
  },

  /**
   * 健康检查
   */
  ping: (): Promise<ApiResponse<{ message: string; timestamp: string }>> => {
    return request.get('/api/admin/config-monitoring/ping');
  },
};
