import apiClient from './client';

// LLM配置相关API
export const llmConfigApi = {
  // 获取所有LLM配置
  getAllConfigs: () => apiClient.get('/config/llm'),
  
  // 获取指定模型配置
  getModelConfig: (modelName: string) => apiClient.get(`/config/llm/${modelName}`),
  
  // 更新模型配置
  updateModelConfig: (modelName: string, config: any) => 
    apiClient.put(`/config/llm/${modelName}`, config),
  
  // 添加新模型配置
  addModelConfig: (config: any) => apiClient.post('/config/llm', config),
  
  // 测试模型连接
  testModelConnection: (modelName: string, testPrompt?: string) => 
    apiClient.post(`/config/llm/${modelName}/test`, { test_prompt: testPrompt }),
  
  // 重新加载配置
  reloadConfig: () => apiClient.post('/config/reload'),
};

// 场景映射相关API
export const scenarioApi = {
  // 获取所有场景映射
  getAllMappings: () => apiClient.get('/scenario/mappings'),
  
  // 获取指定场景映射
  getScenarioMapping: (scenarioName: string) => 
    apiClient.get(`/scenario/mappings/${scenarioName}`),
  
  // 更新场景映射
  updateScenarioMapping: (scenarioName: string, mapping: any) => 
    apiClient.put(`/scenario/mappings/${scenarioName}`, mapping),
  
  // 测试场景配置
  testScenarioConfig: (scenarioName: string, testInput: string) => 
    apiClient.post(`/scenario/${scenarioName}/test`, { test_input: testInput }),
  
  // 获取可用模型列表
  getAvailableModels: () => apiClient.get('/scenario/available-models'),
};

// 模板相关API
export const templateApi = {
  // 获取所有提示词模板
  getPromptTemplates: () => apiClient.get('/template/prompts'),

  // 获取指定提示词模板
  getPromptTemplate: (filename: string) =>
    apiClient.get(`/template/prompts/${filename}`),

  // 更新提示词模板
  updatePromptTemplate: (filename: string, content: string) =>
    apiClient.put(`/template/prompts/${filename}`, { content }),

  // 获取所有消息模板
  getMessageTemplates: () => apiClient.get('/template/messages'),

  // 获取指定消息模板
  getMessageTemplate: (path: string) =>
    apiClient.get(`/template/messages/${path}`),

  // 更新消息模板
  updateMessageTemplate: (path: string, content: any) =>
    apiClient.put(`/template/messages/${path}`, { content }),

  // 测试模板渲染
  testTemplate: (templateName: string, templateType: string, testData: any) =>
    apiClient.post('/template/test', {
      template_name: templateName,
      template_type: templateType,
      test_data: testData
    }),
};

// 系统状态相关API
export const systemApi = {
  // 获取系统状态
  getSystemStatus: () => apiClient.get('/system/status'),

  // 健康检查
  healthCheck: () => apiClient.get('/system/health'),

  // 重新加载配置
  reloadConfig: () => apiClient.post('/system/reload'),
};

// 业务规则配置相关API
export const businessRulesApi = {
  // 获取所有业务规则配置
  getAllRules: () => apiClient.get('/business-rules/'),

  // 获取关注点优先级配置
  getFocusPointPriority: () => apiClient.get('/business-rules/focus-point-priority'),

  // 更新关注点优先级配置
  updateFocusPointPriority: (config: any) =>
    apiClient.put('/business-rules/focus-point-priority', config),

  // 获取重试配置
  getRetryConfig: () => apiClient.get('/business-rules/retry'),

  // 更新重试配置
  updateRetryConfig: (config: any) =>
    apiClient.put('/business-rules/retry', config),

  // 获取需求采集配置
  getRequirementCollectionConfig: () => apiClient.get('/business-rules/requirement-collection'),

  // 更新需求采集配置
  updateRequirementCollectionConfig: (config: any) =>
    apiClient.put('/business-rules/requirement-collection', config),

  // 批量更新业务规则配置
  updateBusinessRules: (rules: any) =>
    apiClient.put('/business-rules/', rules),

  // 验证业务规则配置
  validateRules: () => apiClient.post('/business-rules/validate'),

  // 重新加载业务规则配置
  reloadRules: () => apiClient.post('/business-rules/reload'),
};

// 数据库维护相关API
export const databaseApi = {
  // 获取数据库状态
  getDatabaseStatus: () => apiClient.get('/database/status'),

  // 获取所有表信息
  getTables: () => apiClient.get('/database/tables'),

  // 获取指定表详细信息
  getTableDetails: (tableName: string) =>
    apiClient.get(`/database/tables/${tableName}`),

  // 获取数据库统计信息
  getStatistics: () => apiClient.get('/database/statistics'),

  // 数据库健康检查
  healthCheck: () => apiClient.get('/database/health'),

  // 完整性检查
  integrityCheck: () => apiClient.post('/database/integrity-check'),

  // 创建备份
  createBackup: (backupName?: string) =>
    apiClient.post('/database/backup', { backup_name: backupName }),

  // 优化数据库
  optimizeDatabase: () => apiClient.post('/database/optimize'),

  // 获取备份列表
  getBackups: () => apiClient.get('/database/backups'),

  // 恢复备份
  restoreBackup: (backupName: string) =>
    apiClient.post('/database/restore', { backup_name: backupName }),

  // 删除备份
  deleteBackup: (backupName: string) =>
    apiClient.delete(`/database/backups/${backupName}`),

  // 获取表数据（分页）
  getTableData: (tableName: string, params?: {
    page?: number;
    page_size?: number;
    search?: string;
    order_by?: string;
    order_direction?: 'ASC' | 'DESC';
  }) => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.page_size) queryParams.append('page_size', params.page_size.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.order_by) queryParams.append('order_by', params.order_by);
    if (params?.order_direction) queryParams.append('order_direction', params.order_direction);

    return apiClient.get(`/database/tables/${tableName}/data?${queryParams.toString()}`);
  },

  // 删除表记录
  deleteTableRecord: (tableName: string, recordId: string) =>
    apiClient.delete(`/database/tables/${tableName}/data/${recordId}`),

  // 清理表数据
  cleanupTableData: (tableName: string, cleanupType: 'old_data' | 'test_data' | 'all', daysOld?: number) => {
    const params: any = { cleanup_type: cleanupType };
    if (daysOld) params.days_old = daysOld;
    return apiClient.post(`/database/tables/${tableName}/cleanup`, null, { params });
  },

  // 更新表记录
  updateTableRecord: (tableName: string, recordId: string, updateData: Record<string, any>) =>
    apiClient.put(`/database/tables/${tableName}/data/${recordId}`, updateData),

  // 获取可编辑字段
  getEditableFields: (tableName: string) =>
    apiClient.get(`/database/tables/${tableName}/editable-fields`),
};