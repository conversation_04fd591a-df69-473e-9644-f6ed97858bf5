import React from 'react';
import { Layout, Typography } from 'antd';

const { Header, Content } = Layout;
const { Title } = Typography;

const App: React.FC = () => {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 24px' }}>
        <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
          后台管理 - 最小化测试
        </Title>
      </Header>
      <Content style={{ padding: 24 }}>
        <Title level={2}>系统正常运行</Title>
        <p>如果看到这个页面，说明基础框架没有问题。</p>
      </Content>
    </Layout>
  );
};

export default App;
