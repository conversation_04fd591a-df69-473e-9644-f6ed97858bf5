import React, { useState } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Space, 
  message, 
  Tag,
  Descriptions,
  Tooltip,
  Tabs
} from 'antd';
import { 
  EditOutlined, 
  EyeOutlined,
  ReloadOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { templateApi } from '../../api/services';
import MonacoEditor from '@monaco-editor/react';

const { TabPane } = Tabs;

interface PromptTemplate {
  filename: string;
  content: string;
  last_modified: string;
  size: number;
}

const PromptTemplates: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<PromptTemplate | null>(null);
  const [previewContent, setPreviewContent] = useState<string>('');
  const [isPreviewVisible, setIsPreviewVisible] = useState(false);
  
  const queryClient = useQueryClient();

  // 获取所有提示词模板
  const { data: templates = [], isLoading } = useQuery({
    queryKey: ['prompt-templates'],
    queryFn: async () => {
      const response = await templateApi.getPromptTemplates();
      return response.data;
    }
  });

  // 更新提示词模板
  const updateMutation = useMutation({
    mutationFn: ({ filename, content }: { filename: string; content: string }) => 
      templateApi.updatePromptTemplate(filename, content),
    onSuccess: () => {
      message.success('提示词模板更新成功');
      queryClient.invalidateQueries({ queryKey: ['prompt-templates'] });
      setIsModalVisible(false);
    },
    onError: (error: any) => {
      message.error(`更新失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  const columns = [
    {
      title: '文件名',
      dataIndex: 'filename',
      key: 'filename',
      render: (text: string) => <strong>{text}</strong>
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      render: (size: number) => `${Math.round(size / 1024)} KB`
    },
    {
      title: '最后修改',
      dataIndex: 'last_modified',
      key: 'last_modified',
      render: (date: string) => new Date(date).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: PromptTemplate) => (
        <Space>
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            icon={<EyeOutlined />}
            size="small"
            onClick={() => handlePreview(record)}
          >
            预览
          </Button>
        </Space>
      )
    }
  ];

  const handleEdit = (template: PromptTemplate) => {
    setEditingTemplate(template);
    setIsModalVisible(true);
  };

  const handlePreview = (template: PromptTemplate) => {
    setPreviewContent(template.content);
    setIsPreviewVisible(true);
  };

  const handleModalOk = (content: string) => {
    if (editingTemplate) {
      updateMutation.mutate({ 
        filename: editingTemplate.filename, 
        content 
      });
    }
  };

  const handleReload = () => {
    queryClient.invalidateQueries({ queryKey: ['prompt-templates'] });
    message.success('提示词模板已刷新');
  };

  // 获取文件类型标签
  const getFileTypeTag = (filename: string) => {
    if (filename.includes('system')) return <Tag color="red">系统</Tag>;
    if (filename.includes('user')) return <Tag color="green">用户</Tag>;
    if (filename.includes('assistant')) return <Tag color="blue">助手</Tag>;
    return <Tag>通用</Tag>;
  };

  return (
    <div>
      <Card 
        title="提示词模板管理" 
        extra={
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleReload}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={templates}
          rowKey="filename"
          loading={isLoading}
          pagination={{ pageSize: 10 }}
          expandable={{
            expandedRowRender: (record: PromptTemplate) => (
              <Descriptions column={2} size="small">
                <Descriptions.Item label="文件类型">
                  {getFileTypeTag(record.filename)}
                </Descriptions.Item>
                <Descriptions.Item label="路径">
                  backend/prompts/{record.filename}
                </Descriptions.Item>
                <Descriptions.Item label="内容预览" span={2}>
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: 8, 
                    borderRadius: 4,
                    maxHeight: 100,
                    overflow: 'hidden',
                    fontSize: 12,
                    whiteSpace: 'pre-wrap'
                  }}>
                    {record.content.substring(0, 200)}...
                  </pre>
                </Descriptions.Item>
              </Descriptions>
            )
          }}
        />
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title={`编辑提示词模板: ${editingTemplate?.filename}`}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={800}
        style={{ top: 20 }}
      >
        <MonacoEditor
          height="500px"
          language="markdown"
          theme="vs-dark"
          value={editingTemplate?.content || ''}
          onChange={(value) => {
            if (editingTemplate && value !== undefined) {
              setEditingTemplate({ ...editingTemplate, content: value });
            }
          }}
          options={{
            minimap: { enabled: false },
            wordWrap: 'on',
            lineNumbers: 'on',
            scrollBeyondLastLine: false,
            automaticLayout: true,
            tabSize: 2,
            fontSize: 14
          }}
        />
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Space>
            <Button onClick={() => setIsModalVisible(false)}>取消</Button>
            <Button 
              type="primary" 
              onClick={() => editingTemplate && handleModalOk(editingTemplate.content)}
              loading={updateMutation.isPending}
            >
              保存
            </Button>
          </Space>
        </div>
      </Modal>

      {/* 预览模态框 */}
      <Modal
        title={`预览: ${editingTemplate?.filename}`}
        open={isPreviewVisible}
        onCancel={() => setIsPreviewVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ 
          background: '#f5f5f5', 
          padding: 16, 
          borderRadius: 4,
          maxHeight: 400,
          overflow: 'auto'
        }}>
          <pre style={{ 
            whiteSpace: 'pre-wrap',
            fontSize: 14,
            lineHeight: 1.5
          }}>
            {previewContent}
          </pre>
        </div>
      </Modal>
    </div>
  );
};

export default PromptTemplates;
