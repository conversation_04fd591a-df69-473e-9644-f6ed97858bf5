import React from 'react';
import { Card, Row, Col, Statistic, Typography, Space } from 'antd';
import { 
  SettingOutlined, 
  BranchesOutlined, 
  FileTextOutlined,
  CheckCircleOutlined 
} from '@ant-design/icons';

const { Title } = Typography;

const Dashboard: React.FC = () => {
  return (
    <div>
      <Title level={2}>系统概览</Title>
      
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="LLM模型"
              value={8}
              prefix={<SettingOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="场景映射"
              value={12}
              prefix={<BranchesOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="提示词模板"
              value={15}
              prefix={<FileTextOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="系统状态"
              value="正常"
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
      </Row>
      
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="快速操作" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>• 查看和编辑LLM模型配置</div>
              <div>• 管理场景到模型的映射关系</div>
              <div>• 编辑提示词和消息模板</div>
              <div>• 测试模型连接和配置</div>
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} lg={12}>
          <Card title="系统信息" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>• 配置文件：unified_config.yaml</div>
              <div>• 模板目录：backend/prompts/</div>
              <div>• 系统版本：v1.0.0</div>
              <div>• 最后更新：刚刚</div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;