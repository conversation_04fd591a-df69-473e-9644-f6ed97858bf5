import React, { useState, useEffect, useCallback } from 'react';
import { Card, Row, Col, Button, Statistic, Progress, Alert, Spin, message, Table, Tag } from 'antd';
import { ReloadOutlined, CheckCircleOutlined, ExclamationCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { configMonitoringApi } from '../../api/configMonitoring';

interface HealthSummary {
  overall_status: string;
  health_score: number;
  last_check: string;
  quick_stats: {
    total_configs: number;
    valid_configs: number;
    missing_configs: number;
    processed_files: number;
    eliminated_hardcodes: number;
  };
  status_color: string;
}

interface ProjectStats {
  project_name: string;
  current_phase: string;
  overall_progress: string;
  statistics: {
    total_phases: number;
    completed_phases: number;
    processed_files: number;
    eliminated_hardcodes: number;
    high_risk_eliminated: number;
    medium_risk_eliminated: number;
    hardcode_reduction_rate: string;
    config_templates: number;
    threshold_configs: number;
  };
  milestones: Array<{
    phase: string;
    status: string;
    description: string;
  }>;
}

const ConfigMonitoring: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [healthSummary, setHealthSummary] = useState<HealthSummary | null>(null);
  const [projectStats, setProjectStats] = useState<ProjectStats | null>(null);

  // 获取健康度摘要
  const fetchHealthSummary = async () => {
    try {
      setLoading(true);
      const response = await configMonitoringApi.getHealthSummary();
      if (response.success) {
        setHealthSummary(response.data);
      } else {
        message.error('获取健康度摘要失败');
      }
    } catch (error) {
      message.error('网络请求失败');
      console.error('获取健康度摘要失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取项目统计
  const fetchProjectStats = async () => {
    try {
      const response = await configMonitoringApi.getProjectStats();
      if (response.success) {
        setProjectStats(response.data);
      }
    } catch (error) {
      console.error('获取项目统计失败:', error);
    }
  };

  // 执行完整检查
  const runFullCheck = async () => {
    try {
      setLoading(true);
      const response = await configMonitoringApi.runFullCheck();
      if (response.success) {
        message.success('完整检查已启动，请稍后刷新查看结果');
        // 3秒后自动刷新
        setTimeout(() => {
          fetchHealthSummary();
        }, 3000);
      } else {
        message.error('启动完整检查失败');
      }
    } catch (error) {
      message.error('网络请求失败');
      console.error('执行完整检查失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 刷新数据
  const refreshData = useCallback(() => {
    fetchHealthSummary();
    fetchProjectStats();
  }, []);

  useEffect(() => {
    refreshData();
  }, [refreshData]);

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'HEALTHY':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'WARNING':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'CRITICAL':
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <ExclamationCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  // 获取进度条颜色
  const getProgressColor = (score: number) => {
    if (score >= 95) return '#52c41a';
    if (score >= 80) return '#faad14';
    return '#f5222d';
  };

  // 里程碑表格列定义
  const milestoneColumns = [
    {
      title: '阶段',
      dataIndex: 'phase',
      key: 'phase',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === '完成' ? 'green' : 'orange'}>
          {status}
        </Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1>🔧 配置监控中心</h1>
        <div>
          <Button 
            type="primary" 
            icon={<ReloadOutlined />} 
            onClick={refreshData}
            style={{ marginRight: '8px' }}
            loading={loading}
          >
            刷新数据
          </Button>
          <Button 
            type="default" 
            onClick={runFullCheck}
            loading={loading}
          >
            🔍 完整检查
          </Button>
        </div>
      </div>

      <Spin spinning={loading}>
        {healthSummary && (
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <Col xs={24} sm={12} lg={8}>
              <Card title="📊 配置健康度" size="small">
                <div style={{ textAlign: 'center' }}>
                  <Progress
                    type="circle"
                    percent={healthSummary.health_score}
                    strokeColor={getProgressColor(healthSummary.health_score)}
                    format={(percent) => `${percent}%`}
                  />
                  <div style={{ marginTop: '16px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    {getStatusIcon(healthSummary.overall_status)}
                    <span style={{ marginLeft: '8px' }}>
                      {healthSummary.overall_status === 'HEALTHY' ? '健康' : 
                       healthSummary.overall_status === 'WARNING' ? '警告' : '严重'}
                    </span>
                  </div>
                </div>
              </Card>
            </Col>

            <Col xs={24} sm={12} lg={8}>
              <Card title="📋 配置统计" size="small">
                <Statistic
                  title="总配置项"
                  value={healthSummary.quick_stats.total_configs}
                  suffix="个"
                />
                <Statistic
                  title="有效配置"
                  value={healthSummary.quick_stats.valid_configs}
                  suffix="个"
                  valueStyle={{ color: '#3f8600' }}
                />
                <Statistic
                  title="缺失配置"
                  value={healthSummary.quick_stats.missing_configs}
                  suffix="个"
                  valueStyle={{ color: healthSummary.quick_stats.missing_configs > 0 ? '#cf1322' : '#3f8600' }}
                />
              </Card>
            </Col>

            <Col xs={24} sm={12} lg={8}>
              <Card title="📈 项目成果" size="small">
                <Statistic
                  title="处理文件"
                  value={healthSummary.quick_stats.processed_files}
                  suffix="个"
                />
                <Statistic
                  title="消除硬编码"
                  value={healthSummary.quick_stats.eliminated_hardcodes}
                  suffix="个"
                  valueStyle={{ color: '#3f8600' }}
                />
                <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                  最后检查: {new Date(healthSummary.last_check).toLocaleString()}
                </div>
              </Card>
            </Col>
          </Row>
        )}

        {projectStats && (
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="🚀 项目概览" size="small">
                <div style={{ marginBottom: '16px' }}>
                  <h3>{projectStats.project_name}</h3>
                  <p><strong>当前阶段:</strong> {projectStats.current_phase}</p>
                  <p><strong>整体进度:</strong> {projectStats.overall_progress}</p>
                </div>
                
                <Row gutter={[8, 8]}>
                  <Col span={12}>
                    <Statistic
                      title="已完成阶段"
                      value={projectStats.statistics.completed_phases}
                      suffix={`/ ${projectStats.statistics.total_phases}`}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="硬编码减少率"
                      value={projectStats.statistics.hardcode_reduction_rate}
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="高危硬编码"
                      value={projectStats.statistics.high_risk_eliminated}
                      suffix="个"
                      valueStyle={{ color: '#f5222d' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="中危硬编码"
                      value={projectStats.statistics.medium_risk_eliminated}
                      suffix="个"
                      valueStyle={{ color: '#faad14' }}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>

            <Col xs={24} lg={12}>
              <Card title="📋 项目里程碑" size="small">
                <Table
                  dataSource={projectStats.milestones}
                  columns={milestoneColumns}
                  pagination={false}
                  size="small"
                  rowKey="phase"
                />
              </Card>
            </Col>
          </Row>
        )}

        {healthSummary?.overall_status !== 'HEALTHY' && (
          <Alert
            message="配置状态警告"
            description="系统配置存在问题，建议及时检查和修复"
            type="warning"
            showIcon
            style={{ marginTop: '16px' }}
          />
        )}
      </Spin>
    </div>
  );
};

export default ConfigMonitoring;
