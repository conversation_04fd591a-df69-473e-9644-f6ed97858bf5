import React, { useState } from 'react';
import {
  Card,
  Form,
  Switch,
  InputNumber,
  Button,
  Space,
  message,
  Divider,
  Typography,
  <PERSON>,
  Col,
  Al<PERSON>,
  Toolt<PERSON>,
  Spin
} from 'antd';
import { 
  SaveOutlined, 
  ReloadOutlined, 
  CheckCircleOutlined,
  InfoCircleOutlined 
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { businessRulesApi } from '../../api/services';

const { Title, Text } = Typography;

interface FocusPointPriorityConfig {
  p0: boolean;
  p1: boolean;
  p2: boolean;
}

interface RetryConfig {
  max_pending_attempts: number;
  max_total_attempts: number;
  backoff_factor: number;
}

interface RequirementCollectionConfig {
  min_focus_points: number;
  max_focus_points: number;
  completion_threshold: number;
}

interface QualityControlConfig {
  min_input_length: number;
  max_input_length: number;
  spam_detection_enabled: boolean;
}

interface BusinessRulesConfig {
  focus_point_priority: FocusPointPriorityConfig;
  retry: RetryConfig;
  requirement_collection: RequirementCollectionConfig;
  quality_control: QualityControlConfig;
}

const BusinessRules: React.FC = () => {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  // 获取业务规则配置
  const { data: businessRules, isLoading } = useQuery({
    queryKey: ['businessRules'],
    queryFn: businessRulesApi.getAllRules,
    select: (response) => response.data as BusinessRulesConfig
  });

  // 更新业务规则配置
  const updateMutation = useMutation({
    mutationFn: businessRulesApi.updateBusinessRules,
    onSuccess: () => {
      message.success('业务规则配置更新成功');
      queryClient.invalidateQueries({ queryKey: ['businessRules'] });
    },
    onError: (error: any) => {
      message.error(`更新失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  // 重新加载配置
  const reloadMutation = useMutation({
    mutationFn: businessRulesApi.reloadRules,
    onSuccess: () => {
      message.success('配置重新加载成功');
      queryClient.invalidateQueries({ queryKey: ['businessRules'] });
    },
    onError: (error: any) => {
      message.error(`重新加载失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  // 验证配置
  const validateMutation = useMutation({
    mutationFn: businessRulesApi.validateRules,
    onSuccess: (response) => {
      const result = response.data;
      if (result.valid) {
        message.success('配置验证通过');
      } else {
        message.warning(`配置验证发现 ${result.total_errors} 个错误，${result.total_warnings} 个警告`);
      }
    },
    onError: (error: any) => {
      message.error(`验证失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  // 表单提交
  const handleSubmit = async (values: any) => {
    try {
      await updateMutation.mutateAsync(values);
    } catch (error) {
      // 错误已在mutation中处理
    }
  };

  // 重新加载配置
  const handleReload = () => {
    reloadMutation.mutate();
  };

  // 验证配置
  const handleValidate = () => {
    validateMutation.mutate();
  };

  // 设置表单初始值
  React.useEffect(() => {
    if (businessRules) {
      form.setFieldsValue(businessRules);
    }
  }, [businessRules, form]);

  return (
    <div>
      <Card 
        title="业务规则配置管理" 
        extra={
          <Space>
            <Button 
              icon={<CheckCircleOutlined />} 
              onClick={handleValidate}
              loading={validateMutation.isPending}
            >
              验证配置
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleReload}
              loading={reloadMutation.isPending}
            >
              重新加载
            </Button>
          </Space>
        }
      >
        <Alert
          message="配置说明"
          description="这些配置控制系统的核心业务逻辑，修改后需要重新加载才能生效。请谨慎操作。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Spin spinning={isLoading}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
          >
          {/* 关注点优先级配置 */}
          <Card size="small" title="关注点优先级配置" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item 
                  name={['focus_point_priority', 'p0']} 
                  label={
                    <Space>
                      P0优先级
                      <Tooltip title="最高优先级，核心功能需求，必须采集">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  name={['focus_point_priority', 'p1']} 
                  label={
                    <Space>
                      P1优先级
                      <Tooltip title="高优先级，重要功能需求，3次重试后可跳过">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  name={['focus_point_priority', 'p2']} 
                  label={
                    <Space>
                      P2优先级
                      <Tooltip title="低优先级，可选功能需求，设置为true时会包含在文档生成中">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 重试配置 */}
          <Card size="small" title="重试配置" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item 
                  name={['retry', 'max_pending_attempts']} 
                  label="单个关注点最大重试次数"
                  rules={[{ required: true, message: '请输入重试次数' }]}
                >
                  <InputNumber min={1} max={10} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  name={['retry', 'max_total_attempts']} 
                  label="总体最大重试次数"
                  rules={[{ required: true, message: '请输入总重试次数' }]}
                >
                  <InputNumber min={1} max={20} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  name={['retry', 'backoff_factor']} 
                  label="重试间隔递增因子"
                  rules={[{ required: true, message: '请输入递增因子' }]}
                >
                  <InputNumber min={1} max={5} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 需求采集配置 */}
          <Card size="small" title="需求采集配置" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item 
                  name={['requirement_collection', 'min_focus_points']} 
                  label="最少关注点数量"
                  rules={[{ required: true, message: '请输入最少关注点数量' }]}
                >
                  <InputNumber min={1} max={20} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  name={['requirement_collection', 'max_focus_points']} 
                  label="最多关注点数量"
                  rules={[{ required: true, message: '请输入最多关注点数量' }]}
                >
                  <InputNumber min={1} max={50} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  name={['requirement_collection', 'completion_threshold']} 
                  label="完成度阈值"
                  rules={[{ required: true, message: '请输入完成度阈值' }]}
                >
                  <InputNumber min={0} max={1} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 质量控制配置 */}
          <Card size="small" title="质量控制配置" style={{ marginBottom: 24 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item 
                  name={['quality_control', 'min_input_length']} 
                  label="最短输入长度(字符)"
                  rules={[{ required: true, message: '请输入最短输入长度' }]}
                >
                  <InputNumber min={0} max={100} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  name={['quality_control', 'max_input_length']} 
                  label="最长输入长度(字符)"
                  rules={[{ required: true, message: '请输入最长输入长度' }]}
                >
                  <InputNumber min={1} max={10000} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  name={['quality_control', 'spam_detection_enabled']} 
                  label="启用垃圾信息检测"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              icon={<SaveOutlined />}
              loading={updateMutation.isPending}
              size="large"
            >
              保存配置
            </Button>
          </Form.Item>
        </Form>
        </Spin>
      </Card>
    </div>
  );
};

export default BusinessRules;
