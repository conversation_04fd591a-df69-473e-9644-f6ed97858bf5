import React, { useState } from 'react';
import {
  Card,
  Button,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Space,
  Alert,
  Spin,
  message,
  Modal,
  Input,
  Select,
  Progress,
  Descriptions,
  Tabs
} from 'antd';
import {
  DatabaseOutlined,
  ReloadOutlined,
  SafetyOutlined,
  SaveOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { databaseApi } from '../../api/services';

const { TabPane } = Tabs;
const { confirm } = Modal;

interface DatabaseStatus {
  database_path: string;
  file_size_mb: number;
  sqlite_version: string;
  page_size: number;
  total_pages: number;
  used_pages: number;
  free_pages: number;
  usage_ratio: number;
  last_modified: string;
  is_accessible: boolean;
}

interface TableInfo {
  name: string;
  row_count: number;
  column_count: number;
  columns: Array<{
    name: string;
    type: string;
    not_null: boolean;
    default_value: any;
    primary_key: boolean;
  }>;
}

interface DatabaseHealth {
  status: string;
  health_score: number;
  issues: string[];
  database_accessible: boolean;
  file_size_mb: number;
  usage_ratio: number;
  integrity_ok: boolean;
  checked_at: string;
}

const DatabaseManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [backupName, setBackupName] = useState('');
  const [backupModalVisible, setBackupModalVisible] = useState(false);
  const [selectedTable, setSelectedTable] = useState<string>('');
  const queryClient = useQueryClient();

  // 获取数据库状态
  const { data: dbStatus, isLoading: statusLoading, error: statusError } = useQuery<DatabaseStatus>({
    queryKey: ['database-status'],
    queryFn: () => databaseApi.getDatabaseStatus().then(res => res.data),
    refetchInterval: 30000, // 30秒自动刷新
  });

  // 获取表信息
  const { data: tables, isLoading: tablesLoading } = useQuery<TableInfo[]>({
    queryKey: ['database-tables'],
    queryFn: () => databaseApi.getTables().then(res => res.data),
  });

  // 获取健康状态
  const { data: healthData, isLoading: healthLoading } = useQuery<DatabaseHealth>({
    queryKey: ['database-health'],
    queryFn: () => databaseApi.healthCheck().then(res => res.data),
    refetchInterval: 60000, // 1分钟自动刷新
  });

  // 创建备份
  const createBackupMutation = useMutation({
    mutationFn: (name?: string) => databaseApi.createBackup(name),
    onSuccess: () => {
      message.success('数据库备份创建成功');
      setBackupModalVisible(false);
      setBackupName('');
      queryClient.invalidateQueries({ queryKey: ['database-backups'] });
    },
    onError: (error: any) => {
      message.error(`备份创建失败: ${error.response?.data?.detail || error.message}`);
    },
  });

  // 完整性检查
  const integrityCheckMutation = useMutation({
    mutationFn: () => databaseApi.integrityCheck(),
    onSuccess: () => {
      message.success('完整性检查完成');
      queryClient.invalidateQueries({ queryKey: ['database-health'] });
    },
    onError: (error: any) => {
      message.error(`完整性检查失败: ${error.response?.data?.detail || error.message}`);
    },
  });

  // 优化数据库
  const optimizeMutation = useMutation({
    mutationFn: () => databaseApi.optimizeDatabase(),
    onSuccess: (response) => {
      const result = response.data.data;
      const spaceSaved = result.space_saved_mb;
      const spaceSavedPercent = result.space_saved_percent;
      message.success(`数据库优化完成！节省空间 ${spaceSaved}MB (${spaceSavedPercent.toFixed(1)}%)`);
      queryClient.invalidateQueries({ queryKey: ['database-status'] });
      queryClient.invalidateQueries({ queryKey: ['database-health'] });
    },
    onError: (error: any) => {
      message.error(`数据库优化失败: ${error.response?.data?.detail || error.message}`);
    },
  });

  const handleCreateBackup = () => {
    createBackupMutation.mutate(backupName || undefined);
  };

  const handleIntegrityCheck = () => {
    confirm({
      title: '确认执行完整性检查',
      content: '完整性检查可能需要一些时间，确认要执行吗？',
      onOk: () => {
        integrityCheckMutation.mutate();
      },
    });
  };

  const handleOptimizeDatabase = () => {
    confirm({
      title: '确认优化数据库',
      content: '数据库优化（VACUUM）会回收未使用的空间，可能需要较长时间。优化过程中数据库将被锁定，确认要执行吗？',
      onOk: () => {
        optimizeMutation.mutate();
      },
    });
  };

  const refreshData = () => {
    queryClient.invalidateQueries({ queryKey: ['database-status'] });
    queryClient.invalidateQueries({ queryKey: ['database-tables'] });
    queryClient.invalidateQueries({ queryKey: ['database-health'] });
    message.success('数据已刷新');
  };

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'success';
      case 'warning': return 'warning';
      case 'critical': return 'error';
      default: return 'default';
    }
  };

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircleOutlined />;
      case 'warning': return <WarningOutlined />;
      case 'critical': return <ExclamationCircleOutlined />;
      default: return <DatabaseOutlined />;
    }
  };

  if (statusError) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="数据库连接失败"
          description="无法连接到数据库，请检查数据库配置和状态"
          type="error"
          showIcon
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1 style={{ margin: 0 }}>数据库维护</h1>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={refreshData}
          >
            刷新数据
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => setBackupModalVisible(true)}
          >
            创建备份
          </Button>
          <Button
            icon={<SafetyOutlined />}
            onClick={handleIntegrityCheck}
            loading={integrityCheckMutation.isPending}
          >
            完整性检查
          </Button>
          <Button
            icon={<ThunderboltOutlined />}
            onClick={handleOptimizeDatabase}
            loading={optimizeMutation.isPending}
            type="default"
          >
            优化数据库
          </Button>
        </Space>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="概览" key="overview">
          <Row gutter={[16, 16]}>
            {/* 健康状态卡片 */}
            <Col xs={24} lg={8}>
              <Card title="数据库健康状态" size="small">
                {healthLoading ? (
                  <Spin />
                ) : healthData ? (
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      {getHealthStatusIcon(healthData.status)}
                      <Tag color={getHealthStatusColor(healthData.status)}>
                        {healthData.status.toUpperCase()}
                      </Tag>
                    </div>
                    <Progress
                      percent={healthData.health_score}
                      status={healthData.health_score >= 80 ? 'success' : healthData.health_score >= 60 ? 'normal' : 'exception'}
                      format={percent => `健康度: ${percent}%`}
                    />
                    {healthData.issues.length > 0 && (
                      <Alert
                        message="发现问题"
                        description={
                          <ul style={{ margin: 0, paddingLeft: '20px' }}>
                            {healthData.issues.map((issue, index) => (
                              <li key={index}>{issue}</li>
                            ))}
                          </ul>
                        }
                        type="warning"
                      />
                    )}
                  </Space>
                ) : (
                  <div>无法获取健康状态</div>
                )}
              </Card>
            </Col>

            {/* 基本信息卡片 */}
            <Col xs={24} lg={16}>
              <Card title="数据库基本信息" size="small">
                {statusLoading ? (
                  <Spin />
                ) : dbStatus ? (
                  <Descriptions column={2} size="small">
                    <Descriptions.Item label="数据库路径">
                      {dbStatus.database_path}
                    </Descriptions.Item>
                    <Descriptions.Item label="文件大小">
                      {dbStatus.file_size_mb} MB
                    </Descriptions.Item>
                    <Descriptions.Item label="SQLite版本">
                      {dbStatus.sqlite_version}
                    </Descriptions.Item>
                    <Descriptions.Item label="页面大小">
                      {dbStatus.page_size} bytes
                    </Descriptions.Item>
                    <Descriptions.Item label="总页面数">
                      {dbStatus.total_pages.toLocaleString()}
                    </Descriptions.Item>
                    <Descriptions.Item label="已使用页面">
                      {dbStatus.used_pages.toLocaleString()}
                    </Descriptions.Item>
                    <Descriptions.Item label="空闲页面">
                      {dbStatus.free_pages.toLocaleString()}
                    </Descriptions.Item>
                    <Descriptions.Item label="使用率">
                      <Progress
                        percent={dbStatus.usage_ratio}
                        size="small"
                        status={dbStatus.usage_ratio > 90 ? 'exception' : 'normal'}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="最后修改时间" span={2}>
                      {new Date(dbStatus.last_modified).toLocaleString()}
                    </Descriptions.Item>
                  </Descriptions>
                ) : (
                  <div>无法获取数据库状态</div>
                )}
              </Card>
            </Col>
          </Row>

          {/* 统计信息 */}
          <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
            <Col xs={12} sm={6}>
              <Card size="small">
                <Statistic
                  title="表数量"
                  value={tables?.length || 0}
                  prefix={<DatabaseOutlined />}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card size="small">
                <Statistic
                  title="总记录数"
                  value={tables?.reduce((sum, table) => sum + table.row_count, 0) || 0}
                  prefix={<DatabaseOutlined />}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card size="small">
                <Statistic
                  title="文件大小"
                  value={dbStatus?.file_size_mb || 0}
                  suffix="MB"
                  prefix={<DatabaseOutlined />}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card size="small">
                <Statistic
                  title={
                    <span>
                      使用率
                      <span style={{ fontSize: '12px', color: '#666', marginLeft: '4px' }}>
                        (实际数据占用)
                      </span>
                    </span>
                  }
                  value={dbStatus?.usage_ratio || 0}
                  suffix="%"
                  prefix={<DatabaseOutlined />}
                  valueStyle={{
                    color: (dbStatus?.usage_ratio || 0) > 85 ? '#ff4d4f' :
                           (dbStatus?.usage_ratio || 0) > 70 ? '#faad14' : '#52c41a'
                  }}
                />
                {(dbStatus?.usage_ratio || 0) > 85 && (
                  <div style={{ fontSize: '12px', color: '#ff4d4f', marginTop: '4px' }}>
                    建议执行优化
                  </div>
                )}
              </Card>
            </Col>
          </Row>

          {/* 使用率说明 */}
          <Row style={{ marginTop: '16px' }}>
            <Col span={24}>
              <Alert
                message="数据库使用率说明"
                description={
                  <div>
                    <p><strong>使用率计算方式：</strong>基于实际数据占用的页面数量计算，更准确反映数据库的真实使用情况。</p>
                    <p><strong>优化建议：</strong></p>
                    <ul style={{ marginBottom: 0, paddingLeft: '20px' }}>
                      <li>使用率 &gt; 85%：建议立即执行数据库优化</li>
                      <li>使用率 70-85%：建议定期执行优化</li>
                      <li>使用率 &lt; 70%：数据库状态良好</li>
                    </ul>
                    <p style={{ marginTop: '8px', marginBottom: 0 }}>
                      <strong>注意：</strong>如果使用率显示100%，可能是因为数据库文件已增长但未回收空间，执行"优化数据库"可以解决此问题。
                    </p>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginBottom: '16px' }}
              />
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="表结构" key="tables">
          <Card title="数据库表信息">
            {tablesLoading ? (
              <Spin />
            ) : (
              <Table
                dataSource={tables}
                rowKey="name"
                columns={[
                  {
                    title: '表名',
                    dataIndex: 'name',
                    key: 'name',
                    render: (name: string) => <strong>{name}</strong>,
                  },
                  {
                    title: '记录数',
                    dataIndex: 'row_count',
                    key: 'row_count',
                    render: (count: number) => count.toLocaleString(),
                  },
                  {
                    title: '列数',
                    dataIndex: 'column_count',
                    key: 'column_count',
                  },
                  {
                    title: '主键列',
                    key: 'primary_keys',
                    render: (_, record: TableInfo) => {
                      const primaryKeys = record.columns.filter(col => col.primary_key);
                      return primaryKeys.map(col => (
                        <Tag key={col.name} color="blue">{col.name}</Tag>
                      ));
                    },
                  },
                  {
                    title: '操作',
                    key: 'actions',
                    render: (_, record: TableInfo) => (
                      <Button
                        type="link"
                        onClick={() => {
                          setActiveTab('data');
                          setSelectedTable(record.name);
                        }}
                      >
                        查看数据
                      </Button>
                    ),
                  },
                ]}
                expandable={{
                  expandedRowRender: (record: TableInfo) => (
                    <Table
                      dataSource={record.columns}
                      rowKey="name"
                      size="small"
                      pagination={false}
                      columns={[
                        { title: '列名', dataIndex: 'name', key: 'name' },
                        { title: '类型', dataIndex: 'type', key: 'type' },
                        {
                          title: '非空',
                          dataIndex: 'not_null',
                          key: 'not_null',
                          render: (notNull: boolean) => notNull ? <Tag color="red">NOT NULL</Tag> : <Tag>NULL</Tag>
                        },
                        {
                          title: '主键',
                          dataIndex: 'primary_key',
                          key: 'primary_key',
                          render: (isPK: boolean) => isPK ? <Tag color="blue">PK</Tag> : null
                        },
                        { title: '默认值', dataIndex: 'default_value', key: 'default_value' },
                      ]}
                    />
                  ),
                }}
              />
            )}
          </Card>
        </TabPane>

        <TabPane tab="数据查看" key="data">
          <TableDataViewer
            tables={tables || []}
            selectedTable={selectedTable}
            onTableChange={setSelectedTable}
          />
        </TabPane>
      </Tabs>

      {/* 备份创建模态框 */}
      <Modal
        title="创建数据库备份"
        open={backupModalVisible}
        onOk={handleCreateBackup}
        onCancel={() => {
          setBackupModalVisible(false);
          setBackupName('');
        }}
        confirmLoading={createBackupMutation.isPending}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>创建数据库备份文件，可以用于数据恢复。</div>
          <Input
            placeholder="备份名称（可选，留空将自动生成）"
            value={backupName}
            onChange={(e) => setBackupName(e.target.value)}
          />
        </Space>
      </Modal>
    </div>
  );
};

// 表数据查看器组件
interface TableDataViewerProps {
  tables: TableInfo[];
  selectedTable: string;
  onTableChange: (tableName: string) => void;
}

const TableDataViewer: React.FC<TableDataViewerProps> = ({
  tables,
  selectedTable,
  onTableChange
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [searchText, setSearchText] = useState('');
  const [sortField, setSortField] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC'>('DESC');
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<any>(null);
  const [cleanupModalVisible, setCleanupModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [recordToEdit, setRecordToEdit] = useState<any>(null);
  const [editFormData, setEditFormData] = useState<Record<string, any>>({});
  const queryClient = useQueryClient();

  // 获取表数据
  const { data: tableData, isLoading: dataLoading, error: dataError } = useQuery({
    queryKey: ['table-data', selectedTable, currentPage, pageSize, searchText, sortField, sortOrder],
    queryFn: () => {
      if (!selectedTable) return null;
      return databaseApi.getTableData(selectedTable, {
        page: currentPage,
        page_size: pageSize,
        search: searchText || undefined,
        order_by: sortField || undefined,
        order_direction: sortOrder,
      }).then(res => res.data);
    },
    enabled: !!selectedTable,
  });

  // 获取可编辑字段
  const { data: editableFields } = useQuery({
    queryKey: ['editable-fields', selectedTable],
    queryFn: () => {
      if (!selectedTable) return null;
      return databaseApi.getEditableFields(selectedTable).then(res => res.data.editable_fields);
    },
    enabled: !!selectedTable,
  });

  // 删除记录
  const deleteRecordMutation = useMutation({
    mutationFn: ({ tableName, recordId }: { tableName: string; recordId: string }) =>
      databaseApi.deleteTableRecord(tableName, recordId),
    onSuccess: () => {
      message.success('记录删除成功');
      setDeleteModalVisible(false);
      setRecordToDelete(null);
      queryClient.invalidateQueries({ queryKey: ['table-data', selectedTable] });
      queryClient.invalidateQueries({ queryKey: ['database-tables'] });
    },
    onError: (error: any) => {
      message.error(`删除失败: ${error.response?.data?.detail || error.message}`);
    },
  });

  // 编辑记录
  const editRecordMutation = useMutation({
    mutationFn: ({ tableName, recordId, updateData }: {
      tableName: string;
      recordId: string;
      updateData: Record<string, any>;
    }) => databaseApi.updateTableRecord(tableName, recordId, updateData),
    onSuccess: () => {
      message.success('记录更新成功');
      setEditModalVisible(false);
      setRecordToEdit(null);
      setEditFormData({});
      queryClient.invalidateQueries({ queryKey: ['table-data', selectedTable] });
    },
    onError: (error: any) => {
      message.error(`更新失败: ${error.response?.data?.detail || error.message}`);
    },
  });

  // 清理数据
  const cleanupDataMutation = useMutation({
    mutationFn: ({ tableName, cleanupType, daysOld }: {
      tableName: string;
      cleanupType: 'old_data' | 'test_data' | 'all';
      daysOld?: number;
    }) => databaseApi.cleanupTableData(tableName, cleanupType, daysOld),
    onSuccess: (response) => {
      const deletedCount = response.data?.data?.deleted_count || 0;
      message.success(`数据清理完成，删除了 ${deletedCount} 条记录`);
      setCleanupModalVisible(false);
      queryClient.invalidateQueries({ queryKey: ['table-data', selectedTable] });
      queryClient.invalidateQueries({ queryKey: ['database-tables'] });
    },
    onError: (error: any) => {
      message.error(`清理失败: ${error.response?.data?.detail || error.message}`);
    },
  });

  const handleSearch = (value: string) => {
    setSearchText(value);
    setCurrentPage(1);
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    if (sorter.field) {
      setSortField(sorter.field);
      setSortOrder(sorter.order === 'ascend' ? 'ASC' : 'DESC');
    }
    setCurrentPage(pagination.current);
    setPageSize(pagination.pageSize);
  };

  const handleDeleteRecord = (record: any) => {
    setRecordToDelete(record);
    setDeleteModalVisible(true);
  };

  const handleEditRecord = (record: any) => {
    setRecordToEdit(record);
    // 初始化编辑表单数据，只包含可编辑字段
    const initialData: Record<string, any> = {};
    if (editableFields) {
      editableFields.forEach((field: any) => {
        initialData[field.name] = record[field.name];
      });
    }
    setEditFormData(initialData);
    setEditModalVisible(true);
  };

  const confirmDelete = () => {
    if (recordToDelete && selectedTable) {
      // 获取主键值
      const primaryKey = getPrimaryKeyValue(recordToDelete);
      if (primaryKey) {
        deleteRecordMutation.mutate({
          tableName: selectedTable,
          recordId: primaryKey.toString(),
        });
      }
    }
  };

  const confirmEdit = () => {
    if (recordToEdit && selectedTable) {
      const primaryKey = getPrimaryKeyValue(recordToEdit);
      if (primaryKey) {
        editRecordMutation.mutate({
          tableName: selectedTable,
          recordId: primaryKey.toString(),
          updateData: editFormData,
        });
      }
    }
  };

  const getPrimaryKeyValue = (record: any) => {
    // 尝试常见的主键字段名
    const primaryKeyFields = ['id', 'message_id', 'conversation_id', 'document_id', 'focus_id'];
    for (const field of primaryKeyFields) {
      if (record[field] !== undefined) {
        return record[field];
      }
    }
    // 如果没有找到，返回第一个字段的值
    return Object.values(record)[0];
  };

  const handleCleanupData = (cleanupType: 'old_data' | 'test_data' | 'all', daysOld?: number) => {
    if (selectedTable) {
      cleanupDataMutation.mutate({
        tableName: selectedTable,
        cleanupType,
        daysOld,
      });
    }
  };

  const renderEditField = (field: any) => {
    const value = editFormData[field.name];
    const onChange = (newValue: any) => {
      setEditFormData(prev => ({
        ...prev,
        [field.name]: newValue
      }));
    };

    // 根据验证规则渲染不同的输入组件
    if (field.validation_rules?.type === 'select') {
      return (
        <Select
          value={value}
          onChange={onChange}
          style={{ width: '100%' }}
          placeholder={`请选择${field.name}`}
        >
          {field.validation_rules.options?.map((option: string) => (
            <Select.Option key={option} value={option}>
              {option}
            </Select.Option>
          ))}
        </Select>
      );
    }

    // 根据字段类型渲染输入组件
    switch (field.field_type) {
      case 'number':
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) => onChange(e.target.value ? Number(e.target.value) : null)}
            placeholder={`请输入${field.name}`}
          />
        );

      case 'boolean':
        return (
          <Select
            value={value}
            onChange={onChange}
            style={{ width: '100%' }}
          >
            <Select.Option value={true}>是</Select.Option>
            <Select.Option value={false}>否</Select.Option>
          </Select>
        );

      case 'textarea':
        return (
          <Input.TextArea
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={`请输入${field.name}`}
            rows={4}
            maxLength={field.validation_rules?.maxLength}
          />
        );

      case 'datetime':
        return (
          <Input
            type="datetime-local"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={`请选择${field.name}`}
          />
        );

      default:
        return (
          <Input
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={`请输入${field.name}`}
            maxLength={field.validation_rules?.maxLength}
          />
        );
    }
  };

  // 生成表格列
  const generateColumns = () => {
    if (!tableData?.columns) return [];

    const columns = tableData.columns.map((col: string) => ({
      title: col,
      dataIndex: col,
      key: col,
      sorter: true,
      ellipsis: true,
      render: (value: any) => {
        if (value === null || value === undefined) {
          return <span style={{ color: '#ccc' }}>NULL</span>;
        }
        if (typeof value === 'string' && value.length > 100) {
          return (
            <span title={value}>
              {value.substring(0, 100)}...
            </span>
          );
        }
        return value;
      },
    }));

    // 添加操作列
    columns.push({
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_: any, record: any) => (
        <Space size="small">
          {editableFields && editableFields.length > 0 && (
            <Button
              type="link"
              size="small"
              onClick={() => handleEditRecord(record)}
            >
              编辑
            </Button>
          )}
          <Button
            type="link"
            danger
            size="small"
            onClick={() => handleDeleteRecord(record)}
          >
            删除
          </Button>
        </Space>
      ),
    });

    return columns;
  };

  if (!selectedTable) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <DatabaseOutlined style={{ fontSize: '48px', color: '#ccc' }} />
          <h3>请选择要查看的表</h3>
          <Space wrap>
            {tables.map(table => (
              <Button
                key={table.name}
                onClick={() => onTableChange(table.name)}
              >
                {table.name} ({table.row_count} 条记录)
              </Button>
            ))}
          </Space>
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={`表数据: ${selectedTable}`}
      extra={
        <Space>
          <Input.Search
            placeholder="搜索数据..."
            onSearch={handleSearch}
            style={{ width: 200 }}
          />
          <Button onClick={() => setCleanupModalVisible(true)}>
            数据清理
          </Button>
          <Button onClick={() => onTableChange('')}>
            返回表列表
          </Button>
        </Space>
      }
    >
      {dataError ? (
        <Alert
          message="数据加载失败"
          description={dataError.message}
          type="error"
          showIcon
        />
      ) : (
        <>
          <Table
            dataSource={tableData?.data || []}
            columns={generateColumns()}
            loading={dataLoading}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: tableData?.pagination?.total_count || 0,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            }}
            onChange={handleTableChange}
            scroll={{ x: 'max-content' }}
            size="small"
          />

          {/* 删除确认模态框 */}
          <Modal
            title="确认删除"
            open={deleteModalVisible}
            onOk={confirmDelete}
            onCancel={() => {
              setDeleteModalVisible(false);
              setRecordToDelete(null);
            }}
            confirmLoading={deleteRecordMutation.isPending}
          >
            <p>确定要删除这条记录吗？此操作不可恢复。</p>
            {recordToDelete && (
              <div style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                <strong>记录信息：</strong>
                <pre style={{ fontSize: '12px', margin: '8px 0 0 0' }}>
                  {JSON.stringify(recordToDelete, null, 2)}
                </pre>
              </div>
            )}
          </Modal>

          {/* 编辑记录模态框 */}
          <Modal
            title="编辑记录"
            open={editModalVisible}
            onOk={confirmEdit}
            onCancel={() => {
              setEditModalVisible(false);
              setRecordToEdit(null);
              setEditFormData({});
            }}
            confirmLoading={editRecordMutation.isPending}
            width={600}
          >
            {editableFields && editableFields.length > 0 ? (
              <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                <Alert
                  message="编辑提示"
                  description="只能编辑安全的字段，系统字段和敏感数据不可编辑"
                  type="info"
                  showIcon
                  style={{ marginBottom: '16px' }}
                />
                <Space direction="vertical" style={{ width: '100%' }}>
                  {editableFields.map((field: any) => (
                    <div key={field.name}>
                      <label style={{ display: 'block', marginBottom: '4px', fontWeight: 'bold' }}>
                        {field.name}
                        {field.not_null && <span style={{ color: 'red' }}> *</span>}
                        <span style={{ fontSize: '12px', color: '#666', marginLeft: '8px' }}>
                          ({field.type})
                        </span>
                      </label>
                      {renderEditField(field)}
                    </div>
                  ))}
                </Space>
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <p>该表没有可编辑的字段</p>
              </div>
            )}
          </Modal>

          {/* 数据清理模态框 */}
          <Modal
            title="数据清理"
            open={cleanupModalVisible}
            onCancel={() => setCleanupModalVisible(false)}
            footer={null}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message="注意"
                description="数据清理操作不可恢复，请谨慎操作！"
                type="warning"
                showIcon
              />

              <Button
                block
                onClick={() => handleCleanupData('test_data')}
                loading={cleanupDataMutation.isPending}
              >
                清理测试数据（删除包含 test_、demo_、sample_ 前缀的数据）
              </Button>

              <Button
                block
                onClick={() => handleCleanupData('old_data', 30)}
                loading={cleanupDataMutation.isPending}
              >
                清理30天前的旧数据
              </Button>

              <Button
                block
                danger
                onClick={() => {
                  confirm({
                    title: '确认清空表',
                    content: '这将删除表中的所有数据，确定要继续吗？',
                    onOk: () => handleCleanupData('all'),
                  });
                }}
                loading={cleanupDataMutation.isPending}
              >
                清空整个表
              </Button>
            </Space>
          </Modal>
        </>
      )}
    </Card>
  );
};

export default DatabaseManagement;
