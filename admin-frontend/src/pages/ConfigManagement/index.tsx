import React, { useState } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  InputNumber, 
  Select, 
  Space, 
  message, 
  Tag,
  Descriptions,
  Tooltip,
  Popconfirm
} from 'antd';
import { 
  EditOutlined, 
  DeleteOutlined, 
  PlusOutlined, 
  PlayCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { llmConfigApi } from '../../api/services';

const { Option } = Select;

interface LLMModel {
  name: string;
  provider: string;
  api_key_masked: string;
  api_base: string;
  model_name: string;
  temperature: number;
  max_tokens: number;
  timeout: number;
  max_retries: number;
}

const ConfigManagement: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingModel, setEditingModel] = useState<LLMModel | null>(null);
  const [isTestModalVisible, setIsTestModalVisible] = useState(false);
  const [testingModel, setTestingModel] = useState<string>('');
  const [testPrompt, setTestPrompt] = useState('Hello, this is a test.');
  const [testResult, setTestResult] = useState<any>(null);
  
  const queryClient = useQueryClient();

  // 获取所有LLM配置
  const { data: models = [], isLoading } = useQuery({
    queryKey: ['llm-models'],
    queryFn: async () => {
      const response = await llmConfigApi.getAllConfigs();
      return response.data;
    }
  });

  // 更新模型配置
  const updateMutation = useMutation({
    mutationFn: ({ name, config }: { name: string; config: any }) => 
      llmConfigApi.updateModelConfig(name, config),
    onSuccess: () => {
      message.success('模型配置更新成功');
      queryClient.invalidateQueries({ queryKey: ['llm-models'] });
      setIsModalVisible(false);
    },
    onError: (error: any) => {
      message.error(`更新失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  // 创建新模型
  const createMutation = useMutation({
    mutationFn: (config: any) => llmConfigApi.addModelConfig(config),
    onSuccess: () => {
      message.success('模型创建成功');
      queryClient.invalidateQueries({ queryKey: ['llm-models'] });
      setIsModalVisible(false);
    },
    onError: (error: any) => {
      message.error(`创建失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  // 测试模型连接
  const testMutation = useMutation({
    mutationFn: ({ name, prompt }: { name: string; prompt: string }) => 
      llmConfigApi.testModelConnection(name, prompt),
    onSuccess: (response) => {
      setTestResult(response.data);
      if (response.data.success) {
        message.success('连接测试成功');
      } else {
        message.error('连接测试失败');
      }
    },
    onError: (error: any) => {
      message.error(`测试失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  // 重新加载配置
  const reloadMutation = useMutation({
    mutationFn: () => llmConfigApi.reloadConfig(),
    onSuccess: () => {
      message.success('配置重新加载成功');
      queryClient.invalidateQueries({ queryKey: ['llm-models'] });
    },
    onError: (error: any) => {
      message.error(`重新加载失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  const columns = [
    {
      title: '模型名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <strong>{text}</strong>
    },
    {
      title: '提供商',
      dataIndex: 'provider',
      key: 'provider',
      render: (provider: string) => (
        <Tag color={
          provider === 'deepseek' ? 'blue' :
          provider === 'doubao' ? 'orange' :
          provider === 'qwen' ? 'purple' :
          provider === 'openrouter' ? 'green' : 'default'
        }>
          {provider}
        </Tag>
      )
    },
    {
      title: '模型名称',
      dataIndex: 'model_name',
      key: 'model_name'
    },
    {
      title: '基础URL',
      dataIndex: 'api_base',
      key: 'api_base',
      ellipsis: true,
      render: (url: string) => (
        <Tooltip title={url}>
          <span>{url}</span>
        </Tooltip>
      )
    },
    {
      title: 'API密钥',
      dataIndex: 'api_key_masked',
      key: 'api_key_masked',
      render: (key: string) => <span style={{ fontFamily: 'monospace' }}>{key}</span>
    },
    {
      title: '参数',
      key: 'parameters',
      render: (_: any, record: LLMModel) => (
        <Space direction="vertical" size={0}>
          <span>温度: {record.temperature}</span>
          <span>最大token: {record.max_tokens}</span>
          <span>超时: {record.timeout}s</span>
          <span>重试: {record.max_retries}次</span>
        </Space>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: LLMModel) => (
        <Space>
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            icon={<PlayCircleOutlined />}
            size="small"
            onClick={() => handleTest(record.name)}
          >
            测试
          </Button>
        </Space>
      )
    }
  ];

  const handleEdit = (model: LLMModel) => {
    setEditingModel(model);
    setIsModalVisible(true);
  };

  const handleCreate = () => {
    setEditingModel(null);
    setIsModalVisible(true);
  };

  const handleTest = (modelName: string) => {
    setTestingModel(modelName);
    setIsTestModalVisible(true);
    setTestResult(null);
  };

  const handleModalOk = (values: any) => {
    if (editingModel) {
      updateMutation.mutate({ name: editingModel.name, config: values });
    } else {
      createMutation.mutate(values);
    }
  };

  const handleTestModalOk = () => {
    testMutation.mutate({ name: testingModel, prompt: testPrompt });
  };

  const handleReload = () => {
    reloadMutation.mutate();
  };

  return (
    <div>
      <Card 
        title="LLM配置管理" 
        extra={
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleReload}
              loading={reloadMutation.isPending}
            >
              重新加载
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleCreate}
            >
              添加模型
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={models}
          rowKey="name"
          loading={isLoading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* 编辑/创建模态框 */}
      <Modal
        title={editingModel ? '编辑模型配置' : '创建新模型'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          layout="vertical"
          onFinish={handleModalOk}
          initialValues={editingModel || {
            provider: 'deepseek',
            temperature: 0.7,
            max_tokens: 4000,
            timeout: 30,
            max_retries: 3
          }}
        >
          <Form.Item
            label="模型名称"
            name="name"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input disabled={!!editingModel} />
          </Form.Item>

          <Form.Item
            label="提供商"
            name="provider"
            rules={[{ required: true, message: '请选择提供商' }]}
          >
            <Select>
              <Option value="deepseek">DeepSeek</Option>
              <Option value="doubao">豆包</Option>
              <Option value="qwen">通义千问</Option>
              <Option value="openrouter">OpenRouter</Option>
              <Option value="openai">OpenAI</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="API密钥"
            name="api_key"
            rules={[{ required: true, message: '请输入API密钥' }]}
          >
            <Input.Password placeholder={editingModel ? '留空保持原值' : '输入API密钥'} />
          </Form.Item>

          <Form.Item
            label="基础URL"
            name="api_base"
            rules={[{ required: true, message: '请输入基础URL' }]}
          >
            <Input placeholder="https://api.deepseek.com/v1" />
          </Form.Item>

          <Form.Item
            label="模型名称"
            name="model_name"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input placeholder="deepseek-chat" />
          </Form.Item>

          <Form.Item
            label="温度 (0.0-2.0)"
            name="temperature"
            rules={[{ required: true, message: '请输入温度值' }]}
          >
            <InputNumber min={0} max={2} step={0.1} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            label="最大Token数"
            name="max_tokens"
            rules={[{ required: true, message: '请输入最大Token数' }]}
          >
            <InputNumber min={1} max={100000} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            label="超时时间 (秒)"
            name="timeout"
            rules={[{ required: true, message: '请输入超时时间' }]}
          >
            <InputNumber min={1} max={300} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            label="最大重试次数"
            name="max_retries"
            rules={[{ required: true, message: '请输入最大重试次数' }]}
          >
            <InputNumber min={0} max={10} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={updateMutation.isPending || createMutation.isPending}>
                {editingModel ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setIsModalVisible(false)}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 测试连接模态框 */}
      <Modal
        title={`测试模型连接: ${testingModel}`}
        open={isTestModalVisible}
        onCancel={() => setIsTestModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form layout="vertical">
          <Form.Item label="测试提示词">
            <Input.TextArea
              rows={3}
              value={testPrompt}
              onChange={(e) => setTestPrompt(e.target.value)}
            />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                onClick={handleTestModalOk}
                loading={testMutation.isPending}
              >
                开始测试
              </Button>
              <Button onClick={() => setIsTestModalVisible(false)}>关闭</Button>
            </Space>
          </Form.Item>
        </Form>

        {testResult && (
          <div style={{ marginTop: 16 }}>
            <h4>测试结果:</h4>
            <p>状态: <Tag color={testResult.success ? 'green' : 'red'}>
              {testResult.success ? '成功' : '失败'}
            </Tag></p>
            <p>延迟: {testResult.latency}秒</p>
            {testResult.success && (
              <div>
                <p>响应:</p>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: 8, 
                  borderRadius: 4,
                  maxHeight: 200,
                  overflow: 'auto'
                }}>
                  {testResult.response}
                </pre>
              </div>
            )}
            {testResult.error && (
              <div>
                <p>错误:</p>
                <pre style={{ 
                  background: '#fff2f0', 
                  padding: 8, 
                  borderRadius: 4,
                  color: '#ff4d4f'
                }}>
                  {testResult.error}
                </pre>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ConfigManagement;
