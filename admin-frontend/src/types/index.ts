// LLM配置相关类型
export interface LLMModel {
  name: string;
  provider: string;
  api_key: string;
  api_base: string;
  model_name: string;
  temperature: number;
  max_tokens: number;
  timeout: number;
  max_retries: number;
}

export interface LLMConfigUpdate {
  model_name: string;
  config: Record<string, any>;
}

export interface ConnectionTestResult {
  success: boolean;
  response?: string;
  error?: string;
  latency?: number;
}

// 场景映射相关类型
export interface ScenarioMapping {
  scenario_name: string;
  model_name: string;
  parameters: Record<string, any>;
}

export interface ScenarioTestResult {
  success: boolean;
  response?: string;
  error?: string;
  model_used: string;
  parameters_used: Record<string, any>;
}

// 模板相关类型
export interface PromptTemplate {
  filename: string;
  content: string;
  last_modified: string;
}

export interface MessageTemplate {
  path: string;
  content: string;
  template_type: string;
}

export interface TemplateTestResult {
  success: boolean;
  rendered_content?: string;
  error?: string;
}

// 系统状态相关类型
export interface SystemStatus {
  status: string;
  config_file_status: string;
  template_files_status: string;
  last_reload_time?: string;
}

// API响应通用类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 表单相关类型
export interface FormField {
  name: string;
  label: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'textarea';
  required?: boolean;
  options?: Array<{ label: string; value: any }>;
  placeholder?: string;
  min?: number;
  max?: number;
}