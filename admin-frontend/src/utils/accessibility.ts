/**
 * 辅助功能工具函数
 * 用于修复常见的可访问性问题
 */

/**
 * 移除body元素上的aria-hidden属性
 * 这通常是由某些UI库错误添加的
 */
export const fixBodyAriaHidden = (): void => {
  const body = document.body;
  if (body) {
    if (body.hasAttribute('aria-hidden')) {
      body.removeAttribute('aria-hidden');
      console.log('已移除body元素上的aria-hidden属性');
    }
    // 确保body不会再次被设置为aria-hidden
    body.setAttribute('aria-hidden', 'false');
  }
};

/**
 * 强制移除所有可能的aria-hidden属性
 */
export const forceRemoveAriaHidden = (): void => {
  // 移除body上的aria-hidden
  const body = document.body;
  if (body) {
    body.removeAttribute('aria-hidden');
    body.setAttribute('aria-hidden', 'false');
  }
  
  // 移除其他关键元素上的aria-hidden
  const elements = document.querySelectorAll('[aria-hidden="true"]');
  elements.forEach(el => {
    if (el.tagName === 'BODY' || el.tagName === 'HTML') {
      el.removeAttribute('aria-hidden');
    }
  });
};

/**
 * 初始化所有辅助功能修复
 */
export const initAccessibility = (): (() => void) => {
  // 立即执行修复
  forceRemoveAriaHidden();
  
  // 在DOM加载完成后执行修复
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', forceRemoveAriaHidden);
  } else {
    // DOM已经加载完成
    forceRemoveAriaHidden();
  }
  
  // 使用更激进的监听策略
  let observer: MutationObserver | null = null;
  if (window.MutationObserver) {
    observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'aria-hidden') {
          const target = mutation.target as HTMLElement;
          if (target.tagName === 'BODY' || target.tagName === 'HTML') {
            forceRemoveAriaHidden();
          }
        }
      });
    });
    
    // 监听整个文档的变化
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['aria-hidden'],
      subtree: true
    });
  }
  
  // 定期检查和修复
  const interval = setInterval(forceRemoveAriaHidden, 1000);
  
  // 清理函数
  return () => {
    if (observer) {
      observer.disconnect();
    }
    if (interval) {
      clearInterval(interval);
    }
    document.removeEventListener('DOMContentLoaded', forceRemoveAriaHidden);
  };
};
