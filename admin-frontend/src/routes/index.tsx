import React from 'react';
import { Routes, Route } from 'react-router-dom';
import MainLayout from '../components/Layout/MainLayout';

// 页面组件
const Dashboard = React.lazy(() => import('../pages/Dashboard'));
const ConfigManagement = React.lazy(() => import('../pages/ConfigManagement'));
const ScenarioMapping = React.lazy(() => import('../pages/ScenarioMapping'));
const PromptTemplates = React.lazy(() => import('../pages/PromptTemplates'));
const MessageTemplates = React.lazy(() => import('../pages/MessageTemplates'));
const DatabaseManagement = React.lazy(() => import('../pages/DatabaseManagement'));
const BusinessRules = React.lazy(() => import('../pages/BusinessRules'));
const ConfigMonitoring = React.lazy(() => import('../pages/ConfigMonitoring'));

const AppRoutes: React.FC = () => {
  return (
    <MainLayout>
      <React.Suspense fallback={<div>Loading...</div>}>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/config" element={<ConfigManagement />} />
          <Route path="/scenario" element={<ScenarioMapping />} />
          <Route path="/template/prompts" element={<PromptTemplates />} />
          <Route path="/template/messages" element={<MessageTemplates />} />
          <Route path="/database" element={<DatabaseManagement />} />
          <Route path="/business-rules" element={<BusinessRules />} />
          <Route path="/config-monitoring" element={<ConfigMonitoring />} />
        </Routes>
      </React.Suspense>
    </MainLayout>
  );
};

export default AppRoutes;
