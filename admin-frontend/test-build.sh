#!/bin/bash

echo "🔧 测试前端编译..."

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 请在admin-frontend目录下运行此脚本"
    exit 1
fi

echo "📦 安装依赖..."
npm install

echo "🔍 运行TypeScript检查..."
npx tsc --noEmit

if [ $? -eq 0 ]; then
    echo "✅ TypeScript检查通过"
else
    echo "❌ TypeScript检查失败"
    exit 1
fi

echo "🔍 运行ESLint检查..."
npx eslint src --ext .ts,.tsx

if [ $? -eq 0 ]; then
    echo "✅ ESLint检查通过"
else
    echo "⚠️  ESLint发现警告，但不影响编译"
fi

echo "🏗️  尝试构建..."
npm run build

if [ $? -eq 0 ]; then
    echo "🎉 构建成功！所有问题已修复"
else
    echo "❌ 构建失败，请检查错误信息"
    exit 1
fi
