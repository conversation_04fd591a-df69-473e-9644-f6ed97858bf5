# 代码审查清单 - 硬编码检查

## 概述

这个清单用于在代码审查过程中系统性地检查硬编码问题，确保新代码符合配置化标准。

## 🔍 硬编码检查清单

### 1. 字符串硬编码检查

#### ❌ 需要避免的硬编码
- [ ] **错误消息硬编码**
  ```python
  # ❌ 错误示例
  return "抱歉，处理您的请求时发生错误"
  
  # ✅ 正确示例
  return config_manager.get_message_template("error.general.unknown_error")
  ```

- [ ] **用户提示消息硬编码**
  ```python
  # ❌ 错误示例
  return "请提供更多详细信息"
  
  # ✅ 正确示例
  return config_manager.get_message_template("clarification.general.request")
  ```

- [ ] **数据库查询硬编码**
  ```python
  # ❌ 错误示例
  query = "SELECT * FROM users WHERE id = ?"
  
  # ✅ 正确示例
  query = config_manager.get_database_query("users.get_by_id")
  ```

- [ ] **文件路径硬编码**
  ```python
  # ❌ 错误示例
  log_file = "/var/log/app.log"
  
  # ✅ 正确示例
  log_file = config_manager.get_config("logging.file_path", "logs/app.log")
  ```

### 2. 数值硬编码检查

#### ❌ 需要避免的硬编码
- [ ] **阈值硬编码**
  ```python
  # ❌ 错误示例
  if confidence > 0.7:
  
  # ✅ 正确示例
  threshold = config_manager.get_threshold("confidence.threshold", 0.7)
  if confidence > threshold:
  ```

- [ ] **重试次数硬编码**
  ```python
  # ❌ 错误示例
  max_retries = 3
  
  # ✅ 正确示例
  max_retries = config_manager.get_business_rule("retry.max_attempts", 3)
  ```

- [ ] **超时时间硬编码**
  ```python
  # ❌ 错误示例
  timeout = 30
  
  # ✅ 正确示例
  timeout = config_manager.get_threshold("request.timeout", 30)
  ```

- [ ] **LLM参数硬编码**
  ```python
  # ❌ 错误示例
  temperature = 0.7
  
  # ✅ 正确示例
  temperature = config_manager.get_threshold("llm.temperature.default", 0.7)
  ```

### 3. 业务逻辑硬编码检查

#### ❌ 需要避免的硬编码
- [ ] **优先级数值硬编码**
  ```python
  # ❌ 错误示例
  priority = 7
  
  # ✅ 正确示例
  priority = config_manager.get_business_rule("priority.document_modification", 7)
  ```

- [ ] **状态值硬编码**
  ```python
  # ❌ 错误示例
  if status == "completed":
  
  # ✅ 正确示例
  completed_status = config_manager.get_business_rule("status.completed", "completed")
  if status == completed_status:
  ```

### 4. 配置相关硬编码检查

#### ❌ 需要避免的硬编码
- [ ] **API端点硬编码**
  ```python
  # ❌ 错误示例
  api_url = "https://api.example.com/v1"
  
  # ✅ 正确示例
  api_url = config_manager.get_config("api.base_url", "https://api.example.com/v1")
  ```

- [ ] **模型名称硬编码**
  ```python
  # ❌ 错误示例
  model_name = "gpt-3.5-turbo"
  
  # ✅ 正确示例
  model_name = config_manager.get_config("llm.default_model", "gpt-3.5-turbo")
  ```

## ✅ 允许的硬编码例外

### 1. 技术常量
- [ ] **HTTP状态码**: `200`, `404`, `500` 等
- [ ] **数学常量**: `0`, `1`, `-1`, `2` 等基础数值
- [ ] **布尔值**: `True`, `False`
- [ ] **空值**: `None`, `""`, `[]`, `{}`

### 2. 框架和库相关
- [ ] **日志级别**: `DEBUG`, `INFO`, `WARNING`, `ERROR`
- [ ] **编码格式**: `"utf-8"`
- [ ] **内容类型**: `"application/json"`

### 3. 开发和测试
- [ ] **测试数据**: 以 `test_`, `mock_` 开头的变量
- [ ] **调试信息**: 临时的调试输出
- [ ] **示例代码**: 文档中的示例

## 🛠️ 检查工具使用

### 1. 自动化检测
```bash
# 运行硬编码检测工具
python tools/hardcode_detector.py backend/agents/

# 生成JSON格式报告
python tools/hardcode_detector.py backend/agents/ json
```

### 2. IDE集成检查
- 使用 pylint 或 flake8 的自定义规则
- 配置 pre-commit hooks
- 集成到 CI/CD 流程

## 📋 审查流程

### 1. 代码提交前
- [ ] 运行硬编码检测工具
- [ ] 检查是否有新的硬编码引入
- [ ] 确认所有配置都使用了 `config_manager`

### 2. Pull Request 审查
- [ ] 审查者使用此清单检查代码
- [ ] 确认硬编码检测工具通过
- [ ] 验证配置文件是否需要更新

### 3. 合并前最终检查
- [ ] 所有硬编码问题已解决
- [ ] 配置文件已同步更新
- [ ] 文档已更新（如需要）

## 🎯 最佳实践

### 1. 配置优先原则
- 优先使用 `config_manager` 获取配置
- 为所有配置提供合理的默认值
- 使用有意义的配置键名

### 2. 分层配置策略
- **业务规则**: 使用 `get_business_rule()`
- **技术阈值**: 使用 `get_threshold()`
- **消息模板**: 使用 `get_message_template()`
- **数据库查询**: 使用 `get_database_query()`

### 3. 文档和注释
- 为新增的配置项添加文档
- 在代码中注释配置的用途
- 保持配置文件的结构清晰

## 🚨 常见问题和解决方案

### 1. 临时硬编码
**问题**: 开发过程中的临时硬编码
**解决**: 使用 `TODO` 注释标记，并在代码审查中重点检查

### 2. 第三方库限制
**问题**: 第三方库要求硬编码参数
**解决**: 在调用处使用配置包装，添加注释说明原因

### 3. 性能考虑
**问题**: 担心配置读取影响性能
**解决**: 配置管理器已实现缓存，性能影响可忽略

## 📊 质量指标

### 1. 硬编码密度
- **目标**: 每1000行代码中硬编码问题 < 5个
- **测量**: 使用检测工具定期扫描

### 2. 配置覆盖率
- **目标**: 90%以上的可配置项使用配置管理器
- **测量**: 代码审查和工具检测

### 3. 修复时间
- **目标**: 发现硬编码问题后24小时内修复
- **测量**: 问题跟踪系统

---

**记住**: 配置化不是目的，而是提高代码质量和系统灵活性的手段。在审查过程中要平衡配置化的收益和复杂性。
