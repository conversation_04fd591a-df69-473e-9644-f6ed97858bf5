# 策略开发指南

## 📖 概述

本指南将帮助您为统一决策引擎开发新的策略。策略是决策引擎的核心组件，负责处理特定类型的用户请求。

## 🏗️ 策略架构

### 策略接口

所有策略都必须继承 `DecisionStrategy` 基类并实现以下接口：

```python
from backend.agents.decision_types import DecisionStrategy, AnalyzedContext, DecisionResult

class YourStrategy(DecisionStrategy):
    @property
    def name(self) -> str:
        """策略名称"""
        
    @property
    def supported_intents(self) -> List[str]:
        """支持的意图列表"""
        
    @property
    def priority(self) -> int:
        """策略优先级 (1-9)"""
        
    async def can_handle(self, context: AnalyzedContext) -> bool:
        """判断是否能处理当前上下文"""
        
    async def execute(self, context: AnalyzedContext) -> DecisionResult:
        """执行策略逻辑"""
        
    async def calculate_confidence(self, context: AnalyzedContext) -> float:
        """计算处理置信度"""
```

## 🚀 快速开始

### 1. 创建策略文件

在 `backend/agents/strategies/` 目录下创建新的策略文件：

```bash
touch backend/agents/strategies/your_new_strategy.py
```

### 2. 实现策略类

```python
"""
您的策略名称 - 策略实现

处理特定的业务场景，包括：
- 功能描述1
- 功能描述2
- 功能描述3

优先级: X (1-9)
支持意图: your_intent_name
"""

import logging
from typing import List, Dict, Any
import random
import re

from ..decision_types import DecisionStrategy, AnalyzedContext, DecisionResult, create_decision_result
from ..unified_state_manager import ConversationState


class YourNewStrategy(DecisionStrategy):
    """您的策略实现"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 策略配置
        self.keywords = ["关键词1", "关键词2", "关键词3"]
        self.response_templates = [
            "回复模板1：{user_message}",
            "回复模板2：{user_message}",
            "回复模板3：{user_message}"
        ]
        
        # 匹配模式
        self.patterns = [
            r"正则表达式1",
            r"正则表达式2"
        ]
    
    @property
    def name(self) -> str:
        return "your_new_strategy"
    
    @property
    def supported_intents(self) -> List[str]:
        return ["your_intent_name"]
    
    @property
    def priority(self) -> int:
        return 6  # 设置合适的优先级
    
    async def can_handle(self, context: AnalyzedContext) -> bool:
        """
        判断是否能处理当前上下文
        
        实现策略：
        1. 意图匹配 - 最直接的匹配方式
        2. 关键词匹配 - 基于预定义关键词
        3. 模式匹配 - 使用正则表达式
        4. 上下文分析 - 考虑对话状态和历史
        """
        # 1. 意图匹配
        if context.intent in self.supported_intents:
            return True
        
        # 2. 关键词匹配
        message_lower = context.message.lower()
        keyword_matches = sum(1 for keyword in self.keywords if keyword in message_lower)
        if keyword_matches >= 2:  # 至少匹配2个关键词
            return True
        
        # 3. 模式匹配
        for pattern in self.patterns:
            if re.search(pattern, context.message, re.IGNORECASE):
                return True
        
        # 4. 上下文分析
        if self._analyze_context(context):
            return True
        
        return False
    
    async def execute(self, context: AnalyzedContext) -> DecisionResult:
        """
        执行策略逻辑
        
        步骤：
        1. 分析用户输入
        2. 选择处理方式
        3. 生成回复内容
        4. 计算置信度
        5. 创建决策结果
        """
        try:
            # 1. 分析用户输入
            analysis = self._analyze_user_input(context)
            
            # 2. 选择处理方式
            action = self._determine_action(analysis)
            
            # 3. 生成回复内容
            response_template = self._generate_response(context, analysis)
            
            # 4. 计算置信度
            confidence = await self.calculate_confidence(context)
            
            # 5. 确定下一状态
            next_state = self._determine_next_state(context, analysis)
            
            # 6. 创建决策结果
            result = create_decision_result(
                action=action,
                confidence=confidence,
                intent=context.intent,
                emotion=context.emotion,
                response_template=response_template,
                next_state=next_state,
                strategy_name=self.name,
                metadata={
                    "analysis": analysis,
                    "processing_time": 0.0,  # 可以记录处理时间
                    "strategy_version": "1.0"
                }
            )
            
            self.logger.info(f"策略执行成功: action={action}, confidence={confidence:.3f}")
            return result
            
        except Exception as e:
            self.logger.error(f"策略执行失败: {e}", exc_info=True)
            
            # 返回错误处理结果
            return create_decision_result(
                action="handle_error",
                confidence=0.3,
                intent="unknown",
                emotion="neutral",
                response_template="抱歉，处理您的请求时遇到了问题，请稍后再试。",
                next_state=ConversationState.IDLE,
                strategy_name=self.name,
                metadata={"error": str(e), "fallback": True}
            )
    
    async def calculate_confidence(self, context: AnalyzedContext) -> float:
        """
        计算处理置信度
        
        考虑因素：
        1. 意图匹配度
        2. 关键词匹配度
        3. 消息复杂度
        4. 上下文相关性
        """
        confidence_factors = []
        
        # 1. 意图匹配度
        if context.intent in self.supported_intents:
            confidence_factors.append(0.9)
        else:
            confidence_factors.append(0.4)
        
        # 2. 关键词匹配度
        keyword_score = self._calculate_keyword_score(context)
        confidence_factors.append(keyword_score)
        
        # 3. 消息复杂度
        complexity_score = self._calculate_complexity_score(context)
        confidence_factors.append(complexity_score)
        
        # 4. 上下文相关性
        context_score = self._calculate_context_score(context)
        confidence_factors.append(context_score)
        
        # 计算加权平均
        return sum(confidence_factors) / len(confidence_factors)
    
    def _analyze_user_input(self, context: AnalyzedContext) -> Dict[str, Any]:
        """分析用户输入"""
        return {
            "message_length": len(context.message),
            "keyword_matches": [kw for kw in self.keywords if kw in context.message.lower()],
            "has_question": "?" in context.message or "？" in context.message,
            "urgency_level": self._detect_urgency(context.message)
        }
    
    def _determine_action(self, analysis: Dict[str, Any]) -> str:
        """确定处理动作"""
        if analysis["urgency_level"] == "high":
            return "handle_urgent_request"
        elif analysis["has_question"]:
            return "answer_question"
        else:
            return "provide_general_help"
    
    def _generate_response(self, context: AnalyzedContext, analysis: Dict[str, Any]) -> str:
        """生成回复内容"""
        template = random.choice(self.response_templates)
        return template.format(
            user_message=context.message[:50],  # 限制长度
            keyword_count=len(analysis["keyword_matches"])
        )
    
    def _determine_next_state(self, context: AnalyzedContext, analysis: Dict[str, Any]) -> ConversationState:
        """确定下一个对话状态"""
        if analysis["urgency_level"] == "high":
            return ConversationState.COLLECTING_REQUIREMENTS
        else:
            return ConversationState.IDLE
    
    def _analyze_context(self, context: AnalyzedContext) -> bool:
        """分析上下文相关性"""
        # 实现上下文分析逻辑
        return False
    
    def _calculate_keyword_score(self, context: AnalyzedContext) -> float:
        """计算关键词匹配分数"""
        message_lower = context.message.lower()
        matches = sum(1 for keyword in self.keywords if keyword in message_lower)
        return min(0.9, matches / len(self.keywords) * 2)  # 放大匹配效果
    
    def _calculate_complexity_score(self, context: AnalyzedContext) -> float:
        """计算消息复杂度分数"""
        length = len(context.message)
        if length > 50:
            return 0.8
        elif length > 20:
            return 0.6
        else:
            return 0.4
    
    def _calculate_context_score(self, context: AnalyzedContext) -> float:
        """计算上下文相关性分数"""
        # 根据对话历史、用户状态等计算相关性
        return 0.5  # 默认中等相关性
    
    def _detect_urgency(self, message: str) -> str:
        """检测消息紧急程度"""
        urgent_keywords = ["紧急", "急", "马上", "立即", "赶紧"]
        if any(keyword in message for keyword in urgent_keywords):
            return "high"
        return "normal"


# 导出策略类
__all__ = ['YourNewStrategy']
```

### 3. 注册策略

在 `backend/agents/unified_decision_engine.py` 中注册新策略：

```python
def _auto_load_strategies(self):
    """自动加载和注册策略"""
    try:
        # 导入现有策略
        from .strategies.greeting_strategy import GreetingStrategy
        # ... 其他策略导入
        
        # 导入新策略
        from .strategies.your_new_strategy import YourNewStrategy
        
        # 创建策略实例并注册
        strategies = [
            GreetingStrategy(),
            # ... 其他策略实例
            YourNewStrategy(),  # 添加新策略
            FallbackStrategy()  # 保持回退策略在最后
        ]
        
        for strategy in strategies:
            self.strategy_registry.register_strategy(strategy)
```

## 🧪 测试策略

### 单元测试

```python
import pytest
from backend.agents.strategies.your_new_strategy import YourNewStrategy
from backend.agents.decision_types import create_analyzed_context

class TestYourNewStrategy:
    
    @pytest.fixture
    def strategy(self):
        return YourNewStrategy()
    
    @pytest.mark.asyncio
    async def test_can_handle_supported_intent(self, strategy):
        context = create_analyzed_context(
            message="测试消息",
            intent="your_intent_name"
        )
        
        result = await strategy.can_handle(context)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_execute_returns_valid_result(self, strategy):
        context = create_analyzed_context(
            message="测试消息",
            intent="your_intent_name"
        )
        
        result = await strategy.execute(context)
        
        assert result.action is not None
        assert result.confidence > 0
        assert result.strategy_name == "your_new_strategy"
```

### 集成测试

```python
from backend.agents.unified_decision_engine import get_unified_decision_engine
from backend.agents.decision_types import create_decision_context

async def test_strategy_integration():
    engine = get_unified_decision_engine()
    
    context = create_decision_context(
        session_id='test',
        user_id='test',
        message='触发您策略的消息',
        current_state=ConversationState.IDLE
    )
    
    result = await engine.make_decision(context)
    
    assert result.strategy_name == "your_new_strategy"
    assert result.confidence > 0.5
```

## 📊 最佳实践

### 1. 策略设计原则

- **单一职责**：每个策略只处理一种类型的请求
- **高内聚**：策略内部逻辑紧密相关
- **低耦合**：策略之间相互独立
- **可测试**：易于编写单元测试和集成测试

### 2. 性能优化

- **快速判断**：`can_handle` 方法应该尽可能快速
- **缓存友好**：相同输入应该产生相同输出
- **资源节约**：避免不必要的计算和内存使用

### 3. 错误处理

- **优雅降级**：出错时返回合理的默认结果
- **详细日志**：记录足够的调试信息
- **异常捕获**：捕获并处理所有可能的异常

### 4. 可维护性

- **清晰命名**：使用有意义的变量和方法名
- **充分注释**：解释复杂的业务逻辑
- **模块化**：将复杂逻辑拆分为小方法

## 🔧 调试技巧

### 启用调试日志

```python
import logging
logging.getLogger('backend.agents.strategies').setLevel(logging.DEBUG)
```

### 使用断点调试

```python
async def execute(self, context: AnalyzedContext) -> DecisionResult:
    import pdb; pdb.set_trace()  # 设置断点
    # 策略逻辑
```

### 监控策略性能

```python
# 获取策略使用统计
from backend.agents.unified_decision_engine import get_unified_decision_engine

engine = get_unified_decision_engine()
stats = engine.get_performance_stats()
strategy_stats = stats.get('strategies', {}).get('your_new_strategy', {})

print(f"使用次数: {strategy_stats.get('usage_count', 0)}")
print(f"平均响应时间: {strategy_stats.get('average_response_time_ms', 0)}ms")
print(f"平均置信度: {strategy_stats.get('average_confidence', 0)}")
```

## 📚 参考资料

- [统一决策引擎用户指南](./统一决策引擎用户指南.md)
- [统一决策引擎架构设计](../统一决策引擎架构设计.md)
- [现有策略实现示例](../../backend/agents/strategies/)
- [性能监控指南](../tools/performance-monitoring.md)
