# 策略配置说明文档 (统一配置中的strategies部分)

## 概述

策略配置现已整合到 `backend/config/unified_config.yaml` 文件的 `strategies` 部分，是系统的核心决策引擎配置，被称为"系统大脑"。它定义了在不同状态、不同意图、不同情感组合下系统应该执行什么动作。

> **注意**: 原独立的 `strategies.yaml` 文件已被废弃，所有策略配置现在统一管理在 `unified_config.yaml` 中。

## 文件结构

### 基本层级结构
```yaml
DEFAULT_STRATEGY: # 默认兜底策略
GLOBAL:          # 全局策略
  意图类型:
    子意图类型:    # 可选，支持子意图分层
      情感类型:
        action: "动作指令"
        priority: 优先级数值
        prompt_instruction: "LLM指导语"
状态名称:         # 状态特定策略
  意图类型:
    情感类型:
      # 同上结构
```

### 支持的层级组合
1. **三层结构**: `状态.意图.情感`
2. **四层结构**: `状态.意图.子意图.情感` (支持子意图)

## 策略匹配优先级

系统按以下顺序查找策略（支持子意图的完整优先级）：

1. **状态特定子意图策略**: `(state, intent, sub_intent, emotion)`
2. **状态特定子意图回退至neutral**: `(state, intent, sub_intent, 'neutral')`
3. **状态特定策略**: `(state, intent, emotion)`
4. **状态特定回退至neutral情感**: `(state, intent, 'neutral')`
5. **全局子意图策略**: `(GLOBAL, intent, sub_intent, emotion)`
6. **全局子意图回退至neutral**: `(GLOBAL, intent, sub_intent, 'neutral')`
7. **全局策略**: `(GLOBAL, intent, emotion)`
8. **全局回退至neutral情感**: `(GLOBAL, intent, 'neutral')`
9. **最终默认策略**: `DEFAULT_STRATEGY`

## 配置字段说明

### 必需字段

#### action (string)
- **作用**: 指定要执行的具体动作指令
- **示例**: `"start_requirement_gathering"`, `"process_answer_and_ask_next"`
- **注意**: 必须与ActionStrategyMapper中定义的action对应

#### priority (integer)
- **作用**: 策略优先级，数值越高优先级越高
- **范围**: 通常0-10
- **用途**: 当多个策略可能匹配时，选择优先级最高的

#### prompt_instruction (string)
- **作用**: 给LLM的具体指导语，描述如何处理这种情况
- **要求**: 应该清晰、具体、可操作
- **长度**: 建议50-200字符

## 支持的意图类型

### 基础意图类型
- `provide_information`: 用户提供信息或回答问题
- `ask_question`: 用户提出问题或描述需求
- `request_clarification`: 用户请求澄清或解释
- `confirm`: 用户确认或同意
- `reject`: 用户拒绝或否定
- `modify`: 用户想要修改之前提供的信息
- `complete`: 用户表示完成或结束
- `greeting`: 用户的问候或打招呼
- `general_request`: 用户提出通用请求
- `unknown`: 无法确定用户意图

### 子意图类型

#### provide_information的子意图
- `answer_question`: 回答之前的问题
- `elaborate_details`: 详细阐述信息
- `supplement_info`: 补充额外信息
- `correct_info`: 纠正之前的信息

#### ask_question的子意图
- `requirement_question`: 关于需求收集的问题
- `technical_question`: 技术相关问题
- `process_question`: 关于流程的问题
- `capability_question`: 关于系统能力的问题

#### general_request的子意图
- `request_introduction`: 请求系统自我介绍
- `request_capabilities`: 询问系统能做什么
- `help_request`: 请求帮助或指导
- `general_chat`: 一般性闲聊

## 支持的情感类型

- `positive`: 积极、满意、高兴
- `negative`: 消极、不满、沮丧
- `neutral`: 中性、平静
- `anxious`: 焦虑、担忧
- `confused`: 困惑、不确定

## 支持的状态类型

- `GLOBAL`: 全局策略，适用于所有状态
- `IDLE`: 空闲状态，对话开始时
- `PROCESSING_INTENT`: 意图处理中状态
- `COLLECTING_INFO`: 信息收集中状态
- `DOCUMENTING`: 文档生成/审查中状态

## 配置示例

### 基础配置示例
```yaml
GLOBAL:
  greeting:
    neutral:
      action: "respond_with_greeting"
      priority: 1
      prompt_instruction: "用户向你问候，请友好回应并简要介绍你是一个AI需求采集助手。"
```

### 子意图配置示例
```yaml
GLOBAL:
  ask_question:
    technical_question:  # 子意图
      neutral:
        action: "handle_technical_question"
        priority: 4
        prompt_instruction: "用户提出了技术相关的问题。请提供专业的技术建议。"
    neutral:  # 传统回退策略
      action: "handle_low_confidence_question"
      priority: 3
      prompt_instruction: "用户提出了一个问题，但系统无法确定具体领域。"
```

### 情感适配配置示例
```yaml
GLOBAL:
  provide_information:
    anxious:
      action: "acknowledge_and_reassure"
      priority: 6
      prompt_instruction: "用户带着焦虑的情绪提供信息。请先确认收到信息，然后安抚用户。"
    confused:
      action: "clarify_and_guide"
      priority: 6
      prompt_instruction: "用户在提供信息时显得困惑。请耐心地确认理解用户的信息。"
```

## 最佳实践

### 1. 策略设计原则
- **明确性**: 每个策略应该有明确的适用场景
- **一致性**: 相似情况下的策略应该保持一致
- **渐进性**: 优先级应该反映策略的重要程度
- **人性化**: prompt_instruction应该考虑用户的情感状态

### 2. 优先级设置建议
- **0-2**: 低优先级，通用处理
- **3-5**: 中等优先级，常规处理
- **6-8**: 高优先级，特殊情况处理
- **9-10**: 最高优先级，紧急或重要情况

### 3. prompt_instruction编写指南
- 使用清晰、具体的语言
- 考虑用户的情感状态
- 提供具体的行动指导
- 保持专业和友好的语调

### 4. 子意图使用建议
- 只在需要更精确控制时使用子意图
- 确保子意图策略比通用策略更具体
- 始终提供回退策略（无子意图的版本）

## 维护指南

### 添加新策略
1. 确定适用的状态、意图、情感组合
2. 选择合适的action（可能需要在ActionStrategyMapper中添加）
3. 设置合理的优先级
4. 编写清晰的prompt_instruction
5. 测试策略是否正确匹配

### 修改现有策略
1. 评估修改的影响范围
2. 确保不破坏现有的策略层级
3. 更新相关的测试用例
4. 验证修改后的行为

### 调试策略匹配
1. 检查日志中的策略查找过程
2. 使用测试脚本验证策略匹配
3. 确认优先级设置是否合理

## 常见问题

### Q: 为什么我的策略没有被匹配？
A: 检查以下几点：
- 状态、意图、情感的拼写是否正确
- 是否存在更高优先级的策略
- 是否正确设置了YAML缩进

### Q: 如何处理新的情感类型？
A: 
1. 在意图识别模板中添加新的情感类型
2. 在strategies.yaml中为主要意图添加对应策略
3. 运行测试验证覆盖率

### Q: 子意图什么时候使用？
A: 当需要对同一个主意图进行更细粒度的控制时使用，例如区分不同类型的问题或信息。

## 配置验证和测试

### 自动化测试
```bash
# 测试所有策略配置
python test_emotion_strategies.py

# 测试子意图功能
python test_sub_intent_upgrade.py
```

### 手动验证清单
- [ ] 所有定义的意图类型都有对应策略
- [ ] 所有定义的情感类型都有对应策略
- [ ] 子意图策略比通用策略更具体
- [ ] 优先级设置合理，无冲突
- [ ] prompt_instruction语法正确，无错别字
- [ ] YAML语法正确，缩进一致

## 性能考虑

### 策略查找性能
- 策略查找是O(1)操作，性能影响最小
- 子意图增加了额外的查找层级，但影响可忽略
- 建议将最常用的策略设置为较高优先级

### 内存使用
- 策略配置在启动时加载到内存
- 配置文件大小对运行时性能无影响
- 建议定期清理不再使用的策略

## 扩展指南

### 添加新的状态类型
1. 在系统中定义新的状态枚举
2. 在strategies.yaml中添加对应的状态配置
3. 更新状态转换逻辑
4. 添加相应的测试用例

### 添加新的意图类型
1. 在意图识别模板中定义新意图
2. 在strategies.yaml中添加对应策略
3. 更新意图识别训练数据
4. 验证意图识别准确性

### 添加新的action类型
1. 在ActionStrategyMapper中定义新action
2. 实现对应的处理逻辑
3. 在strategies.yaml中使用新action
4. 测试action执行效果

## 故障排除

### 常见错误及解决方案

#### 1. YAML语法错误
**错误**: `yaml.scanner.ScannerError`
**解决**: 检查缩进、引号、特殊字符

#### 2. 策略未找到
**错误**: 使用默认策略而非预期策略
**解决**:
- 检查状态、意图、情感的拼写
- 验证YAML层级结构
- 确认优先级设置

#### 3. Action不存在
**错误**: `KeyError` 或 action未执行
**解决**:
- 检查ActionStrategyMapper中是否定义了对应action
- 验证action名称拼写

#### 4. 优先级冲突
**错误**: 预期策略未被选中
**解决**:
- 检查是否有更高优先级的策略
- 调整优先级数值
- 使用测试脚本验证

### 调试技巧

#### 1. 启用详细日志
```python
# 在decision_engine.py中查看策略查找日志
self.logger.info(f"策略查找: ({s}, {i}, {e}) -> {'找到' if strategy else '未找到'}")
```

#### 2. 使用测试脚本
```python
# 测试特定策略组合
engine = DecisionEngine()
strategy = engine.get_strategy("ask_question", "anxious", {"current_state": "IDLE"})
print(f"Action: {strategy.get('action')}")
```

#### 3. 验证配置加载
```python
# 检查配置是否正确加载
engine = DecisionEngine()
print(engine.strategies_config.keys())
```

## 版本管理

### 配置版本控制
- 所有策略配置变更都应该通过版本控制管理
- 重要变更前应该备份当前配置
- 使用有意义的提交信息描述配置变更

### 配置迁移
- 添加新字段时保持向后兼容
- 删除字段前确保没有代码依赖
- 提供配置迁移脚本（如需要）

### 环境管理
- 开发环境可以使用实验性配置
- 生产环境配置变更需要充分测试
- 考虑使用配置文件的环境变量替换

## 相关文件

- `backend/agents/decision_engine.py`: 决策引擎实现
- `backend/agents/integrated_reply_system.py`: 策略执行系统
- `backend/prompts/intent_recognition.md`: 意图识别模板
- `test_emotion_strategies.py`: 策略测试脚本
- `test_sub_intent_upgrade.py`: 子意图功能测试
- `docs/development/子意图升级总结.md`: 升级历史记录
