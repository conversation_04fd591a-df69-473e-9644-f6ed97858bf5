# 策略配置快速参考卡片

## 基本语法

```yaml
状态名称:
  意图类型:
    [子意图类型]:  # 可选
      情感类型:
        action: "动作指令"
        priority: 数值
        prompt_instruction: "指导语"
```

## 查找优先级 (从高到低)

1. `状态.意图.子意图.情感`
2. `状态.意图.子意图.neutral`
3. `状态.意图.情感`
4. `状态.意图.neutral`
5. `GLOBAL.意图.子意图.情感`
6. `GLOBAL.意图.子意图.neutral`
7. `GLOBAL.意图.情感`
8. `GLOBAL.意图.neutral`
9. `DEFAULT_STRATEGY`

## 支持的枚举值

### 状态类型
- `GLOBAL` - 全局策略
- `IDLE` - 空闲状态
- `PROCESSING_INTENT` - 意图处理中
- `COLLECTING_INFO` - 信息收集中
- `DOCUMENTING` - 文档生成中

### 意图类型
- `provide_information` - 提供信息
- `ask_question` - 提出问题
- `request_clarification` - 请求澄清
- `confirm` - 确认
- `reject` - 拒绝
- `modify` - 修改
- `complete` - 完成
- `greeting` - 问候
- `general_request` - 通用请求
- `unknown` - 未知意图

### 子意图类型

#### provide_information
- `answer_question` - 回答问题
- `elaborate_details` - 详细阐述
- `supplement_info` - 补充信息
- `correct_info` - 纠正信息

#### ask_question
- `requirement_question` - 需求问题
- `technical_question` - 技术问题
- `process_question` - 流程问题
- `capability_question` - 能力问题

#### general_request
- `request_introduction` - 请求介绍
- `request_capabilities` - 询问能力
- `help_request` - 请求帮助
- `general_chat` - 一般闲聊

### 情感类型
- `positive` - 积极
- `negative` - 消极
- `neutral` - 中性
- `anxious` - 焦虑
- `confused` - 困惑

## 常用Action类型

### 基础Actions
- `respond_with_greeting` - 回复问候
- `start_requirement_gathering` - 开始需求收集
- `process_answer_and_ask_next` - 处理回答并问下一个
- `request_clarification` - 请求澄清
- `acknowledge_and_redirect` - 确认并重定向

### 情感适配Actions
- `respond_with_reassuring_greeting` - 安抚性问候
- `handle_anxious_question` - 处理焦虑问题
- `handle_confused_question` - 处理困惑问题
- `acknowledge_and_reassure` - 确认并安抚
- `clarify_and_guide` - 澄清并引导

### 特殊情况Actions
- `handle_unknown_situation` - 处理未知情况
- `show_empathy_and_clarify` - 共情并澄清
- `provide_suggestions` - 提供建议
- `finalize_and_reset` - 完成并重置

## 优先级建议

| 优先级 | 用途 | 示例场景 |
|--------|------|----------|
| 0-2 | 低优先级 | 通用处理、默认行为 |
| 3-5 | 中等优先级 | 常规业务逻辑 |
| 6-8 | 高优先级 | 特殊情况、情感处理 |
| 9-10 | 最高优先级 | 紧急情况、用户求助 |

## 配置模板

### 基础策略模板
```yaml
GLOBAL:
  意图名称:
    neutral:
      action: "action_name"
      priority: 5
      prompt_instruction: "处理指导语"
```

### 情感适配模板
```yaml
GLOBAL:
  意图名称:
    neutral:
      action: "normal_action"
      priority: 5
      prompt_instruction: "正常处理"
    anxious:
      action: "reassuring_action"
      priority: 6
      prompt_instruction: "安抚用户，然后..."
    confused:
      action: "clarifying_action"
      priority: 6
      prompt_instruction: "澄清疑惑，然后..."
```

### 子意图模板
```yaml
GLOBAL:
  主意图:
    子意图名称:
      neutral:
        action: "specific_action"
        priority: 6
        prompt_instruction: "针对性处理"
    neutral:  # 回退策略
      action: "general_action"
      priority: 3
      prompt_instruction: "通用处理"
```

## 快速检查清单

### 添加新策略时
- [ ] 确定状态、意图、情感组合
- [ ] 选择合适的action
- [ ] 设置合理的优先级
- [ ] 编写清晰的prompt_instruction
- [ ] 检查YAML语法
- [ ] 运行测试验证

### 修改现有策略时
- [ ] 评估影响范围
- [ ] 备份原配置
- [ ] 保持层级一致性
- [ ] 更新相关测试
- [ ] 验证修改效果

### 调试策略问题时
- [ ] 检查日志中的策略查找过程
- [ ] 验证状态、意图、情感的值
- [ ] 确认优先级设置
- [ ] 使用测试脚本验证
- [ ] 检查YAML语法错误

## 测试命令

```bash
# 测试所有策略配置
python test_emotion_strategies.py

# 测试子意图功能
python test_sub_intent_upgrade.py

# 测试特定策略
python -c "
from backend.agents.decision_engine import DecisionEngine
engine = DecisionEngine()
strategy = engine.get_strategy('ask_question', 'anxious', {'current_state': 'IDLE'})
print(f'Action: {strategy.get(\"action\")}')
"
```

## 常见错误

| 错误类型 | 症状 | 解决方法 |
|----------|------|----------|
| YAML语法错误 | 启动失败 | 检查缩进和引号 |
| 策略未匹配 | 使用默认策略 | 检查拼写和层级 |
| Action不存在 | 执行失败 | 检查ActionStrategyMapper |
| 优先级冲突 | 错误的策略被选中 | 调整优先级数值 |

## 相关文件路径

- 配置文件: `backend/config/strategies.yaml`
- 决策引擎: `backend/agents/decision_engine.py`
- 测试脚本: `test_emotion_strategies.py`
- 详细文档: `docs/development/策略配置说明文档.md`
