# 统一决策引擎配置指南

## 📖 概述

本指南介绍如何配置和调优统一决策引擎的各个组件，包括缓存设置、监控参数、策略优先级等。

## ⚙️ 核心配置

### 1. 引擎初始化配置

```python
from backend.agents.unified_decision_engine import UnifiedDecisionEngine
from backend.agents.strategy_registry import StrategyRegistry
from backend.agents.context_analyzer import ContextAnalyzer

# 自定义初始化
engine = UnifiedDecisionEngine(
    strategy_registry=None,      # 使用默认注册中心
    context_analyzer=None,       # 使用默认分析器
    llm_service=None,           # 使用默认LLM服务
    enable_cache=True,          # 启用缓存
    enable_monitoring=True      # 启用监控
)
```

### 2. 缓存配置

```python
from backend.agents.decision_cache import DecisionCache

# 创建自定义缓存
cache = DecisionCache(
    max_size=2000,              # 最大缓存条目数
    ttl_seconds=7200            # 缓存生存时间（2小时）
)

# 缓存配置建议
CACHE_CONFIGS = {
    "development": {
        "max_size": 500,
        "ttl_seconds": 1800     # 30分钟
    },
    "testing": {
        "max_size": 100,
        "ttl_seconds": 300      # 5分钟
    },
    "production": {
        "max_size": 5000,
        "ttl_seconds": 3600     # 1小时
    }
}
```

### 3. 监控配置

```python
from backend.agents.decision_monitor import DecisionMonitor

# 创建自定义监控器
monitor = DecisionMonitor(
    max_records=2000            # 最大记录数量
)

# 监控配置建议
MONITOR_CONFIGS = {
    "development": {
        "max_records": 500
    },
    "testing": {
        "max_records": 100
    },
    "production": {
        "max_records": 10000
    }
}
```

## 🎯 策略配置

### 1. 策略优先级配置

```python
# 策略优先级映射
STRATEGY_PRIORITIES = {
    "greeting_strategy": 9,          # 最高优先级
    "requirement_strategy": 8,       # 高优先级
    "knowledge_base_strategy": 7,    # 中高优先级
    "capabilities_strategy": 6,      # 中等优先级
    "emotional_support_strategy": 5, # 中低优先级
    "custom_strategy": 4,           # 自定义策略
    "fallback_strategy": 1          # 最低优先级（兜底）
}
```

### 2. 策略参数配置

```python
# 问候策略配置
GREETING_CONFIG = {
    "response_templates": [
        "您好！我是AI助手，很高兴为您服务。",
        "Hi！欢迎使用我们的服务。",
        "您好！有什么可以帮助您的吗？"
    ],
    "time_based_greetings": {
        "morning": ["早上好！", "早安！"],
        "afternoon": ["下午好！", "午安！"],
        "evening": ["晚上好！", "晚安！"]
    }
}

# 需求收集策略配置
REQUIREMENT_CONFIG = {
    "requirement_types": {
        "development": ["网站", "APP", "系统", "软件"],
        "design": ["logo", "海报", "UI", "设计"],
        "content": ["文案", "文章", "内容", "写作"],
        "consulting": ["咨询", "建议", "方案", "分析"]
    },
    "entity_patterns": {
        "budget": r"(\d+)万?元?",
        "timeline": r"(\d+)(天|周|月|年)",
        "platform": r"(网站|APP|小程序|系统)"
    }
}
```

### 3. 上下文分析配置

```python
# 意图识别配置
INTENT_CONFIG = {
    "confidence_threshold": 0.7,    # 意图识别置信度阈值
    "max_context_length": 1000,    # 最大上下文长度
    "enable_entity_extraction": True,
    "enable_emotion_analysis": True
}

# 情感分析配置
EMOTION_CONFIG = {
    "emotion_keywords": {
        "positive": ["开心", "高兴", "满意", "棒", "好"],
        "negative": ["生气", "愤怒", "不满", "差", "烂"],
        "neutral": ["一般", "还行", "可以", "普通"]
    },
    "emotion_threshold": 0.6
}
```

## 🔧 环境配置

### 1. 开发环境配置

```python
# development.py
UNIFIED_DECISION_ENGINE_CONFIG = {
    "cache": {
        "enabled": True,
        "max_size": 500,
        "ttl_seconds": 1800
    },
    "monitoring": {
        "enabled": True,
        "max_records": 500,
        "log_level": "DEBUG"
    },
    "strategies": {
        "auto_load": True,
        "custom_strategies": []
    }
}
```

### 2. 生产环境配置

```python
# production.py
UNIFIED_DECISION_ENGINE_CONFIG = {
    "cache": {
        "enabled": True,
        "max_size": 5000,
        "ttl_seconds": 3600
    },
    "monitoring": {
        "enabled": True,
        "max_records": 10000,
        "log_level": "INFO"
    },
    "strategies": {
        "auto_load": True,
        "custom_strategies": [
            "custom_business_strategy",
            "custom_support_strategy"
        ]
    },
    "performance": {
        "max_response_time_ms": 200,
        "cache_hit_rate_target": 0.6,
        "error_rate_threshold": 0.05
    }
}
```

### 3. 测试环境配置

```python
# testing.py
UNIFIED_DECISION_ENGINE_CONFIG = {
    "cache": {
        "enabled": False,           # 测试时禁用缓存
        "max_size": 100,
        "ttl_seconds": 300
    },
    "monitoring": {
        "enabled": True,
        "max_records": 100,
        "log_level": "DEBUG"
    },
    "strategies": {
        "auto_load": False,         # 手动加载策略
        "test_strategies": [
            "mock_greeting_strategy",
            "mock_fallback_strategy"
        ]
    }
}
```

## 📊 性能调优配置

### 1. 缓存调优

```python
# 根据业务场景调整缓存参数
def get_cache_config(scenario):
    configs = {
        "high_frequency": {         # 高频访问场景
            "max_size": 10000,
            "ttl_seconds": 7200
        },
        "low_frequency": {          # 低频访问场景
            "max_size": 1000,
            "ttl_seconds": 1800
        },
        "memory_constrained": {     # 内存受限场景
            "max_size": 500,
            "ttl_seconds": 900
        }
    }
    return configs.get(scenario, configs["low_frequency"])
```

### 2. 监控调优

```python
# 监控数据保留策略
MONITORING_RETENTION = {
    "real_time": {
        "duration_minutes": 60,     # 实时数据保留1小时
        "sample_rate": 1.0          # 100%采样
    },
    "hourly": {
        "duration_hours": 24,       # 小时数据保留24小时
        "sample_rate": 0.1          # 10%采样
    },
    "daily": {
        "duration_days": 30,        # 日数据保留30天
        "sample_rate": 0.01         # 1%采样
    }
}
```

### 3. 策略调优

```python
# 策略性能配置
STRATEGY_PERFORMANCE_CONFIG = {
    "timeout_seconds": 5.0,         # 策略执行超时时间
    "max_retries": 2,               # 最大重试次数
    "circuit_breaker": {
        "failure_threshold": 5,      # 失败阈值
        "recovery_timeout": 60       # 恢复超时时间
    }
}
```

## 🔍 配置验证

### 1. 配置检查脚本

```python
def validate_config(config):
    """验证配置的有效性"""
    errors = []
    
    # 检查缓存配置
    cache_config = config.get('cache', {})
    if cache_config.get('max_size', 0) <= 0:
        errors.append("缓存大小必须大于0")
    
    if cache_config.get('ttl_seconds', 0) <= 0:
        errors.append("缓存TTL必须大于0")
    
    # 检查监控配置
    monitor_config = config.get('monitoring', {})
    if monitor_config.get('max_records', 0) <= 0:
        errors.append("监控记录数必须大于0")
    
    # 检查策略配置
    strategies = config.get('strategies', {}).get('custom_strategies', [])
    for strategy in strategies:
        if not isinstance(strategy, str):
            errors.append(f"策略名称必须是字符串: {strategy}")
    
    return errors

# 使用示例
config = UNIFIED_DECISION_ENGINE_CONFIG
errors = validate_config(config)
if errors:
    print("配置错误:")
    for error in errors:
        print(f"  - {error}")
else:
    print("配置验证通过")
```

### 2. 配置测试

```python
async def test_config(config):
    """测试配置是否正常工作"""
    from backend.agents.unified_decision_engine import UnifiedDecisionEngine
    from backend.agents.decision_types import create_decision_context
    
    # 使用配置创建引擎
    engine = UnifiedDecisionEngine(
        enable_cache=config['cache']['enabled'],
        enable_monitoring=config['monitoring']['enabled']
    )
    
    # 测试决策
    context = create_decision_context(
        session_id='test',
        user_id='test',
        message='测试消息'
    )
    
    result = await engine.make_decision(context)
    
    # 验证结果
    assert result is not None
    assert result.confidence > 0
    assert result.strategy_name is not None
    
    print("配置测试通过")
```

## 📚 配置最佳实践

### 1. 分层配置
- **基础配置**: 所有环境共享的基本设置
- **环境配置**: 特定环境的覆盖设置
- **运行时配置**: 可动态调整的参数

### 2. 配置管理
- 使用配置文件而非硬编码
- 支持环境变量覆盖
- 提供配置验证机制
- 记录配置变更历史

### 3. 性能监控
- 定期检查配置效果
- 根据监控数据调整参数
- 建立配置变更的回滚机制

### 4. 安全考虑
- 敏感配置信息加密存储
- 限制配置文件访问权限
- 审计配置变更操作

## 🔗 相关文档

- [统一决策引擎用户指南](../guides/统一决策引擎用户指南.md)
- [策略开发指南](../guides/策略开发指南.md)
- [性能监控指南](../tools/performance-monitoring.md)
- [统一决策引擎API文档](../统一决策引擎API文档.md)
