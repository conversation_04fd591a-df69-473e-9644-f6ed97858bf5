# 策略配置示例集合

## 基础配置示例

### 1. 简单意图处理
```yaml
GLOBAL:
  greeting:
    neutral:
      action: "respond_with_greeting"
      priority: 1
      prompt_instruction: "用户向你问候，请友好回应并简要介绍你是一个AI需求采集助手。"
```

### 2. 多情感支持
```yaml
GLOBAL:
  ask_question:
    neutral:
      action: "handle_question"
      priority: 3
      prompt_instruction: "用户提出了问题，请分析并提供合适的回应。"
    anxious:
      action: "handle_anxious_question"
      priority: 4
      prompt_instruction: "用户带着焦虑情绪提出问题，请先安抚用户情绪。"
    confused:
      action: "handle_confused_question"
      priority: 4
      prompt_instruction: "用户对问题感到困惑，请用简单语言帮助理清思路。"
```

## 子意图配置示例

### 3. 分层子意图处理
```yaml
GLOBAL:
  ask_question:
    # 技术问题子意图
    technical_question:
      neutral:
        action: "handle_technical_question"
        priority: 5
        prompt_instruction: "用户提出技术问题，请提供专业的技术建议。"
      confused:
        action: "simplify_technical_explanation"
        priority: 6
        prompt_instruction: "用户对技术问题感到困惑，请用通俗语言解释。"
    
    # 需求问题子意图
    requirement_question:
      neutral:
        action: "handle_requirement_question"
        priority: 5
        prompt_instruction: "用户询问需求相关问题，请专业地指导需求收集。"
    
    # 通用回退策略
    neutral:
      action: "handle_general_question"
      priority: 3
      prompt_instruction: "用户提出了问题，但类型不明确，请通用处理。"
```

## 状态特定配置示例

### 4. IDLE状态配置
```yaml
IDLE:
  ask_question:
    requirement_question:
      positive:
        action: "start_enthusiastic_gathering"
        priority: 7
        prompt_instruction: "用户积极地提出需求问题，请热情回应并开始收集。"
      neutral:
        action: "start_requirement_gathering"
        priority: 6
        prompt_instruction: "用户提出需求问题，开始系统性收集信息。"
      anxious:
        action: "start_gentle_gathering"
        priority: 7
        prompt_instruction: "用户带着焦虑提出需求，请温和地开始收集过程。"
    
    greeting:
      neutral:
        action: "welcome_and_introduce"
        priority: 5
        prompt_instruction: "欢迎用户并介绍需求收集流程。"
```

### 5. COLLECTING_INFO状态配置
```yaml
COLLECTING_INFO:
  provide_information:
    answer_question:
      neutral:
        action: "process_answer_and_continue"
        priority: 6
        prompt_instruction: "用户回答了问题，处理答案并继续收集。"
      confused:
        action: "clarify_answer_and_continue"
        priority: 7
        prompt_instruction: "用户的回答显得困惑，请澄清后继续。"
    
    supplement_info:
      neutral:
        action: "acknowledge_supplement_and_continue"
        priority: 5
        prompt_instruction: "用户补充了信息，确认并继续收集流程。"
  
  request_clarification:
    neutral:
      action: "provide_clarification_and_continue"
      priority: 8
      prompt_instruction: "用户请求澄清，提供解释后继续收集。"
    anxious:
      action: "reassure_and_clarify"
      priority: 9
      prompt_instruction: "用户焦虑地请求澄清，先安抚再解释。"
```

## 复杂场景配置示例

### 6. 错误处理和回退
```yaml
GLOBAL:
  unknown:
    neutral:
      action: "request_clarification"
      priority: 5
      prompt_instruction: "无法理解用户意图，礼貌地请求重新描述。"
    anxious:
      action: "gentle_clarification_request"
      priority: 6
      prompt_instruction: "用户焦虑且意图不明，温和地请求澄清。"
    confused:
      action: "supportive_clarification_request"
      priority: 6
      prompt_instruction: "用户困惑且意图不明，提供支持性的澄清请求。"

  reject:
    negative:
      action: "handle_negative_rejection"
      priority: 8
      prompt_instruction: "用户带着负面情绪拒绝，不要道歉，重新组织问题。"
    neutral:
      action: "handle_neutral_rejection"
      priority: 7
      prompt_instruction: "用户中性地拒绝，理解并提供替代选项。"
```

### 7. 完成和重置场景
```yaml
DOCUMENTING:
  confirm:
    positive:
      action: "celebrate_completion"
      priority: 6
      prompt_instruction: "用户积极确认文档，庆祝项目完成。"
    neutral:
      action: "acknowledge_completion"
      priority: 5
      prompt_instruction: "用户确认文档，正式结束流程。"
  
  modify:
    confused:
      action: "clarify_modification_request"
      priority: 8
      prompt_instruction: "用户困惑地要求修改，先澄清具体修改点。"
    neutral:
      action: "execute_document_modification"
      priority: 7
      prompt_instruction: "用户要求修改文档，确认修改点并执行。"
  
  restart:
    neutral:
      action: "confirm_and_restart"
      priority: 8
      prompt_instruction: "用户要求重新开始，确认意图后重置流程。"
```

## 优先级设计示例

### 8. 优先级层次设计
```yaml
COLLECTING_INFO:
  request_clarification:
    # 最高优先级：用户求助
    anxious:
      action: "provide_reassuring_guidance"
      priority: 9
      prompt_instruction: "用户焦虑地求助，最高优先级处理。"
    
    confused:
      action: "provide_step_by_step_guidance"
      priority: 9
      prompt_instruction: "用户困惑地求助，提供详细指导。"
    
    # 高优先级：正常求助
    neutral:
      action: "provide_suggestions"
      priority: 8
      prompt_instruction: "用户正常求助，提供建议。"
  
  provide_information:
    # 中等优先级：正常信息处理
    neutral:
      action: "process_answer_and_ask_next"
      priority: 5
      prompt_instruction: "处理用户回答并继续。"
  
  general_request:
    # 低优先级：通用请求
    neutral:
      action: "acknowledge_and_redirect"
      priority: 3
      prompt_instruction: "简要回应通用请求，引导回主流程。"
```

## 特殊配置示例

### 9. 条件性配置
```yaml
# 使用特殊配置标记
DOCUMENTING:
  _state_config:
    use_simplified_logic: true
    fallback_action: "execute_document_modification"
    fallback_intent: "modify"
    priority_order: ["confirm", "restart", "modify"]
  
  confirm:
    positive:
      action: "finalize_and_reset"
      priority: 6
      prompt_instruction: "用户积极确认，完成并重置。"
```

### 10. 动态优先级示例
```yaml
IDLE:
  ask_question:
    # 子意图具有更高优先级
    requirement_question:
      neutral:
        action: "start_focused_requirement_gathering"
        priority: 7  # 比通用策略高
        prompt_instruction: "明确的需求问题，开始专注收集。"
    
    technical_question:
      neutral:
        action: "handle_technical_in_idle"
        priority: 6  # 中等优先级
        prompt_instruction: "在空闲状态处理技术问题。"
    
    # 通用回退策略，优先级较低
    neutral:
      action: "start_requirement_gathering"
      priority: 5
      prompt_instruction: "通用问题处理，开始需求收集。"
    
    # 特殊情况，优先级最低
    low_confidence:
      action: "handle_low_confidence_question"
      priority: 4
      prompt_instruction: "低置信度问题，谨慎处理。"
```

## 配置验证示例

### 11. 完整性检查配置
```yaml
# 确保所有情感类型都有覆盖
GLOBAL:
  provide_information:
    positive:
      action: "acknowledge_positive_info"
      priority: 4
      prompt_instruction: "用户积极提供信息，表达赞同。"
    negative:
      action: "show_empathy_and_clarify"
      priority: 7
      prompt_instruction: "用户消极提供信息，表示共情。"
    neutral:
      action: "acknowledge_and_redirect"
      priority: 2
      prompt_instruction: "用户中性提供信息，确认并引导。"
    anxious:
      action: "acknowledge_and_reassure"
      priority: 6
      prompt_instruction: "用户焦虑提供信息，确认并安抚。"
    confused:
      action: "clarify_and_guide"
      priority: 6
      prompt_instruction: "用户困惑提供信息，澄清并引导。"
```

## 最佳实践总结

1. **层次清晰**: 状态 > 意图 > 子意图 > 情感
2. **优先级合理**: 特殊情况 > 一般情况 > 默认情况
3. **回退完整**: 每个子意图都有对应的通用回退
4. **情感覆盖**: 重要意图覆盖所有情感类型
5. **指令具体**: prompt_instruction要具体可操作
6. **命名一致**: action命名要与功能一致
