# 数据库配置管理指南

## 📋 概述

本指南说明了项目中数据库查询配置的最佳实践，解决了配置文件与 `database_manager.py` 功能重复的问题。

## 🏗️ 架构设计

### 三层架构

```
┌─────────────────────────────────────┐
│        业务逻辑层 (Manager类)        │
│  message_manager.py                 │
│  focus_point_manager.py             │
│  summary_manager.py                 │
└─────────────────┬───────────────────┘
                  │
┌─────────────────▼───────────────────┐
│      数据库操作层 (DatabaseManager)  │
│  database_manager.py               │
│  - 连接管理                         │
│  - 查询执行                         │
│  - 性能监控                         │
└─────────────────┬───────────────────┘
                  │
┌─────────────────▼───────────────────┐
│      配置层 (SQL查询模板)           │
│  database_queries.yaml             │
│  - SQL查询模板                      │
│  - 参数化查询                       │
└─────────────────────────────────────┘
```

## 🎯 解决的问题

### 问题1: SQL参数不匹配
**错误**: `Incorrect number of bindings supplied. The current statement uses 4, and there are 3 supplied.`

**原因**: 
- SQL查询: `UPDATE conversations SET domain_id = ?, category_id = ?, updated_at = ? WHERE conversation_id = ?`
- 传入参数: `(domain_id, category_id, session_id)` - 缺少 `updated_at`

**解决方案**: 
```python
# 修复前
(self.current_domain, self.current_category, session_id)

# 修复后  
(self.current_domain, self.current_category, datetime.now().isoformat(), session_id)
```

### 问题2: 配置文件与实际代码不同步
**问题**: `database_queries.yaml` 中的字段与实际数据库表结构不匹配

**解决方案**: 统一配置文件和代码中的字段定义

## 📝 最佳实践

### 1. 统一使用配置文件
所有SQL查询都应该从 `database_queries.yaml` 获取：

```python
# ✅ 正确做法
from backend.config.config_manager import config_manager

await self.db_manager.execute_update(
    config_manager.get_database_query("conversations.create_new"),
    (conversation_id, "default_user", "active", now, now, now)
)

# ❌ 错误做法 - 硬编码SQL
await self.db_manager.execute_update(
    """
    INSERT INTO conversations
    (conversation_id, user_id, status, created_at, updated_at, last_activity_at)
    VALUES (?, ?, ?, ?, ?, ?)
    """,
    (conversation_id, "default_user", "active", now, now, now)
)
```

### 2. 配置文件结构
`database_queries.yaml` 按功能模块组织：

```yaml
# 会话相关查询
conversations:
  create_new: |
    INSERT INTO conversations
    (conversation_id, user_id, status, created_at, updated_at, last_activity_at)
    VALUES (?, ?, ?, ?, ?, ?)
    
  update_domain_category: |
    UPDATE conversations 
    SET domain_id = ?, category_id = ?, updated_at = ?
    WHERE conversation_id = ?

# 消息相关查询  
messages:
  save_message: |
    INSERT INTO messages
    (conversation_id, sender_type, content, focus_id, message_type, created_at)
    VALUES (?, ?, ?, ?, ?, ?)
```

### 3. 参数传递规范
确保参数顺序与SQL查询中的占位符一致：

```python
# SQL: UPDATE conversations SET domain_id = ?, category_id = ?, updated_at = ? WHERE conversation_id = ?
# 参数: (domain_id, category_id, updated_at, conversation_id)
await self.db_manager.execute_update(
    config_manager.get_database_query("conversations.update_domain_category"),
    (self.current_domain, self.current_category, datetime.now().isoformat(), session_id)
)
```

## 🔧 迁移指南

### 步骤1: 识别硬编码SQL
使用工具查找硬编码的SQL语句：

```bash
grep -r "INSERT INTO\|UPDATE\|DELETE FROM\|SELECT" backend/data/db/ --include="*.py"
```

### 步骤2: 更新配置文件
将硬编码的SQL添加到 `database_queries.yaml` 中相应的模块下。

### 步骤3: 修改代码
将硬编码SQL替换为配置文件调用：

```python
# 替换前
query = "SELECT * FROM conversations WHERE conversation_id = ?"

# 替换后
query = config_manager.get_database_query("conversations.get_info")
```

### 步骤4: 验证参数匹配
确保传入的参数数量和顺序与SQL查询中的占位符匹配。

## 🚀 优势

1. **统一管理**: 所有SQL查询集中在一个配置文件中
2. **易于维护**: 修改SQL不需要改动代码
3. **避免重复**: 消除硬编码SQL的重复
4. **类型安全**: 通过配置管理器统一访问
5. **版本控制**: SQL变更可以通过配置文件跟踪

## ⚠️ 注意事项

1. **参数顺序**: 确保参数传递顺序与SQL中占位符顺序一致
2. **字段同步**: 配置文件中的字段必须与实际数据库表结构匹配
3. **错误处理**: 配置文件缺失或格式错误时的降级处理
4. **性能考虑**: 避免在循环中重复调用 `config_manager.get_database_query()`

## 📊 当前状态

- ✅ 修复了 `conversations.update_domain_category` 参数不匹配问题
- ✅ 更新了 `messages.save_message` 字段定义
- ✅ 统一了 `message_manager.py` 中的查询调用
- 🔄 其他Manager类的迁移正在进行中

## 🎯 下一步

1. 完成所有Manager类的SQL查询迁移
2. 添加配置文件验证工具
3. 建立SQL查询的单元测试
4. 完善错误处理和降级机制
