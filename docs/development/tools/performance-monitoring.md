# 性能监控使用指南

## 概述

性能监控系统提供全面的性能数据收集、分析和报告功能。系统会自动监控API响应时间、LLM调用性能、数据库查询性能、**统一决策引擎性能**和系统资源使用情况。

## 🆕 统一决策引擎监控

### 新增监控功能
- **决策响应时间**: 监控每次决策的执行时间
- **策略使用统计**: 跟踪各策略的使用频率和性能
- **缓存命中率**: 监控决策缓存的效果
- **错误率统计**: 跟踪决策失败和错误情况
- **置信度分析**: 分析决策置信度分布

## 功能特性

### 🚀 自动监控
- **API响应时间**: 自动跟踪所有API端点的响应时间
- **LLM调用性能**: 监控大语言模型调用的延迟和成功率
- **数据库查询**: 跟踪数据库操作的执行时间
- **决策引擎性能**: 监控统一决策引擎的响应时间和策略使用情况
- **系统资源**: 实时监控CPU和内存使用情况

### 📊 数据收集
- **实时指标**: 响应时间、吞吐量、错误率
- **历史数据**: 保存性能历史记录用于趋势分析
- **资源使用**: CPU、内存使用率的时间序列数据
- **自动保存**: 定期保存性能数据到文件

### 🔧 管理接口
- **状态查询**: 查看监控系统运行状态
- **数据导出**: 导出性能报告
- **指标重置**: 清空当前统计数据
- **配置管理**: 动态调整监控参数

## 快速开始

### 1. 启动服务
性能监控会在API服务启动时自动初始化：

```bash
cd backend
python -m api.main
```

### 2. 查看监控状态
访问以下端点查看监控状态：

```bash
# 查看监控状态
curl http://localhost:8000/performance/status

# 查看性能统计
curl http://localhost:8000/performance/stats
```

### 3. 监控统一决策引擎
查看决策引擎性能数据：

```python
from backend.agents.unified_decision_engine import get_unified_decision_engine

# 获取决策引擎实例
engine = get_unified_decision_engine()

# 获取完整性能统计
stats = engine.get_performance_stats()

# 查看整体性能
overall = stats.get('overall', {})
print(f"总请求数: {overall.get('total_requests', 0)}")
print(f"成功率: {overall.get('success_rate_percent', 0)}%")
print(f"平均响应时间: {overall.get('average_response_time_ms', 0)}ms")

# 查看缓存性能
cache = stats.get('cache', {})
print(f"缓存命中率: {cache.get('hit_rate_percent', 0)}%")
print(f"缓存大小: {cache.get('cache_size', 0)}")

# 查看策略使用情况
strategies = stats.get('strategies', {})
for name, metrics in strategies.items():
    print(f"{name}: 使用{metrics['usage_count']}次, 平均置信度{metrics['average_confidence']:.3f}")
```

### 4. 测试监控功能
运行测试脚本验证功能：

```bash
python backend/scripts/test_performance_monitoring.py
```

## API端点

### 性能统计
```http
GET /performance/stats
```
返回当前性能统计数据，包括：
- API调用指标
- LLM调用指标  
- 数据库查询指标
- 系统资源使用情况

### 监控状态
```http
GET /performance/status
```
返回监控系统状态信息：
- 监控是否激活
- 配置参数
- 指标数量统计

### 重置统计
```http
POST /performance/reset
```
清空当前所有性能统计数据。

### 保存报告
```http
POST /performance/save
```
将当前性能数据保存到文件。

## 配置选项

### 基本配置
性能监控配置现在统一在 `backend/config/business_rules.yaml` 中管理：

```yaml
# 性能优化规则
performance:
  # 是否启用缓存
  enable_caching: true
  # 缓存TTL（秒）
  cache_ttl: 3600
  # 最大缓存大小
  max_cache_size: 1000

  # 数据库查询优化
  database:
    # 是否启用查询缓存
    enable_query_cache: true
    # 批量操作的最大大小
    max_batch_size: 100
    # 连接池大小
    connection_pool_size: 10
```

### 代码中使用

#### API性能跟踪
```python
from backend.utils.performance_monitor import performance_monitor

@performance_monitor.track_api_call("my_endpoint")
async def my_api_function():
    # API逻辑
    return result
```

#### LLM调用跟踪
```python
with performance_monitor.track_llm_call("deepseek", "deepseek-chat", "completion"):
    response = await llm_client.call(prompt)
```

#### 数据库查询跟踪
```python
with performance_monitor.track_db_query("select", "users"):
    results = db.execute("SELECT * FROM users")
```

## 数据存储

### 存储位置
性能数据默认保存在 `logs/performance/` 目录下：
- 实时数据：内存中保存
- 历史数据：定期保存到JSON文件
- 自动保存：每5分钟保存一次

### 数据格式
性能报告采用JSON格式：

```json
{
  "api_metrics": {
    "chat": {
      "count": 100,
      "avg_time": 0.25,
      "min_time": 0.1,
      "max_time": 1.2
    }
  },
  "llm_metrics": {
    "deepseek_deepseek-chat_completion": {
      "count": 50,
      "avg_time": 1.5
    }
  },
  "resource_metrics": {
    "avg_cpu": 25.5,
    "avg_memory": 45.2
  }
}
```

## 监控指标说明

### API指标
- **count**: 调用次数
- **avg_time**: 平均响应时间（秒）
- **min_time**: 最小响应时间
- **max_time**: 最大响应时间

### LLM指标
- **count**: 调用次数
- **avg_time**: 平均响应时间
- **provider**: 服务提供商
- **model**: 模型名称

### 系统资源
- **avg_cpu**: 平均CPU使用率（%）
- **avg_memory**: 平均内存使用率（%）
- **timestamps**: 采样时间点

## 故障排除

### 常见问题

1. **监控未启动**
   - 检查 `performance_monitor.enabled` 是否为 `True`
   - 查看启动日志中的错误信息

2. **数据未保存**
   - 确认 `logs/performance` 目录有写权限
   - 检查磁盘空间是否充足

3. **指标数据为空**
   - 确认API调用已添加性能跟踪装饰器
   - 检查是否有实际的请求流量

### 调试方法

1. **查看日志**
```bash
tail -f logs/app.log | grep performance
```

2. **检查状态**
```bash
curl http://localhost:8000/performance/status
```

3. **运行测试**
```bash
python backend/scripts/test_performance_monitoring.py
```

## 最佳实践

1. **合理设置采样率**: 避免过度监控影响性能
2. **定期清理数据**: 防止日志文件过大
3. **设置告警阈值**: 及时发现性能问题
4. **监控关键路径**: 重点关注核心业务接口

## 扩展开发

如需添加自定义监控指标，可以：

1. 继承 `PerformanceMetric` 类
2. 在 `PerformanceMonitor` 中注册新指标
3. 使用装饰器或上下文管理器收集数据

详细的开发文档请参考源码注释。
