# 消息回复监控和分析系统使用指南

## 📋 概述

消息回复监控和分析系统是一个功能强大的实时监控工具，用于跟踪和分析AI回复的性能、质量和用户满意度。

## 🚀 主要功能

### 1. 实时监控
- 回复响应时间监控
- 成功率和错误率统计
- 回退率监控
- 质量分数评估

### 2. 用户满意度跟踪
- 1-5分满意度评分
- 用户反馈文本收集
- 满意度趋势分析
- 回复类型满意度排名

### 3. 告警系统
- 自动异常检测
- 多级别告警（INFO/WARNING/ERROR/CRITICAL）
- 可配置告警规则
- 告警历史记录

### 4. 趋势分析
- 历史数据分析
- 性能趋势预测
- 多维度数据对比
- 自动洞察生成

## 📊 核心数据结构

### ReplyMetric（回复指标）
```python
@dataclass
class ReplyMetric:
    session_id: str          # 会话ID
    user_id: str            # 用户ID
    reply_key: str          # 回复键（如 "greeting.welcome"）
    reply_type: str         # 回复类型（static/dynamic/template）
    content: str            # 回复内容
    response_time: float    # 响应时间（秒）
    success: bool           # 是否成功
    satisfaction_score: Optional[int]  # 满意度评分（1-5）
    quality_score: float    # 质量分数（0-1）
    timestamp: datetime     # 时间戳
    context: Dict[str, Any] # 上下文信息
    error_message: Optional[str]      # 错误信息
    fallback_used: bool = False       # 是否使用回退
    llm_model: Optional[str] = None   # 使用的LLM模型
    template_version: Optional[str] = None  # 模板版本
```

## 🛠️ 基本使用方法

### 1. 初始化系统

```python
from backend.agents.reply_monitoring_system import ReplyMonitoringSystem

# 初始化监控系统
monitoring_system = ReplyMonitoringSystem()
await monitoring_system.initialize_database()
```

### 2. 记录回复指标

```python
from datetime import datetime

# 创建回复指标
metric = ReplyMetric(
    session_id="session_123",
    user_id="user_456",
    reply_key="greeting.welcome",
    reply_type="static",
    content="欢迎使用我们的服务！",
    response_time=1.2,
    success=True,
    satisfaction_score=4,
    quality_score=0.85,
    timestamp=datetime.now(),
    context={"intent": "greeting", "emotion": "positive"},
    llm_model="doubao-pro-32k"
)

# 记录指标
await monitoring_system.record_reply_metric(metric)
```

### 3. 记录用户满意度

```python
# 记录用户反馈
await monitoring_system.record_satisfaction_feedback(
    session_id="session_123",
    user_id="user_456",
    reply_key="greeting.welcome",
    satisfaction_score=4,  # 1-5分
    feedback_text="回复很友好",
    context={"source": "web_interface"}
)
```

## 📈 数据获取方法

### 1. 实时指标

```python
# 获取最近5分钟的实时指标
real_time_metrics = await monitoring_system.get_real_time_metrics(time_window=5)

# 返回数据包含：
# - total_replies: 总回复数
# - success_rate: 成功率
# - error_rate: 错误率
# - fallback_rate: 回退率
# - response_time: 响应时间统计（平均值、中位数、P95等）
# - quality_score: 质量分数统计
# - satisfaction: 满意度统计
# - active_alerts: 活跃告警数
# - reply_types: 回复类型分布
```

### 2. 趋势分析

```python
# 获取最近7天的趋势分析
trend_analysis = await monitoring_system.get_trend_analysis(days=7)

# 返回数据包含：
# - daily_data: 每日数据
# - trends: 趋势方向（上升/下降/稳定）
# - summary: 趋势摘要
```

### 3. 满意度分析

```python
# 获取满意度分析
satisfaction_analysis = await monitoring_system.get_satisfaction_analysis(days=7)

# 返回数据包含：
# - total_feedback: 总反馈数
# - average_satisfaction: 平均满意度
# - score_distribution: 分数分布
# - reply_rankings: 回复类型排名
# - daily_trends: 每日趋势
# - insights: 自动洞察
```

### 4. 告警管理

```python
# 获取活跃告警
active_alerts = await monitoring_system.get_active_alerts()

# 获取告警历史
alert_history = await monitoring_system.get_alert_history(hours=24)
```

### 5. 性能报告

```python
# 生成综合性能报告
performance_report = await monitoring_system.get_performance_report(days=7)

# 返回完整的性能分析报告，包含：
# - overall_score: 总体评分
# - summary: 摘要信息
# - real_time_metrics: 实时指标
# - trend_analysis: 趋势分析
# - satisfaction_analysis: 满意度分析
# - alerts: 告警信息
# - recommendations: 改进建议
# - system_health: 系统健康状态
```

## 🚨 告警系统

### 默认告警规则

系统预设了以下告警规则：

1. **响应时间过高** - 平均响应时间超过5秒（WARNING）
2. **成功率过低** - 成功率低于90%（ERROR）
3. **用户满意度过低** - 平均满意度低于3分（WARNING）
4. **错误率过高** - 错误率超过10%（CRITICAL）
5. **回退率过高** - 回退率超过20%（WARNING）

### 自定义告警规则

```python
from backend.agents.reply_monitoring_system import AlertRule, MetricType, AlertLevel

# 创建自定义告警规则
custom_rule = AlertRule(
    rule_id="custom_quality_low",
    name="质量分数过低",
    metric_type=MetricType.QUALITY_SCORE,
    threshold=0.7,
    comparison="<",
    time_window=10,  # 10分钟
    alert_level=AlertLevel.WARNING,
    description="平均质量分数低于0.7"
)

# 添加到监控系统
monitoring_system._alert_rules[custom_rule.rule_id] = custom_rule
```

## 📊 仪表板集成

### 获取仪表板数据

```python
async def get_dashboard_data():
    """获取仪表板所需的所有数据"""
    return {
        "real_time": await monitoring_system.get_real_time_metrics(60),
        "trends": await monitoring_system.get_trend_analysis(7),
        "satisfaction": await monitoring_system.get_satisfaction_analysis(7),
        "alerts": await monitoring_system.get_active_alerts(),
        "stats": monitoring_system.get_stats(),
        "performance_report": await monitoring_system.get_performance_report(7)
    }
```

## 🔧 最佳实践

### 1. 集成到回复流程

```python
async def send_reply_with_monitoring(session_id, user_id, reply_content, reply_key):
    """发送回复并记录监控指标"""
    start_time = time.time()
    
    try:
        # 发送回复
        success = await send_reply(reply_content)
        response_time = time.time() - start_time
        
        # 记录监控指标
        metric = ReplyMetric(
            session_id=session_id,
            user_id=user_id,
            reply_key=reply_key,
            reply_type="dynamic",
            content=reply_content,
            response_time=response_time,
            success=success,
            quality_score=calculate_quality_score(reply_content),
            timestamp=datetime.now(),
            context={"method": "llm_generation"}
        )
        
        await monitoring_system.record_reply_metric(metric)
        
        return success
        
    except Exception as e:
        # 记录失败指标
        response_time = time.time() - start_time
        metric = ReplyMetric(
            session_id=session_id,
            user_id=user_id,
            reply_key=reply_key,
            reply_type="dynamic",
            content="",
            response_time=response_time,
            success=False,
            quality_score=0.0,
            timestamp=datetime.now(),
            context={"method": "llm_generation"},
            error_message=str(e)
        )
        
        await monitoring_system.record_reply_metric(metric)
        raise
```

### 2. 定期生成报告

```python
import schedule
import asyncio

async def generate_daily_report():
    """生成每日性能报告"""
    report = await monitoring_system.get_performance_report(days=1)
    
    # 发送报告到管理员邮箱或保存到文件
    save_report_to_file(report)
    
    # 如果有严重问题，发送告警
    if report.get("system_health") == "严重":
        send_alert_notification(report)

# 每天凌晨生成报告
schedule.every().day.at("00:00").do(lambda: asyncio.run(generate_daily_report()))
```

### 3. 实时监控

```python
async def real_time_monitoring_loop():
    """实时监控循环"""
    while True:
        try:
            # 获取实时指标
            metrics = await monitoring_system.get_real_time_metrics(5)
            
            # 检查关键指标
            if metrics.get("success_rate", 1) < 0.8:
                logger.warning(f"成功率过低: {metrics['success_rate']:.2%}")
            
            if metrics.get("response_time", {}).get("average", 0) > 10:
                logger.warning(f"响应时间过长: {metrics['response_time']['average']:.2f}秒")
            
            # 等待30秒后再次检查
            await asyncio.sleep(30)
            
        except Exception as e:
            logger.error(f"实时监控出错: {e}")
            await asyncio.sleep(60)  # 出错时等待更长时间
```

## 📝 注意事项

1. **数据库初始化** - 首次使用前必须调用 `initialize_database()`
2. **异步操作** - 所有数据库操作都是异步的，需要使用 `await`
3. **内存管理** - 系统使用缓存来提高性能，会自动限制缓存大小
4. **错误处理** - 所有方法都有完善的错误处理，不会因为监控失败而影响主业务
5. **性能影响** - 监控操作设计为轻量级，对主业务性能影响最小

## 🎯 使用场景

1. **实时监控仪表板** - 显示系统实时状态
2. **性能优化** - 识别性能瓶颈和优化机会
3. **质量保证** - 监控回复质量和用户满意度
4. **故障诊断** - 快速定位和解决问题
5. **业务分析** - 了解用户行为和系统使用模式

通过这个监控系统，您可以全面了解AI回复系统的运行状态，及时发现问题，持续优化用户体验。
