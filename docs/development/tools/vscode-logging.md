# VSCode 日志查阅指南

## 🚀 快速开始

### 1. 安装推荐扩展
VSCode会自动提示安装项目推荐的扩展，或者手动安装以下核心扩展：

```
必装扩展：
- Log File Highlighter (emilast.LogFileHighlighter)
- Log Viewer (berublan.vscode-log-viewer)
- JSON Tools (eriklynd.json-tools)

推荐扩展：
- Regex Previewer (chrmarti.regex)
- Rainbow CSV (mechatroner.rainbow-csv)
```

### 2. 应用配置
将 `.vscode/log-settings.json` 中的配置复制到您的 `.vscode/settings.json` 文件中。

## 📋 日志文件结构

```
logs/
├── app.log          # 应用日志 (DEBUG级别，包含所有业务流程)
├── error.log        # 错误日志 (ERROR/CRITICAL级别)
├── session.log      # 会话日志 (INFO级别，追踪会话链路)
└── performance/     # 性能监控报告
```

## 🎨 日志高亮效果

配置完成后，您将看到以下高亮效果：

### 日志级别高亮
- 🔴 **ERROR**: 红色背景，白色文字
- 🟠 **CRITICAL**: 深红色背景，白色文字
- 🟡 **WARNING**: 黄色背景，深色文字
- 🔵 **INFO**: 蓝色背景，白色文字
- 🟣 **DEBUG**: 紫色背景，白色文字

### 特殊内容高亮
- 🟢 **时间戳**: 绿色文字
- 🟠 **session_id**: 橙色文字
- 🟣 **user_id**: 粉色文字
- 🔵 **会话操作**: 青色加粗文字

## 🔍 日志查阅技巧

### 1. 快速搜索
```
Ctrl+F (Cmd+F)     # 当前文件搜索
Ctrl+Shift+F       # 全局搜索
```

### 2. 常用搜索模式
```bash
# 搜索特定会话
"session_id":"your-session-id"

# 搜索特定用户
"user_id":"user-123"

# 搜索错误日志
"level":"ERROR"

# 搜索特定时间段
"2025-06-17 15:"

# 搜索LLM调用
"type":"llm_call"

# 搜索用户输入
"type":"user_input"

# 搜索AI回复
"type":"ai_response"
```

### 3. 正则表达式搜索
启用正则表达式模式 (.*) 进行高级搜索：

```regex
# 搜索所有会话开始
\[会话开始\]

# 搜索特定时间范围
2025-06-17 1[5-6]:\d{2}:\d{2}

# 搜索长时间的LLM调用 (>5秒)
"duration":[5-9]\.\d+|"duration":[1-9]\d+

# 搜索大量Token使用 (>1000)
"total_tokens":[1-9]\d{3,}
```

### 4. 使用Log Viewer扩展
1. 右键点击日志文件
2. 选择 "Open with Log Viewer"
3. 使用过滤器按日志级别筛选
4. 使用时间范围过滤器

## 📊 JSON日志格式化

### 自动格式化
1. 选中JSON日志行
2. 右键选择 "Format Document" 或按 `Shift+Alt+F`
3. JSON会被自动格式化为易读格式

### 手动格式化
使用JSON Tools扩展：
1. 选中JSON内容
2. `Ctrl+Shift+P` 打开命令面板
3. 输入 "JSON: Prettify" 并执行

## 🔧 高级技巧

### 1. 多文件对比
```
1. 打开两个日志文件
2. 右键选择 "Select for Compare"
3. 在另一个文件上右键选择 "Compare with Selected"
```

### 2. 实时日志监控
```
1. 打开日志文件
2. 文件会自动刷新显示最新内容
3. 使用 Ctrl+End 快速跳到文件末尾
```

### 3. 书签功能
```
Ctrl+Shift+P → "Bookmarks: Toggle"  # 添加书签
Ctrl+Shift+P → "Bookmarks: List"    # 查看所有书签
```

### 4. 折叠长JSON
```
Ctrl+Shift+[    # 折叠当前区域
Ctrl+Shift+]    # 展开当前区域
Ctrl+K Ctrl+0   # 折叠所有
Ctrl+K Ctrl+J   # 展开所有
```

## 🎯 针对不同日志的查阅策略

### app.log 查阅
- **目的**: 了解完整的业务流程
- **关注点**: 业务流程、状态转换、性能指标
- **搜索建议**: 按session_id追踪完整链路

### error.log 查阅
- **目的**: 快速定位和解决问题
- **关注点**: 错误类型、异常堆栈、错误上下文
- **搜索建议**: 按错误类型、组件、时间段筛选

### session.log 查阅
- **目的**: 分析用户行为和会话质量
- **关注点**: 用户输入、AI回复、会话流程
- **搜索建议**: 按会话ID追踪用户交互

## 🛠️ 自定义配置

### 添加自定义高亮模式
在 `.vscode/settings.json` 中添加：

```json
{
    "pattern": "您的正则表达式",
    "foreground": "#颜色代码",
    "background": "#背景颜色",
    "fontWeight": "bold"
}
```

### 调整字体和行高
```json
"[log]": {
    "editor.fontSize": 13,
    "editor.lineHeight": 1.4,
    "editor.fontFamily": "Consolas, 'Courier New', monospace"
}
```

## 📈 性能优化

### 处理大日志文件
1. 启用大文件优化：`"editor.largeFileOptimizations": false`
2. 增加内存限制：`"files.maxMemoryForLargeFilesMB": 4096`
3. 使用文件分割：日志文件会自动按10MB分割

### 搜索优化
1. 使用具体的搜索词而不是通配符
2. 限制搜索范围到特定文件或目录
3. 使用正则表达式进行精确匹配

## 🚨 故障排除

### 扩展不工作
1. 检查扩展是否已安装并启用
2. 重启VSCode
3. 检查文件关联配置

### 高亮不显示
1. 确认文件被识别为log类型
2. 检查自定义模式配置
3. 尝试重新打开文件

### 性能问题
1. 关闭不必要的扩展
2. 减少同时打开的日志文件数量
3. 使用文件过滤器减少显示内容

## 📚 相关资源

- [Log File Highlighter 文档](https://marketplace.visualstudio.com/items?itemName=emilast.LogFileHighlighter)
- [Log Viewer 文档](https://marketplace.visualstudio.com/items?itemName=berublan.vscode-log-viewer)
- [VSCode 搜索文档](https://code.visualstudio.com/docs/editor/codebasics#_search-across-files)
- [正则表达式参考](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions)

通过这些配置和技巧，您可以在VSCode中高效地查阅和分析日志文件，快速定位问题并了解系统运行状况！
