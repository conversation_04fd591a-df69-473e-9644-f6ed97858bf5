# 模块接口和调用规范

## 📋 概述

本文档详细说明各模块的接口定义、调用方式和集成规范，是开发者进行模块开发和集成的重要参考。

## 🔧 核心模块接口

### 1. 配置系统接口

#### unified_config_loader.py
```python
class UnifiedConfigLoader:
    def get_config_value(self, key_path: str) -> Any
    def reload_config(self) -> None
    def validate_config(self) -> Dict[str, Any]

# 使用示例
from backend.config.unified_config_loader import get_unified_config

config = get_unified_config()
message = config.get_config_value("message_templates.error.technical_issue")
```

**调用规范**:
- 所有模块通过 `get_unified_config()` 获取配置实例
- 使用点号分隔的路径访问配置项
- 配置键名必须在 `unified_config.defaults.yaml` 中定义

### 2. AI代理基类接口

#### base.py
```python
class AutoGenBaseAgent:
    def __init__(self, name: str, config: dict = None)
    def process_message(self, message: str, context: dict = None) -> str
    def get_agent_info(self) -> dict
    def validate_input(self, message: str) -> bool

# 继承示例
class CustomAgent(AutoGenBaseAgent):
    def process_message(self, message: str, context: dict = None) -> str:
        # 实现具体的处理逻辑
        return self.generate_response(message, context)
```

**调用规范**:
- 所有Agent必须继承 `AutoGenBaseAgent`
- 必须实现 `process_message` 方法
- 使用配置系统获取Agent相关配置
- 返回结果必须是字符串类型

### 3. 策略模块接口

#### strategies/base_strategy.py
```python
class BaseStrategy:
    def can_handle(self, message: str, context: dict) -> bool
    def process(self, message: str, context: dict) -> str
    def get_priority(self) -> int

# 实现示例
class RequirementStrategy(BaseStrategy):
    def can_handle(self, message: str, context: dict) -> bool:
        return self.keyword_matcher.match(message)
    
    def process(self, message: str, context: dict) -> str:
        return self.generate_requirement_response(message)
```

**调用规范**:
- 策略按优先级排序执行
- `can_handle` 返回 True 的策略会被选中
- 策略必须是无状态的，支持并发调用

### 4. 处理器模块接口

#### handlers/base_handler.py
```python
class BaseHandler:
    def handle(self, request: dict) -> dict
    def validate_request(self, request: dict) -> bool
    def format_response(self, result: Any) -> dict

# 使用示例
class ConversationHandler(BaseHandler):
    def handle(self, request: dict) -> dict:
        message = request.get('message')
        response = self.process_conversation(message)
        return self.format_response(response)
```

**调用规范**:
- 处理器接收标准化的请求字典
- 返回标准化的响应字典
- 必须包含错误处理逻辑

## 🔄 模块间调用规范

### 1. API层 → 处理器层

```python
# API层调用示例
@app.post("/chat")
async def chat_endpoint(request: ChatRequest):
    handler = ConversationHandler()
    result = handler.handle({
        'message': request.message,
        'user_id': request.user_id,
        'session_id': request.session_id
    })
    return result
```

### 2. 处理器层 → 代理层

```python
# 处理器调用代理示例
class ConversationHandler:
    def process_conversation(self, message: str):
        # 选择合适的代理
        agent = self.select_agent(message)
        
        # 调用代理处理
        response = agent.process_message(message, {
            'user_id': self.user_id,
            'session_id': self.session_id
        })
        
        return response
```

### 3. 代理层 → 策略层

```python
# 代理调用策略示例
class MessageReplyManager:
    def generate_reply(self, message: str):
        # 遍历策略
        for strategy in self.strategies:
            if strategy.can_handle(message, self.context):
                return strategy.process(message, self.context)
        
        # 回退策略
        return self.fallback_strategy.process(message, self.context)
```

## 📊 数据格式规范

### 1. 请求数据格式

```python
# 标准请求格式
{
    "message": str,           # 用户消息
    "user_id": str,          # 用户ID
    "session_id": str,       # 会话ID
    "context": dict,         # 上下文信息
    "metadata": dict         # 元数据
}
```

### 2. 响应数据格式

```python
# 标准响应格式
{
    "success": bool,         # 处理是否成功
    "data": Any,            # 响应数据
    "message": str,         # 响应消息
    "error": str,           # 错误信息（可选）
    "metadata": dict        # 元数据（可选）
}
```

### 3. 配置数据格式

```yaml
# 配置文件格式
message_templates:
  category:
    template_name: "模板内容 {placeholder}"

thresholds:
  category:
    threshold_name: 0.8

database:
  queries:
    query_name: "SELECT * FROM table WHERE condition = ?"
```

## 🔌 集成接口规范

### 1. 新模块集成步骤

1. **继承基类**
```python
from backend.agents.base import AutoGenBaseAgent

class NewAgent(AutoGenBaseAgent):
    def __init__(self):
        super().__init__("NewAgent")
```

2. **注册配置**
```yaml
# 在配置文件中添加
agents:
  new_agent:
    enabled: true
    config:
      parameter1: value1
```

3. **注册到系统**
```python
# 在相应的管理器中注册
agent_manager.register_agent("new_agent", NewAgent)
```

### 2. API接口集成

```python
# 添加新的API端点
@router.post("/new-endpoint")
async def new_endpoint(request: NewRequest):
    try:
        handler = NewHandler()
        result = handler.handle(request.dict())
        return {"success": True, "data": result}
    except Exception as e:
        return {"success": False, "error": str(e)}
```

### 3. 前端集成

```typescript
// API客户端
export const newApi = {
  callNewEndpoint: (data: NewRequest): Promise<ApiResponse> => {
    return request.post('/new-endpoint', data);
  }
};

// 组件使用
const handleNewAction = async () => {
  const response = await newApi.callNewEndpoint(requestData);
  if (response.success) {
    // 处理成功响应
  }
};
```

## 🔍 调试和测试接口

### 1. 模块测试接口

```python
# 测试基类
class ModuleTestBase:
    def setup_test_environment(self):
        """设置测试环境"""
        pass
    
    def test_module_functionality(self):
        """测试模块功能"""
        pass
    
    def cleanup_test_environment(self):
        """清理测试环境"""
        pass
```

### 2. 调试接口

```python
# 调试工具
class DebugHelper:
    @staticmethod
    def log_module_call(module_name: str, method: str, args: dict):
        """记录模块调用"""
        logger.debug(f"Module: {module_name}, Method: {method}, Args: {args}")
    
    @staticmethod
    def validate_interface(obj: Any, interface: type) -> bool:
        """验证接口实现"""
        return all(hasattr(obj, method) for method in interface.__dict__)
```

## ⚠️ 注意事项和最佳实践

### 1. 错误处理规范

```python
# 统一错误处理
try:
    result = module.process(data)
except ModuleException as e:
    logger.error(f"Module error: {e}")
    return {"success": False, "error": str(e)}
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    return {"success": False, "error": "Internal server error"}
```

### 2. 日志记录规范

```python
# 标准日志格式
logger.info(f"[{module_name}] Processing request: {request_id}")
logger.debug(f"[{module_name}] Input data: {data}")
logger.error(f"[{module_name}] Error occurred: {error}")
```

### 3. 性能监控规范

```python
# 性能监控装饰器
@performance_monitor
def process_message(self, message: str) -> str:
    # 处理逻辑
    return result
```

### 4. 配置验证规范

```python
# 配置验证
def validate_module_config(config: dict) -> bool:
    required_keys = ['param1', 'param2']
    return all(key in config for key in required_keys)
```

---

💡 **提示**: 遵循这些接口规范可以确保模块间的良好集成和系统的可维护性。新开发的模块必须符合这些规范。
