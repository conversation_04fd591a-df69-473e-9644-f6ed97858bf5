# 价格关键词优化记录

## 🎯 问题描述

**用户反馈问题：**
用户输入："我对设计这张海报具体需要多少钱没有概念，您能给我建议吗？"
被系统错误识别为复合意图（知识库查询 + 业务需求），但用户的真实意图应该是纯粹的业务需求咨询。

## 🔍 根本原因分析

### 问题根源
关键词 `"多少钱"` 在配置中被归类为知识库查询关键词，但实际使用中存在语义歧义：

1. **知识库查询场景**：询问平台标准价格
   - "你们平台开发网站多少钱？"
   - "设计海报的标准价格是多少？"

2. **业务咨询场景**：在需求收集中询问预算建议
   - "我对设计这张海报具体需要多少钱没有概念，您能给我建议吗？"
   - "这个项目大概需要多少预算？"

### 错误识别流程
1. 关键词匹配：`"多少钱"` → `search_knowledge_base`，`"设计"` → `business_requirement`
2. 复合意图处理：检测到两种意图 → `composite_knowledge_requirement`
3. 结果：错误的复合意图识别

## 🔧 解决方案

### 策略选择
采用**关键词精简 + LLM语义理解**的组合方案：
- 删除容易产生歧义的价格关键词
- 保留明确的知识库查询关键词
- 让LLM处理模糊的价格表达

### 具体修改内容

#### 1. 删除的模糊关键词
从知识库查询关键词中删除：
- `"多少钱"` - 语义模糊，上下文相关
- `"费用"` - 可能是查询也可能是咨询
- `"成本"` - 更多用于业务咨询
- `"预算"` - 明显是业务咨询
- `"花费"` - 通常是业务咨询
- `"开销"` - 通常是业务咨询

#### 2. 保留的明确关键词
保留明确的知识库查询关键词：
- `"价格"` - 相对明确
- `"收费"` - 通常指标准收费
- `"套餐"` - 明确的产品查询
- `"计费"` - 计费方式查询
- `"收费标准"` - 明确的标准查询
- `"价格表"` - 明确的文档查询
- `"价格说明"` - 明确的说明查询
- `"付费"` - 付费方式查询
- `"免费"` - 免费服务查询
- `"报价"` - 正式报价查询

## 📁 修改的文件

### 1. backend/config/keywords_config.yaml
- **pricing 分类**：删除模糊关键词，保留明确关键词
- **search_knowledge_base 意图**：同步更新关键词列表
- **strategy_keywords.knowledge_base_strategy.pricing**：同步更新

### 2. backend/agents/simplified_decision_engine.py
- **备用硬编码关键词**：添加 `"价格表"` 保持一致性

### 3. backend/handlers/requirement_handler.py
- **备用关键词配置**：添加 `"价格表"` 保持一致性

## 🎯 预期效果

### ✅ 解决的问题
1. **消除价格咨询的误判**：
   - "我对设计这张海报具体需要多少钱没有概念，您能给我建议吗？" → `business_requirement`
   - "这个项目大概需要多少预算？" → `business_requirement`

2. **保持知识库查询的准确性**：
   - "你们的收费标准是什么？" → `search_knowledge_base`
   - "价格表在哪里？" → `search_knowledge_base`

### 🔄 处理流程优化
1. **明确的知识库查询**：直接通过关键词匹配识别
2. **模糊的价格表达**：交给LLM进行语义理解
3. **业务价格咨询**：LLM能更准确地识别为业务需求

## 🧪 测试建议

### 测试用例
建议测试以下场景验证修改效果：

#### 应该识别为 business_requirement 的场景：
- "我对设计这张海报具体需要多少钱没有概念，您能给我建议吗？"
- "这个项目大概需要多少预算？"
- "开发这样的app费用大概是多少？"
- "我不知道这个需要多少成本"

#### 应该识别为 search_knowledge_base 的场景：
- "你们的收费标准是什么？"
- "价格表在哪里可以看到？"
- "有什么套餐可以选择？"
- "你们是怎么计费的？"

#### 边界情况：
- "你们平台设计海报多少钱？" - 可能需要LLM判断
- "价格是多少？" - 应该仍然识别为知识库查询

## 📊 风险评估

### 低风险
- 保留了核心的明确查询关键词
- LLM作为语义理解的兜底机制
- 不影响其他意图的识别逻辑

### 需要观察的指标
- LLM调用频率是否显著增加
- 价格相关查询的识别准确率
- 用户满意度反馈

## 🔄 后续优化方向

1. **监控LLM识别效果**：观察删除关键词后LLM的识别准确性
2. **收集用户反馈**：根据实际使用情况调整关键词配置
3. **考虑上下文感知**：如果需要，可以增加基于对话状态的价格意图分析

## ✅ 验证检查清单

- [x] 删除了模糊的价格关键词
- [x] 保留了明确的知识库查询关键词
- [x] 更新了所有相关配置文件
- [x] 保持了备用关键词的一致性
- [x] 文档记录了修改原因和预期效果

---

**修改完成时间：** 2025-08-12
**修改原因：** 解决价格相关表达的意图识别歧义问题
**预期效果：** 提升价格咨询场景的意图识别准确性
