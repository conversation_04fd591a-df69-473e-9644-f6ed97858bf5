# 开发者快速参考卡片

## 🚀 快速启动

### 启动服务
```bash
# 主后端服务
cd backend && uvicorn api.main:app --reload --port 8000

# 管理后端服务  
cd admin-backend && python main.py  # :8002

# 管理前端服务
cd admin-frontend && npm start      # :3000
```

### 重要端口
- **主API**: http://localhost:8000
- **管理API**: http://localhost:8002  
- **管理界面**: http://localhost:3000

## 📁 关键目录

| 目录 | 用途 | 重要文件 |
|------|------|----------|
| `backend/agents/` | AI代理 | `base.py`, `message_reply_manager.py`, **`intent_classification_llm.py`** |
| `backend/config/` | 配置管理 | `unified_config_loader.py` |
| `backend/handlers/` | 业务处理 | `conversation_handler.py` |
| `backend/prompts/` | **🆕 提示词模板** | **`structured_intent_classification.md`** |
| `admin-backend/` | 管理后端 | `main.py`, `admin_api/routers/` |
| `admin-frontend/src/pages/` | 管理页面 | `ConfigMonitoring/` |

## 🔧 核心模块

### 配置系统
```python
from backend.config.unified_config_loader import get_unified_config
config = get_unified_config()
message = config.get_config_value("message_templates.error.technical_issue")
```

### AI代理基类
```python
from backend.agents.base import AutoGenBaseAgent

class MyAgent(AutoGenBaseAgent):
    def process_message(self, message: str, context: dict = None) -> str:
        return "处理结果"
```

### 🆕 结构化意图分类
```python
from backend.agents.intent_classification_llm import IntentClassificationLLM
from backend.agents.simplified_decision_engine import get_simplified_decision_engine

# 使用决策引擎（推荐）
engine = get_simplified_decision_engine()
result = await engine.recognize_intent(message, context)

# 直接使用分类器（高级用法）
classifier = IntentClassificationLLM(llm_service, config, prompt_loader)
classification = await classifier.classify_intent(message, context, keyword_hints)
```

### 策略模式
```python
class MyStrategy(BaseStrategy):
    def can_handle(self, message: str, context: dict) -> bool:
        return True

    def process(self, message: str, context: dict) -> str:
        return "策略处理结果"
```

## 🔄 调用链路

### 传统链路
```
用户请求 → FastAPI → ConversationHandler → MessageReplyManager → Strategy → Agent → LLM → 响应
```

### 🆕 结构化意图分类链路
```
用户请求 → SimplifiedDecisionEngine → 智能调度 → IntentClassificationLLM → 结构化分析 → 意图决策 → 响应
                                    ↓
                                传统关键词匹配（回退）
```

## 📊 数据格式

### API请求格式
```json
{
    "message": "用户消息",
    "user_id": "用户ID", 
    "session_id": "会话ID",
    "context": {}
}
```

### API响应格式
```json
{
    "success": true,
    "data": "响应数据",
    "message": "响应消息"
}
```

## 🔍 调试技巧

### 查看日志
```bash
tail -f logs/app.log          # 应用日志
tail -f logs/error.log        # 错误日志
tail -f admin-backend/logs/   # 管理后端日志
```

### 测试API
```bash
# 测试主API
curl -X POST "http://localhost:8000/chat" -H "Content-Type: application/json" -d '{"message":"测试"}'

# 测试管理API
curl -X GET "http://localhost:8002/api/admin/config-monitoring/ping"
```

### 配置检查
```python
python -c "
from backend.services.config_monitoring_service import ConfigMonitoringService
service = ConfigMonitoringService()
result = service.check_config_integrity()
print(f'健康度: {result[\"health_score\"]}%')
"
```

## ⚙️ 配置文件

### 主要配置文件
- `backend/config/unified_config.defaults.yaml` - 默认配置
- `backend/config/env_config.yaml` - 环境配置
- `admin-backend/config.yaml` - 管理后端配置

### 配置结构
```yaml
message_templates:
  error:
    technical_issue: "系统错误消息"
  
thresholds:
  keyword_match_threshold: 0.8

database:
  queries:
    get_user: "SELECT * FROM users WHERE id = ?"
```

## 🔌 常用API

### 主系统API
- `POST /chat` - 聊天接口
- `GET /health` - 健康检查
- `POST /upload` - 文件上传

### 管理系统API
- `GET /api/admin/config-monitoring/dashboard` - 监控仪表板
- `GET /api/admin/config-monitoring/health-summary` - 健康摘要
- `POST /api/admin/config-monitoring/run-full-check` - 完整检查

## 🐛 常见问题

### 服务启动失败
1. 检查端口占用: `lsof -i :8000`
2. 检查配置文件: 确保YAML格式正确
3. 检查依赖: `pip install -r requirements.txt`

### 配置加载失败
1. 检查文件路径: 确保配置文件存在
2. 检查YAML语法: 使用YAML验证工具
3. 检查权限: 确保文件可读

### API调用失败
1. 检查服务状态: 确保后端服务运行
2. 检查CORS配置: 确保跨域设置正确
3. 检查请求格式: 确保JSON格式正确

## 📚 重要文档

### 必读文档
1. [项目架构和模块关系文档](项目架构和模块关系文档.md)
2. [系统架构图](architecture/系统架构图.md)
3. [配置化开发规范](development/standards/配置化开发规范.md)

### 开发文档
1. [模块接口和调用规范](development/模块接口和调用规范.md)
2. [配置监控系统使用指南](admin/配置监控系统使用指南.md)
3. [硬编码消除项目总结](projects/hardcode-elimination/)

## 🔧 开发工具

### 代码质量
```bash
# 代码格式化
black backend/
isort backend/

# 类型检查
mypy backend/

# 代码检查
flake8 backend/
```

### 测试
```bash
# 运行测试
pytest backend/tests/

# 配置验证
python backend/tests/validation/config_integrity_test.py
```

## 📞 获取帮助

### 文档位置
- 项目文档: `docs/`
- API文档: 启动服务后访问 `/docs`
- 架构图: `docs/architecture/`

### 调试资源
- 日志文件: `logs/`
- 配置文件: `backend/config/`
- 测试脚本: `backend/tests/`

---

💡 **提示**: 将此卡片加入书签，开发时可快速查找关键信息！
