# 关键词配置迁移指南

## 📋 迁移概述

本指南帮助开发者从旧的分散关键词配置迁移到新的统一关键词配置系统。

**迁移版本**: v1.0  
**完成时间**: 2025-08-08  
**兼容性**: 向后兼容，支持渐进式迁移

## 🎯 迁移目标

### 从这里迁移
- ❌ 8个不同位置的重复关键词定义
- ❌ 硬编码关键词列表
- ❌ 分散的配置管理

### 迁移到这里
- ✅ 1个统一关键词配置文件
- ✅ 统一关键词加载器
- ✅ 缓存优化的性能

## 🗺️ 迁移映射

### 配置文件映射

| 旧配置位置 | 新配置位置 | 状态 |
|------------|------------|------|
| `simplified_decision_engine.py` 硬编码 | `keywords_config.yaml` | ✅ 已迁移 |
| `knowledge_base_strategy.py` 硬编码 | `keywords_config.yaml` | ✅ 已迁移 |
| `context_analyzer.py` 硬编码 | `keywords_config.yaml` | ✅ 已迁移 |
| `requirement_handler.py` 硬编码 | `keywords_config.yaml` | ✅ 已迁移 |
| `unified_config.yaml` keyword_acceleration | `keywords_config.yaml` | 🔄 已弃用 |
| `unified_config.yaml` keyword_rules | `keywords_config.yaml` | 🔄 已弃用 |

### 关键词分类映射

| 旧分类 | 新分类 | 说明 |
|--------|--------|------|
| 硬编码价格关键词 | `pricing` | 价格相关关键词 |
| 硬编码功能关键词 | `features` | 功能相关关键词 |
| 硬编码使用关键词 | `usage` | 使用方法关键词 |
| 硬编码注册关键词 | `registration` | 注册相关关键词 |
| 硬编码支持关键词 | `support` | 技术支持关键词 |
| 硬编码产品关键词 | `product_info` | 产品信息关键词 |

## 🔧 迁移步骤

### 步骤1: 检查当前配置
```bash
# 检查是否存在旧的硬编码关键词
grep -r "价格\|费用\|收费" backend/agents/
grep -r "功能\|特点\|能力" backend/agents/
```

### 步骤2: 验证新配置
```python
from backend.config.keywords_loader import get_keywords_loader

# 验证加载器工作正常
loader = get_keywords_loader()
keywords = loader.get_all_knowledge_base_keywords_flat()
print(f"加载了 {len(keywords)} 个关键词")
```

### 步骤3: 测试兼容性
```bash
# 运行迁移测试
python -m pytest tests/test_keywords_migration.py -v
```

### 步骤4: 更新代码引用
如果你有自定义模块使用硬编码关键词，请按以下方式更新：

#### 旧方式 ❌
```python
# 硬编码关键词列表
keywords = ["价格", "费用", "收费", "多少钱"]
```

#### 新方式 ✅
```python
from backend.config.keywords_loader import get_keywords_loader

# 从统一配置加载
loader = get_keywords_loader()
keywords = loader.get_knowledge_base_keywords()['pricing']
```

## 📊 迁移验证

### 功能验证清单
- [ ] 关键词加载器正常初始化
- [ ] 所有关键词正确加载
- [ ] 意图识别功能正常
- [ ] 缓存机制工作正常
- [ ] 性能指标符合预期

### 验证脚本
```python
#!/usr/bin/env python3
"""迁移验证脚本"""

from backend.config.keywords_loader import get_keywords_loader

def verify_migration():
    try:
        # 1. 验证加载器
        loader = get_keywords_loader()
        print("✅ 关键词加载器初始化成功")
        
        # 2. 验证关键词数量
        all_keywords = loader.get_all_knowledge_base_keywords_flat()
        assert len(all_keywords) >= 60, f"关键词数量不足: {len(all_keywords)}"
        print(f"✅ 关键词数量验证通过: {len(all_keywords)}个")
        
        # 3. 验证分类
        categories = loader.get_knowledge_base_keywords()
        expected_categories = ['pricing', 'features', 'usage', 'registration', 'support', 'product_info']
        for category in expected_categories:
            assert category in categories, f"缺少分类: {category}"
        print(f"✅ 关键词分类验证通过: {len(categories)}个分类")
        
        # 4. 验证缓存
        stats = loader.get_stats()
        print(f"✅ 缓存状态: 命中{stats['cache_hits']}, 未命中{stats['cache_misses']}")
        
        print("🎉 迁移验证完全通过！")
        return True
        
    except Exception as e:
        print(f"❌ 迁移验证失败: {e}")
        return False

if __name__ == "__main__":
    verify_migration()
```

## 🚨 常见问题

### Q1: 迁移后关键词识别不准确
**A**: 检查关键词分类是否正确，可能需要调整分类映射

### Q2: 性能下降
**A**: 确保使用单例模式，检查缓存是否正常工作

### Q3: 配置文件格式错误
**A**: 验证YAML格式，确保缩进和语法正确

### Q4: 旧配置仍在生效
**A**: 检查模块是否正确使用新的加载器，而非硬编码关键词

## 🔄 回滚计划

如果迁移出现问题，可以按以下步骤回滚：

### 紧急回滚
1. 恢复备份的配置文件
2. 重启应用服务
3. 验证功能正常

### 代码回滚
```bash
# 回滚到迁移前的提交
git revert <migration_commit_hash>

# 或者切换到迁移前的分支
git checkout pre-migration-branch
```

### 配置回滚
```bash
# 恢复旧配置文件
cp backup/unified_config.yaml.bak backend/config/unified_config.yaml

# 删除新配置文件
rm backend/config/keywords_config.yaml
rm backend/config/keywords_loader.py
```

## 📈 迁移后优化

### 性能优化
1. **监控缓存命中率**: 目标 > 70%
2. **优化关键词分类**: 根据使用频率调整
3. **定期清理无用关键词**: 保持配置精简

### 功能扩展
1. **添加新关键词分类**: 根据业务需求扩展
2. **支持动态配置**: 运行时更新关键词
3. **多语言支持**: 扩展国际化功能

## 📚 相关资源

### 文档链接
- [关键词配置重构文档](./关键词配置重构文档.md)
- [快速参考指南](./关键词配置重构-快速参考.md)
- [技术决策记录](./ADR-关键词配置重构技术决策.md)

### 代码示例
- [统一关键词加载器](../backend/config/keywords_loader.py)
- [关键词配置文件](../backend/config/keywords_config.yaml)

### 测试用例
- [迁移测试](../tests/test_keywords_migration.py)
- [集成测试](../tests/test_keywords_integration.py)

## 📞 支持联系

如果在迁移过程中遇到问题，请：
1. 查看相关文档和FAQ
2. 运行验证脚本检查状态
3. 联系开发团队获取支持

---

**迁移指南版本**: 1.0  
**最后更新**: 2025-08-08  
**维护人员**: 开发团队
