# 结构化意图分类架构文档

## 📋 概览

**实施日期**: 2025-08-13  
**版本**: v1.0  
**状态**: ✅ 已实施  

## 🎯 架构目标

### 核心问题解决
- **价格咨询误判**: 解决"我对设计这张海报具体需要多少钱没有概念，您能给我建议吗？"被误识别为复合意图的问题
- **复合意图识别不准确**: 无法精确区分真正的复合意图和单一意图的多种表述
- **关键词匹配局限性**: 无法穷尽所有可能的关键词表达

### 解决方案
- **结构化LLM分类**: 使用专门的LLM进行语义理解和意图分类
- **混合架构**: 保留关键词筛选的高效性，结合LLM的准确性
- **智能调度**: 根据复杂度选择最适合的处理方式

## 🏗️ 系统架构

### 架构对比

| 方面 | 原架构 | 新架构 |
|------|--------|--------|
| **意图识别方式** | 关键词匹配 + LLM回退 | 关键词筛选 + 结构化LLM分类 |
| **复合意图处理** | 外部逻辑处理 | LLM内置智能处理 |
| **准确性** | 依赖关键词完整性 | 语义理解 + 上下文感知 |
| **可维护性** | 需要维护大量关键词 | 主要维护提示词模板 |
| **扩展性** | 新意图需要添加关键词 | 新意图只需更新模板 |
| **调试能力** | 有限的调试信息 | 丰富的分析和理由 |

### 核心组件

#### 1. IntentClassificationLLM
**文件**: `backend/agents/intent_classification_llm.py`

**职责**:
- 执行结构化意图分类
- 复合意图识别和解析
- 置信度评估
- 错误处理和回退

**核心方法**:
```python
async def classify_intent(
    self, 
    message: str, 
    context: Optional[List[Dict]], 
    keyword_hints: Optional[str] = None
) -> IntentClassificationResult
```

**输出结构**:
```python
@dataclass
class IntentClassificationResult:
    is_composite: bool
    intents: List[Dict[str, Any]]
    primary_intent: str
    analysis: str
    confidence: float
    processing_method: str
```

#### 2. SimplifiedDecisionEngine (升级)
**文件**: `backend/agents/simplified_decision_engine.py`

**新增方法**:
- `_should_use_structured_classification()`: 智能调度判断
- `_process_structured_classification_result()`: 处理结构化分类结果
- `_resolve_composite_intent_by_priority()`: 按优先级解析复合意图
- `_fallback_to_traditional_method()`: 传统方法回退

**核心改进**:
- 混合架构实现
- 保持API兼容性
- 增强错误处理
- 丰富日志信息

## 🔄 处理流程

### 智能调度流程

```mermaid
graph TD
    A[用户输入] --> B[关键词匹配]
    B --> C{需要结构化分类?}
    C -->|是| D[结构化意图分类LLM]
    C -->|否| E[传统关键词处理]
    D --> F{LLM调用成功?}
    F -->|是| G[处理结构化结果]
    F -->|否| H[回退到传统方法]
    G --> I[返回最终意图]
    E --> I
    H --> I
```

### 调度判断条件

系统在以下情况下使用结构化分类：
1. **关键词无匹配**: `detected_intents == ["needs_llm_analysis"]`
2. **复合意图**: `len(detected_intents) > 1`
3. **价格表达模糊**: 包含模糊的价格咨询表达
4. **依赖上下文**: 包含指代词或省略表达
5. **长文本输入**: `len(message) > 100`

## 📝 模板系统

### 结构化分类模板
**文件**: `backend/prompts/structured_intent_classification.md`

**核心特性**:
- 严格JSON输出格式
- 复合意图识别指导
- 状态感知规则
- 丰富的示例和边界情况处理

**输出格式**:
```json
{
  "is_composite": true/false,
  "intents": [
    {
      "intent": "意图名称",
      "confidence": 0.95,
      "text_span": "对应的文本片段",
      "reason": "判断理由"
    }
  ],
  "primary_intent": "最主要的意图",
  "analysis": "整体分析说明"
}
```

## ⚙️ 配置系统

### 系统配置
```yaml
system:
  use_structured_classification: true  # 功能开关
```

### LLM配置
```yaml
llm_models:
  structured_intent_classification: "doubao-pro-32k"

llm_parameters:
  structured_intent_classification:
    temperature: 0.2
    max_tokens: 4000
    timeout: 35
```

### 阈值配置
```yaml
thresholds:
  structured_classification: 0.6
  composite_confidence_diff: 0.15
```

## 🧪 测试验证

### 测试覆盖
- **单一意图**: 基础意图识别准确性
- **复合意图**: 多意图识别和主次判断
- **边界情况**: 模糊表达和上下文依赖
- **状态感知**: 不同对话状态下的识别效果

### 性能指标
- **意图识别准确率**: > 85%
- **复合意图识别率**: > 80%
- **平均响应时间**: < 3秒
- **回退率**: < 10%

## 🔧 运维监控

### 关键指标
1. **功能指标**
   - 意图识别准确率
   - 复合意图检测准确率
   - 价格咨询误判率

2. **性能指标**
   - LLM调用成功率
   - 平均响应时间
   - 回退到传统方法的比例

3. **稳定性指标**
   - 错误率
   - 断路器触发频率
   - 系统可用性

### 日志监控
- 结构化分类调用日志
- 智能调度决策日志
- 回退机制触发日志
- 性能指标记录

## 🚀 部署指南

### 渐进式部署
1. **阶段1**: 并行测试（保持原系统，记录对比数据）
2. **阶段2**: 灰度发布（部分用户使用新系统）
3. **阶段3**: 全面切换（所有用户使用新系统）

### 回滚计划
- 通过配置开关快速回滚: `system.use_structured_classification: false`
- 保持原有代码路径完整
- 监控关键指标异常

## 📈 后续优化

### 短期优化
1. **性能优化**: 缓存常见意图识别结果
2. **准确性提升**: 根据实际使用数据调整模板
3. **成本控制**: 优化LLM调用策略

### 长期规划
1. **多语言支持**: 扩展到英文等其他语言
2. **个性化**: 基于用户历史的意图识别优化
3. **实时学习**: 基于用户反馈的模型微调

## 🎉 总结

结构化意图分类系统成功实现了：

1. **核心问题解决**: 价格咨询误判问题得到根本性解决
2. **架构升级**: 从关键词依赖升级为语义理解
3. **功能增强**: 新增复合意图精确识别能力
4. **向后兼容**: 保持现有API接口不变
5. **可维护性提升**: 配置驱动，易于扩展和维护

这次升级为系统的智能化和准确性奠定了坚实基础，为后续的功能扩展和优化提供了良好的架构支撑。
