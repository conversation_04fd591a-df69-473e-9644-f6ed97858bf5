# 会话状态管理架构文档

## 简介
会话状态管理是对话系统的核心模块之一，负责跟踪和管理用户会话的状态。通过 `ConversationStateMachine` 和 `UnifiedStateManager`，系统能够高效地处理复杂的状态流转，支持多用户并发和状态持久化。

## 核心特点
- **状态机模式**：基于状态机模式实现清晰的状态流转逻辑
- **统一管理**：通过UnifiedStateManager统一管理所有会话状态
- **持久化支持**：状态信息持久化到数据库，支持会话恢复
- **缓存机制**：内置状态缓存，提高查询性能
- **并发安全**：支持多用户并发访问

## 状态定义
系统定义了以下5个核心状态：
- **IDLE**：空闲状态，等待用户输入
- **PROCESSING_INTENT**：意图处理状态
- **COLLECTING_INFO**：信息收集状态，收集用户需求
- **DOCUMENTING**：文档生成与修改状态
- **COMPLETED**：完成状态，任务已完成

## 主要组件

### 1. ConversationStateMachine
- **功能**：实现基于状态机模式的对话状态管理
- **特点**：每个状态都有独立的处理器，支持状态进入/退出回调
- **状态处理器**：IdleState、CollectingInfoState、DocumentingState、CompletedState

### 2. UnifiedStateManager
- **功能**：统一管理所有会话状态，提供状态查询、设置、转换验证
- **特点**：单例模式、状态缓存、数据库持久化、转换规则验证

## 状态流转图
```
IDLE ──────────────────────────────────────────────────────────────────┐
  │                                                                    │
  │ (用户需求输入)                                                        │
  ▼                                                                    │
COLLECTING_INFO ──────────────────────────────────────────────────────┤
  │                                                                    │
  │ (确认信息完整)                                                        │
  ▼                                                                    │
DOCUMENTING ──────────────────────────────────────────────────────────┤
  │                                                                    │
  │ (确认文档)                                                           │
  ▼                                                                    │
COMPLETED ────────────────────────────────────────────────────────────┘
  │ (重新开始)
  └─────────────────────────────────────────────────────────────────────┘
```

## 工作流程

### 状态机处理流程
1. **消息接收**：`ConversationStateMachine.process_message()` 接收用户消息
2. **状态查询**：通过 `UnifiedStateManager.get_conversation_state()` 获取当前状态
3. **状态处理**：根据当前状态调用对应的状态处理器处理消息
4. **状态转换**：如果需要状态转换，执行状态转换逻辑
5. **状态持久化**：通过 `UnifiedStateManager.set_conversation_state()` 持久化新状态
6. **响应返回**：返回处理结果和响应消息

### 状态管理流程
1. **状态缓存**：优先从内存缓存获取状态信息
2. **数据库查询**：缓存未命中时从数据库查询状态
3. **状态推断**：根据数据库中的业务数据推断当前状态
4. **缓存更新**：更新内存缓存以提高后续查询性能

## 使用示例

### 基本使用方式
```python
from backend.agents.conversation_state_machine import ConversationStateMachine
from backend.agents.unified_state_manager import get_state_manager

# 初始化状态机
state_manager = get_state_manager()
state_machine = ConversationStateMachine(state_manager)

# 处理用户消息
async def handle_user_message(message: str, session_id: str, user_id: str):
    # 获取当前状态
    current_state = await state_manager.get_conversation_state(session_id, user_id)
    print(f"当前状态: {current_state.value}")

    # 处理消息并可能触发状态转换
    result = await state_machine.process_message(message, session_id, user_id)

    print(f"处理结果: {result.response}")
    if result.new_state:
        print(f"状态转换: {current_state.value} -> {result.new_state.value}")

    return result.response
```

### 状态管理示例
```python
from backend.agents.unified_state_manager import UnifiedStateManager, ConversationState

# 获取状态管理器实例
state_manager = UnifiedStateManager()

# 手动设置状态
async def set_user_state(session_id: str, user_id: str, new_state: ConversationState):
    success = await state_manager.set_conversation_state(
        session_id, user_id, new_state, "manual"
    )
    return success

# 获取状态转换规则
def get_transition_rules():
    rules = state_manager.get_transition_rules()
    return rules
```

## 技术细节

### 状态转换规则
状态转换规则在 `unified_config.yaml` 中定义：
```yaml
conversation:
  state_transitions:
    IDLE:
      user_requirement: "COLLECTING_INFO"
      system_capability_query: "IDLE"
    COLLECTING_INFO:
      confirm_info: "DOCUMENTING"
      restart: "IDLE"
    DOCUMENTING:
      confirm_document: "COMPLETED"
      modify_document: "DOCUMENTING"
      restart: "IDLE"
    COMPLETED:
      new_requirement: "COLLECTING_INFO"
      restart: "IDLE"
```

### 数据库设计
状态信息存储在以下表中：
- `conversations`：存储会话基本信息和当前状态
- `focus_points`：存储关注点状态信息
- `documents`：存储文档状态信息

### 缓存机制
- **缓存键格式**：`{session_id}:{user_id}`
- **缓存过期时间**：5分钟
- **缓存更新策略**：写入时更新，读取时检查过期

## 注意事项

### 开发注意事项
- **状态一致性**：确保数据库状态与缓存状态的一致性
- **并发安全**：状态转换操作需要考虑并发访问的安全性
- **错误处理**：状态转换失败时需要有适当的错误处理和回滚机制

### 扩展指南
- **新增状态**：在 `ConversationState` 枚举中添加新状态
- **状态处理器**：为新状态创建对应的状态处理器类
- **转换规则**：在配置文件中定义新状态的转换规则
- **数据库更新**：如需要，更新数据库表结构

### 性能优化
- **缓存策略**：合理设置缓存过期时间，平衡性能和数据一致性
- **数据库查询**：优化状态查询的SQL语句，添加必要的索引
- **状态推断**：优化从业务数据推断状态的逻辑，减少数据库查询次数

### 故障排除
- **状态不一致**：检查缓存和数据库的状态是否一致
- **转换失败**：检查转换规则配置和触发器参数
- **性能问题**：检查缓存命中率和数据库查询性能
