# 系统架构图

## 🏗️ 整体系统架构

```mermaid
graph TB
    subgraph "用户层"
        U1[普通用户] --> WEB1[主前端系统]
        U2[管理员] --> WEB2[管理后台]
    end
    
    subgraph "前端层"
        WEB1[frontend/]
        WEB2[admin-frontend/]
    end
    
    subgraph "API网关层"
        WEB1 --> API1[backend/api/main.py:8000]
        WEB2 --> API2[admin-backend/main.py:8002]
    end
    
    subgraph "业务服务层"
        API1 --> SVC1[ConversationHandler]
        API1 --> SVC2[MessageReplyManager]
        API2 --> SVC3[ConfigMonitoringService]
        API2 --> SVC4[AdminServices]
    end
    
    subgraph "AI代理层"
        SVC1 --> AGT1[KeywordAccelerator]
        SVC1 --> AGT2[ConversationStateMachine]
        SVC2 --> AGT3[DynamicReplyGenerator]
        SVC2 --> AGT4[Strategies]
    end
    
    subgraph "数据层"
        AGT1 --> DB1[(SQLite数据库)]
        AGT2 --> DB1
        AGT3 --> CFG1[配置文件]
        AGT4 --> CFG1
        SVC3 --> CFG1
    end
    
    subgraph "外部服务"
        AGT3 --> LLM1[LLM服务]
        AGT4 --> LLM1
    end
```

## 🔄 核心业务流程架构

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API层
    participant H as 处理器
    participant S as 策略层
    participant L as LLM服务
    participant D as 数据库
    
    U->>F: 发送消息
    F->>A: HTTP请求
    A->>H: 路由到处理器
    H->>S: 选择处理策略
    S->>L: 调用AI服务
    L-->>S: 返回AI响应
    S->>D: 保存对话记录
    S-->>H: 返回处理结果
    H-->>A: 返回响应数据
    A-->>F: HTTP响应
    F-->>U: 显示结果
```

## 🏛️ 分层架构详解

### 1. 表现层 (Presentation Layer)
```
frontend/          # 用户界面
admin-frontend/    # 管理界面
```
**职责**: 用户交互、数据展示、界面逻辑

### 2. API层 (API Layer)
```
backend/api/main.py      # 主API服务 :8000
admin-backend/main.py    # 管理API服务 :8002
```
**职责**: 请求路由、参数验证、响应格式化

### 3. 业务服务层 (Business Service Layer)
```
handlers/           # 业务处理器
services/          # 业务服务
admin_services/    # 管理服务
```
**职责**: 业务逻辑处理、服务编排、事务管理

### 4. AI代理层 (AI Agent Layer)
```
agents/            # AI代理
strategies/        # 处理策略
```
**职责**: AI决策、智能处理、策略选择

### 5. 数据访问层 (Data Access Layer)
```
config/            # 配置管理
data/             # 数据存储
```
**职责**: 数据持久化、配置管理、缓存处理

## 🔧 模块依赖关系图

```mermaid
graph LR
    subgraph "配置系统"
        CFG[unified_config_loader]
        YAML[配置文件]
        CFG --> YAML
    end
    
    subgraph "基础层"
        BASE[base.py]
        UTILS[utils/]
        BASE --> CFG
        UTILS --> CFG
    end
    
    subgraph "策略层"
        STR1[fallback_strategy]
        STR2[requirement_strategy]
        STR3[capabilities_strategy]
        STR1 --> BASE
        STR2 --> BASE
        STR3 --> BASE
    end
    
    subgraph "代理层"
        AGT1[KeywordAccelerator]
        AGT2[ConversationStateMachine]
        AGT3[MessageReplyManager]
        AGT1 --> BASE
        AGT2 --> BASE
        AGT3 --> STR1
        AGT3 --> STR2
        AGT3 --> STR3
    end
    
    subgraph "处理器层"
        HDL1[ConversationHandler]
        HDL2[KnowledgeBaseHandler]
        HDL1 --> AGT1
        HDL1 --> AGT2
        HDL1 --> AGT3
        HDL2 --> AGT3
    end
    
    subgraph "API层"
        API[main.py]
        API --> HDL1
        API --> HDL2
    end
```

## 📊 数据流架构

```mermaid
flowchart TD
    subgraph "输入数据流"
        A[用户输入] --> B[前端验证]
        B --> C[API接收]
        C --> D[参数解析]
    end
    
    subgraph "处理数据流"
        D --> E[路由分发]
        E --> F[业务处理]
        F --> G[策略选择]
        G --> H[AI处理]
        H --> I[结果生成]
    end
    
    subgraph "输出数据流"
        I --> J[响应格式化]
        J --> K[API返回]
        K --> L[前端渲染]
        L --> M[用户展示]
    end
    
    subgraph "数据持久化"
        F --> N[(数据库)]
        G --> O[配置文件]
        H --> P[日志文件]
    end
```

## 🔐 安全架构

```mermaid
graph TB
    subgraph "安全边界"
        subgraph "前端安全"
            F1[输入验证]
            F2[XSS防护]
            F3[CSRF防护]
        end
        
        subgraph "API安全"
            A1[参数验证]
            A2[CORS配置]
            A3[速率限制]
        end
        
        subgraph "业务安全"
            B1[权限控制]
            B2[数据脱敏]
            B3[审计日志]
        end
        
        subgraph "数据安全"
            D1[数据加密]
            D2[备份策略]
            D3[访问控制]
        end
    end
    
    F1 --> A1
    A1 --> B1
    B1 --> D1
```

## 🚀 部署架构

```mermaid
graph TB
    subgraph "开发环境"
        DEV1[frontend :3000]
        DEV2[admin-frontend :3001]
        DEV3[backend :8000]
        DEV4[admin-backend :8002]
    end
    
    subgraph "生产环境"
        PROD1[Nginx反向代理]
        PROD2[前端静态文件]
        PROD3[后端服务集群]
        PROD4[数据库集群]
        PROD5[日志系统]
    end
    
    DEV1 -.->|构建部署| PROD2
    DEV2 -.->|构建部署| PROD2
    DEV3 -.->|容器化部署| PROD3
    DEV4 -.->|容器化部署| PROD3
    
    PROD1 --> PROD2
    PROD1 --> PROD3
    PROD3 --> PROD4
    PROD3 --> PROD5
```

## 📈 性能架构

```mermaid
graph LR
    subgraph "性能优化层"
        subgraph "前端优化"
            FE1[代码分割]
            FE2[懒加载]
            FE3[缓存策略]
        end
        
        subgraph "API优化"
            API1[连接池]
            API2[异步处理]
            API3[响应压缩]
        end
        
        subgraph "业务优化"
            BIZ1[缓存机制]
            BIZ2[批量处理]
            BIZ3[队列系统]
        end
        
        subgraph "数据优化"
            DB1[索引优化]
            DB2[查询优化]
            DB3[分库分表]
        end
    end
    
    FE1 --> API1
    API1 --> BIZ1
    BIZ1 --> DB1
```

---

💡 **提示**: 这些架构图展示了系统的不同视角，建议结合具体代码理解各层的职责和交互关系。
