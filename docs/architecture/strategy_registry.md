# 策略注册和管理文档

## 简介
策略注册和管理是统一决策引擎的重要组成部分。通过 `StrategyRegistry`，系统能够动态加载和管理各种策略。

## 功能
- 注册和管理策略。
- 提供策略的动态加载机制。
- 支持策略的扩展和自定义。

## 主要组件
1. **StrategyRegistry**：负责策略的注册和管理。
2. **具体策略实现**：如 `FallbackStrategy`、`GreetingStrategy` 等。

## 工作流程
1. 在系统初始化时，`StrategyRegistry` 加载所有已注册的策略。
2. 根据业务需求，调用相应的策略执行逻辑。
3. 支持动态添加或移除策略。

## 示例
```python
from backend.agents.strategies.registry import StrategyRegistry

registry = StrategyRegistry()
strategy = registry.get_strategy("greeting")
response = strategy.execute(context)
print(f"策略回复: {response}")
```

## 注意事项
- 确保所有策略实现符合统一接口。
- 在添加新策略时，需要更新 `StrategyRegistry` 的注册逻辑。
