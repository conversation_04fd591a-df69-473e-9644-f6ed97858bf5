# 统一决策引擎文档索引

## 📚 文档概览

统一决策引擎是需求采集系统的智能核心，本文档索引帮助您快速找到所需的技术文档和使用指南。

## 🎯 快速导航

### 👥 **用户角色导航**

#### 🔰 **新用户 - 快速入门**
1. [统一决策引擎用户指南](./development/guides/统一决策引擎用户指南.md) - 系统概述和基本使用
2. [统一决策引擎架构设计](./统一决策引擎架构设计.md) - 了解系统架构
3. [统一决策引擎API文档](./development/统一决策引擎API文档.md) - API接口说明

#### 👨‍💻 **开发者 - 功能扩展**
1. [策略开发指南](./development/guides/策略开发指南.md) - 开发新策略
2. [统一决策引擎配置指南](./development/configuration/统一决策引擎配置指南.md) - 系统配置
3. [统一决策引擎API文档](./development/统一决策引擎API文档.md) - 完整API参考

#### 🔧 **运维人员 - 系统管理**
1. [性能监控指南](./development/tools/performance-monitoring.md) - 性能监控和调优
2. [统一决策引擎配置指南](./development/configuration/统一决策引擎配置指南.md) - 生产环境配置
3. [统一决策引擎用户指南](./development/guides/统一决策引擎用户指南.md) - 故障排除

#### 📋 **项目经理 - 项目管理**
1. [统一决策引擎实施计划](./统一决策引擎实施计划.md) - 项目实施过程
2. [统一决策引擎架构设计](./统一决策引擎架构设计.md) - 技术架构和价值
3. [功能测试和性能验证报告](./功能测试和性能验证报告.md) - 质量保证

## 📖 **文档分类**

### 🏗️ **架构设计文档**

| 文档名称 | 描述 | 适用人群 | 更新状态 |
|----------|------|----------|----------|
| [统一决策引擎架构设计](./统一决策引擎架构设计.md) | 完整的系统架构设计和实施总结 | 所有角色 | ✅ 最新 |
| [统一决策引擎实施计划](./统一决策引擎实施计划.md) | 4阶段实施计划和进度跟踪 | 项目经理、开发者 | ✅ 已完成 |

### 📋 **用户指南文档**

| 文档名称 | 描述 | 适用人群 | 更新状态 |
|----------|------|----------|----------|
| [统一决策引擎用户指南](./development/guides/统一决策引擎用户指南.md) | 系统使用、监控和故障排除 | 用户、运维 | ✅ 最新 |
| [策略开发指南](./development/guides/策略开发指南.md) | 策略开发的完整指南 | 开发者 | ✅ 最新 |

### 🔧 **技术参考文档**

| 文档名称 | 描述 | 适用人群 | 更新状态 |
|----------|------|----------|----------|
| [统一决策引擎API文档](./development/统一决策引擎API文档.md) | 完整的API接口文档 | 开发者 | ✅ 最新 |
| [统一决策引擎配置指南](./development/configuration/统一决策引擎配置指南.md) | 系统配置和调优指南 | 开发者、运维 | ✅ 最新 |
| [性能监控指南](./development/tools/performance-monitoring.md) | 性能监控和分析 | 运维、开发者 | ✅ 已更新 |

### 📊 **测试和验证文档**

| 文档名称 | 描述 | 适用人群 | 更新状态 |
|----------|------|----------|----------|
| [功能测试和性能验证报告](./功能测试和性能验证报告.md) | 系统测试结果 | 所有角色 | ✅ 最新 |
| [策略冲突分析报告](./策略冲突分析报告.json) | 策略冲突检测结果 | 开发者 | ✅ 最新 |

## 🚀 **快速开始路径**

### 路径1: 我想了解系统
```
1. 统一决策引擎架构设计 (了解整体架构)
   ↓
2. 统一决策引擎用户指南 (学习基本使用)
   ↓
3. 统一决策引擎API文档 (查看接口详情)
```

### 路径2: 我想开发新策略
```
1. 策略开发指南 (学习开发方法)
   ↓
2. 统一决策引擎API文档 (了解接口规范)
   ↓
3. 统一决策引擎配置指南 (配置开发环境)
```

### 路径3: 我想部署和运维
```
1. 统一决策引擎配置指南 (配置生产环境)
   ↓
2. 性能监控指南 (设置监控)
   ↓
3. 统一决策引擎用户指南 (故障排除)
```

### 路径4: 我想了解项目过程
```
1. 统一决策引擎实施计划 (了解实施过程)
   ↓
2. 功能测试和性能验证报告 (查看质量保证)
   ↓
3. 统一决策引擎架构设计 (查看最终成果)
```

## 🔍 **常见问题快速索引**

### ❓ **使用问题**
- **如何使用统一决策引擎？** → [统一决策引擎用户指南](./development/guides/统一决策引擎用户指南.md#快速开始)
- **如何查看性能数据？** → [统一决策引擎用户指南](./development/guides/统一决策引擎用户指南.md#性能监控)
- **如何处理错误？** → [统一决策引擎用户指南](./development/guides/统一决策引擎用户指南.md#故障排除)

### ⚙️ **开发问题**
- **如何开发新策略？** → [策略开发指南](./development/guides/策略开发指南.md#快速开始)
- **如何调用API？** → [统一决策引擎API文档](./development/统一决策引擎API文档.md#核心决策接口)
- **如何配置系统？** → [统一决策引擎配置指南](./development/configuration/统一决策引擎配置指南.md#核心配置)

### 🔧 **运维问题**
- **如何监控性能？** → [性能监控指南](./development/tools/performance-monitoring.md#统一决策引擎监控)
- **如何调优系统？** → [统一决策引擎配置指南](./development/configuration/统一决策引擎配置指南.md#性能调优配置)
- **如何处理故障？** → [统一决策引擎用户指南](./development/guides/统一决策引擎用户指南.md#故障排除)

### 📋 **架构问题**
- **系统架构是什么？** → [统一决策引擎架构设计](./统一决策引擎架构设计.md#核心架构设计)
- **有哪些策略？** → [统一决策引擎架构设计](./统一决策引擎架构设计.md#策略体系设计)
- **如何扩展系统？** → [策略开发指南](./development/guides/策略开发指南.md)

## 📈 **文档更新记录**

| 日期 | 更新内容 | 影响文档 |
|------|----------|----------|
| 2025-07-28 | 统一决策引擎项目完成，更新所有文档 | 全部文档 |
| 2025-07-28 | 新增用户指南和开发指南 | 新增4个指南文档 |
| 2025-07-28 | 更新性能监控文档，增加决策引擎监控 | 性能监控指南 |
| 2025-07-28 | 完成架构设计文档，添加实施总结 | 架构设计文档 |

## 📞 **获取帮助**

### 🔗 **相关资源**
- **项目代码**: `backend/agents/` 目录
- **配置文件**: `backend/config/` 目录
- **测试文件**: `tests/` 目录

### 📧 **技术支持**
如果您在使用过程中遇到问题：

1. **查阅文档**: 首先查看相关的技术文档
2. **检查日志**: 查看系统日志和监控数据
3. **运行测试**: 执行相关的测试用例
4. **联系团队**: 联系开发团队获取支持

### 🎯 **文档反馈**
如果您发现文档问题或有改进建议：

- 文档内容错误或过时
- 缺少重要信息
- 需要更多示例
- 其他改进建议

请联系文档维护团队进行更新。

---

**📚 统一决策引擎文档体系 - 助力您的开发和运维工作！**

**文档状态**: ✅ **完整更新**  
**维护状态**: 🔄 **持续维护**  
**质量等级**: ⭐⭐⭐⭐⭐ **五星级**
