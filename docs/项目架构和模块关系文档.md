# 项目架构和模块关系文档

## 📋 项目概述

智能需求采集系统是一个基于AI的需求收集和处理平台，采用前后端分离架构，包含主应用系统和独立的后台管理系统。

## 🏗️ 项目目录结构

```
需求采集项目/
├── 📁 backend/                    # 主后端系统
│   ├── agents/                    # AI代理模块
│   │   ├── strategies/           # 策略模块
│   │   ├── conversation_flow/    # 对话流程
│   │   ├── 🆕 intent_classification_llm.py  # 结构化意图分类
│   │   └── ...
│   ├── api/                      # API接口层
│   ├── config/                   # 配置管理
│   ├── handlers/                 # 处理器模块
│   ├── services/                 # 业务服务层
│   ├── utils/                    # 工具模块
│   ├── 🆕 prompts/               # 提示词模板
│   └── data/                     # 数据存储
├── 📁 admin-backend/              # 后台管理后端
│   ├── admin_api/                # 管理API
│   ├── admin_services/           # 管理服务
│   ├── admin_utils/              # 管理工具
│   └── main.py                   # 管理系统入口
├── 📁 admin-frontend/             # 后台管理前端
│   ├── src/
│   │   ├── pages/               # 页面组件
│   │   ├── components/          # 通用组件
│   │   ├── api/                 # API客户端
│   │   └── utils/               # 前端工具
│   └── package.json
├── 📁 frontend/                   # 主前端系统
├── 📁 docs/                      # 项目文档
├── 📁 scripts/                   # 脚本工具
└── 📁 logs/                      # 日志文件
```

## 🔧 核心模块详解

### 1. 主后端系统 (backend/)

#### 🤖 AI代理模块 (agents/)
**核心职责**: 处理用户交互和AI决策

```
agents/
├── base.py                       # 基础Agent类
├── dynamic_reply_generator.py    # 动态回复生成器
├── keyword_accelerator.py        # 关键词加速器
├── conversation_state_machine.py # 对话状态机
├── message_reply_manager.py      # 消息回复管理器
├── review_and_refine.py          # 文档审查和优化
├── rag_knowledge_base_agent.py   # RAG知识库代理
├── conversation_flow_reply_mixin.py # 对话流程回复混入
├── 🆕 simplified_decision_engine.py # 简化决策引擎（已升级）
├── 🆕 intent_classification_llm.py  # 结构化意图分类LLM
├── strategies/                   # 策略模块
│   ├── fallback_strategy.py     # 回退策略
│   ├── requirement_strategy.py  # 需求策略
│   ├── capabilities_strategy.py # 能力策略
│   ├── emotional_support_strategy.py # 情感支持策略
│   └── knowledge_base_strategy.py # 知识库策略
└── conversation_flow/            # 对话流程
    └── core_refactored.py       # 对话流程核心
```

#### 🔌 API接口层 (api/)
**核心职责**: 提供RESTful API服务

```
api/
├── main.py                      # FastAPI主应用
├── admin/                       # 管理API
│   └── config_monitoring.py    # 配置监控API
└── endpoints/                   # API端点
```

#### ⚙️ 配置管理 (config/)
**核心职责**: 统一配置管理和加载

```
config/
├── unified_config_loader.py     # 统一配置加载器
├── unified_config.defaults.yaml # 默认配置文件
├── settings.py                  # 系统设置
└── env_config.yaml             # 环境配置
```

#### 🔄 处理器模块 (handlers/)
**核心职责**: 业务逻辑处理

```
handlers/
├── conversation_handler.py      # 对话处理器
├── knowledge_base_handler.py    # 知识库处理器
├── document_handler.py          # 文档处理器
└── composite_handler.py         # 复合处理器
```

#### 🛠️ 服务层 (services/)
**核心职责**: 业务服务和工具

```
services/
├── config_monitoring_service.py # 配置监控服务
├── llm_service.py               # LLM服务
└── database_service.py          # 数据库服务
```

### 2. 后台管理系统

#### 🖥️ 管理后端 (admin-backend/)
**核心职责**: 提供管理功能的API服务

```
admin-backend/
├── main.py                      # 管理系统入口
├── admin_api/                   # 管理API
│   ├── routers/                # API路由
│   │   ├── config.py           # 配置管理
│   │   ├── scenario.py         # 场景管理
│   │   ├── template.py         # 模板管理
│   │   ├── system.py           # 系统管理
│   │   ├── database.py         # 数据库管理
│   │   ├── business_rules.py   # 业务规则管理
│   │   └── config_monitoring.py # 配置监控
│   └── middleware/             # 中间件
├── admin_services/             # 管理服务
│   └── config_monitoring_service.py # 配置监控服务
└── admin_utils/                # 管理工具
    └── exceptions.py           # 异常处理
```

#### 🎨 管理前端 (admin-frontend/)
**核心职责**: 提供管理界面

```
admin-frontend/
├── src/
│   ├── pages/                  # 页面组件
│   │   ├── Config/            # 配置管理页面
│   │   ├── Scenario/          # 场景管理页面
│   │   ├── Template/          # 模板管理页面
│   │   ├── System/            # 系统管理页面
│   │   ├── Database/          # 数据库管理页面
│   │   ├── BusinessRules/     # 业务规则页面
│   │   └── ConfigMonitoring/  # 配置监控页面
│   ├── components/            # 通用组件
│   │   └── Layout/           # 布局组件
│   ├── api/                   # API客户端
│   │   └── configMonitoring.ts # 配置监控API
│   ├── routes/                # 路由配置
│   └── utils/                 # 前端工具
│       └── request.ts         # HTTP请求工具
└── package.json
```

## 🔄 模块间调用关系

### 核心调用链路

#### 1. 用户请求处理流程
```
用户请求 → FastAPI(main.py) → ConversationHandler → 
MessageReplyManager → Strategies → Agents → LLM服务 → 响应
```

#### 2. 配置管理流程
```
应用启动 → unified_config_loader → 加载配置文件 → 
各模块获取配置 → 运行时配置更新
```

#### 3. 后台管理流程
```
管理界面 → admin-frontend → HTTP请求 → admin-backend → 
admin_services → backend服务 → 数据库操作
```

### 详细调用关系图

```mermaid
graph TB
    subgraph "前端层"
        A[用户界面] --> B[admin-frontend]
        B --> C[API客户端]
    end
    
    subgraph "API层"
        C --> D[admin-backend/main.py]
        D --> E[路由器]
        E --> F[配置监控路由]
        E --> G[其他管理路由]
    end
    
    subgraph "服务层"
        F --> H[ConfigMonitoringService]
        G --> I[其他管理服务]
        H --> J[backend/services]
    end
    
    subgraph "核心业务层"
        J --> K[统一配置加载器]
        J --> L[AI代理模块]
        J --> M[处理器模块]
        L --> N[策略模块]
        M --> O[对话流程]
    end
    
    subgraph "数据层"
        K --> P[配置文件]
        O --> Q[数据库]
        N --> R[知识库]
    end
```

## 🔗 关键依赖关系

### 配置系统依赖
```
所有模块 → unified_config_loader → 配置文件
```

### AI代理依赖
```
ConversationHandler → MessageReplyManager → Strategies → 
base.py → LLM服务
```

### 监控系统依赖
```
admin-frontend → admin-backend → ConfigMonitoringService → 
backend/services → 配置系统
```

## 📊 模块职责矩阵

| 模块 | 主要职责 | 依赖模块 | 被依赖模块 |
|------|----------|----------|------------|
| unified_config_loader | 配置管理 | 配置文件 | 所有业务模块 |
| base.py | Agent基类 | 配置系统 | 所有Agent |
| strategies/ | 业务策略 | base.py, 配置系统 | MessageReplyManager |
| handlers/ | 业务处理 | strategies/, 配置系统 | API层 |
| admin-backend | 管理API | backend/services | admin-frontend |
| ConfigMonitoringService | 配置监控 | 配置系统 | 管理界面 |

## 🚀 启动顺序和依赖

### 系统启动顺序
1. **配置系统初始化** - 加载所有配置文件
2. **数据库连接** - 建立数据库连接
3. **AI服务初始化** - 初始化LLM服务
4. **业务模块加载** - 加载Agents和Handlers
5. **API服务启动** - 启动FastAPI服务
6. **管理系统启动** - 启动admin-backend
7. **前端服务启动** - 启动前端界面

### 关键依赖检查
- ✅ 配置文件完整性
- ✅ 数据库连接状态
- ✅ LLM服务可用性
- ✅ 端口占用情况

## 🔧 开发和维护指南

### 新模块开发
1. 继承适当的基类（如AutoGenBaseAgent）
2. 实现必要的接口方法
3. 注册到配置系统
4. 添加到路由或处理链

### 模块修改原则
1. 保持接口兼容性
2. 更新相关配置
3. 测试依赖模块
4. 更新文档

### 故障排查顺序
1. 检查配置系统
2. 验证模块依赖
3. 查看日志文件
4. 测试API端点

## 🆕 结构化意图分类系统 (2025-08-13)

### 系统概述
结构化意图分类系统是对原有意图识别架构的重大升级，从关键词匹配+LLM回退升级为混合架构（关键词筛选+结构化意图分类LLM）。

### 核心组件

#### IntentClassificationLLM
- **文件**: `backend/agents/intent_classification_llm.py`
- **职责**: 执行结构化意图分类，支持复合意图识别
- **特性**: 语义理解、上下文感知、置信度评估

#### SimplifiedDecisionEngine (升级)
- **文件**: `backend/agents/simplified_decision_engine.py`
- **升级内容**: 集成混合架构，智能调度，回退机制
- **兼容性**: 保持原有API接口不变

#### 结构化分类模板
- **文件**: `backend/prompts/structured_intent_classification.md`
- **特性**: 严格JSON输出，复合意图识别指导，状态感知规则

### 架构优势
1. **准确性提升**: 从关键词依赖升级为语义理解
2. **复合意图支持**: 精确识别和处理复合意图
3. **可维护性**: 主要维护提示词模板而非大量关键词
4. **向后兼容**: 保持现有API接口不变
5. **智能回退**: LLM失败时自动回退到传统方法

### 配置管理
- **功能开关**: `system.use_structured_classification`
- **LLM配置**: `llm_models.structured_intent_classification`
- **参数调优**: `llm_parameters.structured_intent_classification`

---

💡 **提示**: 这个架构文档是理解整个系统的关键，建议新开发者优先阅读此文档。结构化意图分类系统的详细文档请参考 `docs/architecture/结构化意图分类架构.md`。
