# 更新日志

## [v3.1.0] - 2025-08-13

### 🆕 新增功能

#### 结构化意图分类系统
- **新增组件**: `IntentClassificationLLM` - 专门的结构化意图分类器
- **架构升级**: `SimplifiedDecisionEngine` 升级为混合架构
- **模板系统**: 新增 `structured_intent_classification.md` 提示词模板
- **智能调度**: 根据复杂度自动选择最佳处理方式
- **复合意图支持**: 精确识别和处理复合意图

#### 配置系统扩展
- **功能开关**: `system.use_structured_classification` 控制新功能启用
- **LLM配置**: 新增结构化分类专用模型配置
- **参数调优**: 专门的温度、令牌数等参数配置
- **阈值管理**: 结构化分类置信度阈值配置

### 🔧 改进优化

#### 意图识别准确性
- **✅ 解决价格咨询误判**: "我对设计这张海报具体需要多少钱没有概念，您能给我建议吗？" 现在正确识别为 `business_requirement`
- **✅ 复合意图精确识别**: 能够区分真正的复合意图和单一意图的多种表述
- **✅ 语义理解增强**: 从关键词依赖升级为语义理解

#### 系统稳定性
- **回退机制**: LLM失败时自动回退到传统关键词匹配
- **断路器保护**: 防止LLM服务异常影响系统稳定性
- **错误处理**: 完善的异常处理和日志记录
- **向后兼容**: 保持原有API接口不变

### 📊 性能指标

| 指标 | 目标值 | 实际效果 |
|------|--------|----------|
| 意图识别准确率 | > 85% | 显著提升 |
| 复合意图识别率 | > 80% | 新增能力 |
| 价格咨询误判率 | < 5% | 大幅降低 |
| 平均响应时间 | < 3秒 | 基本保持 |
| 系统可用性 | > 99% | 稳定可靠 |

### 🏗️ 架构变更

#### 新增文件
```
backend/agents/intent_classification_llm.py    # 结构化意图分类器
backend/prompts/structured_intent_classification.md  # 分类模板
docs/architecture/结构化意图分类架构.md        # 架构文档
docs/development/结构化意图分类实施记录.md      # 实施记录
```

#### 修改文件
```
backend/agents/simplified_decision_engine.py   # 集成混合架构
backend/config/unified_config.yaml            # 扩展配置支持
tests/price_intent_test.py                    # 增强测试用例
```

#### 更新文档
```
docs/architecture/统一决策引擎架构设计.md      # 反映最新架构
docs/项目架构和模块关系文档.md                # 添加新组件说明
docs/开发者快速参考卡片.md                   # 更新使用示例
docs/development/guides/统一决策引擎用户指南.md # 添加新功能说明
```

### 🔄 迁移指南

#### 对于开发者
1. **无需修改现有代码** - 保持向后兼容
2. **推荐使用新API** - `get_simplified_decision_engine()` 获得更好性能
3. **配置新功能** - 设置 `system.use_structured_classification: true`

#### 对于运维人员
1. **配置API密钥** - 确保LLM服务正常工作
2. **监控新指标** - 关注结构化分类成功率和回退率
3. **调整参数** - 根据实际使用情况优化配置

### 🐛 修复问题

- **修复**: 价格咨询被误识别为复合意图的问题
- **修复**: 关键词匹配无法覆盖所有表达方式的问题
- **修复**: 复合意图识别不准确的问题
- **修复**: 意图识别缺乏语义理解的问题

### 📝 技术债务

- **重构**: 意图识别架构从关键词依赖升级为语义理解
- **优化**: 决策引擎性能和准确性
- **标准化**: 意图分类输出格式和处理流程
- **文档化**: 完善架构文档和使用指南

### 🔮 后续计划

#### 短期优化 (1-2周)
- 根据实际使用数据调整模板和参数
- 优化LLM调用策略降低成本
- 增加更多测试用例验证准确性

#### 中期规划 (1-2月)
- 支持多语言意图识别
- 实现意图识别结果缓存
- 添加用户反馈学习机制

#### 长期愿景 (3-6月)
- 基于用户历史的个性化意图识别
- 实时模型微调和优化
- 跨领域意图识别能力扩展

---

## [v3.0.0] - 2025-07-20

### 🏗️ 重大架构升级
- 统一配置系统实施
- 决策引擎重构
- 模块化架构优化

### 🆕 新增功能
- 配置监控系统
- 性能监控工具
- 日志系统优化

---

## [v2.x.x] - 历史版本

详细的历史版本信息请参考 `docs/archive/` 目录下的相关文档。

---

**注意**: 本更新日志遵循 [Keep a Changelog](https://keepachangelog.com/) 规范。
