"""
自定义异常类
用于后台管理系统的错误处理
"""


class AdminBaseException(Exception):
    """后台管理系统基础异常类"""
    
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class ConfigurationError(AdminBaseException):
    """配置相关错误"""
    pass


class FileOperationError(AdminBaseException):
    """文件操作错误"""
    pass


class DatabaseError(AdminBaseException):
    """数据库操作错误"""
    pass


class ValidationError(AdminBaseException):
    """数据验证错误"""
    pass


class ServiceError(AdminBaseException):
    """服务层错误"""
    pass


class NetworkError(AdminBaseException):
    """网络连接错误"""
    pass


class AuthenticationError(AdminBaseException):
    """认证错误"""
    pass


class AuthorizationError(AdminBaseException):
    """授权错误"""
    pass
