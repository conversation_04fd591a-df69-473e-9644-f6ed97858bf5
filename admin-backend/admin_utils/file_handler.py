"""
通用文件处理工具
"""

import os
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

from admin_api.middleware.exception_handler import FileOperationError

logger = logging.getLogger(__name__)


class FileHandler:
    """通用文件处理器"""
    
    def __init__(self, base_path: str = "backend"):
        """
        初始化文件处理器
        
        Args:
            base_path: 基础路径
        """
        self.base_path = Path(base_path)
        self.allowed_extensions = {'.md', '.yaml', '.yml', '.txt'}
        self.allowed_directories = {'prompts', 'config'}
    
    def read_file(self, file_path: str) -> str:
        """
        读取文件内容
        
        Args:
            file_path: 文件路径（相对于base_path）
            
        Returns:
            文件内容
            
        Raises:
            FileOperationError: 文件读取失败
        """
        full_path = self.base_path / file_path
        
        # 安全检查
        self._validate_file_path(full_path)
        
        try:
            if not full_path.exists():
                raise FileOperationError(f"文件不存在: {file_path}")
            
            if not full_path.is_file():
                raise FileOperationError(f"路径不是文件: {file_path}")
            
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            logger.info(f"成功读取文件: {file_path}")
            return content
            
        except UnicodeDecodeError:
            raise FileOperationError(f"文件编码错误，请确保文件为UTF-8编码: {file_path}")
        except Exception as e:
            raise FileOperationError(f"读取文件失败: {str(e)}")
    
    def write_file(self, file_path: str, content: str, backup: bool = True) -> bool:
        """
        写入文件内容
        
        Args:
            file_path: 文件路径（相对于base_path）
            content: 文件内容
            backup: 是否创建备份
            
        Returns:
            是否写入成功
            
        Raises:
            FileOperationError: 文件写入失败
        """
        full_path = self.base_path / file_path
        
        # 安全检查
        self._validate_file_path(full_path)
        
        try:
            # 创建备份
            if backup and full_path.exists():
                self._create_file_backup(full_path)
            
            # 确保目录存在
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入文件
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"成功写入文件: {file_path}")
            return True
            
        except Exception as e:
            raise FileOperationError(f"写入文件失败: {str(e)}")
    
    def list_files(self, directory: str, extension: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        列出目录中的文件
        
        Args:
            directory: 目录路径（相对于base_path）
            extension: 文件扩展名过滤
            
        Returns:
            文件信息列表
        """
        full_dir = self.base_path / directory
        
        # 安全检查
        self._validate_directory_path(full_dir)
        
        try:
            if not full_dir.exists():
                return []
            
            if not full_dir.is_dir():
                raise FileOperationError(f"路径不是目录: {directory}")
            
            files = []
            for file_path in full_dir.iterdir():
                if file_path.is_file():
                    # 扩展名过滤
                    if extension and not file_path.name.endswith(extension):
                        continue
                    
                    # 只允许特定扩展名
                    if file_path.suffix not in self.allowed_extensions:
                        continue
                    
                    stat = file_path.stat()
                    files.append({
                        "name": file_path.name,
                        "path": str(file_path.relative_to(self.base_path)),
                        "size": stat.st_size,
                        "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        "extension": file_path.suffix
                    })
            
            # 按修改时间排序
            files.sort(key=lambda x: x['modified_time'], reverse=True)
            
            logger.info(f"列出目录文件: {directory}, 找到 {len(files)} 个文件")
            return files
            
        except Exception as e:
            raise FileOperationError(f"列出文件失败: {str(e)}")
    
    def file_exists(self, file_path: str) -> bool:
        """
        检查文件是否存在
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件是否存在
        """
        try:
            full_path = self.base_path / file_path
            return full_path.exists() and full_path.is_file()
        except Exception:
            return False
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        full_path = self.base_path / file_path
        
        # 安全检查
        self._validate_file_path(full_path)
        
        try:
            if not full_path.exists():
                raise FileOperationError(f"文件不存在: {file_path}")
            
            stat = full_path.stat()
            return {
                "name": full_path.name,
                "path": file_path,
                "size": stat.st_size,
                "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "extension": full_path.suffix,
                "is_file": full_path.is_file(),
                "is_directory": full_path.is_dir()
            }
            
        except Exception as e:
            raise FileOperationError(f"获取文件信息失败: {str(e)}")
    
    def _validate_file_path(self, file_path: Path) -> None:
        """
        验证文件路径安全性
        
        Args:
            file_path: 文件路径
            
        Raises:
            FileOperationError: 路径不安全
        """
        try:
            # 解析绝对路径
            resolved_path = file_path.resolve()
            base_resolved = self.base_path.resolve()
            
            # 检查是否在允许的基础路径内
            if not str(resolved_path).startswith(str(base_resolved)):
                raise FileOperationError("访问路径超出允许范围")
            
            # 检查文件扩展名
            if file_path.suffix and file_path.suffix not in self.allowed_extensions:
                raise FileOperationError(f"不允许的文件类型: {file_path.suffix}")
            
            # 检查目录
            relative_path = resolved_path.relative_to(base_resolved)
            if relative_path.parts:
                first_dir = relative_path.parts[0]
                if first_dir not in self.allowed_directories:
                    raise FileOperationError(f"不允许访问目录: {first_dir}")
                    
        except ValueError as e:
            raise FileOperationError(f"路径验证失败: {str(e)}")
    
    def _validate_directory_path(self, dir_path: Path) -> None:
        """
        验证目录路径安全性
        
        Args:
            dir_path: 目录路径
            
        Raises:
            FileOperationError: 路径不安全
        """
        try:
            # 解析绝对路径
            resolved_path = dir_path.resolve()
            base_resolved = self.base_path.resolve()
            
            # 检查是否在允许的基础路径内
            if not str(resolved_path).startswith(str(base_resolved)):
                raise FileOperationError("访问路径超出允许范围")
            
            # 检查目录名
            relative_path = resolved_path.relative_to(base_resolved)
            if relative_path.parts:
                first_dir = relative_path.parts[0]
                if first_dir not in self.allowed_directories:
                    raise FileOperationError(f"不允许访问目录: {first_dir}")
                    
        except ValueError as e:
            raise FileOperationError(f"目录路径验证失败: {str(e)}")
    
    def _create_file_backup(self, file_path: Path) -> str:
        """
        创建文件备份
        
        Args:
            file_path: 文件路径
            
        Returns:
            备份文件路径
        """
        try:
            backup_dir = Path("admin-backend/backups/files")
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{file_path.stem}_backup_{timestamp}{file_path.suffix}"
            backup_path = backup_dir / backup_name
            
            shutil.copy2(file_path, backup_path)
            logger.info(f"创建文件备份: {backup_path}")
            
            return str(backup_path)
        except Exception as e:
            logger.warning(f"创建文件备份失败: {str(e)}")
            return ""