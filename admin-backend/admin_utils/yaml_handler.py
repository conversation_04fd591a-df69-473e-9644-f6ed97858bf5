"""
YAML文件处理工具
"""

import yaml
import os
import shutil
from pathlib import Path
from typing import Any, Dict, Optional
from datetime import datetime
import logging

from admin_api.middleware.exception_handler import FileOperationError, ConfigValidationError

logger = logging.getLogger(__name__)


class YAMLHandler:
    """YAML文件处理器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化YAML处理器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认路径
        """
        # 获取项目根目录
        project_root = Path(__file__).parent.parent.parent
        
        if config_path is None:
            self.config_path = project_root / "backend" / "config" / "unified_config.yaml"
        else:
            self.config_path = Path(config_path)
            
        # 确保备份目录存在
        self.backup_dir = Path(__file__).parent / "backups"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"使用配置文件路径: {self.config_path}")
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载YAML配置文件
        
        Returns:
            配置字典
            
        Raises:
            FileOperationError: 文件读取失败
            ConfigValidationError: 配置格式错误
        """
        try:
            if not self.config_path.exists():
                raise FileOperationError(f"配置文件不存在: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                raw_config = yaml.safe_load(f)
            
            if raw_config is None:
                raise ConfigValidationError("配置文件为空或格式错误")
            
            config = self._resolve_env_variables(raw_config)
            
            logger.info(f"成功加载配置文件: {self.config_path}")
            return config
            
        except yaml.YAMLError as e:
            raise ConfigValidationError(f"YAML格式错误: {str(e)}")
        except Exception as e:
            raise FileOperationError(f"读取配置文件失败: {str(e)}")

    def _resolve_env_variables(self, config: Any) -> Any:
        """递归解析配置中的环境变量占位符"""
        import re

        if isinstance(config, dict):
            return {key: self._resolve_env_variables(value) for key, value in config.items()}
        elif isinstance(config, list):
            return [self._resolve_env_variables(item) for item in config]
        elif isinstance(config, str):
            # 匹配 ${VAR_NAME} 或 ${VAR_NAME:-default_value} 格式的环境变量
            def replace_env_var(match):
                var_name = match.group(1)
                default_value = match.group(2)
                env_value = os.getenv(var_name)
                if env_value is None:
                    if default_value is not None:
                        return default_value
                    else:
                        logger.warning(f"环境变量 {var_name} 未设置，保持原值")
                        return match.group(0)  # 返回原始占位符
                return env_value

            return re.sub(r'\$\{([^}]+?)(?:\:-(.*?))?\}', replace_env_var, config)
        else:
            return config
    
    def save_config(self, config: Dict[str, Any], backup: bool = True) -> bool:
        """
        保存配置到YAML文件
        
        Args:
            config: 配置字典
            backup: 是否创建备份
            
        Returns:
            是否保存成功
            
        Raises:
            FileOperationError: 文件写入失败
            ConfigValidationError: 配置验证失败
        """
        try:
            # 验证配置格式
            self._validate_config(config)
            
            # 创建备份
            if backup:
                self._create_backup()
            
            # 写入配置文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(
                    config,
                    f,
                    default_flow_style=False,
                    allow_unicode=True,
                    indent=2,
                    sort_keys=False
                )
            
            logger.info(f"成功保存配置文件: {self.config_path}")
            return True
            
        except Exception as e:
            raise FileOperationError(f"保存配置文件失败: {str(e)}")
    
    def get_config_section(self, section_path: str) -> Any:
        """
        获取配置的指定部分
        
        Args:
            section_path: 配置路径，如 "llm.models.deepseek-chat"
            
        Returns:
            配置值
        """
        config = self.load_config()
        
        try:
            keys = section_path.split('.')
            value = config
            for key in keys:
                value = value[key]
            return value
        except KeyError:
            raise ConfigValidationError(f"配置路径不存在: {section_path}")
    
    def update_config_section(self, section_path: str, value: Any, backup: bool = True) -> bool:
        """
        更新配置的指定部分
        
        Args:
            section_path: 配置路径
            value: 新值
            backup: 是否创建备份
            
        Returns:
            是否更新成功
        """
        config = self.load_config()
        
        try:
            keys = section_path.split('.')
            current = config
            
            # 导航到目标位置的父级
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
            
            # 设置值
            current[keys[-1]] = value
            
            # 保存配置
            return self.save_config(config, backup)
            
        except Exception as e:
            raise ConfigValidationError(f"更新配置失败: {str(e)}")
    
    def _validate_config(self, config: Dict[str, Any]) -> None:
        """
        验证配置格式
        
        Args:
            config: 配置字典
            
        Raises:
            ConfigValidationError: 配置验证失败
        """
        # 基础结构验证
        required_sections = ['system', 'llm', 'message_templates']
        for section in required_sections:
            if section not in config:
                raise ConfigValidationError(f"缺少必需的配置段: {section}")
        
        # LLM配置验证
        if 'llm' in config:
            llm_config = config['llm']
            if 'models' not in llm_config:
                raise ConfigValidationError("LLM配置中缺少models段")
            
            # 验证每个模型配置
            for model_name, model_config in llm_config['models'].items():
                required_fields = ['provider', 'api_base', 'model_name']
                for field in required_fields:
                    if field not in model_config:
                        raise ConfigValidationError(f"模型 {model_name} 缺少必需字段: {field}")
    
    def _create_backup(self) -> str:
        """
        创建配置文件备份
        
        Returns:
            备份文件路径
        """
        if not self.config_path.exists():
            return ""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"unified_config_backup_{timestamp}.yaml"
        backup_path = self.backup_dir / backup_filename
        
        try:
            shutil.copy2(self.config_path, backup_path)
            logger.info(f"创建配置备份: {backup_path}")
            
            # 清理旧备份（保留最近10个）
            self._cleanup_old_backups()
            
            return str(backup_path)
        except Exception as e:
            logger.warning(f"创建备份失败: {str(e)}")
            return ""
    
    def _cleanup_old_backups(self, keep_count: int = 10) -> None:
        """
        清理旧的备份文件
        
        Args:
            keep_count: 保留的备份数量
        """
        try:
            backup_files = list(self.backup_dir.glob("unified_config_backup_*.yaml"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # 删除多余的备份
            for backup_file in backup_files[keep_count:]:
                backup_file.unlink()
                logger.info(f"删除旧备份: {backup_file}")
                
        except Exception as e:
            logger.warning(f"清理备份文件失败: {str(e)}")
    
    def list_backups(self) -> list:
        """
        列出所有备份文件
        
        Returns:
            备份文件信息列表
        """
        try:
            backup_files = list(self.backup_dir.glob("unified_config_backup_*.yaml"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            backups = []
            for backup_file in backup_files:
                stat = backup_file.stat()
                backups.append({
                    "filename": backup_file.name,
                    "path": str(backup_file),
                    "size": stat.st_size,
                    "created_time": datetime.fromtimestamp(stat.st_mtime).isoformat()
                })
            
            return backups
        except Exception as e:
            logger.error(f"列出备份文件失败: {str(e)}")
            return []
