"""
后台管理系统 FastAPI 应用入口

专注于LLM配置、场景映射和模板管理的轻量级后台管理系统
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging

from admin_api.routers import config, scenario, template, system, database, business_rules, config_monitoring
from admin_api.middleware.exception_handler import add_exception_handlers


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化
    logging.info("后台管理系统启动中...")
    yield
    # 关闭时的清理
    logging.info("后台管理系统关闭")


# 创建FastAPI应用
app = FastAPI(
    title="智能需求采集系统 - 后台管理",
    description="LLM配置、场景映射和模板管理的后台管理系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS中间件 - 开发环境使用更宽松配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 开发环境允许所有来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加异常处理器
add_exception_handlers(app)

# 注册路由
app.include_router(config.router, prefix="/api/admin/config", tags=["LLM配置管理"])
app.include_router(scenario.router, prefix="/api/admin/scenario", tags=["场景映射管理"])
app.include_router(template.router, prefix="/api/admin/template", tags=["模板管理"])
app.include_router(system.router, prefix="/api/admin/system", tags=["系统管理"])
app.include_router(database.router, prefix="/api/admin/database", tags=["数据库维护"])
app.include_router(business_rules.router, prefix="/api/admin/business-rules", tags=["业务规则管理"])
app.include_router(config_monitoring.router, prefix="/api/admin/config-monitoring", tags=["配置监控"])


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "智能需求采集系统 - 后台管理API",
        "version": "1.0.0",
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "admin-backend"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8002,
        reload=True,
        log_level="info"
    )
