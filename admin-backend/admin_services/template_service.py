"""
模板管理服务
"""

import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

from admin_utils.yaml_handler import <PERSON>AMLHandler
from admin_utils.file_handler import FileHandler
from admin_api.middleware.exception_handler import (
    ConfigValidationError, 
    FileOperationError
)

logger = logging.getLogger(__name__)


class TemplateService:
    """模板管理服务"""
    
    def __init__(self):
        """初始化模板服务"""
        self.yaml_handler = YAMLHandler()
        # 获取项目根目录下的backend路径
        project_root = Path(__file__).parent.parent.parent
        backend_path = project_root / "backend"
        self.file_handler = FileHandler(str(backend_path))
    
    async def get_prompt_templates(self) -> List[Dict[str, Any]]:
        """
        获取所有提示词模板

        Returns:
            提示词模板列表
        """
        try:
            file_list = self.file_handler.list_files("prompts", ".md")

            templates = []
            for file_info in file_list:
                # 读取文件内容
                try:
                    content = self.file_handler.read_file(file_info["path"])
                except Exception as e:
                    logger.warning(f"读取模板文件失败 {file_info['name']}: {str(e)}")
                    content = ""

                # 转换为响应模型格式
                template = {
                    "filename": file_info["name"],
                    "content": content,
                    "last_modified": file_info["modified_time"],
                    "size": file_info["size"]
                }
                templates.append(template)

            logger.info(f"获取到 {len(templates)} 个提示词模板")
            return templates

        except Exception as e:
            logger.error(f"获取提示词模板失败: {str(e)}")
            raise FileOperationError(f"获取提示词模板失败: {str(e)}")
    
    async def get_prompt_template(self, filename: str) -> Optional[Dict[str, Any]]:
        """
        获取指定提示词模板
        
        Args:
            filename: 文件名
            
        Returns:
            模板内容和信息
        """
        try:
            file_path = f"prompts/{filename}"
            
            if not self.file_handler.file_exists(file_path):
                return None
            
            content = self.file_handler.read_file(file_path)
            file_info = self.file_handler.get_file_info(file_path)
            
            result = {
                "filename": filename,
                "content": content,
                "size": file_info["size"],
                "last_modified": file_info["modified_time"]
            }
            
            logger.info(f"获取提示词模板: {filename}")
            return result
            
        except Exception as e:
            logger.error(f"获取提示词模板失败: {str(e)}")
            raise FileOperationError(f"获取提示词模板失败: {str(e)}")
    
    async def update_prompt_template(self, filename: str, content: str) -> bool:
        """
        更新提示词模板
        
        Args:
            filename: 文件名
            content: 模板内容
            
        Returns:
            是否更新成功
        """
        try:
            file_path = f"prompts/{filename}"
            
            # 验证文件名
            if not filename.endswith('.md'):
                raise ConfigValidationError("提示词模板文件必须是.md格式")
            
            # 写入文件
            success = self.file_handler.write_file(file_path, content)
            
            if success:
                logger.info(f"更新提示词模板成功: {filename}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新提示词模板失败: {str(e)}")
            raise FileOperationError(f"更新提示词模板失败: {str(e)}")
    
    async def get_message_templates(self) -> List[Dict[str, Any]]:
        """
        获取所有消息模板

        Returns:
            消息模板列表
        """
        try:
            config = self.yaml_handler.load_config()
            message_templates = config.get('message_templates', {})

            # 将嵌套字典转换为扁平列表
            templates = []
            self._flatten_templates(message_templates, "", templates)

            logger.info(f"获取到 {len(templates)} 个消息模板")
            return templates

        except Exception as e:
            logger.error(f"获取消息模板失败: {str(e)}")
            raise FileOperationError(f"获取消息模板失败: {str(e)}")

    def _flatten_templates(self, data: Any, path: str, result: List[Dict[str, Any]]) -> None:
        """
        递归展开嵌套的模板结构

        Args:
            data: 模板数据
            path: 当前路径
            result: 结果列表
        """
        if isinstance(data, dict):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                if isinstance(value, (dict, list)):
                    # 如果值是复杂类型，继续递归
                    self._flatten_templates(value, current_path, result)
                else:
                    # 如果值是简单类型，添加到结果中
                    result.append({
                        "path": current_path,
                        "content": value,
                        "template_type": "string"
                    })
        elif isinstance(data, list):
            result.append({
                "path": path,
                "content": data,
                "template_type": "array"
            })
        else:
            result.append({
                "path": path,
                "content": data,
                "template_type": "string"
            })
    
    async def get_message_template(self, template_path: str) -> Optional[Any]:
        """
        获取指定消息模板
        
        Args:
            template_path: 模板路径，如 "system.welcome"
            
        Returns:
            模板内容
        """
        try:
            config = self.yaml_handler.load_config()
            message_templates = config.get('message_templates', {})
            
            # 解析路径
            keys = template_path.split('.')
            current = message_templates
            
            for key in keys:
                if not isinstance(current, dict) or key not in current:
                    return None
                current = current[key]
            
            logger.info(f"获取消息模板: {template_path}")
            return current
            
        except Exception as e:
            logger.error(f"获取消息模板失败: {str(e)}")
            raise FileOperationError(f"获取消息模板失败: {str(e)}")
    
    async def update_message_template(self, template_path: str, content: Any) -> bool:
        """
        更新消息模板
        
        Args:
            template_path: 模板路径
            content: 模板内容
            
        Returns:
            是否更新成功
        """
        try:
            # 使用YAML处理器的更新方法
            section_path = f"message_templates.{template_path}"
            success = self.yaml_handler.update_config_section(section_path, content)
            
            if success:
                logger.info(f"更新消息模板成功: {template_path}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新消息模板失败: {str(e)}")
            raise FileOperationError(f"更新消息模板失败: {str(e)}")
    
    async def validate_template(self, template_type: str, content: str) -> Dict[str, Any]:
        """
        验证模板内容
        
        Args:
            template_type: 模板类型 (prompt/message)
            content: 模板内容
            
        Returns:
            验证结果
        """
        try:
            errors = []
            warnings = []
            
            if template_type == "prompt":
                # 验证Markdown格式
                if not content.strip():
                    errors.append("提示词模板不能为空")
                
                # 检查常见的模板变量格式
                if "{" in content and "}" in content:
                    warnings.append("检测到可能的变量占位符，请确认格式正确")
                    
            elif template_type == "message":
                # 验证消息模板
                if not content.strip():
                    errors.append("消息模板不能为空")
                
                # 检查变量格式
                import re
                variables = re.findall(r'\{([^}]+)\}', content)
                if variables:
                    warnings.append(f"检测到变量: {', '.join(variables)}")
            
            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings
            }
            
        except Exception as e:
            logger.error(f"模板验证失败: {str(e)}")
            return {
                "valid": False,
                "errors": [f"模板验证失败: {str(e)}"],
                "warnings": []
            }
    
    async def test_template(self, template_name: str, template_type: str, test_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        测试模板渲染
        
        Args:
            template_name: 模板名称
            template_type: 模板类型 (prompt/message)
            test_data: 测试数据
            
        Returns:
            测试结果
        """
        try:
            if template_type == "prompt":
                template = await self.get_prompt_template(template_name)
                if not template:
                    return {
                        "success": False,
                        "error": f"提示词模板 {template_name} 不存在"
                    }
                
                content = template["content"]
                
                # 简单的变量替换测试
                try:
                    rendered_content = content.format(**test_data)
                    return {
                        "success": True,
                        "rendered_content": rendered_content
                    }
                except KeyError as e:
                    return {
                        "success": False,
                        "error": f"缺少变量: {str(e)}"
                    }
                    
            elif template_type == "message":
                template = await self.get_message_template(template_name)
                if template is None:
                    return {
                        "success": False,
                        "error": f"消息模板 {template_name} 不存在"
                    }
                
                # 如果是字符串模板，尝试格式化
                if isinstance(template, str):
                    try:
                        rendered_content = template.format(**test_data)
                        return {
                            "success": True,
                            "rendered_content": rendered_content
                        }
                    except KeyError as e:
                        return {
                            "success": False,
                            "error": f"缺少变量: {str(e)}"
                        }
                else:
                    return {
                        "success": True,
                        "rendered_content": str(template)
                    }
            
            return {
                "success": False,
                "error": f"不支持的模板类型: {template_type}"
            }
            
        except Exception as e:
            logger.error(f"模板测试失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def validate_all_templates(self) -> Dict[str, Any]:
        """
        验证所有模板
        
        Returns:
            验证结果
        """
        try:
            errors = []
            warnings = []
            
            # 验证提示词模板
            prompt_templates = await self.get_prompt_templates()
            for template in prompt_templates:
                try:
                    template_content = await self.get_prompt_template(template["name"])
                    if template_content:
                        validation = await self.validate_template("prompt", template_content["content"])
                        if not validation["valid"]:
                            errors.extend([f"提示词模板 {template['name']}: {error}" for error in validation["errors"]])
                        warnings.extend([f"提示词模板 {template['name']}: {warning}" for warning in validation["warnings"]])
                except Exception as e:
                    errors.append(f"验证提示词模板 {template['name']} 失败: {str(e)}")
            
            # 验证消息模板
            message_templates = await self.get_message_templates()
            for key, value in message_templates.items():
                if isinstance(value, str):
                    validation = await self.validate_template("message", value)
                    if not validation["valid"]:
                        errors.extend([f"消息模板 {key}: {error}" for error in validation["errors"]])
                    warnings.extend([f"消息模板 {key}: {warning}" for warning in validation["warnings"]])
            
            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "total_prompt_templates": len(prompt_templates),
                "total_message_templates": len(message_templates)
            }
            
        except Exception as e:
            logger.error(f"验证所有模板失败: {str(e)}")
            return {
                "valid": False,
                "errors": [f"验证失败: {str(e)}"],
                "warnings": [],
                "total_prompt_templates": 0,
                "total_message_templates": 0
            }