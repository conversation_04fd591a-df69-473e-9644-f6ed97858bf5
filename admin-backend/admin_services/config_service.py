"""
LLM配置管理服务
"""

import asyncio
import time
import httpx
from typing import Dict, Any, List, Optional
import logging

from admin_utils.yaml_handler import YAMLHandler
from admin_api.middleware.exception_handler import (
    ConfigValidationError, 
    FileOperationError, 
    ModelConnectionError
)

logger = logging.getLogger(__name__)


class ConfigService:
    """LLM配置管理服务"""
    
    def __init__(self):
        """初始化配置服务"""
        self.yaml_handler = YAMLHandler()
        self.http_client = httpx.AsyncClient(timeout=30.0)
    
    async def get_all_llm_models(self) -> List[Dict[str, Any]]:
        """
        获取所有LLM模型配置
        
        Returns:
            模型配置列表
        """
        try:
            config = self.yaml_handler.load_config()
            llm_config = config.get("llm", {})
            models = llm_config.get("models", {})
            
            result = []
            for model_name, model_config in models.items():
                # 脱敏API密钥
                masked_config = self._mask_sensitive_data(model_config.copy())
                masked_config["name"] = model_name
                result.append(masked_config)
            
            logger.info(f"获取到 {len(result)} 个LLM模型配置")
            return result
            
        except Exception as e:
            logger.error(f"获取LLM模型配置失败: {str(e)}")
            raise FileOperationError(f"获取LLM模型配置失败: {str(e)}")
    
    async def get_llm_model(self, model_name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定LLM模型配置
        
        Args:
            model_name: 模型名称
            
        Returns:
            模型配置字典，如果不存在返回None
        """
        try:
            config = self.yaml_handler.load_config()
            llm_config = config.get("llm", {})
            models = llm_config.get("models", {})
            
            if model_name not in models:
                return None
            
            model_config = self.yaml_handler._resolve_env_variables(models[model_name]).copy()
            # 脱敏API密钥
            masked_config = self._mask_sensitive_data(model_config)
            masked_config["name"] = model_name
            
            logger.info(f"获取模型配置: {model_name}")
            return masked_config
            
        except Exception as e:
            logger.error(f"获取模型配置失败: {str(e)}")
            raise FileOperationError(f"获取模型配置失败: {str(e)}")
    
    async def update_llm_model(self, model_name: str, update_data: Dict[str, Any]) -> bool:
        """
        更新LLM模型配置
        
        Args:
            model_name: 模型名称
            update_data: 更新数据
            
        Returns:
            是否更新成功
        """
        try:
            config = self.yaml_handler.load_config()
            llm_config = config.get("llm", {})
            models = llm_config.get("models", {})
            
            if model_name not in models:
                return False
            
            # 验证更新数据
            self._validate_model_config(update_data)
            
            # 更新配置
            current_config = models[model_name]
            for key, value in update_data.items():
                if value is not None:  # 只更新非None值
                    current_config[key] = value
            
            # 保存配置
            success = self.yaml_handler.save_config(config)
            
            if success:
                logger.info(f"更新模型配置成功: {model_name}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新模型配置失败: {str(e)}")
            raise FileOperationError(f"更新模型配置失败: {str(e)}")
    
    async def create_llm_model(self, model_data: Dict[str, Any]) -> bool:
        """
        创建新的LLM模型配置
        
        Args:
            model_data: 模型配置数据
            
        Returns:
            是否创建成功
        """
        try:
            if "name" not in model_data:
                raise ConfigValidationError("缺少模型名称")
            
            model_name = model_data.pop("name")
            
            # 验证配置数据
            self._validate_model_config(model_data, required=True)
            
            config = self.yaml_handler.load_config()
            llm_config = config.setdefault("llm", {})
            models = llm_config.setdefault("models", {})
            
            if model_name in models:
                raise ConfigValidationError(f"模型 {model_name} 已存在")
            
            # 添加新模型配置
            models[model_name] = model_data
            
            # 保存配置
            success = self.yaml_handler.save_config(config)
            
            if success:
                logger.info(f"创建模型配置成功: {model_name}")
            
            return success
            
        except Exception as e:
            logger.error(f"创建模型配置失败: {str(e)}")
            if isinstance(e, ConfigValidationError):
                raise
            raise FileOperationError(f"创建模型配置失败: {str(e)}")
    
    async def test_model_connection(self, model_name: str, test_prompt: str) -> Dict[str, Any]:
        """
        测试模型连接
        
        Args:
            model_name: 模型名称
            test_prompt: 测试提示词
            
        Returns:
            测试结果
        """
        try:
            # 获取模型配置
            config = self.yaml_handler.load_config()
            llm_config = config.get("llm", {})
            models = llm_config.get("models", {})
            
            if model_name not in models:
                raise ModelConnectionError(f"模型 {model_name} 不存在")
            
            model_config = self.yaml_handler._resolve_env_variables(models[model_name])
            
            # 构建测试请求
            start_time = time.time()
            
            try:
                # 根据不同提供商构建请求
                if model_config.get("provider") == "deepseek":
                    result = await self._test_deepseek_connection(model_config, test_prompt)
                elif model_config.get("provider") == "doubao":
                    result = await self._test_doubao_connection(model_config, test_prompt)
                elif model_config.get("provider") == "qwen":
                    result = await self._test_qwen_connection(model_config, test_prompt)
                elif model_config.get("provider") == "openrouter":
                    result = await self._test_openrouter_connection(model_config, test_prompt)
                else:
                    result = await self._test_generic_openai_connection(model_config, test_prompt)
                
                latency = time.time() - start_time
                
                return {
                    "success": True,
                    "response": result,
                    "error": None,
                    "latency": round(latency, 3)
                }
                
            except Exception as e:
                latency = time.time() - start_time
                return {
                    "success": False,
                    "response": None,
                    "error": str(e),
                    "latency": round(latency, 3)
                }
                
        except Exception as e:
            logger.error(f"测试模型连接失败: {str(e)}")
            raise ModelConnectionError(f"测试模型连接失败: {str(e)}")
    
    async def reload_config(self) -> bool:
        """
        重新加载配置
        
        Returns:
            是否重新加载成功
        """
        try:
            # 重新加载配置文件
            self.yaml_handler.load_config()
            
            # 这里可以添加通知主系统重新加载配置的逻辑
            # 例如发送信号或调用主系统的重载API
            
            logger.info("配置重新加载成功")
            return True
            
        except Exception as e:
            logger.error(f"重新加载配置失败: {str(e)}")
            return False
    
    async def validate_config(self) -> Dict[str, Any]:
        """
        验证配置文件
        
        Returns:
            验证结果
        """
        try:
            config = self.yaml_handler.load_config()
            
            errors = []
            warnings = []
            
            # 验证LLM配置
            llm_config = config.get("llm", {})
            if not llm_config:
                errors.append("缺少LLM配置段")
            else:
                models = llm_config.get("models", {})
                if not models:
                    errors.append("LLM配置中没有模型定义")
                else:
                    for model_name, model_config in models.items():
                        model_errors = self._validate_single_model_config(model_name, model_config)
                        errors.extend(model_errors)
                
                # 验证场景映射
                scenario_mapping = llm_config.get("scenario_mapping", {})
                for scenario, model_name in scenario_mapping.items():
                    if model_name not in models:
                        errors.append(f"场景 {scenario} 映射的模型 {model_name} 不存在")
            
            # 验证消息模板
            message_templates = config.get("message_templates", {})
            if not message_templates:
                warnings.append("缺少消息模板配置")
            
            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "total_models": len(llm_config.get("models", {})),
                "total_scenarios": len(llm_config.get("scenario_mapping", {}))
            }
            
        except Exception as e:
            logger.error(f"配置验证失败: {str(e)}")
            return {
                "valid": False,
                "errors": [f"配置验证失败: {str(e)}"],
                "warnings": [],
                "total_models": 0,
                "total_scenarios": 0
            }
    
    def _mask_sensitive_data(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        脱敏敏感数据
        
        Args:
            config: 配置字典
            
        Returns:
            脱敏后的配置字典
        """
        masked_config = config.copy()
        
        # 脱敏API密钥
        if "api_key" in masked_config:
            api_key = masked_config["api_key"]
            if api_key and len(api_key) > 8:
                masked_config["api_key_masked"] = f"{api_key[:4]}****{api_key[-4:]}"
            else:
                masked_config["api_key_masked"] = "****"
            # 移除原始API密钥
            del masked_config["api_key"]
        
        return masked_config
    
    def _validate_model_config(self, config: Dict[str, Any], required: bool = False) -> None:
        """
        验证模型配置
        
        Args:
            config: 模型配置
            required: 是否验证必需字段
            
        Raises:
            ConfigValidationError: 配置验证失败
        """
        if required:
            required_fields = ["provider", "api_base", "model_name"]
            for field in required_fields:
                if field not in config or not config[field]:
                    raise ConfigValidationError(f"缺少必需字段: {field}")
        
        # 验证数值字段
        numeric_fields = {
            "temperature": (0.0, 2.0),
            "max_tokens": (1, 100000),
            "timeout": (1, 300),
            "max_retries": (0, 10)
        }
        
        for field, (min_val, max_val) in numeric_fields.items():
            if field in config:
                value = config[field]
                if not isinstance(value, (int, float)):
                    raise ConfigValidationError(f"字段 {field} 必须是数字")
                if not (min_val <= value <= max_val):
                    raise ConfigValidationError(f"字段 {field} 值必须在 {min_val} 到 {max_val} 之间")
    
    def _validate_single_model_config(self, model_name: str, config: Dict[str, Any]) -> List[str]:
        """
        验证单个模型配置
        
        Args:
            model_name: 模型名称
            config: 模型配置
            
        Returns:
            错误列表
        """
        errors = []
        
        required_fields = ["provider", "api_base", "model_name"]
        for field in required_fields:
            if field not in config or not config[field]:
                errors.append(f"模型 {model_name} 缺少必需字段: {field}")
        
        # 验证数值字段
        numeric_fields = {
            "temperature": (0.0, 2.0),
            "max_tokens": (1, 100000),
            "timeout": (1, 300),
            "max_retries": (0, 10)
        }
        
        for field, (min_val, max_val) in numeric_fields.items():
            if field in config:
                value = config[field]
                if not isinstance(value, (int, float)):
                    errors.append(f"模型 {model_name} 字段 {field} 必须是数字")
                elif not (min_val <= value <= max_val):
                    errors.append(f"模型 {model_name} 字段 {field} 值必须在 {min_val} 到 {max_val} 之间")
        
        return errors
    
    async def _test_generic_openai_connection(self, config: Dict[str, Any], prompt: str) -> str:
        """
        测试通用OpenAI兼容API连接
        
        Args:
            config: 模型配置
            prompt: 测试提示词
            
        Returns:
            API响应
        """
        headers = {
            "Authorization": f'Bearer {config["api_key"]}',
            "Content-Type": "application/json"
        }
        
        data = {
            "model": config["model_name"],
            "messages": [{"role": "user", "content": prompt}],
            "temperature": config.get("temperature", 0.7),
            "max_tokens": min(config.get("max_tokens", 1000), 100)  # 测试时限制token数
        }
        
        response = await self.http_client.post(
            f'{config["api_base"]}/chat/completions',
            headers=headers,
            json=data,
            timeout=config.get("timeout", 30)
        )
        
        if response.status_code != 200:
            raise ModelConnectionError(f"API请求失败: {response.status_code} - {response.text}")
        
        result = response.json()
        if "choices" not in result or not result["choices"]:
            raise ModelConnectionError("API响应格式错误")
        
        return result["choices"][0]["message"]["content"]
    
    async def _test_deepseek_connection(self, config: Dict[str, Any], prompt: str) -> str:
        """测试DeepSeek连接"""
        return await self._test_generic_openai_connection(config, prompt)
    
    async def _test_doubao_connection(self, config: Dict[str, Any], prompt: str) -> str:
        """测试豆包连接"""
        return await self._test_generic_openai_connection(config, prompt)
    
    async def _test_qwen_connection(self, config: Dict[str, Any], prompt: str) -> str:
        """测试通义千问连接"""
        return await self._test_generic_openai_connection(config, prompt)
    
    async def _test_openrouter_connection(self, config: Dict[str, Any], prompt: str) -> str:
        """测试OpenRouter连接"""
        return await self._test_generic_openai_connection(config, prompt)
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.http_client.aclose()
