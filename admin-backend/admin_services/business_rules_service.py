"""
业务规则配置管理服务
"""

import logging
from typing import Dict, Any, Optional
from admin_utils.yaml_handler import <PERSON>AMLHandler
from admin_api.middleware.exception_handler import ConfigValidationError, FileOperationError

logger = logging.getLogger(__name__)


class BusinessRulesService:
    """业务规则配置管理服务"""
    
    def __init__(self):
        """初始化业务规则服务"""
        self.yaml_handler = YAMLHandler()
    
    async def get_all_business_rules(self) -> Dict[str, Any]:
        """
        获取所有业务规则配置
        
        Returns:
            业务规则配置字典
        """
        try:
            config = self.yaml_handler.load_config()
            business_rules = config.get("business_rules", {})
            
            # 确保所有必要的配置项都存在，提供默认值
            result = {
                "focus_point_priority": business_rules.get("focus_point_priority", {
                    "p0": True,
                    "p1": True,
                    "p2": False
                }),
                "retry": business_rules.get("retry", {
                    "max_pending_attempts": 3,
                    "max_total_attempts": 5,
                    "backoff_factor": 1.5
                }),
                "requirement_collection": business_rules.get("requirement_collection", {
                    "min_focus_points": 3,
                    "max_focus_points": 10,
                    "completion_threshold": 0.8
                }),
                "quality_control": business_rules.get("quality_control", {
                    "min_input_length": 2,
                    "max_input_length": 1000,
                    "spam_detection_enabled": True
                })
            }
            
            logger.info("获取业务规则配置成功")
            return result
            
        except Exception as e:
            logger.error(f"获取业务规则配置失败: {str(e)}")
            raise FileOperationError(f"获取业务规则配置失败: {str(e)}")
    
    async def get_focus_point_priority(self) -> Dict[str, bool]:
        """
        获取关注点优先级配置
        
        Returns:
            关注点优先级配置
        """
        try:
            config = self.yaml_handler.load_config()
            business_rules = config.get("business_rules", {})
            focus_point_priority = business_rules.get("focus_point_priority", {
                "p0": True,
                "p1": True,
                "p2": False
            })
            
            logger.info("获取关注点优先级配置成功")
            return focus_point_priority
            
        except Exception as e:
            logger.error(f"获取关注点优先级配置失败: {str(e)}")
            raise FileOperationError(f"获取关注点优先级配置失败: {str(e)}")
    
    async def update_focus_point_priority(self, priority_config: Dict[str, bool]) -> bool:
        """
        更新关注点优先级配置
        
        Args:
            priority_config: 新的优先级配置
            
        Returns:
            是否更新成功
        """
        try:
            # 验证配置
            self._validate_focus_point_priority(priority_config)
            
            config = self.yaml_handler.load_config()
            business_rules = config.setdefault("business_rules", {})
            business_rules["focus_point_priority"] = priority_config
            
            # 保存配置
            success = self.yaml_handler.save_config(config)
            
            if success:
                logger.info(f"更新关注点优先级配置成功: {priority_config}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新关注点优先级配置失败: {str(e)}")
            raise FileOperationError(f"更新关注点优先级配置失败: {str(e)}")
    
    async def get_retry_config(self) -> Dict[str, Any]:
        """
        获取重试配置
        
        Returns:
            重试配置
        """
        try:
            config = self.yaml_handler.load_config()
            business_rules = config.get("business_rules", {})
            retry_config = business_rules.get("retry", {
                "max_pending_attempts": 3,
                "max_total_attempts": 5,
                "backoff_factor": 1.5
            })
            
            logger.info("获取重试配置成功")
            return retry_config
            
        except Exception as e:
            logger.error(f"获取重试配置失败: {str(e)}")
            raise FileOperationError(f"获取重试配置失败: {str(e)}")
    
    async def update_retry_config(self, retry_config: Dict[str, Any]) -> bool:
        """
        更新重试配置
        
        Args:
            retry_config: 新的重试配置
            
        Returns:
            是否更新成功
        """
        try:
            # 验证配置
            self._validate_retry_config(retry_config)
            
            config = self.yaml_handler.load_config()
            business_rules = config.setdefault("business_rules", {})
            business_rules["retry"] = retry_config
            
            # 保存配置
            success = self.yaml_handler.save_config(config)
            
            if success:
                logger.info(f"更新重试配置成功: {retry_config}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新重试配置失败: {str(e)}")
            raise FileOperationError(f"更新重试配置失败: {str(e)}")
    
    async def get_requirement_collection_config(self) -> Dict[str, Any]:
        """
        获取需求采集配置
        
        Returns:
            需求采集配置
        """
        try:
            config = self.yaml_handler.load_config()
            business_rules = config.get("business_rules", {})
            requirement_config = business_rules.get("requirement_collection", {
                "min_focus_points": 3,
                "max_focus_points": 10,
                "completion_threshold": 0.8
            })
            
            logger.info("获取需求采集配置成功")
            return requirement_config
            
        except Exception as e:
            logger.error(f"获取需求采集配置失败: {str(e)}")
            raise FileOperationError(f"获取需求采集配置失败: {str(e)}")
    
    async def update_requirement_collection_config(self, requirement_config: Dict[str, Any]) -> bool:
        """
        更新需求采集配置
        
        Args:
            requirement_config: 新的需求采集配置
            
        Returns:
            是否更新成功
        """
        try:
            # 验证配置
            self._validate_requirement_collection_config(requirement_config)
            
            config = self.yaml_handler.load_config()
            business_rules = config.setdefault("business_rules", {})
            business_rules["requirement_collection"] = requirement_config
            
            # 保存配置
            success = self.yaml_handler.save_config(config)
            
            if success:
                logger.info(f"更新需求采集配置成功: {requirement_config}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新需求采集配置失败: {str(e)}")
            raise FileOperationError(f"更新需求采集配置失败: {str(e)}")
    
    async def update_business_rules(self, rules_update: Dict[str, Any]) -> bool:
        """
        批量更新业务规则配置
        
        Args:
            rules_update: 要更新的业务规则配置
            
        Returns:
            是否更新成功
        """
        try:
            config = self.yaml_handler.load_config()
            business_rules = config.setdefault("business_rules", {})
            
            # 逐个更新配置项
            for key, value in rules_update.items():
                if key == "focus_point_priority":
                    self._validate_focus_point_priority(value)
                elif key == "retry":
                    self._validate_retry_config(value)
                elif key == "requirement_collection":
                    self._validate_requirement_collection_config(value)
                elif key == "quality_control":
                    self._validate_quality_control_config(value)
                
                business_rules[key] = value
            
            # 保存配置
            success = self.yaml_handler.save_config(config)
            
            if success:
                logger.info(f"批量更新业务规则配置成功: {list(rules_update.keys())}")
            
            return success
            
        except Exception as e:
            logger.error(f"批量更新业务规则配置失败: {str(e)}")
            raise FileOperationError(f"批量更新业务规则配置失败: {str(e)}")
    
    async def validate_business_rules(self) -> Dict[str, Any]:
        """
        验证业务规则配置
        
        Returns:
            验证结果
        """
        try:
            config = self.yaml_handler.load_config()
            business_rules = config.get("business_rules", {})
            
            errors = []
            warnings = []
            
            # 验证关注点优先级配置
            focus_priority = business_rules.get("focus_point_priority", {})
            if not focus_priority:
                warnings.append("缺少关注点优先级配置")
            else:
                try:
                    self._validate_focus_point_priority(focus_priority)
                except ConfigValidationError as e:
                    errors.append(f"关注点优先级配置错误: {str(e)}")
            
            # 验证重试配置
            retry_config = business_rules.get("retry", {})
            if not retry_config:
                warnings.append("缺少重试配置")
            else:
                try:
                    self._validate_retry_config(retry_config)
                except ConfigValidationError as e:
                    errors.append(f"重试配置错误: {str(e)}")
            
            # 验证需求采集配置
            requirement_config = business_rules.get("requirement_collection", {})
            if not requirement_config:
                warnings.append("缺少需求采集配置")
            else:
                try:
                    self._validate_requirement_collection_config(requirement_config)
                except ConfigValidationError as e:
                    errors.append(f"需求采集配置错误: {str(e)}")
            
            result = {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "total_errors": len(errors),
                "total_warnings": len(warnings)
            }
            
            logger.info(f"业务规则配置验证完成: {len(errors)} 个错误, {len(warnings)} 个警告")
            return result
            
        except Exception as e:
            logger.error(f"业务规则配置验证失败: {str(e)}")
            raise FileOperationError(f"业务规则配置验证失败: {str(e)}")
    
    async def reload_business_rules(self) -> bool:
        """
        重新加载业务规则配置
        
        Returns:
            是否重新加载成功
        """
        try:
            # 这里可以添加重新加载逻辑，比如清除缓存、通知其他服务等
            # 目前只是验证配置文件是否可以正常加载
            config = self.yaml_handler.load_config()
            business_rules = config.get("business_rules", {})
            
            logger.info("业务规则配置重新加载成功")
            return True
            
        except Exception as e:
            logger.error(f"业务规则配置重新加载失败: {str(e)}")
            return False

    def _validate_focus_point_priority(self, config: Dict[str, bool]) -> None:
        """验证关注点优先级配置"""
        required_keys = ["p0", "p1", "p2"]
        for key in required_keys:
            if key not in config:
                raise ConfigValidationError(f"缺少关注点优先级配置项: {key}")
            if not isinstance(config[key], bool):
                raise ConfigValidationError(f"关注点优先级配置项 {key} 必须是布尔值")

    def _validate_retry_config(self, config: Dict[str, Any]) -> None:
        """验证重试配置"""
        if "max_pending_attempts" in config:
            if not isinstance(config["max_pending_attempts"], int) or config["max_pending_attempts"] < 1:
                raise ConfigValidationError("max_pending_attempts 必须是大于0的整数")

        if "max_total_attempts" in config:
            if not isinstance(config["max_total_attempts"], int) or config["max_total_attempts"] < 1:
                raise ConfigValidationError("max_total_attempts 必须是大于0的整数")

        if "backoff_factor" in config:
            if not isinstance(config["backoff_factor"], (int, float)) or config["backoff_factor"] <= 0:
                raise ConfigValidationError("backoff_factor 必须是大于0的数字")

    def _validate_requirement_collection_config(self, config: Dict[str, Any]) -> None:
        """验证需求采集配置"""
        if "min_focus_points" in config:
            if not isinstance(config["min_focus_points"], int) or config["min_focus_points"] < 1:
                raise ConfigValidationError("min_focus_points 必须是大于0的整数")

        if "max_focus_points" in config:
            if not isinstance(config["max_focus_points"], int) or config["max_focus_points"] < 1:
                raise ConfigValidationError("max_focus_points 必须是大于0的整数")

        if "completion_threshold" in config:
            if not isinstance(config["completion_threshold"], (int, float)) or not (0 <= config["completion_threshold"] <= 1):
                raise ConfigValidationError("completion_threshold 必须是0到1之间的数字")

        # 检查最小值不能大于最大值
        if ("min_focus_points" in config and "max_focus_points" in config and
            config["min_focus_points"] > config["max_focus_points"]):
            raise ConfigValidationError("min_focus_points 不能大于 max_focus_points")

    def _validate_quality_control_config(self, config: Dict[str, Any]) -> None:
        """验证质量控制配置"""
        if "min_input_length" in config:
            if not isinstance(config["min_input_length"], int) or config["min_input_length"] < 0:
                raise ConfigValidationError("min_input_length 必须是非负整数")

        if "max_input_length" in config:
            if not isinstance(config["max_input_length"], int) or config["max_input_length"] < 1:
                raise ConfigValidationError("max_input_length 必须是大于0的整数")

        if "spam_detection_enabled" in config:
            if not isinstance(config["spam_detection_enabled"], bool):
                raise ConfigValidationError("spam_detection_enabled 必须是布尔值")

        # 检查最小值不能大于最大值
        if ("min_input_length" in config and "max_input_length" in config and
            config["min_input_length"] > config["max_input_length"]):
            raise ConfigValidationError("min_input_length 不能大于 max_input_length")
