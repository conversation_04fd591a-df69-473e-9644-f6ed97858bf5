"""
数据库维护服务
提供数据库状态查看、表结构查询、数据统计等功能
"""

import sqlite3
import os
import logging
import json
import shutil
import zipfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from admin_utils.exceptions import DatabaseError, FileOperationError

logger = logging.getLogger(__name__)


@dataclass
class TableInfo:
    """表信息数据类"""
    name: str
    row_count: int
    size_kb: float
    last_modified: Optional[str] = None


@dataclass
class DatabaseStats:
    """数据库统计信息数据类"""
    total_size_mb: float
    table_count: int
    total_rows: int
    tables: List[TableInfo]
    last_vacuum: Optional[str] = None
    integrity_check: bool = True


class DatabaseService:
    """数据库维护服务类"""
    
    def __init__(self):
        """初始化数据库服务"""
        # 获取项目根目录下的backend路径
        project_root = Path(__file__).parent.parent.parent
        self.db_path = project_root / "backend" / "data" / "aidatabase.db"
        self.backup_dir = project_root / "admin-backend" / "backups"

        # 创建备份目录
        self.backup_dir.mkdir(exist_ok=True)

        if not self.db_path.exists():
            raise DatabaseError(f"数据库文件不存在: {self.db_path}")
    
    def _get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            conn.row_factory = sqlite3.Row
            return conn
        except sqlite3.Error as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise DatabaseError(f"数据库连接失败: {str(e)}")
    
    async def get_database_status(self) -> Dict[str, Any]:
        """
        获取数据库基本状态信息
        
        Returns:
            数据库状态信息
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 获取数据库文件大小
                file_size = self.db_path.stat().st_size / (1024 * 1024)  # MB
                
                # 获取数据库版本信息
                cursor.execute("SELECT sqlite_version()")
                sqlite_version = cursor.fetchone()[0]
                
                # 获取页面大小和页面数量
                cursor.execute("PRAGMA page_size")
                page_size = cursor.fetchone()[0]

                cursor.execute("PRAGMA page_count")
                page_count = cursor.fetchone()[0]

                # 获取空闲页面数量
                cursor.execute("PRAGMA freelist_count")
                freelist_count = cursor.fetchone()[0]

                # 获取实际数据使用情况 - 更准确的方法
                try:
                    # 计算所有表的实际数据页数
                    cursor.execute("""
                        SELECT name FROM sqlite_master
                        WHERE type='table' AND name NOT LIKE 'sqlite_%'
                    """)
                    tables = cursor.fetchall()

                    actual_used_pages = 0
                    for table in tables:
                        table_name = table[0]
                        try:
                            # 获取表的页数统计
                            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                            row_count = cursor.fetchone()[0]

                            # 估算该表使用的页数（粗略估算）
                            if row_count > 0:
                                # 假设每页平均存储的记录数（这是一个估算）
                                estimated_rows_per_page = max(1, page_size // 100)  # 假设每行平均100字节
                                table_pages = max(1, (row_count + estimated_rows_per_page - 1) // estimated_rows_per_page)
                                actual_used_pages += table_pages
                        except sqlite3.Error:
                            # 如果某个表查询失败，跳过
                            continue

                    # 如果实际计算失败，使用传统方法
                    if actual_used_pages == 0:
                        used_pages = page_count - freelist_count
                        usage_ratio = (used_pages / page_count * 100) if page_count > 0 else 0
                    else:
                        # 使用实际数据页数计算使用率
                        usage_ratio = (actual_used_pages / page_count * 100) if page_count > 0 else 0
                        used_pages = actual_used_pages

                except sqlite3.Error as e:
                    logger.warning(f"计算实际使用率失败，使用传统方法: {e}")
                    # 回退到传统计算方法
                    used_pages = page_count - freelist_count
                    usage_ratio = (used_pages / page_count * 100) if page_count > 0 else 0
                
                status = {
                    "database_path": str(self.db_path),
                    "file_size_mb": round(file_size, 2),
                    "sqlite_version": sqlite_version,
                    "page_size": page_size,
                    "total_pages": page_count,
                    "used_pages": used_pages,
                    "free_pages": freelist_count,
                    "usage_ratio": round(usage_ratio, 2),
                    "last_modified": datetime.fromtimestamp(
                        self.db_path.stat().st_mtime
                    ).isoformat(),
                    "is_accessible": True
                }
                
                logger.info("获取数据库状态成功")
                return status
                
        except Exception as e:
            logger.error(f"获取数据库状态失败: {str(e)}")
            raise DatabaseError(f"获取数据库状态失败: {str(e)}")

    async def optimize_database(self) -> Dict[str, Any]:
        """
        优化数据库（执行VACUUM操作）

        Returns:
            优化结果信息
        """
        try:
            # 获取优化前的状态
            before_status = await self.get_database_status()
            before_size = before_status["file_size_mb"]

            with self._get_connection() as conn:
                cursor = conn.cursor()

                logger.info("开始执行数据库优化（VACUUM）...")
                start_time = datetime.now()

                # 执行VACUUM操作
                cursor.execute("VACUUM")

                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

            # 获取优化后的状态
            after_status = await self.get_database_status()
            after_size = after_status["file_size_mb"]

            space_saved = before_size - after_size
            space_saved_percent = (space_saved / before_size * 100) if before_size > 0 else 0

            result = {
                "success": True,
                "duration_seconds": round(duration, 2),
                "before_size_mb": before_size,
                "after_size_mb": after_size,
                "space_saved_mb": round(space_saved, 2),
                "space_saved_percent": round(space_saved_percent, 2),
                "before_usage_ratio": before_status["usage_ratio"],
                "after_usage_ratio": after_status["usage_ratio"],
                "optimized_at": datetime.now().isoformat()
            }

            logger.info(f"数据库优化完成: 节省空间 {space_saved:.2f}MB ({space_saved_percent:.1f}%)")
            return result

        except Exception as e:
            logger.error(f"数据库优化失败: {str(e)}")
            raise DatabaseError(f"数据库优化失败: {str(e)}")
    
    async def get_table_list(self) -> List[Dict[str, Any]]:
        """
        获取所有表的列表和基本信息
        
        Returns:
            表信息列表
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 获取所有表名
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                    ORDER BY name
                """)
                
                tables = []
                for row in cursor.fetchall():
                    table_name = row[0]
                    
                    # 获取表的行数
                    cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    row_count = cursor.fetchone()[0]
                    
                    # 获取表结构信息
                    cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                    columns = cursor.fetchall()
                    
                    table_info = {
                        "name": table_name,
                        "row_count": row_count,
                        "column_count": len(columns),
                        "columns": [
                            {
                                "name": col[1],
                                "type": col[2],
                                "not_null": bool(col[3]),
                                "default_value": col[4],
                                "primary_key": bool(col[5])
                            }
                            for col in columns
                        ]
                    }
                    tables.append(table_info)
                
                logger.info(f"获取到 {len(tables)} 个表的信息")
                return tables
                
        except Exception as e:
            logger.error(f"获取表列表失败: {str(e)}")
            raise DatabaseError(f"获取表列表失败: {str(e)}")
    
    async def get_table_details(self, table_name: str) -> Dict[str, Any]:
        """
        获取指定表的详细信息
        
        Args:
            table_name: 表名
            
        Returns:
            表的详细信息
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 验证表是否存在
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name=?
                """, (table_name,))
                
                if not cursor.fetchone():
                    raise DatabaseError(f"表 '{table_name}' 不存在")
                
                # 获取表结构
                cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                columns = cursor.fetchall()
                
                # 获取索引信息
                cursor.execute(f"PRAGMA index_list(`{table_name}`)")
                indexes = cursor.fetchall()
                
                # 获取外键信息
                cursor.execute(f"PRAGMA foreign_key_list(`{table_name}`)")
                foreign_keys = cursor.fetchall()
                
                # 获取行数
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                row_count = cursor.fetchone()[0]
                
                # 获取创建语句
                cursor.execute("""
                    SELECT sql FROM sqlite_master 
                    WHERE type='table' AND name=?
                """, (table_name,))
                create_sql = cursor.fetchone()[0]
                
                table_details = {
                    "name": table_name,
                    "row_count": row_count,
                    "columns": [
                        {
                            "cid": col[0],
                            "name": col[1],
                            "type": col[2],
                            "not_null": bool(col[3]),
                            "default_value": col[4],
                            "primary_key": bool(col[5])
                        }
                        for col in columns
                    ],
                    "indexes": [
                        {
                            "name": idx[1],
                            "unique": bool(idx[2]),
                            "origin": idx[3],
                            "partial": bool(idx[4])
                        }
                        for idx in indexes
                    ],
                    "foreign_keys": [
                        {
                            "id": fk[0],
                            "seq": fk[1],
                            "table": fk[2],
                            "from_column": fk[3],
                            "to_column": fk[4],
                            "on_update": fk[5],
                            "on_delete": fk[6],
                            "match": fk[7]
                        }
                        for fk in foreign_keys
                    ],
                    "create_sql": create_sql
                }
                
                logger.info(f"获取表 '{table_name}' 详细信息成功")
                return table_details
                
        except Exception as e:
            logger.error(f"获取表详细信息失败: {str(e)}")
            raise DatabaseError(f"获取表详细信息失败: {str(e)}")
    
    async def get_database_statistics(self) -> Dict[str, Any]:
        """
        获取数据库统计信息
        
        Returns:
            数据库统计信息
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 获取所有表的统计信息
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                """)
                
                tables_stats = []
                total_rows = 0
                
                for row in cursor.fetchall():
                    table_name = row[0]
                    
                    # 获取行数
                    cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    row_count = cursor.fetchone()[0]
                    total_rows += row_count
                    
                    tables_stats.append({
                        "name": table_name,
                        "row_count": row_count
                    })
                
                # 获取数据库文件大小
                file_size_mb = self.db_path.stat().st_size / (1024 * 1024)
                
                # 执行完整性检查
                cursor.execute("PRAGMA integrity_check")
                integrity_result = cursor.fetchone()[0]
                integrity_ok = integrity_result == "ok"
                
                statistics = {
                    "total_size_mb": round(file_size_mb, 2),
                    "table_count": len(tables_stats),
                    "total_rows": total_rows,
                    "tables": tables_stats,
                    "integrity_check": integrity_ok,
                    "integrity_message": integrity_result,
                    "generated_at": datetime.now().isoformat()
                }
                
                logger.info("获取数据库统计信息成功")
                return statistics

        except Exception as e:
            logger.error(f"获取数据库统计信息失败: {str(e)}")
            raise DatabaseError(f"获取数据库统计信息失败: {str(e)}")

    async def get_table_data(
        self,
        table_name: str,
        page: int = 1,
        page_size: int = 20,
        search: Optional[str] = None,
        order_by: Optional[str] = None,
        order_direction: str = "DESC"
    ) -> Dict[str, Any]:
        """
        获取表数据（分页）

        Args:
            table_name: 表名
            page: 页码
            page_size: 每页记录数
            search: 搜索关键词
            order_by: 排序字段
            order_direction: 排序方向

        Returns:
            分页的表数据
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # 验证表是否存在
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name=?
                """, (table_name,))

                if not cursor.fetchone():
                    raise DatabaseError(f"表 '{table_name}' 不存在")

                # 获取表结构
                cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                columns_info = cursor.fetchall()
                columns = [col[1] for col in columns_info]  # 列名

                # 构建基础查询
                base_query = f"FROM `{table_name}`"
                where_clause = ""
                params = []

                # 添加搜索条件
                if search:
                    # 对所有文本列进行搜索
                    text_columns = []
                    for col_info in columns_info:
                        col_type = col_info[2].upper()
                        if 'TEXT' in col_type or 'VARCHAR' in col_type or 'CHAR' in col_type:
                            text_columns.append(col_info[1])

                    if text_columns:
                        search_conditions = []
                        for col in text_columns:
                            search_conditions.append(f"`{col}` LIKE ?")
                            params.append(f"%{search}%")
                        where_clause = f"WHERE {' OR '.join(search_conditions)}"

                # 构建排序
                order_clause = ""
                if order_by and order_by in columns:
                    order_clause = f"ORDER BY `{order_by}` {order_direction}"
                elif columns:
                    # 默认按第一列排序
                    order_clause = f"ORDER BY `{columns[0]}` {order_direction}"

                # 获取总记录数
                count_query = f"SELECT COUNT(*) {base_query} {where_clause}"
                cursor.execute(count_query, params)
                total_count = cursor.fetchone()[0]

                # 获取分页数据
                offset = (page - 1) * page_size
                data_query = f"""
                    SELECT * {base_query} {where_clause} {order_clause}
                    LIMIT ? OFFSET ?
                """
                cursor.execute(data_query, params + [page_size, offset])

                rows = cursor.fetchall()

                # 转换为字典格式
                data = []
                for row in rows:
                    row_dict = {}
                    for i, col in enumerate(columns):
                        value = row[i]
                        # 对敏感字段进行脱敏处理
                        if self._is_sensitive_field(table_name, col):
                            value = self._mask_sensitive_data(value)
                        row_dict[col] = value
                    data.append(row_dict)

                # 计算分页信息
                total_pages = (total_count + page_size - 1) // page_size

                result = {
                    "table_name": table_name,
                    "columns": columns,
                    "data": data,
                    "pagination": {
                        "page": page,
                        "page_size": page_size,
                        "total_count": total_count,
                        "total_pages": total_pages,
                        "has_next": page < total_pages,
                        "has_prev": page > 1
                    },
                    "search": search,
                    "order_by": order_by,
                    "order_direction": order_direction
                }

                logger.info(f"获取表 {table_name} 数据成功，页码: {page}")
                return result

        except Exception as e:
            logger.error(f"获取表数据失败: {str(e)}")
            raise DatabaseError(f"获取表数据失败: {str(e)}")

    def _is_sensitive_field(self, table_name: str, column_name: str) -> bool:
        """
        判断是否为敏感字段

        Args:
            table_name: 表名
            column_name: 列名

        Returns:
            是否为敏感字段
        """
        sensitive_patterns = {
            'password', 'pwd', 'secret', 'token', 'key', 'salt',
            'email', 'phone', 'mobile', 'address', 'id_card'
        }

        # 特定表的敏感字段
        table_sensitive_fields = {
            'admin_users': {'password_hash', 'salt', 'email'},
            'messages': {'content'},  # 对话内容可能包含敏感信息
            'documents': {'content', 'feedback'},  # 文档内容可能包含敏感信息
        }

        column_lower = column_name.lower()

        # 检查通用敏感字段模式
        for pattern in sensitive_patterns:
            if pattern in column_lower:
                return True

        # 检查特定表的敏感字段
        if table_name in table_sensitive_fields:
            return column_name in table_sensitive_fields[table_name]

        return False

    def _mask_sensitive_data(self, value: Any) -> str:
        """
        对敏感数据进行脱敏处理

        Args:
            value: 原始值

        Returns:
            脱敏后的值
        """
        if value is None:
            return None

        value_str = str(value)
        if len(value_str) <= 6:
            return "***"

        # 保留前2位和后2位，中间用*替代
        return value_str[:2] + "*" * (len(value_str) - 4) + value_str[-2:]

    async def delete_table_record(self, table_name: str, record_id: str) -> Dict[str, Any]:
        """
        删除表记录

        Args:
            table_name: 表名
            record_id: 记录ID

        Returns:
            删除结果
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # 验证表是否存在
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name=?
                """, (table_name,))

                if not cursor.fetchone():
                    raise DatabaseError(f"表 '{table_name}' 不存在")

                # 获取主键字段
                cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                columns_info = cursor.fetchall()
                primary_key = None

                for col_info in columns_info:
                    if col_info[5]:  # pk字段
                        primary_key = col_info[1]
                        break

                if not primary_key:
                    raise DatabaseError(f"表 '{table_name}' 没有主键，无法删除记录")

                # 检查记录是否存在
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE `{primary_key}` = ?", (record_id,))
                if cursor.fetchone()[0] == 0:
                    raise DatabaseError(f"记录不存在")

                # 删除记录
                cursor.execute(f"DELETE FROM `{table_name}` WHERE `{primary_key}` = ?", (record_id,))
                conn.commit()

                result = {
                    "table_name": table_name,
                    "deleted_record_id": record_id,
                    "primary_key": primary_key,
                    "deleted_at": datetime.now().isoformat()
                }

                logger.info(f"删除表 {table_name} 记录成功，ID: {record_id}")
                return result

        except Exception as e:
            logger.error(f"删除表记录失败: {str(e)}")
            raise DatabaseError(f"删除表记录失败: {str(e)}")

    async def update_table_record(
        self,
        table_name: str,
        record_id: str,
        update_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        更新表记录

        Args:
            table_name: 表名
            record_id: 记录ID
            update_data: 要更新的数据

        Returns:
            更新结果
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # 验证表是否存在
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name=?
                """, (table_name,))

                if not cursor.fetchone():
                    raise DatabaseError(f"表 '{table_name}' 不存在")

                # 获取可编辑字段
                editable_fields = await self.get_editable_fields(table_name)
                editable_field_names = {field['name'] for field in editable_fields}

                # 过滤出可编辑的字段
                filtered_data = {}
                for field, value in update_data.items():
                    if field in editable_field_names:
                        filtered_data[field] = value
                    else:
                        logger.warning(f"字段 '{field}' 不可编辑，已忽略")

                if not filtered_data:
                    raise DatabaseError("没有可更新的字段")

                # 获取主键字段
                cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                columns_info = cursor.fetchall()
                primary_key = None

                for col_info in columns_info:
                    if col_info[5]:  # pk字段
                        primary_key = col_info[1]
                        break

                if not primary_key:
                    raise DatabaseError(f"表 '{table_name}' 没有主键，无法更新记录")

                # 检查记录是否存在
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE `{primary_key}` = ?", (record_id,))
                if cursor.fetchone()[0] == 0:
                    raise DatabaseError(f"记录不存在")

                # 构建更新SQL
                set_clauses = []
                params = []
                for field, value in filtered_data.items():
                    set_clauses.append(f"`{field}` = ?")
                    params.append(value)

                # 添加updated_at字段（如果存在）
                cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                columns = [col[1] for col in cursor.fetchall()]
                if 'updated_at' in columns:
                    set_clauses.append("`updated_at` = ?")
                    params.append(datetime.now().isoformat())

                params.append(record_id)

                update_sql = f"""
                    UPDATE `{table_name}`
                    SET {', '.join(set_clauses)}
                    WHERE `{primary_key}` = ?
                """

                cursor.execute(update_sql, params)
                conn.commit()

                # 获取更新后的记录
                cursor.execute(f"SELECT * FROM `{table_name}` WHERE `{primary_key}` = ?", (record_id,))
                updated_record = cursor.fetchone()

                if updated_record:
                    columns = [col[1] for col in columns_info]
                    record_dict = {}
                    for i, col in enumerate(columns):
                        record_dict[col] = updated_record[i]
                else:
                    record_dict = {}

                result = {
                    "table_name": table_name,
                    "record_id": record_id,
                    "primary_key": primary_key,
                    "updated_fields": list(filtered_data.keys()),
                    "updated_record": record_dict,
                    "updated_at": datetime.now().isoformat()
                }

                logger.info(f"更新表 {table_name} 记录成功，ID: {record_id}")
                return result

        except Exception as e:
            logger.error(f"更新表记录失败: {str(e)}")
            raise DatabaseError(f"更新表记录失败: {str(e)}")

    async def get_editable_fields(self, table_name: str) -> List[Dict[str, Any]]:
        """
        获取表的可编辑字段

        Args:
            table_name: 表名

        Returns:
            可编辑字段列表
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # 验证表是否存在
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name=?
                """, (table_name,))

                if not cursor.fetchone():
                    raise DatabaseError(f"表 '{table_name}' 不存在")

                # 获取表结构
                cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                columns_info = cursor.fetchall()

                editable_fields = []

                for col_info in columns_info:
                    col_name = col_info[1]
                    col_type = col_info[2]
                    not_null = bool(col_info[3])
                    default_value = col_info[4]
                    is_primary_key = bool(col_info[5])

                    # 判断字段是否可编辑
                    if self._is_field_editable(table_name, col_name, is_primary_key):
                        field_info = {
                            "name": col_name,
                            "type": col_type,
                            "not_null": not_null,
                            "default_value": default_value,
                            "primary_key": is_primary_key,
                            "editable": True,
                            "field_type": self._get_field_type(col_type),
                            "validation_rules": self._get_validation_rules(table_name, col_name, col_type)
                        }
                        editable_fields.append(field_info)

                logger.info(f"获取表 {table_name} 可编辑字段成功")
                return editable_fields

        except Exception as e:
            logger.error(f"获取可编辑字段失败: {str(e)}")
            raise DatabaseError(f"获取可编辑字段失败: {str(e)}")

    def _is_field_editable(self, table_name: str, column_name: str, is_primary_key: bool) -> bool:
        """
        判断字段是否可编辑

        Args:
            table_name: 表名
            column_name: 列名
            is_primary_key: 是否为主键

        Returns:
            是否可编辑
        """
        # 主键不可编辑
        if is_primary_key:
            return False

        # 系统字段不可编辑
        system_fields = {
            'created_at', 'updated_at', 'last_activity_at', 'last_login',
            'password_hash', 'salt', 'token', 'secret'
        }

        if column_name in system_fields:
            return False

        # 特定表的不可编辑字段
        table_readonly_fields = {
            'messages': {'conversation_id', 'user_id', 'sender_type', 'content', 'message_type'},
            'documents': {'document_id', 'conversation_id', 'user_id', 'version', 'content'},
            'conversations': {'conversation_id', 'user_id', 'created_at'},
            'admin_users': {'username', 'password_hash', 'salt', 'created_at', 'last_login'},
        }

        if table_name in table_readonly_fields:
            if column_name in table_readonly_fields[table_name]:
                return False

        # 敏感字段不可编辑
        if self._is_sensitive_field(table_name, column_name):
            return False

        return True

    def _get_field_type(self, sql_type: str) -> str:
        """
        根据SQL类型确定前端字段类型

        Args:
            sql_type: SQL数据类型

        Returns:
            前端字段类型
        """
        sql_type_upper = sql_type.upper()

        if 'INT' in sql_type_upper:
            return 'number'
        elif 'REAL' in sql_type_upper or 'FLOAT' in sql_type_upper or 'DOUBLE' in sql_type_upper:
            return 'number'
        elif 'BOOL' in sql_type_upper:
            return 'boolean'
        elif 'TEXT' in sql_type_upper and 'LONG' in sql_type_upper:
            return 'textarea'
        elif 'TEXT' in sql_type_upper or 'VARCHAR' in sql_type_upper or 'CHAR' in sql_type_upper:
            return 'text'
        elif 'DATE' in sql_type_upper or 'TIME' in sql_type_upper:
            return 'datetime'
        else:
            return 'text'

    def _get_validation_rules(self, table_name: str, column_name: str, sql_type: str) -> Dict[str, Any]:
        """
        获取字段验证规则

        Args:
            table_name: 表名
            column_name: 列名
            sql_type: SQL数据类型

        Returns:
            验证规则
        """
        rules = {}

        # 特定字段的验证规则
        field_rules = {
            'status': {
                'type': 'select',
                'options': ['active', 'inactive', 'draft', 'confirmed', 'rejected', 'pending']
            },
            'priority': {
                'type': 'select',
                'options': ['low', 'medium', 'high', 'urgent']
            },
            'role': {
                'type': 'select',
                'options': ['admin', 'customer_service', 'manager', 'viewer']
            },
            'email': {
                'type': 'email',
                'pattern': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            }
        }

        if column_name in field_rules:
            rules.update(field_rules[column_name])

        # 根据SQL类型添加验证规则
        if 'VARCHAR' in sql_type.upper():
            # 提取长度限制
            import re
            match = re.search(r'VARCHAR\((\d+)\)', sql_type.upper())
            if match:
                rules['maxLength'] = int(match.group(1))

        return rules

    async def cleanup_table_data(
        self,
        table_name: str,
        cleanup_type: str,
        days_old: Optional[int] = 30
    ) -> Dict[str, Any]:
        """
        清理表数据

        Args:
            table_name: 表名
            cleanup_type: 清理类型
            days_old: 保留天数

        Returns:
            清理结果
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # 验证表是否存在
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name=?
                """, (table_name,))

                if not cursor.fetchone():
                    raise DatabaseError(f"表 '{table_name}' 不存在")

                deleted_count = 0

                if cleanup_type == "all":
                    # 清空整个表
                    cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    deleted_count = cursor.fetchone()[0]
                    cursor.execute(f"DELETE FROM `{table_name}`")

                elif cleanup_type == "old_data":
                    # 清理旧数据（基于时间字段）
                    time_columns = ['created_at', 'updated_at', 'timestamp']
                    time_column = None

                    cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                    columns_info = cursor.fetchall()

                    for col_info in columns_info:
                        if col_info[1] in time_columns:
                            time_column = col_info[1]
                            break

                    if time_column:
                        cutoff_date = datetime.now() - timedelta(days=days_old)
                        cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE `{time_column}` < ?",
                                     (cutoff_date.isoformat(),))
                        deleted_count = cursor.fetchone()[0]
                        cursor.execute(f"DELETE FROM `{table_name}` WHERE `{time_column}` < ?",
                                     (cutoff_date.isoformat(),))
                    else:
                        raise DatabaseError(f"表 '{table_name}' 没有时间字段，无法按时间清理")

                elif cleanup_type == "test_data":
                    # 清理测试数据（基于特定模式）
                    test_patterns = ['test_', 'demo_', 'sample_']
                    conditions = []
                    params = []

                    # 查找可能包含测试数据的文本字段
                    cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                    columns_info = cursor.fetchall()

                    text_columns = []
                    for col_info in columns_info:
                        col_type = col_info[2].upper()
                        if 'TEXT' in col_type or 'VARCHAR' in col_type:
                            text_columns.append(col_info[1])

                    for col in text_columns:
                        for pattern in test_patterns:
                            conditions.append(f"`{col}` LIKE ?")
                            params.append(f"{pattern}%")

                    if conditions:
                        where_clause = " OR ".join(conditions)
                        cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE {where_clause}", params)
                        deleted_count = cursor.fetchone()[0]
                        cursor.execute(f"DELETE FROM `{table_name}` WHERE {where_clause}", params)
                    else:
                        deleted_count = 0

                conn.commit()

                result = {
                    "table_name": table_name,
                    "cleanup_type": cleanup_type,
                    "deleted_count": deleted_count,
                    "days_old": days_old if cleanup_type == "old_data" else None,
                    "cleaned_at": datetime.now().isoformat()
                }

                logger.info(f"清理表 {table_name} 数据成功，删除 {deleted_count} 条记录")
                return result

        except Exception as e:
            logger.error(f"清理表数据失败: {str(e)}")
            raise DatabaseError(f"清理表数据失败: {str(e)}")

    async def create_backup(self, backup_name: Optional[str] = None) -> Dict[str, Any]:
        """
        创建数据库备份

        Args:
            backup_name: 备份名称，如果不提供则自动生成

        Returns:
            备份信息
        """
        try:
            # 生成备份文件名
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"aidatabase_backup_{timestamp}"

            backup_file = self.backup_dir / f"{backup_name}.db"
            backup_zip = self.backup_dir / f"{backup_name}.zip"

            # 创建数据库备份
            shutil.copy2(self.db_path, backup_file)

            # 创建压缩备份
            with zipfile.ZipFile(backup_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(backup_file, backup_file.name)

                # 添加备份元数据
                metadata = {
                    "backup_name": backup_name,
                    "original_db_path": str(self.db_path),
                    "backup_time": datetime.now().isoformat(),
                    "db_size_mb": round(self.db_path.stat().st_size / (1024 * 1024), 2),
                    "sqlite_version": None
                }

                # 获取SQLite版本
                try:
                    with self._get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute("SELECT sqlite_version()")
                        metadata["sqlite_version"] = cursor.fetchone()[0]
                except Exception:
                    pass

                zipf.writestr("backup_metadata.json", json.dumps(metadata, indent=2))

            # 删除临时的.db文件，保留压缩文件
            backup_file.unlink()

            backup_info = {
                "backup_name": backup_name,
                "backup_file": str(backup_zip),
                "file_size_mb": round(backup_zip.stat().st_size / (1024 * 1024), 2),
                "created_at": datetime.now().isoformat(),
                "status": "success"
            }

            logger.info(f"数据库备份创建成功: {backup_name}")
            return backup_info

        except Exception as e:
            logger.error(f"创建数据库备份失败: {str(e)}")
            raise DatabaseError(f"创建数据库备份失败: {str(e)}")

    async def list_backups(self) -> List[Dict[str, Any]]:
        """
        获取所有备份列表

        Returns:
            备份列表
        """
        try:
            backups = []

            for backup_file in self.backup_dir.glob("*.zip"):
                try:
                    # 读取备份元数据
                    with zipfile.ZipFile(backup_file, 'r') as zipf:
                        if "backup_metadata.json" in zipf.namelist():
                            metadata_content = zipf.read("backup_metadata.json").decode('utf-8')
                            metadata = json.loads(metadata_content)
                        else:
                            # 如果没有元数据，从文件名推断
                            metadata = {
                                "backup_name": backup_file.stem,
                                "backup_time": datetime.fromtimestamp(
                                    backup_file.stat().st_mtime
                                ).isoformat()
                            }

                    backup_info = {
                        "backup_name": metadata.get("backup_name", backup_file.stem),
                        "backup_file": str(backup_file),
                        "file_size_mb": round(backup_file.stat().st_size / (1024 * 1024), 2),
                        "created_at": metadata.get("backup_time"),
                        "db_size_mb": metadata.get("db_size_mb"),
                        "sqlite_version": metadata.get("sqlite_version")
                    }
                    backups.append(backup_info)

                except Exception as e:
                    logger.warning(f"读取备份文件 {backup_file} 失败: {str(e)}")
                    continue

            # 按创建时间排序
            backups.sort(key=lambda x: x.get("created_at", ""), reverse=True)

            logger.info(f"获取到 {len(backups)} 个备份文件")
            return backups

        except Exception as e:
            logger.error(f"获取备份列表失败: {str(e)}")
            raise DatabaseError(f"获取备份列表失败: {str(e)}")

    async def restore_backup(self, backup_name: str) -> Dict[str, Any]:
        """
        从备份恢复数据库

        Args:
            backup_name: 备份名称

        Returns:
            恢复结果信息
        """
        try:
            backup_zip = self.backup_dir / f"{backup_name}.zip"

            if not backup_zip.exists():
                raise DatabaseError(f"备份文件不存在: {backup_name}")

            # 创建当前数据库的备份
            current_backup_name = f"pre_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            await self.create_backup(current_backup_name)

            # 从压缩文件中提取数据库
            temp_db_file = self.backup_dir / f"temp_restore_{backup_name}.db"

            with zipfile.ZipFile(backup_zip, 'r') as zipf:
                # 查找数据库文件
                db_files = [f for f in zipf.namelist() if f.endswith('.db')]
                if not db_files:
                    raise DatabaseError("备份文件中没有找到数据库文件")

                # 提取数据库文件
                zipf.extract(db_files[0], self.backup_dir)
                extracted_file = self.backup_dir / db_files[0]
                extracted_file.rename(temp_db_file)

            # 验证提取的数据库文件
            try:
                with sqlite3.connect(str(temp_db_file)) as conn:
                    cursor = conn.cursor()
                    cursor.execute("PRAGMA integrity_check")
                    result = cursor.fetchone()[0]
                    if result != "ok":
                        raise DatabaseError(f"备份数据库完整性检查失败: {result}")
            except sqlite3.Error as e:
                raise DatabaseError(f"备份数据库文件损坏: {str(e)}")

            # 替换当前数据库
            backup_current = self.db_path.with_suffix('.db.backup')
            shutil.move(self.db_path, backup_current)
            shutil.move(temp_db_file, self.db_path)

            # 删除临时备份
            backup_current.unlink()

            restore_info = {
                "backup_name": backup_name,
                "restored_at": datetime.now().isoformat(),
                "pre_restore_backup": current_backup_name,
                "status": "success"
            }

            logger.info(f"数据库恢复成功: {backup_name}")
            return restore_info

        except Exception as e:
            logger.error(f"数据库恢复失败: {str(e)}")
            # 清理临时文件
            temp_files = [
                self.backup_dir / f"temp_restore_{backup_name}.db",
                self.db_path.with_suffix('.db.backup')
            ]
            for temp_file in temp_files:
                if temp_file.exists():
                    try:
                        temp_file.unlink()
                    except Exception:
                        pass
            raise DatabaseError(f"数据库恢复失败: {str(e)}")

    async def delete_backup(self, backup_name: str) -> Dict[str, Any]:
        """
        删除指定备份

        Args:
            backup_name: 备份名称

        Returns:
            删除结果信息
        """
        try:
            backup_zip = self.backup_dir / f"{backup_name}.zip"

            if not backup_zip.exists():
                raise DatabaseError(f"备份文件不存在: {backup_name}")

            file_size = backup_zip.stat().st_size
            backup_zip.unlink()

            delete_info = {
                "backup_name": backup_name,
                "deleted_at": datetime.now().isoformat(),
                "freed_space_mb": round(file_size / (1024 * 1024), 2),
                "status": "success"
            }

            logger.info(f"备份删除成功: {backup_name}")
            return delete_info

        except Exception as e:
            logger.error(f"删除备份失败: {str(e)}")
            raise DatabaseError(f"删除备份失败: {str(e)}")
