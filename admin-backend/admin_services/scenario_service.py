"""
场景映射管理服务
"""

import logging
from typing import Dict, Any, List, Optional

from admin_utils.yaml_handler import YAMLHandler
from admin_api.middleware.exception_handler import (
    ConfigValidationError, 
    FileOperationError
)

logger = logging.getLogger(__name__)


class ScenarioService:
    """场景映射管理服务"""
    
    def __init__(self):
        """初始化场景服务"""
        self.yaml_handler = YAMLHandler()
    
    async def get_all_scenarios(self) -> List[Dict[str, Any]]:
        """
        获取所有场景映射
        
        Returns:
            场景映射列表
        """
        try:
            config = self.yaml_handler.load_config()
            llm_config = config.get('llm', {})
            scenario_mapping = llm_config.get('scenario_mapping', {})
            scenario_params = llm_config.get('scenario_params', {})
            
            result = []
            for scenario_name, model_name in scenario_mapping.items():
                params = scenario_params.get(scenario_name, {})
                result.append({
                    "scenario_name": scenario_name,
                    "model_name": model_name,
                    "parameters": params
                })
            
            logger.info(f"获取到 {len(result)} 个场景映射")
            return result
            
        except Exception as e:
            logger.error(f"获取场景映射失败: {str(e)}")
            raise FileOperationError(f"获取场景映射失败: {str(e)}")
    
    async def get_scenario(self, scenario_name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定场景映射
        
        Args:
            scenario_name: 场景名称
            
        Returns:
            场景映射字典，如果不存在返回None
        """
        try:
            config = self.yaml_handler.load_config()
            llm_config = config.get('llm', {})
            scenario_mapping = llm_config.get('scenario_mapping', {})
            scenario_params = llm_config.get('scenario_params', {})
            
            if scenario_name not in scenario_mapping:
                return None
            
            model_name = scenario_mapping[scenario_name]
            params = scenario_params.get(scenario_name, {})
            
            result = {
                "scenario_name": scenario_name,
                "model_name": model_name,
                "parameters": params,
                "scenario": scenario_name,  # 兼容旧字段名
                "model": model_name        # 兼容旧字段名
            }
            
            logger.info(f"获取场景映射: {scenario_name}")
            return result
            
        except Exception as e:
            logger.error(f"获取场景映射失败: {str(e)}")
            raise FileOperationError(f"获取场景映射失败: {str(e)}")
    
    async def update_scenario(self, scenario_name: str, update_data: Dict[str, Any]) -> bool:
        """
        更新场景映射
        
        Args:
            scenario_name: 场景名称
            update_data: 更新数据
            
        Returns:
            是否更新成功
        """
        try:
            config = self.yaml_handler.load_config()
            llm_config = config.get('llm', {})
            scenario_mapping = llm_config.get('scenario_mapping', {})
            scenario_params = llm_config.get('scenario_params', {})
            
            if scenario_name not in scenario_mapping:
                return False
            
            # 更新模型映射
            if 'model' in update_data:
                scenario_mapping[scenario_name] = update_data['model']
            
            # 更新参数
            if 'parameters' in update_data:
                scenario_params[scenario_name] = update_data['parameters']
            
            # 保存配置
            success = self.yaml_handler.save_config(config)
            
            if success:
                logger.info(f"更新场景映射成功: {scenario_name}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新场景映射失败: {str(e)}")
            raise FileOperationError(f"更新场景映射失败: {str(e)}")
    
    async def get_available_models(self) -> List[str]:
        """
        获取可用的模型列表
        
        Returns:
            模型名称列表
        """
        try:
            config = self.yaml_handler.load_config()
            llm_config = config.get('llm', {})
            models = llm_config.get('models', {})
            
            return list(models.keys())
            
        except Exception as e:
            logger.error(f"获取可用模型列表失败: {str(e)}")
            raise FileOperationError(f"获取可用模型列表失败: {str(e)}")
    
    async def test_scenario(self, scenario_name: str, test_input: str) -> Dict[str, Any]:
        """
        测试场景配置
        
        Args:
            scenario_name: 场景名称
            test_input: 测试输入
            
        Returns:
            测试结果
        """
        try:
            # 获取场景配置
            scenario = await self.get_scenario(scenario_name)
            if not scenario:
                raise ConfigValidationError(f"场景 {scenario_name} 不存在")
            
            # 这里可以添加实际的场景测试逻辑
            # 目前返回模拟结果
            return {
                "success": True,
                "scenario": scenario_name,
                "model_used": scenario['model_name'],
                "parameters_used": scenario['parameters'],
                "test_input": test_input,
                "response": f"场景 {scenario_name} 测试成功，使用模型 {scenario['model_name']}"
            }
            
        except Exception as e:
            logger.error(f"场景测试失败: {str(e)}")
            return {
                "success": False,
                "scenario": scenario_name,
                "error": str(e)
            }
