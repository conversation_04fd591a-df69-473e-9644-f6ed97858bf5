"""
数据库维护管理路由
提供数据库状态查看、表结构查询、数据统计等API接口
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Dict, List, Any, Optional
from pydantic import BaseModel

from admin_services.database_service import DatabaseService
from admin_utils.exceptions import DatabaseError, FileOperationError


class BackupRequest(BaseModel):
    """备份请求模型"""
    backup_name: Optional[str] = None


class RestoreRequest(BaseModel):
    """恢复请求模型"""
    backup_name: str

router = APIRouter()

# 初始化服务
database_service = DatabaseService()


class DatabaseStatusResponse(BaseModel):
    """数据库状态响应模型"""
    database_path: str
    file_size_mb: float
    sqlite_version: str
    page_size: int
    total_pages: int
    used_pages: int
    free_pages: int
    usage_ratio: float
    last_modified: str
    is_accessible: bool


class TableColumnInfo(BaseModel):
    """表列信息模型"""
    name: str
    type: str
    not_null: bool
    default_value: Optional[str]
    primary_key: bool


class TableBasicInfo(BaseModel):
    """表基本信息模型"""
    name: str
    row_count: int
    column_count: int
    columns: List[TableColumnInfo]


class TableIndexInfo(BaseModel):
    """表索引信息模型"""
    name: str
    unique: bool
    origin: str
    partial: bool


class TableForeignKeyInfo(BaseModel):
    """表外键信息模型"""
    id: int
    seq: int
    table: str
    from_column: str
    to_column: str
    on_update: str
    on_delete: str
    match: str


class TableDetailInfo(BaseModel):
    """表详细信息模型"""
    name: str
    row_count: int
    columns: List[Dict[str, Any]]
    indexes: List[TableIndexInfo]
    foreign_keys: List[TableForeignKeyInfo]
    create_sql: str


class DatabaseStatistics(BaseModel):
    """数据库统计信息模型"""
    total_size_mb: float
    table_count: int
    total_rows: int
    tables: List[Dict[str, Any]]
    integrity_check: bool
    integrity_message: str
    generated_at: str


@router.get("/status", response_model=DatabaseStatusResponse, summary="获取数据库状态")
async def get_database_status():
    """
    获取数据库基本状态信息
    
    Returns:
        数据库状态信息，包括文件大小、版本、页面使用情况等
    """
    try:
        status = await database_service.get_database_status()
        return DatabaseStatusResponse(**status)
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据库状态失败: {str(e)}")


@router.get("/tables", response_model=List[TableBasicInfo], summary="获取所有表信息")
async def get_table_list():
    """
    获取数据库中所有表的基本信息
    
    Returns:
        表信息列表，包括表名、行数、列数等
    """
    try:
        tables = await database_service.get_table_list()
        return [TableBasicInfo(**table) for table in tables]
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取表列表失败: {str(e)}")


@router.get("/tables/{table_name}", response_model=TableDetailInfo, summary="获取表详细信息")
async def get_table_details(table_name: str):
    """
    获取指定表的详细信息

    Args:
        table_name: 表名

    Returns:
        表的详细信息，包括列结构、索引、外键等
    """
    try:
        details = await database_service.get_table_details(table_name)
        return TableDetailInfo(**details)
    except DatabaseError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取表详细信息失败: {str(e)}")


@router.get("/tables/{table_name}/data", summary="获取表数据")
async def get_table_data(
    table_name: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页记录数"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    order_by: Optional[str] = Query(None, description="排序字段"),
    order_direction: str = Query("DESC", regex="^(ASC|DESC)$", description="排序方向")
):
    """
    获取表数据（分页）

    Args:
        table_name: 表名
        page: 页码
        page_size: 每页记录数
        search: 搜索关键词
        order_by: 排序字段
        order_direction: 排序方向

    Returns:
        分页的表数据
    """
    try:
        data = await database_service.get_table_data(
            table_name=table_name,
            page=page,
            page_size=page_size,
            search=search,
            order_by=order_by,
            order_direction=order_direction
        )
        return data
    except DatabaseError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取表数据失败: {str(e)}")


@router.delete("/tables/{table_name}/data/{record_id}", summary="删除表记录")
async def delete_table_record(table_name: str, record_id: str):
    """
    删除指定表的记录

    Args:
        table_name: 表名
        record_id: 记录ID

    Returns:
        删除结果
    """
    try:
        result = await database_service.delete_table_record(table_name, record_id)
        return {
            "success": True,
            "message": f"记录删除成功",
            "data": result
        }
    except DatabaseError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除记录失败: {str(e)}")


@router.put("/tables/{table_name}/data/{record_id}", summary="编辑表记录")
async def update_table_record(
    table_name: str,
    record_id: str,
    update_data: Dict[str, Any]
):
    """
    编辑指定表的记录

    Args:
        table_name: 表名
        record_id: 记录ID
        update_data: 要更新的数据

    Returns:
        更新结果
    """
    try:
        result = await database_service.update_table_record(
            table_name=table_name,
            record_id=record_id,
            update_data=update_data
        )
        return {
            "success": True,
            "message": f"记录更新成功",
            "data": result
        }
    except DatabaseError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新记录失败: {str(e)}")


@router.get("/tables/{table_name}/editable-fields", summary="获取可编辑字段")
async def get_editable_fields(table_name: str):
    """
    获取指定表的可编辑字段列表

    Args:
        table_name: 表名

    Returns:
        可编辑字段信息
    """
    try:
        fields = await database_service.get_editable_fields(table_name)
        return {
            "table_name": table_name,
            "editable_fields": fields
        }
    except DatabaseError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取可编辑字段失败: {str(e)}")


@router.post("/tables/{table_name}/cleanup", summary="清理表数据")
async def cleanup_table_data(
    table_name: str,
    cleanup_type: str = Query(..., regex="^(old_data|test_data|all)$", description="清理类型"),
    days_old: Optional[int] = Query(30, ge=1, description="保留天数（仅对old_data有效）")
):
    """
    清理表数据

    Args:
        table_name: 表名
        cleanup_type: 清理类型 (old_data: 清理旧数据, test_data: 清理测试数据, all: 清空表)
        days_old: 保留天数

    Returns:
        清理结果
    """
    try:
        result = await database_service.cleanup_table_data(
            table_name=table_name,
            cleanup_type=cleanup_type,
            days_old=days_old
        )
        return {
            "success": True,
            "message": f"数据清理完成",
            "data": result
        }
    except DatabaseError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据清理失败: {str(e)}")


@router.get("/statistics", response_model=DatabaseStatistics, summary="获取数据库统计信息")
async def get_database_statistics():
    """
    获取数据库统计信息
    
    Returns:
        数据库统计信息，包括总大小、表数量、行数等
    """
    try:
        stats = await database_service.get_database_statistics()
        return DatabaseStatistics(**stats)
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据库统计信息失败: {str(e)}")


@router.get("/tables/{table_name}/data", summary="获取表数据预览")
async def get_table_data(
    table_name: str,
    limit: int = Query(default=100, ge=1, le=1000, description="返回记录数量限制"),
    offset: int = Query(default=0, ge=0, description="偏移量")
):
    """
    获取指定表的数据预览
    
    Args:
        table_name: 表名
        limit: 返回记录数量限制（1-1000）
        offset: 偏移量
        
    Returns:
        表数据预览
    """
    try:
        # 这里需要在DatabaseService中添加相应方法
        # 暂时返回基本信息
        details = await database_service.get_table_details(table_name)
        return {
            "table_name": table_name,
            "total_rows": details["row_count"],
            "limit": limit,
            "offset": offset,
            "message": "数据预览功能待实现"
        }
    except DatabaseError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取表数据失败: {str(e)}")


@router.post("/integrity-check", summary="执行数据库完整性检查")
async def run_integrity_check():
    """
    执行数据库完整性检查
    
    Returns:
        完整性检查结果
    """
    try:
        # 这里需要在DatabaseService中添加相应方法
        stats = await database_service.get_database_statistics()
        return {
            "integrity_check": stats["integrity_check"],
            "integrity_message": stats["integrity_message"],
            "checked_at": stats["generated_at"]
        }
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"完整性检查失败: {str(e)}")


@router.post("/optimize", summary="优化数据库")
async def optimize_database():
    """
    优化数据库（执行VACUUM操作）

    Returns:
        优化结果信息
    """
    try:
        result = await database_service.optimize_database()
        return {
            "success": True,
            "message": "数据库优化完成",
            "data": result
        }
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库优化失败: {str(e)}")


@router.get("/health", summary="数据库健康检查")
async def database_health_check():
    """
    数据库健康检查

    Returns:
        数据库健康状态
    """
    try:
        status = await database_service.get_database_status()
        stats = await database_service.get_database_statistics()

        # 简单的健康评估
        health_score = 100
        issues = []

        # 检查使用率 - 调整阈值，因为我们改进了计算方法
        if status["usage_ratio"] > 85:
            health_score -= 20
            issues.append("数据库使用率过高，建议执行优化")
        elif status["usage_ratio"] > 70:
            health_score -= 10
            issues.append("数据库使用率较高")

        # 检查完整性
        if not stats["integrity_check"]:
            health_score -= 50
            issues.append("数据库完整性检查失败")

        # 检查文件大小
        if status["file_size_mb"] > 1000:  # 1GB
            health_score -= 10
            issues.append("数据库文件较大，建议优化")

        # 检查空闲页面比例
        if status["free_pages"] == 0 and status["total_pages"] > 100:
            health_score -= 5
            issues.append("数据库没有空闲页面，建议执行优化")

        health_status = "healthy" if health_score >= 80 else "warning" if health_score >= 60 else "critical"

        return {
            "status": health_status,
            "health_score": health_score,
            "issues": issues,
            "database_accessible": status["is_accessible"],
            "file_size_mb": status["file_size_mb"],
            "usage_ratio": status["usage_ratio"],
            "integrity_ok": stats["integrity_check"],
            "checked_at": stats["generated_at"]
        }
    except Exception as e:
        return {
            "status": "error",
            "health_score": 0,
            "issues": [f"健康检查失败: {str(e)}"],
            "database_accessible": False,
            "checked_at": None
        }


# 备份和恢复相关API

@router.post("/backup", summary="创建数据库备份")
async def create_database_backup(request: BackupRequest):
    """
    创建数据库备份

    Args:
        request: 备份请求，包含可选的备份名称

    Returns:
        备份创建结果
    """
    try:
        backup_info = await database_service.create_backup(request.backup_name)
        return {
            "success": True,
            "message": "数据库备份创建成功",
            "data": backup_info
        }
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建备份失败: {str(e)}")


@router.get("/backups", summary="获取备份列表")
async def get_backup_list():
    """
    获取所有数据库备份列表

    Returns:
        备份列表
    """
    try:
        backups = await database_service.list_backups()
        return {
            "success": True,
            "data": backups,
            "total": len(backups)
        }
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取备份列表失败: {str(e)}")


@router.post("/restore", summary="从备份恢复数据库")
async def restore_database(request: RestoreRequest):
    """
    从备份恢复数据库

    Args:
        request: 恢复请求，包含备份名称

    Returns:
        恢复结果
    """
    try:
        restore_info = await database_service.restore_backup(request.backup_name)
        return {
            "success": True,
            "message": "数据库恢复成功",
            "data": restore_info
        }
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库恢复失败: {str(e)}")


@router.delete("/backups/{backup_name}", summary="删除指定备份")
async def delete_backup(backup_name: str):
    """
    删除指定的数据库备份

    Args:
        backup_name: 备份名称

    Returns:
        删除结果
    """
    try:
        delete_info = await database_service.delete_backup(backup_name)
        return {
            "success": True,
            "message": "备份删除成功",
            "data": delete_info
        }
    except DatabaseError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除备份失败: {str(e)}")


# 注意：错误处理应该在应用级别配置，这里移除了router级别的异常处理器
