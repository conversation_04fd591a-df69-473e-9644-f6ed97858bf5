"""
模板管理路由
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, List
from pydantic import BaseModel

from admin_services.template_service import TemplateService

router = APIRouter()


class PromptTemplateResponse(BaseModel):
    """提示词模板响应模型"""
    filename: str
    content: str
    last_modified: str
    size: int


class MessageTemplateResponse(BaseModel):
    """消息模板响应模型"""
    path: str
    content: Any
    template_type: str  # string, object, array


class TemplateUpdateRequest(BaseModel):
    """模板更新请求模型"""
    content: str


class TemplateTestRequest(BaseModel):
    """模板测试请求模型"""
    template_name: str
    template_type: str  # prompt, message
    test_data: Dict[str, Any] = {}


class TemplateTestResponse(BaseModel):
    """模板测试响应模型"""
    success: bool
    rendered_content: str = None
    error: str = None


def get_template_service() -> TemplateService:
    """获取模板服务实例"""
    return TemplateService()


# 提示词模板相关路由
@router.get("/prompts", response_model=List[PromptTemplateResponse])
async def get_all_prompt_templates(
    template_service: TemplateService = Depends(get_template_service)
):
    """获取所有提示词模板"""
    try:
        templates = await template_service.get_prompt_templates()
        return templates
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取提示词模板失败: {str(e)}")


@router.get("/prompts/{filename}", response_model=PromptTemplateResponse)
async def get_prompt_template(
    filename: str,
    template_service: TemplateService = Depends(get_template_service)
):
    """获取指定提示词模板"""
    try:
        template = await template_service.get_prompt_template(filename)
        if not template:
            raise HTTPException(status_code=404, detail=f"模板文件 {filename} 不存在")
        return template
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取提示词模板失败: {str(e)}")


@router.put("/prompts/{filename}")
async def update_prompt_template(
    filename: str,
    update_request: TemplateUpdateRequest,
    template_service: TemplateService = Depends(get_template_service)
):
    """更新提示词模板"""
    try:
        success = await template_service.update_prompt_template(filename, update_request.content)
        if not success:
            raise HTTPException(status_code=404, detail=f"模板文件 {filename} 不存在")
        return {"message": f"提示词模板 {filename} 更新成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新提示词模板失败: {str(e)}")


# 消息模板相关路由
@router.get("/messages", response_model=List[MessageTemplateResponse])
async def get_all_message_templates(
    template_service: TemplateService = Depends(get_template_service)
):
    """获取所有消息模板"""
    try:
        templates = await template_service.get_message_templates()
        return templates
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取消息模板失败: {str(e)}")


@router.get("/messages/{path:path}", response_model=MessageTemplateResponse)
async def get_message_template(
    path: str,
    template_service: TemplateService = Depends(get_template_service)
):
    """获取指定消息模板"""
    try:
        template = await template_service.get_message_template(path)
        if not template:
            raise HTTPException(status_code=404, detail=f"消息模板 {path} 不存在")
        return template
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取消息模板失败: {str(e)}")


@router.put("/messages/{path:path}")
async def update_message_template(
    path: str,
    update_request: TemplateUpdateRequest,
    template_service: TemplateService = Depends(get_template_service)
):
    """更新消息模板"""
    try:
        success = await template_service.update_message_template(path, update_request.content)
        if not success:
            raise HTTPException(status_code=404, detail=f"消息模板 {path} 不存在")
        return {"message": f"消息模板 {path} 更新成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新消息模板失败: {str(e)}")


# 模板测试相关路由
@router.post("/test", response_model=TemplateTestResponse)
async def test_template(
    test_request: TemplateTestRequest,
    template_service: TemplateService = Depends(get_template_service)
):
    """测试模板渲染"""
    try:
        result = await template_service.test_template(
            test_request.template_name,
            test_request.template_type,
            test_request.test_data
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模板测试失败: {str(e)}")


@router.post("/validate")
async def validate_template_syntax(
    template_service: TemplateService = Depends(get_template_service)
):
    """验证模板语法"""
    try:
        result = await template_service.validate_all_templates()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模板验证失败: {str(e)}")