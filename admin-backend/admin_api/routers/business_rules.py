"""
业务规则配置管理路由
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, List
from pydantic import BaseModel

from admin_services.business_rules_service import BusinessRulesService
from admin_api.middleware.exception_handler import ConfigValidationError, FileOperationError

router = APIRouter()


class FocusPointPriorityConfig(BaseModel):
    """关注点优先级配置模型"""
    p0: bool = True
    p1: bool = True
    p2: bool = False


class RetryConfig(BaseModel):
    """重试配置模型"""
    max_pending_attempts: int = 3
    max_total_attempts: int = 5
    backoff_factor: float = 1.5


class RequirementCollectionConfig(BaseModel):
    """需求采集配置模型"""
    min_focus_points: int = 3
    max_focus_points: int = 10
    completion_threshold: float = 0.8


class QualityControlConfig(BaseModel):
    """质量控制配置模型"""
    min_input_length: int = 2
    max_input_length: int = 1000
    spam_detection_enabled: bool = True


class BusinessRulesResponse(BaseModel):
    """业务规则响应模型"""
    focus_point_priority: FocusPointPriorityConfig
    retry: RetryConfig
    requirement_collection: RequirementCollectionConfig
    quality_control: QualityControlConfig


class BusinessRulesUpdate(BaseModel):
    """业务规则更新模型"""
    focus_point_priority: FocusPointPriorityConfig = None
    retry: RetryConfig = None
    requirement_collection: RequirementCollectionConfig = None
    quality_control: QualityControlConfig = None


def get_business_rules_service() -> BusinessRulesService:
    """获取业务规则服务实例"""
    return BusinessRulesService()


@router.get("/", response_model=BusinessRulesResponse)
async def get_business_rules(
    service: BusinessRulesService = Depends(get_business_rules_service)
):
    """获取所有业务规则配置"""
    try:
        rules = await service.get_all_business_rules()
        return rules
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取业务规则配置失败: {str(e)}")


@router.get("/focus-point-priority", response_model=FocusPointPriorityConfig)
async def get_focus_point_priority(
    service: BusinessRulesService = Depends(get_business_rules_service)
):
    """获取关注点优先级配置"""
    try:
        config = await service.get_focus_point_priority()
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取关注点优先级配置失败: {str(e)}")


@router.put("/focus-point-priority")
async def update_focus_point_priority(
    config: FocusPointPriorityConfig,
    service: BusinessRulesService = Depends(get_business_rules_service)
):
    """更新关注点优先级配置"""
    try:
        success = await service.update_focus_point_priority(config.dict())
        if success:
            return {"message": "关注点优先级配置更新成功"}
        else:
            raise HTTPException(status_code=500, detail="关注点优先级配置更新失败")
    except ConfigValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新关注点优先级配置失败: {str(e)}")


@router.get("/retry", response_model=RetryConfig)
async def get_retry_config(
    service: BusinessRulesService = Depends(get_business_rules_service)
):
    """获取重试配置"""
    try:
        config = await service.get_retry_config()
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取重试配置失败: {str(e)}")


@router.put("/retry")
async def update_retry_config(
    config: RetryConfig,
    service: BusinessRulesService = Depends(get_business_rules_service)
):
    """更新重试配置"""
    try:
        success = await service.update_retry_config(config.dict())
        if success:
            return {"message": "重试配置更新成功"}
        else:
            raise HTTPException(status_code=500, detail="重试配置更新失败")
    except ConfigValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新重试配置失败: {str(e)}")


@router.get("/requirement-collection", response_model=RequirementCollectionConfig)
async def get_requirement_collection_config(
    service: BusinessRulesService = Depends(get_business_rules_service)
):
    """获取需求采集配置"""
    try:
        config = await service.get_requirement_collection_config()
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取需求采集配置失败: {str(e)}")


@router.put("/requirement-collection")
async def update_requirement_collection_config(
    config: RequirementCollectionConfig,
    service: BusinessRulesService = Depends(get_business_rules_service)
):
    """更新需求采集配置"""
    try:
        success = await service.update_requirement_collection_config(config.dict())
        if success:
            return {"message": "需求采集配置更新成功"}
        else:
            raise HTTPException(status_code=500, detail="需求采集配置更新失败")
    except ConfigValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新需求采集配置失败: {str(e)}")


@router.put("/")
async def update_business_rules(
    rules: BusinessRulesUpdate,
    service: BusinessRulesService = Depends(get_business_rules_service)
):
    """批量更新业务规则配置"""
    try:
        # 过滤掉None值
        update_data = {k: v for k, v in rules.dict().items() if v is not None}
        
        success = await service.update_business_rules(update_data)
        if success:
            return {"message": "业务规则配置更新成功"}
        else:
            raise HTTPException(status_code=500, detail="业务规则配置更新失败")
    except ConfigValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新业务规则配置失败: {str(e)}")


@router.post("/validate")
async def validate_business_rules(
    service: BusinessRulesService = Depends(get_business_rules_service)
):
    """验证业务规则配置"""
    try:
        result = await service.validate_business_rules()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"业务规则配置验证失败: {str(e)}")


@router.post("/reload")
async def reload_business_rules(
    service: BusinessRulesService = Depends(get_business_rules_service)
):
    """重新加载业务规则配置"""
    try:
        success = await service.reload_business_rules()
        if success:
            return {"message": "业务规则配置重新加载成功"}
        else:
            raise HTTPException(status_code=500, detail="业务规则配置重新加载失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重新加载业务规则配置失败: {str(e)}")
