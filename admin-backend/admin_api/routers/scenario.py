"""
场景映射管理路由
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, List
from pydantic import BaseModel

from admin_services.scenario_service import ScenarioService

router = APIRouter()


class ScenarioMappingResponse(BaseModel):
    """场景映射响应模型"""
    scenario_name: str
    model_name: str
    parameters: Dict[str, Any]
    # 兼容旧字段名
    scenario: str = None
    model: str = None


class ScenarioMappingUpdate(BaseModel):
    """场景映射更新模型"""
    model_name: str = None
    parameters: Dict[str, Any] = None


class ScenarioTestRequest(BaseModel):
    """场景测试请求模型"""
    test_input: str


class ScenarioTestResponse(BaseModel):
    """场景测试响应模型"""
    success: bool
    response: str = None
    error: str = None
    model_used: str = None
    parameters_used: Dict[str, Any] = None


def get_scenario_service() -> ScenarioService:
    """获取场景服务实例"""
    return ScenarioService()


@router.get("/mappings", response_model=List[ScenarioMappingResponse])
async def get_all_scenario_mappings(
    scenario_service: ScenarioService = Depends(get_scenario_service)
):
    """获取所有场景映射"""
    try:
        mappings = await scenario_service.get_all_scenarios()
        return mappings
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取场景映射失败: {str(e)}")


@router.get("/mappings/{scenario_name}", response_model=ScenarioMappingResponse)
async def get_scenario_mapping(
    scenario_name: str,
    scenario_service: ScenarioService = Depends(get_scenario_service)
):
    """获取指定场景映射"""
    try:
        mapping = await scenario_service.get_scenario(scenario_name)
        if not mapping:
            raise HTTPException(status_code=404, detail=f"场景 {scenario_name} 不存在")
        return mapping
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取场景映射失败: {str(e)}")


@router.put("/mappings/{scenario_name}")
async def update_scenario_mapping(
    scenario_name: str,
    update_data: ScenarioMappingUpdate,
    scenario_service: ScenarioService = Depends(get_scenario_service)
):
    """更新场景映射"""
    try:
        success = await scenario_service.update_scenario(
            scenario_name,
            update_data.model_dump(exclude_unset=True)
        )
        if not success:
            raise HTTPException(status_code=404, detail=f"场景 {scenario_name} 不存在")
        return {"message": f"场景 {scenario_name} 映射更新成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新场景映射失败: {str(e)}")


@router.post("/{scenario_name}/test", response_model=ScenarioTestResponse)
async def test_scenario_mapping(
    scenario_name: str,
    test_request: ScenarioTestRequest,
    scenario_service: ScenarioService = Depends(get_scenario_service)
):
    """测试场景映射"""
    try:
        result = await scenario_service.test_scenario(scenario_name, test_request.test_input)
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"场景测试失败: {str(e)}")


@router.get("/available-models")
async def get_available_models(
    scenario_service: ScenarioService = Depends(get_scenario_service)
):
    """获取可用的模型列表"""
    try:
        models = await scenario_service.get_available_models()
        return {"models": models}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取可用模型失败: {str(e)}")
