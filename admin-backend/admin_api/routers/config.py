"""
LLM配置管理路由
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, List
from pydantic import BaseModel

from admin_services.config_service import ConfigService
from admin_api.middleware.exception_handler import ConfigValidationError, FileOperationError

router = APIRouter()


class LLMModelResponse(BaseModel):
    """LLM模型响应模型"""
    name: str
    provider: str
    api_key_masked: str  # 脱敏后的API密钥
    api_base: str
    model_name: str
    temperature: float
    max_tokens: int
    timeout: int
    max_retries: int


class LLMModelUpdate(BaseModel):
    """LLM模型更新模型"""
    provider: str = None
    api_key: str = None
    api_base: str = None
    model_name: str = None
    temperature: float = None
    max_tokens: int = None
    timeout: int = None
    max_retries: int = None


class ConnectionTestRequest(BaseModel):
    """连接测试请求模型"""
    test_prompt: str = "Hello, this is a connection test."


class ConnectionTestResponse(BaseModel):
    """连接测试响应模型"""
    success: bool
    response: str | None = None
    error: str | None = None
    latency: float | None = None


def get_config_service() -> ConfigService:
    """获取配置服务实例"""
    return ConfigService()


@router.get("/llm", response_model=List[LLMModelResponse])
async def get_all_llm_models(
    config_service: ConfigService = Depends(get_config_service)
):
    """获取所有LLM模型配置"""
    try:
        models = await config_service.get_all_llm_models()
        return models
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取LLM配置失败: {str(e)}")


@router.get("/llm/{model_name}", response_model=LLMModelResponse)
async def get_llm_model(
    model_name: str,
    config_service: ConfigService = Depends(get_config_service)
):
    """获取指定LLM模型配置"""
    try:
        model = await config_service.get_llm_model(model_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"模型 {model_name} 不存在")
        return model
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型配置失败: {str(e)}")


@router.put("/llm/{model_name}")
async def update_llm_model(
    model_name: str,
    update_data: LLMModelUpdate,
    config_service: ConfigService = Depends(get_config_service)
):
    """更新LLM模型配置"""
    try:
        success = await config_service.update_llm_model(model_name, update_data.model_dump(exclude_unset=True))
        if not success:
            raise HTTPException(status_code=404, detail=f"模型 {model_name} 不存在")
        return {"message": f"模型 {model_name} 配置更新成功"}
    except ConfigValidationError as e:
        raise HTTPException(status_code=400, detail=f"配置验证失败: {str(e)}")
    except FileOperationError as e:
        raise HTTPException(status_code=500, detail=f"文件操作失败: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")


@router.post("/llm")
async def create_llm_model(
    model_data: Dict[str, Any],
    config_service: ConfigService = Depends(get_config_service)
):
    """创建新的LLM模型配置"""
    try:
        success = await config_service.create_llm_model(model_data)
        if not success:
            raise HTTPException(status_code=400, detail="创建模型配置失败")
        return {"message": "模型配置创建成功"}
    except ConfigValidationError as e:
        raise HTTPException(status_code=400, detail=f"配置验证失败: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建配置失败: {str(e)}")


@router.post("/llm/{model_name}/test", response_model=ConnectionTestResponse)
async def test_llm_connection(
    model_name: str,
    test_request: ConnectionTestRequest,
    config_service: ConfigService = Depends(get_config_service)
):
    """测试LLM模型连接"""
    try:
        result = await config_service.test_model_connection(model_name, test_request.test_prompt)
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"连接测试失败: {str(e)}")


@router.post("/reload")
async def reload_config(
    config_service: ConfigService = Depends(get_config_service)
):
    """重新加载配置"""
    try:
        success = await config_service.reload_config()
        if success:
            return {"message": "配置重新加载成功"}
        else:
            raise HTTPException(status_code=500, detail="配置重新加载失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重新加载配置失败: {str(e)}")


@router.post("/validate")
async def validate_config(
    config_service: ConfigService = Depends(get_config_service)
):
    """验证配置文件"""
    try:
        result = await config_service.validate_config()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置验证失败: {str(e)}")
