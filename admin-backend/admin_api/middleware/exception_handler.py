"""
统一异常处理中间件
"""

from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
import logging
from datetime import datetime
from typing import Any, Dict

logger = logging.getLogger(__name__)


class AdminException(Exception):
    """管理系统基础异常类"""
    def __init__(self, message: str, code: str = "ADMIN_ERROR", status_code: int = 400):
        self.message = message
        self.code = code
        self.status_code = status_code
        super().__init__(self.message)


class ConfigValidationError(AdminException):
    """配置验证错误"""
    def __init__(self, message: str):
        super().__init__(message, "CONFIG_VALIDATION_ERROR", 400)


class FileOperationError(AdminException):
    """文件操作错误"""
    def __init__(self, message: str):
        super().__init__(message, "FILE_OPERATION_ERROR", 500)


class ModelConnectionError(AdminException):
    """模型连接错误"""
    def __init__(self, message: str):
        super().__init__(message, "MODEL_CONNECTION_ERROR", 503)


def create_error_response(code: str, message: str, details: Any = None) -> Dict[str, Any]:
    """创建标准错误响应"""
    response = {
        "error": {
            "code": code,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
    }
    if details:
        response["error"]["details"] = details
    return response


def add_exception_handlers(app: FastAPI):
    """添加异常处理器到FastAPI应用"""
    
    @app.exception_handler(AdminException)
    async def admin_exception_handler(request: Request, exc: AdminException):
        """处理管理系统自定义异常"""
        logger.error(f"Admin exception: {exc.code} - {exc.message}")
        return JSONResponse(
            status_code=exc.status_code,
            content=create_error_response(exc.code, exc.message)
        )
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """处理HTTP异常"""
        logger.error(f"HTTP exception: {exc.status_code} - {exc.detail}")
        return JSONResponse(
            status_code=exc.status_code,
            content=create_error_response("HTTP_ERROR", str(exc.detail))
        )
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """处理请求验证异常"""
        logger.error(f"Validation error: {exc.errors()}")
        return JSONResponse(
            status_code=422,
            content=create_error_response(
                "VALIDATION_ERROR",
                "请求参数验证失败",
                exc.errors()
            )
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """处理通用异常"""
        logger.error(f"Unexpected error: {str(exc)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content=create_error_response(
                "INTERNAL_ERROR",
                "服务器内部错误，请稍后重试"
            )
        )