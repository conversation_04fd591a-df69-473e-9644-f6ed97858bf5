# 业务逻辑完整性核查清单

## 📋 核查概述

本文档是对需求采集系统现有业务逻辑的完整梳理，确保架构简化改造不会遗漏任何关键业务规则。

## 🔄 1. 会话状态管理业务逻辑

### 1.1 会话状态定义
```python
class ConversationState(Enum):
    IDLE = auto()              # 1 空闲状态
    PROCESSING_INTENT = auto() # 2 处理意图  
    COLLECTING_INFO = auto()   # 3 收集需求信息
    DOCUMENTING = auto()       # 4 文档生成与修改
```

### 1.2 状态转换规则
| 当前状态 | 可转换到的状态 | 触发条件 | 业务逻辑 |
|---------|---------------|----------|---------|
| IDLE | PROCESSING_INTENT | 接收到用户输入 | 开始处理用户意图 |
| PROCESSING_INTENT | COLLECTING_INFO | 意图识别为需求采集 | 进入信息收集阶段 |
| PROCESSING_INTENT | IDLE | 意图识别为简单交互 | 完成简单交互，回到空闲 |
| COLLECTING_INFO | DOCUMENTING | 关注点收集完成 | 开始文档生成 |
| COLLECTING_INFO | IDLE | 用户重启会话 | 重置会话状态 |
| DOCUMENTING | IDLE | 文档确认完成 | 完成整个需求采集流程 |
| DOCUMENTING | COLLECTING_INFO | 文档需要修改 | 返回信息收集阶段 |

### 1.3 状态持久化逻辑
- **会话创建**：自动创建新会话记录
- **状态更新**：每次状态变化都更新数据库
- **会话恢复**：支持从数据库恢复会话状态
- **会话超时**：处理长时间未活动的会话

## 🎯 2. 关注点状态管理业务逻辑

### 2.1 关注点状态定义
```python
FOCUS_POINT_STATES = {
    "pending": "等待处理",
    "processing": "正在处理", 
    "completed": "已完成",
    "skipped": "已跳过"
}
```

### 2.2 关注点优先级规则
| 优先级 | 标识 | 业务规则 | 处理策略 |
|--------|------|---------|----------|
| P0 | 最高 | 必须采集 | 不允许跳过 |
| P1 | 高 | 必须采集 | 重试3次后可跳过 |
| P2 | 中 | 可选采集 | 允许跳过 |

### 2.3 关注点处理逻辑
```python
# 关注点处理流程
async def process_focus_point(point_id: str, user_input: str):
    # 1. 设置为processing状态
    await set_point_processing(session_id, user_id, point_id)
    
    # 2. 提取信息
    extracted_info = await extract_information(user_input, point_definition)
    
    # 3. 更新状态
    if extracted_info:
        await update_focus_point_status(session_id, user_id, point_id, "completed", extracted_info)
    else:
        await update_focus_point_status(session_id, user_id, point_id, "pending")
```

### 2.4 重试机制业务规则
- **最大重试次数**：3次 (`max_pending_attempts: 3`)
- **重试触发条件**：用户回答不完整或无法提取有效信息
- **重试后处理**：超过重试次数后跳过该关注点（P2级别）或继续重试（P0/P1级别）

### 2.5 关注点状态安全机制
```python
# 状态安全规则
- 同一时间只能有一个关注点处于"processing"状态
- 设置新的processing状态前，必须清理所有现有的processing状态
- 处理异常时，自动将processing状态重置为pending
```

## 📄 3. 文档确认流程业务逻辑

### 3.1 文档确认关键词规则
```yaml
# 确认关键词（优先级：0 - 最低）
confirmation_keywords:
  - "确认", "没问题", "正确", "同意", "好的", "ok", "yes"
  - "可以了", "这样就行", "没有问题", "很好", "满意"
  - "that's good", "looks good", "perfect", "great"

# 否定/修改关键词（优先级：10 - 最高）
negation_keywords:
  - "不", "不是", "不要", "修改", "调整", "改", "换"
  - "删除", "去掉", "增加", "添加", "但是", "不过"
  - "no", "not", "modify", "change", "update", "remove"
```

### 3.2 文档确认判断逻辑
```python
def determine_document_intent(user_input: str, current_state: str) -> str:
    # 1. 状态检查：只在DOCUMENTING状态下生效
    if current_state != "DOCUMENTING":
        return "unknown"
    
    # 2. 优先级匹配：否定词优先级高于确认词
    if contains_negation_keywords(user_input):
        return "modify"  # 需要修改
    elif contains_confirmation_keywords(user_input):
        return "confirm"  # 确认文档
    else:
        return "unclear"  # 需要进一步确认
```

### 3.3 文档生成业务规则
- **触发条件**：关注点收集完成度达到阈值
- **生成策略**：基于已收集的关注点信息生成结构化文档
- **版本管理**：保留文档生成历史，支持回退
- **用户确认**：必须经过用户确认才能完成流程

## 🔐 4. 用户会话隔离机制

### 4.1 会话隔离规则
```python
# 会话隔离的关键字段
SESSION_ISOLATION_FIELDS = {
    "user_id": "用户唯一标识",
    "session_id": "会话唯一标识", 
    "created_at": "创建时间",
    "updated_at": "最后更新时间"
}
```

### 4.2 并发安全机制
- **用户级锁定**：每个用户的操作使用独立的锁
- **会话数据隔离**：不同用户的会话数据完全隔离
- **状态同步**：确保并发操作不会导致状态不一致

### 4.3 会话生命周期管理
```python
# 会话生命周期
class SessionLifecycle:
    def create_session(user_id: str, session_id: str):
        # 创建新会话记录
        # 初始化会话状态为IDLE
        # 设置创建时间和更新时间
        pass
    
    def update_session(session_id: str, user_id: str, new_state: str):
        # 更新会话状态
        # 更新最后活动时间
        # 保存会话上下文
        pass
    
    def restore_session(session_id: str, user_id: str):
        # 从数据库恢复会话状态
        # 加载会话上下文
        # 验证会话有效性
        pass
```

## 🎮 5. 意图识别业务逻辑

### 5.1 快速意图识别规则
```yaml
quick_intent_rules:
  - intent: "modify"
    keywords_config_key: "document_confirmation.negation_keywords"
    priority: 10  # 最高优先级
    allowed_states: ["DOCUMENTING"]
    
  - intent: "restart"
    keywords_config_key: "new_chat_request.keywords"
    priority: 5   # 中等优先级
    # 所有状态下都生效
    
  - intent: "confirm"
    keywords_config_key: "document_confirmation.confirmation_keywords"
    priority: 0   # 最低优先级
    allowed_states: ["DOCUMENTING"]
```

### 5.2 意图识别优先级机制
- **优先级排序**：数字越大优先级越高
- **状态限制**：某些意图只在特定状态下生效
- **关键词重叠处理**：高优先级意图覆盖低优先级意图

## 🔧 6. 业务规则验证机制

### 6.1 关键业务不变量
```python
BUSINESS_INVARIANTS = {
    "no_orphaned_focus_points": "不能有孤立的关注点",
    "single_processing_point": "同时只能有一个处理中的关注点",
    "state_transition_validity": "状态转换必须符合业务规则",
    "user_session_isolation": "用户会话必须完全隔离",
    "document_confirmation_integrity": "文档确认流程完整性"
}
```

### 6.2 业务规则检查点
1. **状态转换前检查**：验证转换是否符合业务规则
2. **关注点操作前检查**：验证关注点状态一致性
3. **文档生成前检查**：验证关注点完整性
4. **用户操作前检查**：验证用户权限和会话有效性

## 📊 7. 数据一致性保障

### 7.1 关键数据表关系
```sql
conversations (会话表)
├── messages (消息表)
├── focus_points_status (关注点状态表)
├── documents (文档表)
└── summaries (摘要表)
```

### 7.2 数据一致性规则
- **外键约束**：确保数据关系完整性
- **事务管理**：复杂操作使用事务保证原子性
- **状态同步**：内存缓存与数据库状态保持一致

## 🚨 8. 错误处理和恢复机制

### 8.1 错误分类和处理策略
| 错误类型 | 处理策略 | 恢复机制 |
|---------|----------|----------|
| 数据库连接错误 | 重试3次 | 使用备用连接 |
| 状态不一致 | 从数据库恢复 | 重置到安全状态 |
| 关注点处理失败 | 重置处理状态 | 重新尝试处理 |
| 文档生成失败 | 保留原始数据 | 手动重试机制 |

### 8.2 业务逻辑恢复机制
```python
class BusinessLogicRecovery:
    async def recover_from_processing_state(session_id: str, user_id: str):
        # 清理所有processing状态的关注点
        # 恢复到pending状态
        # 记录恢复操作日志
        
    async def recover_from_state_inconsistency(session_id: str, user_id: str):
        # 从数据库重新加载会话状态
        # 验证关注点状态一致性
        # 修复不一致的状态
```

## ✅ 9. 业务逻辑完整性检查清单

### 9.1 核心业务流程检查
- [ ] 会话状态转换规则完整
- [ ] 关注点状态管理正确
- [ ] 文档确认流程完整
- [ ] 用户会话隔离有效
- [ ] 意图识别准确
- [ ] 错误处理机制健全

### 9.2 数据一致性检查
- [ ] 会话状态与数据库同步
- [ ] 关注点状态与缓存一致
- [ ] 文档版本管理正确
- [ ] 用户数据隔离完整

### 9.3 业务规则验证
- [ ] 所有业务规则都有对应的验证逻辑
- [ ] 关键业务不变量得到保护
- [ ] 异常情况有恢复机制
- [ ] 业务逻辑可测试可验证

## 🎯 10. 架构简化改造风险评估

### 10.1 高风险项（需要特别关注）
1. **关注点状态同步**：新架构必须保持关注点状态的一致性
2. **会话状态转换**：简化处理不能跳过必要的状态转换
3. **文档确认逻辑**：关键词匹配不能误判用户意图

### 10.2 中风险项（需要验证）
1. **意图识别准确性**：简化后的意图识别需要保持准确率
2. **并发安全性**：新架构需要保持并发安全
3. **错误恢复机制**：简化后的错误处理需要完整

### 10.3 低风险项（可以优化）
1. **性能优化**：在保持业务逻辑完整性的前提下提升性能
2. **代码结构**：简化代码结构但保持功能完整
3. **配置管理**：优化配置管理但保持业务规则有效

## 📝 总结

本业务逻辑完整性核查清单包含了需求采集系统的所有核心业务规则，架构简化改造必须确保：

1. **100%业务逻辑保留**：所有业务规则都必须在新架构中得到保持
2. **状态管理完整**：会话状态和关注点状态管理机制完整
3. **数据一致性保障**：数据库和缓存状态保持一致
4. **错误处理健全**：异常情况有完整的处理和恢复机制
5. **用户体验不降级**：业务流程的用户体验不能降级

**关键原则**：任何影响核心业务逻辑的变更都必须经过严格验证，确保业务连续性和数据完整性。