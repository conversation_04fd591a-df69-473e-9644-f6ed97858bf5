# 业务规则文档

## 📋 文档概述

**文档版本**：v1.0  
**创建时间**：2024年12月21日  
**适用范围**：需求采集系统架构简化改造  
**维护责任**：架构改造团队  

## 🎯 核心业务规则

### 1. 会话状态管理规则

#### 1.1 状态定义规则
```yaml
conversation_states:
  IDLE: 
    description: "空闲状态，等待用户输入"
    entry_conditions: ["系统启动", "任务完成", "用户重置"]
    
  PROCESSING_INTENT:
    description: "处理用户意图"
    entry_conditions: ["接收到用户输入"]
    max_duration: "30秒"
    
  COLLECTING_INFO:
    description: "收集需求信息"
    entry_conditions: ["意图识别为需求采集"]
    
  DOCUMENTING:
    description: "文档生成与修改"
    entry_conditions: ["关注点收集完成"]
```

#### 1.2 状态转换规则
```yaml
state_transitions:
  valid_transitions:
    - from: "IDLE"
      to: "PROCESSING_INTENT"
      trigger: "user_input_received"
      validation: "输入非空且有效"
      
    - from: "PROCESSING_INTENT"
      to: "COLLECTING_INFO"
      trigger: "intent_recognized_as_requirement"
      validation: "意图识别成功"
      
    - from: "COLLECTING_INFO"
      to: "DOCUMENTING"
      trigger: "focus_points_completed"
      validation: "P0和P1关注点全部完成"
      
    - from: "DOCUMENTING"
      to: "IDLE"
      trigger: "document_confirmed"
      validation: "用户确认文档"
      
  invalid_transitions:
    - "IDLE -> COLLECTING_INFO"  # 跳过意图处理
    - "IDLE -> DOCUMENTING"      # 跳过信息收集
    - "PROCESSING_INTENT -> DOCUMENTING"  # 跳过信息收集
```

#### 1.3 状态持久化规则
```yaml
state_persistence:
  storage_method: "数据库推断 + 内存缓存"
  recovery_strategy: "从数据库状态推断当前状态"
  consistency_check: "每次操作前验证状态一致性"
  
  state_inference_rules:
    DOCUMENTING: "存在未确认的文档"
    COLLECTING_INFO: "存在领域分类信息且无未确认文档"
    IDLE: "默认状态"
```

### 2. 关注点管理规则

#### 2.1 优先级规则
```yaml
focus_point_priorities:
  P0:
    description: "最高优先级，核心功能需求"
    required: true
    max_attempts: null  # 无限重试
    skip_allowed: false
    failure_action: "escalate_to_human"
    
  P1:
    description: "高优先级，重要功能需求"
    required: true
    max_attempts: 3
    skip_allowed: true  # 3次失败后可跳过
    failure_action: "continue_with_warning"
    
  P2:
    description: "低优先级，可选功能需求"
    required: false
    max_attempts: 1
    skip_allowed: true  # 可直接跳过
    failure_action: "skip_silently"
```

#### 2.2 状态转换规则
```yaml
focus_point_states:
  pending:
    description: "等待处理"
    next_states: ["processing", "skipped"]
    
  processing:
    description: "正在处理"
    constraints: "同时只能有一个关注点处于此状态"
    next_states: ["completed", "pending", "skipped"]
    timeout: "5分钟"
    
  completed:
    description: "已完成"
    final_state: true
    
  skipped:
    description: "已跳过"
    conditions: ["P2优先级", "P1达到重试上限"]
    final_state: true
```

#### 2.3 并发控制规则
```yaml
concurrency_rules:
  single_processing_constraint:
    description: "同时只能有一个关注点处于processing状态"
    implementation: "设置新processing前清理所有现有processing状态"
    
  atomic_operations:
    - "状态更新操作必须原子性"
    - "重试计数更新必须与状态更新同步"
    
  conflict_resolution:
    strategy: "last_write_wins"
    validation: "操作前检查状态一致性"
```

### 3. 文档确认流程规则

#### 3.1 关键词优先级规则
```yaml
keyword_priorities:
  modify_keywords:
    priority: 10  # 最高优先级
    keywords: ["修改", "调整", "不对", "但是", "需要改", "不是"]
    intent: "modify"
    
  restart_keywords:
    priority: 5   # 中等优先级
    keywords: ["重新开始", "新聊天", "重置", "清空"]
    intent: "restart"
    
  confirm_keywords:
    priority: 0   # 最低优先级
    keywords: ["确认", "没问题", "正确", "ok", "好的", "同意"]
    intent: "confirm"
    
  priority_resolution:
    rule: "高优先级关键词覆盖低优先级关键词"
    example: "好的，但是需要修改" -> "modify" (优先级10 > 0)
```

#### 3.2 状态限制规则
```yaml
state_restrictions:
  confirm_operation:
    allowed_states: ["DOCUMENTING"]
    preconditions: ["存在待确认文档"]
    postconditions: ["状态转换为IDLE", "文档状态更新为confirmed"]
    
  modify_operation:
    allowed_states: ["DOCUMENTING"]
    preconditions: ["存在待确认文档"]
    postconditions: ["保持DOCUMENTING状态", "文档进入修改模式"]
    
  restart_operation:
    allowed_states: ["IDLE", "PROCESSING_INTENT", "COLLECTING_INFO", "DOCUMENTING"]
    preconditions: []
    postconditions: ["状态重置为IDLE", "清理所有会话数据"]
```

#### 3.3 文档版本管理规则
```yaml
document_versioning:
  version_calculation:
    method: "自动递增"
    query: "SELECT MAX(version) FROM documents WHERE conversation_id = ? AND user_id = ?"
    default: 1
    
  version_states:
    draft: "草稿状态，可修改"
    confirmed: "已确认，不可修改"
    rejected: "已拒绝，可基于此版本创建新版本"
    
  modification_rules:
    - "只能修改draft状态的文档"
    - "确认后的文档不可修改，只能创建新版本"
    - "每次修改创建新版本"
```

### 4. 用户会话隔离规则

#### 4.1 数据隔离规则
```yaml
data_isolation:
  mandatory_filters:
    - "所有数据库查询必须包含user_id过滤"
    - "会话数据按user_id + session_id隔离"
    - "文档数据按user_id + conversation_id隔离"
    
  session_context:
    scope: "单用户单会话"
    lifetime: "会话期间"
    cleanup: "会话结束时清理"
    
  cross_user_access:
    policy: "严格禁止"
    validation: "每次操作前验证用户权限"
```

#### 4.2 并发安全规则
```yaml
concurrency_safety:
  user_level_isolation:
    description: "不同用户的操作完全隔离"
    implementation: "基于user_id的数据分区"
    
  session_level_safety:
    description: "同用户多会话安全"
    constraints: ["每个会话独立的session_id", "会话数据不共享"]
    
  operation_atomicity:
    database_transactions: "使用数据库事务保证原子性"
    state_consistency: "操作失败时回滚所有变更"
```

### 5. 错误处理和恢复规则

#### 5.1 错误分类规则
```yaml
error_categories:
  critical_errors:
    types: ["数据库连接失败", "系统崩溃", "数据损坏"]
    action: "立即停止服务，触发告警"
    recovery: "人工介入"
    
  recoverable_errors:
    types: ["网络超时", "LLM服务暂时不可用", "临时资源不足"]
    action: "自动重试"
    max_retries: 3
    backoff_strategy: "指数退避"
    
  business_logic_errors:
    types: ["状态转换无效", "权限验证失败", "数据验证失败"]
    action: "返回错误信息，保持当前状态"
    logging: "记录详细错误日志"
```

#### 5.2 状态恢复规则
```yaml
state_recovery:
  session_recovery:
    trigger: "会话中断或异常"
    method: "从数据库重新构建会话状态"
    validation: "验证恢复后状态的一致性"
    
  focus_point_recovery:
    processing_state_cleanup:
      trigger: "系统重启或异常检测"
      action: "将所有processing状态重置为pending"
      
  data_consistency_recovery:
    inconsistency_detection: "定期检查内存与数据库状态一致性"
    resolution_strategy: "以数据库状态为准"
```

### 6. 性能和资源管理规则

#### 6.1 响应时间要求
```yaml
performance_requirements:
  keyword_matching: "< 1ms"
  semantic_matching: "< 10ms"
  llm_processing: "< 500ms"
  state_transition: "< 5ms"
  database_operations: "< 100ms"
  
  timeout_rules:
    user_input_processing: "30秒"
    document_generation: "60秒"
    session_idle_timeout: "30分钟"
```

#### 6.2 资源限制规则
```yaml
resource_limits:
  concurrent_sessions_per_user: 5
  max_message_length: 10000
  max_document_size: "1MB"
  max_conversation_turns: 100
  
  cleanup_rules:
    inactive_sessions: "30分钟后清理"
    old_documents: "保留最近10个版本"
    log_retention: "30天"
```

## 🔒 业务约束和验证规则

### 1. 数据完整性约束
```yaml
data_integrity:
  foreign_key_constraints:
    - "focus_point_status.session_id -> conversations.session_id"
    - "documents.conversation_id -> conversations.conversation_id"
    
  business_constraints:
    - "每个会话最多一个未确认文档"
    - "关注点状态变更必须记录时间戳"
    - "用户ID不能为空或无效"
```

### 2. 业务逻辑验证规则
```yaml
validation_rules:
  state_transition_validation:
    - "验证转换前状态的有效性"
    - "检查转换条件是否满足"
    - "确保转换后状态的一致性"
    
  focus_point_validation:
    - "P0关注点不能被跳过"
    - "processing状态的唯一性"
    - "重试次数不能超过限制"
    
  document_validation:
    - "确认操作只能在DOCUMENTING状态执行"
    - "文档内容不能为空"
    - "版本号必须递增"
```

## 📊 监控和告警规则

### 1. 关键指标监控
```yaml
monitoring_metrics:
  business_logic_metrics:
    - "状态转换成功率: >= 99.9%"
    - "关注点处理成功率: >= 95%"
    - "文档生成成功率: >= 98%"
    - "用户会话隔离完整性: 100%"
    
  performance_metrics:
    - "平均响应时间: < 100ms"
    - "LLM调用率: < 30%"
    - "错误率: < 1%"
```

### 2. 告警规则
```yaml
alert_rules:
  critical_alerts:
    - "业务逻辑违规率 > 0.1%"
    - "数据一致性检查失败"
    - "用户数据泄露检测"
    
  warning_alerts:
    - "响应时间 > 1秒"
    - "错误率 > 0.5%"
    - "资源使用率 > 80%"
```

---

**文档维护说明**：
- 本文档应在每次架构变更后及时更新
- 所有业务规则变更必须经过评审和测试
- 定期检查规则的执行情况和有效性
