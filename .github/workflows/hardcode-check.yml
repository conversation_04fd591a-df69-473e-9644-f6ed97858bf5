# GitHub Actions 工作流：硬编码检查
# 在每次 Pull Request 和 Push 时自动检查硬编码问题

name: 硬编码检查

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'backend/**/*.py'
      - 'backend/config/**/*.yaml'
      - 'tools/**/*.py'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'backend/**/*.py'
      - 'backend/config/**/*.yaml'
      - 'tools/**/*.py'

jobs:
  hardcode-detection:
    runs-on: ubuntu-latest
    name: 硬编码检测

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置 Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install pyyaml

    - name: 运行硬编码检测
      id: hardcode-check
      run: |
        echo "开始硬编码检测..."
        python tools/hardcode_detector.py backend/ json > hardcode_report.json
        
        # 检查是否有高危险问题
        HIGH_ISSUES=$(python -c "
        import json
        with open('hardcode_report.json', 'r') as f:
            issues = json.load(f)
        high_issues = [i for i in issues if i['severity'] == 'HIGH']
        print(len(high_issues))
        ")
        
        echo "high_issues=$HIGH_ISSUES" >> $GITHUB_OUTPUT
        
        # 生成文本报告
        python tools/hardcode_detector.py backend/ text > hardcode_report.txt

    - name: 上传硬编码检测报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: hardcode-report
        path: |
          hardcode_report.json
          hardcode_report.txt

    - name: 检查高危险问题
      if: steps.hardcode-check.outputs.high_issues != '0'
      run: |
        echo "❌ 发现 ${{ steps.hardcode-check.outputs.high_issues }} 个高危险硬编码问题！"
        echo "请查看报告并修复这些问题。"
        cat hardcode_report.txt
        exit 1

    - name: 显示检测结果
      if: steps.hardcode-check.outputs.high_issues == '0'
      run: |
        echo "✅ 未发现高危险硬编码问题"
        echo "检测报告摘要："
        head -20 hardcode_report.txt

  config-validation:
    runs-on: ubuntu-latest
    name: 配置文件验证

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置 Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install pyyaml

    - name: 验证配置文件
      run: |
        echo "开始配置文件验证..."
        python tools/config_validator.py --config-dir backend/config/

    - name: 检查配置完整性
      run: |
        echo "检查配置管理器集成..."
        # 这里可以添加更多的集成测试
        python -c "
        import sys
        sys.path.insert(0, '.')
        try:
            from backend.config.config_manager import config_manager
            print('✅ 配置管理器导入成功')
        except Exception as e:
            print(f'❌ 配置管理器导入失败: {e}')
            sys.exit(1)
        "

  code-quality:
    runs-on: ubuntu-latest
    name: 代码质量检查

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置 Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort bandit

    - name: 代码格式检查
      run: |
        echo "检查代码格式..."
        black --check --line-length=100 backend/ tools/

    - name: 导入排序检查
      run: |
        echo "检查导入排序..."
        isort --check-only --profile=black --line-length=100 backend/ tools/

    - name: 代码风格检查
      run: |
        echo "检查代码风格..."
        flake8 --max-line-length=100 --ignore=E203,W503 backend/ tools/

    - name: 安全检查
      run: |
        echo "运行安全检查..."
        bandit -r backend/ -f json -o bandit-report.json || true

    - name: 上传安全检查报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-report
        path: bandit-report.json

  integration-test:
    runs-on: ubuntu-latest
    name: 配置集成测试
    needs: [hardcode-detection, config-validation]

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置 Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: 安装项目依赖
      run: |
        python -m pip install --upgrade pip
        # 这里应该安装项目的实际依赖
        pip install pyyaml

    - name: 测试配置系统集成
      run: |
        echo "测试配置系统集成..."
        python -c "
        import sys
        sys.path.insert(0, '.')
        
        # 测试配置管理器的各种功能
        from backend.config.config_manager import config_manager
        
        # 测试业务规则
        priority = config_manager.get_business_rule('priority.document_modification', 7)
        print(f'✅ 业务规则测试通过: {priority}')
        
        # 测试阈值
        threshold = config_manager.get_threshold('extraction.completeness_threshold', 0.7)
        print(f'✅ 阈值测试通过: {threshold}')
        
        # 测试消息模板
        template = config_manager.get_message_template('error.general.unknown_error')
        print(f'✅ 消息模板测试通过: {template[:50]}...')
        
        # 测试数据库查询
        query = config_manager.get_database_query('focus_points.check_exists')
        print(f'✅ 数据库查询测试通过: {query[:50]}...')
        
        print('✅ 所有配置系统集成测试通过')
        "

  summary:
    runs-on: ubuntu-latest
    name: 检查结果汇总
    needs: [hardcode-detection, config-validation, code-quality, integration-test]
    if: always()

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 下载所有报告
      uses: actions/download-artifact@v3

    - name: 生成汇总报告
      run: |
        echo "# 硬编码检查汇总报告" > summary.md
        echo "" >> summary.md
        echo "## 检查结果" >> summary.md
        echo "" >> summary.md
        
        # 检查各个作业的状态
        if [ "${{ needs.hardcode-detection.result }}" = "success" ]; then
          echo "✅ 硬编码检测: 通过" >> summary.md
        else
          echo "❌ 硬编码检测: 失败" >> summary.md
        fi
        
        if [ "${{ needs.config-validation.result }}" = "success" ]; then
          echo "✅ 配置验证: 通过" >> summary.md
        else
          echo "❌ 配置验证: 失败" >> summary.md
        fi
        
        if [ "${{ needs.code-quality.result }}" = "success" ]; then
          echo "✅ 代码质量: 通过" >> summary.md
        else
          echo "❌ 代码质量: 失败" >> summary.md
        fi
        
        if [ "${{ needs.integration-test.result }}" = "success" ]; then
          echo "✅ 集成测试: 通过" >> summary.md
        else
          echo "❌ 集成测试: 失败" >> summary.md
        fi
        
        echo "" >> summary.md
        echo "## 详细报告" >> summary.md
        echo "" >> summary.md
        echo "请查看各个检查步骤的详细输出和上传的报告文件。" >> summary.md
        
        cat summary.md

    - name: 上传汇总报告
      uses: actions/upload-artifact@v3
      with:
        name: summary-report
        path: summary.md
