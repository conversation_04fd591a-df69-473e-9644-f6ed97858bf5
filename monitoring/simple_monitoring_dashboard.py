#!/usr/bin/env python3
"""
简化版监控仪表板

功能：
1. 启动监控系统
2. 实时显示关键指标
3. 简单的Web界面
4. 基本的数据导出

使用方法：
# 控制台模式(默认)
python simple_monitoring_dashboard.py

# Web模式(指定端口)
python simple_monitoring_dashboard.py --web --port 8080

# 在zsh等shell中使用时，如遇到参数解析问题，请使用：
python simple_monitoring_dashboard.py "--web" "--port 8080"

参数说明：
--web     : 启用Web界面模式
--port    : 指定Web服务端口(默认8080)
-v/--verbose: 显示详细日志
"""

import asyncio
import json
import logging
import signal
import sys
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径(向上两级获取项目根目录)
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from backend.utils.monitoring_dashboard import start_monitoring, stop_monitoring, monitoring_dashboard
    from backend.utils.system_monitor import system_monitor
    print("✅ 成功导入监控模块")
except ImportError as e:
    print(f"❌ 导入监控模块失败: {e}")
    sys.exit(1)


class SimpleMonitoringDashboard:
    """简化版监控仪表板"""
    
    def __init__(self, web_mode=False, port=8080):
        self.web_mode = web_mode
        self.port = port
        self.running = False
        self.logger = logging.getLogger(__name__)
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在停止...")
        self.stop()
    
    async def start(self):
        """启动监控仪表板"""
        print("🎯 启动简化版监控仪表板")
        print("="*40)
        
        try:
            # 启动监控系统
            print("📊 启动监控系统...")
            start_monitoring()
            self.running = True
            
            print("✅ 监控系统已启动")
            
            if self.web_mode:
                await self._start_web_mode()
            else:
                await self._start_console_mode()
                
        except Exception as e:
            self.logger.error(f"启动失败: {e}", exc_info=True)
            print(f"❌ 启动失败: {e}")
            self.stop()
    
    async def _start_console_mode(self):
        """控制台模式"""
        print("\n📺 控制台监控模式")
        print("按 Ctrl+C 停止监控")
        print("="*40)
        
        while self.running:
            try:
                await self._display_metrics()
                await asyncio.sleep(10)  # 每10秒更新一次
            except Exception as e:
                print(f"⚠️ 显示指标时出错: {e}")
                await asyncio.sleep(5)
    
    async def _start_web_mode(self):
        """Web模式"""
        print(f"\n🌐 Web监控模式")
        print(f"📍 访问地址: http://127.0.0.1:{self.port}")
        print("按 Ctrl+C 停止服务")
        print("="*40)
        
        # 启动简单的HTTP服务器
        await self._run_web_server()
    
    async def _display_metrics(self):
        """显示监控指标"""
        try:
            # 获取监控数据
            dashboard_data = await monitoring_dashboard.get_real_time_metrics()
            
            if "error" in dashboard_data:
                print(f"⚠️ 获取数据错误: {dashboard_data['error']}")
                return
            
            # 清屏并显示
            print("\033[2J\033[H")  # 清屏
            print("📊 实时监控数据")
            print("="*50)
            print(f"⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            
            # 提取关键数据
            real_time_stats = dashboard_data.get("real_time_stats", {})
            performance_summary = dashboard_data.get("performance_summary", {})
            health_status = dashboard_data.get("health_status", {})
            
            # 显示关键指标
            print("🚀 性能指标:")
            print(f"   响应时间: {real_time_stats.get('avg_response_time', 0):.2f}ms")
            print(f"   成功率: {real_time_stats.get('success_rate', 0):.1f}%")
            print(f"   请求/分钟: {real_time_stats.get('requests_per_minute', 0):.1f}")
            print()
            
            print("📈 业务指标:")
            print(f"   加速率: {real_time_stats.get('acceleration_rate', 0):.1f}%")
            print(f"   用户满意度: {real_time_stats.get('user_satisfaction', 0):.1f}/5.0")
            print()
            
            print("💻 系统状态:")
            print(f"   整体状态: {health_status.get('overall_status', 'unknown')}")
            print(f"   CPU使用率: {health_status.get('cpu_usage', 0):.1f}%")
            print(f"   内存使用率: {health_status.get('memory_usage', 0):.1f}%")
            print()
            
            # 显示关键词匹配统计
            keyword_stats = performance_summary.get("keyword_matching", {})
            if keyword_stats:
                print("🔍 关键词匹配:")
                print(f"   平均时间: {keyword_stats.get('avg_time', 0):.3f}ms")
                print(f"   总匹配数: {keyword_stats.get('total_matches', 0)}")
                print()
            
            print("按 Ctrl+C 停止监控")
            print("="*50)
            
        except Exception as e:
            print(f"⚠️ 显示指标失败: {e}")
    
    async def _run_web_server(self):
        """运行Web服务器"""
        import http.server
        import socketserver
        import threading
        import urllib.parse
        
        class MonitoringHandler(http.server.SimpleHTTPRequestHandler):
            def __init__(self, *args, dashboard_instance=None, **kwargs):
                self.dashboard = dashboard_instance
                super().__init__(*args, **kwargs)
            
            def do_GET(self):
                if self.path == '/' or self.path == '/dashboard':
                    self._serve_dashboard()
                elif self.path == '/api/metrics':
                    self._serve_metrics()
                elif self.path == '/api/export':
                    self._serve_export()
                else:
                    self.send_error(404, "页面未找到")
            
            def _serve_dashboard(self):
                """提供仪表板页面"""
                html = self._generate_dashboard_html()
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.send_header('Content-length', len(html.encode('utf-8')))
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _serve_metrics(self):
                """提供指标API"""
                try:
                    # 这里需要同步调用，简化处理
                    metrics = {
                        "timestamp": datetime.now().isoformat(),
                        "status": "running" if system_monitor.is_running else "stopped",
                        "message": "监控数据API - 请使用完整版获取详细数据"
                    }
                    
                    response = json.dumps(metrics, ensure_ascii=False, indent=2)
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json; charset=utf-8')
                    self.send_header('Content-length', len(response.encode('utf-8')))
                    self.end_headers()
                    self.wfile.write(response.encode('utf-8'))
                    
                except Exception as e:
                    self.send_error(500, f"获取指标失败: {e}")
            
            def _serve_export(self):
                """提供数据导出"""
                try:
                    export_data = {
                        "timestamp": datetime.now().isoformat(),
                        "system_status": "running" if system_monitor.is_running else "stopped",
                        "export_note": "简化版导出 - 请使用完整版获取详细数据"
                    }
                    
                    response = json.dumps(export_data, ensure_ascii=False, indent=2)
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json; charset=utf-8')
                    self.send_header('Content-Disposition', 'attachment; filename="monitoring_export.json"')
                    self.send_header('Content-length', len(response.encode('utf-8')))
                    self.end_headers()
                    self.wfile.write(response.encode('utf-8'))
                    
                except Exception as e:
                    self.send_error(500, f"导出失败: {e}")
            
            def _generate_dashboard_html(self):
                """生成仪表板HTML"""
                return f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版监控仪表板</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
        .header {{ background: #2c3e50; color: white; padding: 20px; text-align: center; border-radius: 8px; margin-bottom: 20px; }}
        .metrics {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }}
        .metric-card {{ background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .metric-title {{ font-weight: bold; color: #2c3e50; margin-bottom: 10px; }}
        .metric-value {{ font-size: 24px; font-weight: bold; color: #3498db; }}
        .status-running {{ color: #27ae60; }}
        .status-stopped {{ color: #e74c3c; }}
        .controls {{ text-align: center; margin: 20px 0; }}
        .btn {{ background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 0 5px; }}
        .btn:hover {{ background: #2980b9; }}
        .timestamp {{ text-align: center; color: #7f8c8d; margin-bottom: 20px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 简化版监控仪表板</h1>
        <p>实时系统监控</p>
    </div>
    
    <div class="timestamp" id="timestamp">最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
    
    <div class="controls">
        <button class="btn" onclick="refreshData()">🔄 刷新数据</button>
        <button class="btn" onclick="exportData()">📁 导出数据</button>
    </div>
    
    <div class="metrics">
        <div class="metric-card">
            <div class="metric-title">🎯 系统状态</div>
            <div class="metric-value status-running" id="system-status">运行中</div>
        </div>
        
        <div class="metric-card">
            <div class="metric-title">⏰ 运行时间</div>
            <div class="metric-value" id="uptime">--</div>
        </div>
        
        <div class="metric-card">
            <div class="metric-title">📊 监控模式</div>
            <div class="metric-value">简化版</div>
        </div>
        
        <div class="metric-card">
            <div class="metric-title">🔗 API状态</div>
            <div class="metric-value status-running">可用</div>
        </div>
    </div>
    
    <div style="margin-top: 30px; text-align: center; color: #7f8c8d;">
        <p>💡 提示: 这是简化版监控仪表板</p>
        <p>如需完整功能，请安装 FastAPI: <code>pip install fastapi uvicorn</code></p>
        <p>然后运行: <code>python monitoring_web_server.py</code></p>
    </div>

    <script>
        function refreshData() {{
            fetch('/api/metrics')
                .then(response => response.json())
                .then(data => {{
                    document.getElementById('timestamp').textContent = 
                        '最后更新: ' + new Date().toLocaleString();
                    
                    const statusElement = document.getElementById('system-status');
                    statusElement.textContent = data.status === 'running' ? '运行中' : '已停止';
                    statusElement.className = 'metric-value status-' + data.status;
                }})
                .catch(err => {{
                    console.error('刷新失败:', err);
                    alert('刷新数据失败: ' + err.message);
                }});
        }}
        
        function exportData() {{
            window.open('/api/export', '_blank');
        }}
        
        // 自动刷新
        setInterval(refreshData, 30000);
        
        // 计算运行时间
        const startTime = new Date();
        setInterval(() => {{
            const now = new Date();
            const uptime = Math.floor((now - startTime) / 1000);
            const hours = Math.floor(uptime / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const seconds = uptime % 60;
            document.getElementById('uptime').textContent = 
                hours + 'h ' + minutes + 'm ' + seconds + 's';
        }}, 1000);
    </script>
</body>
</html>
                """
        
        # 创建处理器工厂
        def handler_factory(*args, **kwargs):
            return MonitoringHandler(*args, dashboard_instance=self, **kwargs)
        
        # 启动服务器
        try:
            with socketserver.TCPServer(("127.0.0.1", self.port), handler_factory) as httpd:
                print(f"✅ Web服务器已启动在端口 {self.port}")
                print(f"🌐 访问 http://127.0.0.1:{self.port} 查看监控仪表板")
                
                # 在后台运行控制台更新
                console_task = asyncio.create_task(self._background_console_updates())
                
                # 运行服务器
                await asyncio.get_event_loop().run_in_executor(None, httpd.serve_forever)
                
        except Exception as e:
            print(f"❌ Web服务器启动失败: {e}")
            # 回退到控制台模式
            print("🔄 回退到控制台模式...")
            await self._start_console_mode()
    
    async def _background_console_updates(self):
        """后台控制台更新"""
        while self.running:
            try:
                # 每30秒在控制台输出一次状态
                await asyncio.sleep(30)
                if self.running:
                    print(f"📊 {datetime.now().strftime('%H:%M:%S')} - 监控系统运行中...")
            except Exception:
                break
    
    def stop(self):
        """停止监控"""
        print("\n🛑 正在停止监控仪表板...")
        self.running = False
        
        try:
            stop_monitoring()
            print("✅ 监控系统已停止")
        except Exception as e:
            print(f"⚠️ 停止时出现错误: {e}")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="简化版监控仪表板")
    parser.add_argument("--web", action="store_true", help="启用Web模式")
    parser.add_argument("--port", type=int, default=8080, help="Web服务端口，默认8080")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细日志")
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🎯 简化版监控仪表板")
    print("="*30)
    print(f"模式: {'Web' if args.web else '控制台'}")
    if args.web:
        print(f"端口: {args.port}")
    print()
    
    # 创建并启动仪表板
    dashboard = SimpleMonitoringDashboard(web_mode=args.web, port=args.port)
    
    try:
        await dashboard.start()
    except KeyboardInterrupt:
        print("\n用户中断，正在退出...")
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        logging.getLogger(__name__).error(f"运行失败: {e}", exc_info=True)
    finally:
        dashboard.stop()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
