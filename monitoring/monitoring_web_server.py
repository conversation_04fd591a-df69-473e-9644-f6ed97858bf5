#!/usr/bin/env python3
"""
监控仪表板Web服务器

功能：
1. 提供Web界面展示监控数据
2. 实时数据API接口
3. 历史数据查询
4. 告警管理界面

启动方式：
python monitoring_web_server.py --port 8080
"""

import asyncio
import json
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from backend.utils.monitoring_dashboard import start_monitoring, monitoring_dashboard
    from backend.utils.system_monitor import system_monitor
    print("✅ 成功导入监控模块")
except ImportError as e:
    print(f"❌ 导入监控模块失败: {e}")
    sys.exit(1)

# 尝试导入FastAPI，如果没有则使用简单的HTTP服务器
try:
    from fastapi import FastAPI, HTTPException
    from fastapi.responses import HTMLResponse, JSONResponse
    from fastapi.staticfiles import StaticFiles
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    print("⚠️ FastAPI未安装，将使用简单HTTP服务器")


class MonitoringWebServer:
    """监控Web服务器"""
    
    def __init__(self, port=8080, host="127.0.0.1"):
        self.port = port
        self.host = host
        self.logger = logging.getLogger(__name__)
        
        if FASTAPI_AVAILABLE:
            self.app = self._create_fastapi_app()
        else:
            self.app = None
    
    def _create_fastapi_app(self):
        """创建FastAPI应用"""
        app = FastAPI(
            title="监控仪表板",
            description="实时系统监控和性能分析",
            version="1.0.0"
        )
        
        # 添加路由
        app.add_api_route("/", self.dashboard_page, methods=["GET"], response_class=HTMLResponse)
        app.add_api_route("/api/metrics", self.get_metrics, methods=["GET"])
        app.add_api_route("/api/health", self.get_health, methods=["GET"])
        app.add_api_route("/api/alerts", self.get_alerts, methods=["GET"])
        app.add_api_route("/api/export", self.export_data, methods=["POST"])
        
        return app
    
    async def dashboard_page(self):
        """仪表板主页"""
        html_content = self._generate_dashboard_html()
        return HTMLResponse(content=html_content)
    
    async def get_metrics(self):
        """获取监控指标API"""
        try:
            metrics = await monitoring_dashboard.get_real_time_metrics()
            return JSONResponse(content=metrics)
        except Exception as e:
            self.logger.error(f"获取指标失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_health(self):
        """健康检查API"""
        try:
            health_data = {
                "status": "healthy" if system_monitor.is_running else "stopped",
                "timestamp": datetime.now().isoformat(),
                "uptime": system_monitor.get_uptime() if hasattr(system_monitor, 'get_uptime') else 0
            }
            return JSONResponse(content=health_data)
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_alerts(self):
        """获取告警信息API"""
        try:
            alerts_data = {
                "active_alerts": [
                    {
                        "rule_name": alert.rule_name,
                        "message": alert.message,
                        "severity": alert.severity,
                        "timestamp": alert.timestamp.isoformat()
                    }
                    for alert in system_monitor.alert_manager.active_alerts
                ],
                "total_count": len(system_monitor.alert_manager.active_alerts)
            }
            return JSONResponse(content=alerts_data)
        except Exception as e:
            self.logger.error(f"获取告警失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def export_data(self):
        """导出数据API"""
        try:
            filename = await monitoring_dashboard.export_metrics_data()
            return JSONResponse(content={"filename": filename, "status": "success"})
        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def _generate_dashboard_html(self):
        """生成仪表板HTML"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监控仪表板</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .header { background: #2c3e50; color: white; padding: 1rem; text-align: center; }
        .container { max-width: 1200px; margin: 0 auto; padding: 2rem; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin-bottom: 2rem; }
        .metric-card { background: white; border-radius: 8px; padding: 1.5rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric-title { font-size: 1.1rem; font-weight: bold; color: #2c3e50; margin-bottom: 1rem; }
        .metric-value { font-size: 2rem; font-weight: bold; color: #3498db; margin-bottom: 0.5rem; }
        .metric-unit { font-size: 0.9rem; color: #7f8c8d; }
        .status-healthy { color: #27ae60; }
        .status-warning { color: #f39c12; }
        .status-critical { color: #e74c3c; }
        .refresh-btn { background: #3498db; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; margin-bottom: 1rem; }
        .refresh-btn:hover { background: #2980b9; }
        .loading { text-align: center; color: #7f8c8d; }
        .error { color: #e74c3c; text-align: center; }
        .timestamp { text-align: center; color: #7f8c8d; margin-bottom: 1rem; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 监控仪表板</h1>
        <p>实时系统监控和性能分析</p>
    </div>
    
    <div class="container">
        <button class="refresh-btn" onclick="loadMetrics()">🔄 刷新数据</button>
        <div class="timestamp" id="timestamp"></div>
        
        <div id="loading" class="loading">正在加载监控数据...</div>
        <div id="error" class="error" style="display: none;"></div>
        
        <div id="metrics-container" style="display: none;">
            <div class="metrics-grid">
                <!-- 性能指标 -->
                <div class="metric-card">
                    <div class="metric-title">⚡ 平均响应时间</div>
                    <div class="metric-value" id="avg-response-time">--</div>
                    <div class="metric-unit">毫秒</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">📈 成功率</div>
                    <div class="metric-value" id="success-rate">--</div>
                    <div class="metric-unit">%</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">🚀 加速率</div>
                    <div class="metric-value" id="acceleration-rate">--</div>
                    <div class="metric-unit">%</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">💾 CPU使用率</div>
                    <div class="metric-value" id="cpu-usage">--</div>
                    <div class="metric-unit">%</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">🧠 内存使用率</div>
                    <div class="metric-value" id="memory-usage">--</div>
                    <div class="metric-unit">%</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">🎯 系统状态</div>
                    <div class="metric-value" id="system-status">--</div>
                    <div class="metric-unit">状态</div>
                </div>
            </div>
            
            <!-- 告警信息 -->
            <div class="metric-card">
                <div class="metric-title">🚨 活跃告警</div>
                <div id="alerts-container">
                    <div class="loading">加载告警信息...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function loadMetrics() {
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const container = document.getElementById('metrics-container');
            
            loading.style.display = 'block';
            error.style.display = 'none';
            container.style.display = 'none';
            
            try {
                const response = await fetch('/api/metrics');
                if (!response.ok) throw new Error('获取数据失败');
                
                const data = await response.json();
                updateMetrics(data);
                
                loading.style.display = 'none';
                container.style.display = 'block';
                
                // 更新时间戳
                document.getElementById('timestamp').textContent = 
                    '最后更新: ' + new Date().toLocaleString();
                
            } catch (err) {
                loading.style.display = 'none';
                error.style.display = 'block';
                error.textContent = '加载失败: ' + err.message;
            }
        }
        
        function updateMetrics(data) {
            const realTimeStats = data.real_time_stats || {};
            const healthStatus = data.health_status || {};
            
            // 更新性能指标
            document.getElementById('avg-response-time').textContent = 
                (realTimeStats.avg_response_time || 0).toFixed(2);
            document.getElementById('success-rate').textContent = 
                (realTimeStats.success_rate || 0).toFixed(1);
            document.getElementById('acceleration-rate').textContent = 
                (realTimeStats.acceleration_rate || 0).toFixed(1);
            document.getElementById('cpu-usage').textContent = 
                (healthStatus.cpu_usage || 0).toFixed(1);
            document.getElementById('memory-usage').textContent = 
                (healthStatus.memory_usage || 0).toFixed(1);
            
            // 更新系统状态
            const statusElement = document.getElementById('system-status');
            const status = healthStatus.overall_status || 'unknown';
            statusElement.textContent = status;
            statusElement.className = 'metric-value status-' + 
                (status === 'healthy' ? 'healthy' : 
                 status === 'warning' ? 'warning' : 'critical');
        }
        
        async function loadAlerts() {
            try {
                const response = await fetch('/api/alerts');
                const data = await response.json();
                
                const container = document.getElementById('alerts-container');
                if (data.active_alerts.length === 0) {
                    container.innerHTML = '<div style="color: #27ae60;">✅ 无活跃告警</div>';
                } else {
                    container.innerHTML = data.active_alerts.map(alert => 
                        `<div class="status-${alert.severity}">
                            ${alert.severity.toUpperCase()}: ${alert.message}
                        </div>`
                    ).join('');
                }
            } catch (err) {
                document.getElementById('alerts-container').innerHTML = 
                    '<div class="error">加载告警失败</div>';
            }
        }
        
        // 初始加载
        loadMetrics();
        loadAlerts();
        
        // 自动刷新
        setInterval(() => {
            loadMetrics();
            loadAlerts();
        }, 30000); // 30秒刷新一次
    </script>
</body>
</html>
        """
    
    async def start(self):
        """启动Web服务器"""
        print(f"🌐 启动监控Web服务器...")
        print(f"📍 地址: http://{self.host}:{self.port}")
        
        # 启动监控系统
        start_monitoring()
        
        if FASTAPI_AVAILABLE:
            # 使用FastAPI
            config = uvicorn.Config(
                app=self.app,
                host=self.host,
                port=self.port,
                log_level="info"
            )
            server = uvicorn.Server(config)
            await server.serve()
        else:
            # 使用简单HTTP服务器
            await self._start_simple_server()
    
    async def _start_simple_server(self):
        """启动简单HTTP服务器"""
        import http.server
        import socketserver
        import threading
        
        class MonitoringHandler(http.server.SimpleHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/':
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html; charset=utf-8')
                    self.end_headers()
                    html = self._generate_dashboard_html()
                    self.wfile.write(html.encode('utf-8'))
                elif self.path == '/api/metrics':
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    # 这里需要同步调用，简化处理
                    metrics = {"message": "请使用FastAPI版本获取完整功能"}
                    self.wfile.write(json.dumps(metrics).encode('utf-8'))
                else:
                    self.send_error(404)
        
        with socketserver.TCPServer((self.host, self.port), MonitoringHandler) as httpd:
            print(f"✅ 简单HTTP服务器已启动")
            print(f"⚠️ 建议安装FastAPI以获得完整功能: pip install fastapi uvicorn")
            httpd.serve_forever()


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="监控仪表板Web服务器")
    parser.add_argument("--port", type=int, default=8080, help="端口号，默认8080")
    parser.add_argument("--host", default="127.0.0.1", help="主机地址，默认127.0.0.1")
    
    args = parser.parse_args()
    
    print("🎯 监控仪表板Web服务器")
    print("="*40)
    
    server = MonitoringWebServer(port=args.port, host=args.host)
    
    try:
        await server.start()
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
