#!/usr/bin/env python3
"""
监控仪表板启动脚本

功能：
1. 启动系统监控
2. 提供Web界面访问监控数据
3. 实时显示关键指标
4. 支持数据导出和告警管理

使用方法：
python start_monitoring.py [--port 8080] [--host 0.0.0.0] [--export-interval 300]
"""

import asyncio
import argparse
import json
import logging
import signal
import sys
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from backend.utils.performance_monitor import performance_monitor
    from backend.monitoring.performance_monitor import PerformanceMonitor
    print("✅ 成功导入监控模块")
except ImportError as e:
    print(f"❌ 导入监控模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)


class MonitoringService:
    """监控服务"""
    
    def __init__(self, export_interval=300):
        self.export_interval = export_interval  # 数据导出间隔（秒）
        self.running = False
        self.logger = logging.getLogger(__name__)
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在优雅关闭...")
        self.stop()
    
    async def start(self):
        """启动监控服务"""
        print("🚀 启动监控服务...")
        
        try:
            # 启动性能监控
            if not performance_monitor.enabled:
                performance_monitor.enabled = True
                performance_monitor.start_monitoring()
            self.running = True

            print("✅ 监控系统已启动")
            print("📊 开始收集监控数据...")

            # 显示初始状态
            await self._display_initial_status()

            # 启动监控循环
            await self._monitoring_loop()
            
        except Exception as e:
            self.logger.error(f"启动监控服务失败: {e}", exc_info=True)
            print(f"❌ 启动失败: {e}")
            self.stop()
    
    async def _display_initial_status(self):
        """显示初始状态"""
        print("\n" + "="*60)
        print("📊 监控仪表板状态")
        print("="*60)
        
        try:
            # 获取性能监控数据
            if performance_monitor.enabled:
                # 获取系统指标
                system_metrics = performance_monitor.get_system_metrics()

                print(f"🎯 系统状态: {'运行中' if performance_monitor.enabled else '未启动'}")
                print(f"💾 CPU使用率: {system_metrics.cpu_percent:.1f}%")
                print(f"🧠 内存使用率: {system_metrics.memory_percent:.1f}%")
                print(f"🧵 线程数: {system_metrics.thread_count}")
                print(f"💽 磁盘使用率: {system_metrics.disk_usage_percent:.1f}%")

                # 显示API指标统计
                api_count = len(performance_monitor.api_metrics)
                llm_count = len(performance_monitor.llm_metrics)
                db_count = len(performance_monitor.db_metrics)

                print(f"📊 监控指标: API({api_count}) LLM({llm_count}) DB({db_count})")
            else:
                print("⚠️ 性能监控未启用")

        except Exception as e:
            print(f"⚠️ 显示初始状态失败: {e}")
    
    async def _monitoring_loop(self):
        """监控主循环"""
        last_export_time = time.time()
        display_interval = 30  # 显示间隔30秒
        last_display_time = time.time()
        
        print(f"\n🔄 监控循环已启动（显示间隔: {display_interval}秒，导出间隔: {self.export_interval}秒）")
        print("按 Ctrl+C 停止监控\n")
        
        while self.running:
            try:
                current_time = time.time()
                
                # 定期显示监控数据
                if current_time - last_display_time >= display_interval:
                    await self._display_monitoring_data()
                    last_display_time = current_time
                
                # 定期导出数据
                if current_time - last_export_time >= self.export_interval:
                    await self._export_monitoring_data()
                    last_export_time = current_time
                
                # 检查告警
                await self._check_alerts()
                
                # 短暂休眠
                await asyncio.sleep(5)
                
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}", exc_info=True)
                await asyncio.sleep(10)  # 错误时等待更长时间
    
    async def _display_monitoring_data(self):
        """显示监控数据"""
        try:
            if not performance_monitor.enabled:
                print(f"⚠️ {datetime.now().strftime('%H:%M:%S')} - 性能监控未启用")
                return

            # 获取系统指标
            system_metrics = performance_monitor.get_system_metrics()

            # 计算一些基本统计
            api_count = len(performance_monitor.api_metrics)
            llm_count = len(performance_monitor.llm_metrics)
            db_count = len(performance_monitor.db_metrics)
            
            # 清屏并显示数据
            print("\033[2J\033[H")  # 清屏
            print("📊 实时监控数据")
            print("="*60)
            print(f"⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            
            # 性能指标
            print("🚀 性能指标:")
            print(f"   响应时间: {real_time_stats.get('avg_response_time', 0):.2f}ms (P95: {real_time_stats.get('p95_response_time', 0):.2f}ms)")
            print(f"   请求速率: {real_time_stats.get('requests_per_minute', 0):.1f}/min")
            print(f"   成功率: {real_time_stats.get('success_rate', 0):.1f}%")
            print(f"   错误率: {real_time_stats.get('error_rate', 0):.1f}%")
            print()
            
            # 业务指标
            print("📈 业务指标:")
            print(f"   加速率: {real_time_stats.get('acceleration_rate', 0):.1f}%")
            print(f"   用户满意度: {real_time_stats.get('user_satisfaction', 0):.1f}/5.0")
            
            # 关键词匹配性能
            keyword_stats = performance_summary.get("keyword_matching", {})
            print(f"   关键词匹配: {keyword_stats.get('avg_time', 0):.2f}ms (总计: {keyword_stats.get('total_matches', 0)})")
            print()
            
            # 系统状态
            print("💻 系统状态:")
            print(f"   整体状态: {health_status.get('overall_status', 'unknown')}")
            print(f"   CPU使用率: {health_status.get('cpu_usage', 0):.1f}%")
            print(f"   内存使用率: {health_status.get('memory_usage', 0):.1f}%")
            print(f"   活跃线程: {health_status.get('active_threads', 0)}")
            print(f"   数据库状态: {health_status.get('database_status', 'unknown')}")
            print()
            
            # 告警状态
            print("🚨 告警状态:")
            print(f"   活跃告警: {alert_summary.get('active_alerts', 0)}")
            print(f"   今日告警: {alert_summary.get('total_alerts_today', 0)}")
            print(f"   严重告警: {alert_summary.get('critical_alerts', 0)}")
            print()
            
            print("按 Ctrl+C 停止监控")
            print("="*60)
            
        except Exception as e:
            print(f"⚠️ 显示监控数据失败: {e}")
    
    async def _export_monitoring_data(self):
        """导出监控数据"""
        try:
            filename = await monitoring_dashboard.export_metrics_data()
            print(f"📁 {datetime.now().strftime('%H:%M:%S')} - 数据已导出: {filename}")
        except Exception as e:
            print(f"⚠️ 导出数据失败: {e}")
    
    async def _check_alerts(self):
        """检查告警"""
        try:
            active_alerts = system_monitor.alert_manager.active_alerts
            if active_alerts:
                for alert in active_alerts:
                    print(f"🚨 {alert.severity.upper()}: {alert.message} (规则: {alert.rule_name})")
        except Exception as e:
            self.logger.error(f"检查告警失败: {e}")
    
    def stop(self):
        """停止监控服务"""
        print("\n🛑 正在停止监控服务...")
        self.running = False
        
        try:
            performance_monitor.shutdown()
            print("✅ 监控服务已停止")
        except Exception as e:
            print(f"⚠️ 停止监控时出现错误: {e}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="监控仪表板启动脚本")
    parser.add_argument("--export-interval", type=int, default=300, 
                       help="数据导出间隔（秒），默认300秒")
    parser.add_argument("--verbose", "-v", action="store_true", 
                       help="启用详细日志")
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🎯 监控仪表板启动器")
    print("="*40)
    print(f"数据导出间隔: {args.export_interval}秒")
    print(f"日志级别: {'DEBUG' if args.verbose else 'INFO'}")
    print()
    
    # 创建并启动监控服务
    service = MonitoringService(export_interval=args.export_interval)
    
    try:
        await service.start()
    except KeyboardInterrupt:
        print("\n用户中断，正在退出...")
    except Exception as e:
        print(f"❌ 服务运行失败: {e}")
        logging.getLogger(__name__).error(f"服务运行失败: {e}", exc_info=True)
    finally:
        service.stop()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
