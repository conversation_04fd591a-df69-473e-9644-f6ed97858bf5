# 监控仪表板运行指南

## 📊 **监控系统概述**

我们的监控系统提供了全面的实时监控能力，包括：
- **性能监控**: 响应时间、吞吐量、错误率
- **业务监控**: 加速率、匹配准确率、用户满意度
- **系统监控**: CPU、内存、线程数等资源使用
- **告警管理**: 实时告警、历史记录、规则配置

## 🚀 **快速启动监控系统**

### **方法1: 在Python代码中启动**

```python
# 导入监控模块
from backend.monitoring.dashboard import start_monitoring, monitoring_dashboard

# 启动监控
start_monitoring()

# 获取实时数据
dashboard_data = await monitoring_dashboard.get_real_time_metrics()
print(dashboard_data)
```

### **方法2: 独立运行监控服务**

```bash
# 运行监控仪表板服务
python run_monitoring_dashboard.py

# 或者使用我们创建的启动脚本
python start_monitoring.py
```

### **方法3: 集成到主应用**

监控系统已经集成到主应用中，当您启动主服务时会自动启动监控。

## 📈 **监控数据访问方式**

### **1. 实时API接口**

```python
# 获取实时指标
real_time_data = await monitoring_dashboard.get_real_time_metrics()

# 获取历史数据
history_data = await monitoring_dashboard.get_historical_data(
    start_time=datetime.now() - timedelta(hours=1),
    end_time=datetime.now()
)

# 获取性能报告
performance_report = await monitoring_dashboard.generate_performance_report()
```

### **2. 数据导出**

```python
# 导出监控数据到JSON文件
filename = await monitoring_dashboard.export_metrics_data()
print(f"数据已导出到: {filename}")
```

### **3. Web界面访问**

监控数据可以通过Web界面访问（需要启动Web服务）。

## 🎯 **监控指标说明**

### **性能指标**
- `avg_response_time`: 平均响应时间（毫秒）
- `p95_response_time`: 95%分位响应时间
- `p99_response_time`: 99%分位响应时间
- `requests_per_minute`: 每分钟请求数
- `success_rate`: 成功率（%）
- `error_rate`: 错误率（%）

### **业务指标**
- `acceleration_rate`: 关键词加速率（%）
- `keyword_matching_accuracy`: 关键词匹配准确率
- `user_satisfaction`: 用户满意度评分
- `intent_recognition_accuracy`: 意图识别准确率

### **系统指标**
- `cpu_usage`: CPU使用率（%）
- `memory_usage`: 内存使用率（%）
- `active_threads`: 活跃线程数
- `database_connections`: 数据库连接数

### **告警指标**
- `active_alerts`: 活跃告警列表
- `alert_count`: 告警总数
- `critical_alerts`: 严重告警数

## 🔧 **监控配置**

### **告警阈值配置**

```python
# 配置响应时间告警
system_monitor.alert_manager.add_rule(
    name="high_response_time",
    condition="avg_response_time > 1000",  # 响应时间超过1秒
    severity="warning",
    message="平均响应时间过高"
)

# 配置错误率告警
system_monitor.alert_manager.add_rule(
    name="high_error_rate",
    condition="error_rate > 5",  # 错误率超过5%
    severity="critical",
    message="错误率过高"
)
```

### **数据保留配置**

```python
# 设置数据保留时间（小时）
monitoring_dashboard.data_retention_hours = 48  # 保留48小时
```

## 📊 **监控数据示例**

### **实时指标数据结构**

```json
{
  "real_time_stats": {
    "requests_per_minute": 120.5,
    "success_rate": 98.2,
    "error_rate": 1.8,
    "avg_response_time": 45.3,
    "p95_response_time": 120.0,
    "p99_response_time": 250.0,
    "acceleration_rate": 85.6,
    "user_satisfaction": 4.7
  },
  "performance_summary": {
    "keyword_matching": {
      "avg_time": 0.02,
      "p95_time": 0.05,
      "total_matches": 1250
    },
    "intent_recognition": {
      "avg_time": 150.0,
      "p95_time": 300.0,
      "total_recognitions": 890
    }
  },
  "health_status": {
    "overall_status": "healthy",
    "cpu_usage": 25.3,
    "memory_usage": 45.7,
    "active_threads": 12,
    "database_status": "connected"
  },
  "alert_summary": {
    "active_alerts": 0,
    "total_alerts_today": 3,
    "critical_alerts": 0,
    "warning_alerts": 0
  }
}
```

## 🎨 **Web界面功能**

### **主要页面**
1. **概览页面**: 关键指标总览
2. **性能页面**: 详细性能分析
3. **业务页面**: 业务指标监控
4. **系统页面**: 系统资源监控
5. **告警页面**: 告警管理和历史

### **实时更新**
- 数据每30秒自动刷新
- 支持手动刷新
- 实时图表展示趋势

### **交互功能**
- 时间范围选择
- 指标筛选和排序
- 数据导出
- 告警确认和处理

## 🔍 **故障排查**

### **常见问题**

1. **监控数据为空**
   ```python
   # 检查监控是否启动
   if not system_monitor.is_running:
       start_monitoring()
   ```

2. **性能数据不准确**
   ```python
   # 重置性能计数器
   system_monitor.performance.reset_counters()
   ```

3. **告警不触发**
   ```python
   # 检查告警规则
   rules = system_monitor.alert_manager.get_rules()
   print(f"当前告警规则: {rules}")
   ```

### **日志查看**

```bash
# 查看监控日志
tail -f logs/monitoring.log

# 查看性能日志
tail -f logs/performance/performance.log
```

## 📱 **移动端支持**

监控界面支持响应式设计，可以在移动设备上查看：
- 关键指标卡片布局
- 触摸友好的交互
- 简化的图表展示

## 🔐 **安全考虑**

### **访问控制**
- 监控数据包含敏感信息，建议配置访问控制
- 支持基于角色的权限管理
- 可配置IP白名单

### **数据脱敏**
- 自动脱敏用户敏感信息
- 可配置脱敏规则
- 支持数据加密存储

## 🎯 **最佳实践**

### **监控策略**
1. **分层监控**: 应用层 → 业务层 → 系统层
2. **关键指标**: 专注于影响用户体验的核心指标
3. **告警分级**: 区分不同严重程度的告警
4. **趋势分析**: 关注长期趋势而非短期波动

### **性能优化**
1. **监控开销**: 控制监控本身的性能开销
2. **数据采样**: 对高频数据进行采样
3. **异步处理**: 使用异步方式收集和处理数据
4. **缓存策略**: 合理使用缓存减少计算开销

### **运维建议**
1. **定期检查**: 定期检查监控系统健康状态
2. **数据备份**: 定期备份重要的监控数据
3. **容量规划**: 根据监控数据进行容量规划
4. **持续改进**: 根据监控结果持续优化系统

## 🚀 **高级功能**

### **自定义指标**
```python
# 添加自定义业务指标
system_monitor.business_metrics.add_custom_metric(
    name="custom_conversion_rate",
    value=0.85,
    tags={"service": "recommendation"}
)
```

### **集成外部系统**
```python
# 集成Prometheus
from backend.monitoring.integrations import PrometheusExporter
prometheus_exporter = PrometheusExporter()
prometheus_exporter.start()
```

### **机器学习预测**
```python
# 启用异常检测
system_monitor.enable_anomaly_detection()

# 获取预测结果
predictions = await system_monitor.get_performance_predictions()
```

---

**通过这个全面的监控系统，您可以实时了解系统状态，快速发现和解决问题，确保系统稳定高效运行！** 🎉
