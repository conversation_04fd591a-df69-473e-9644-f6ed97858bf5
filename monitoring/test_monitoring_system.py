#!/usr/bin/env python3
"""
监控系统测试脚本

功能：
1. 测试监控系统的基本功能
2. 验证数据收集和展示
3. 模拟负载测试监控效果
4. 检查告警系统

使用方法：
python test_monitoring_system.py
"""

import asyncio
import json
import logging
import sys
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from backend.utils.monitoring_dashboard import start_monitoring, stop_monitoring, monitoring_dashboard
    from backend.utils.system_monitor import system_monitor
    print("✅ 成功导入监控模块")
except ImportError as e:
    print(f"❌ 导入监控模块失败: {e}")
    sys.exit(1)


class MonitoringSystemTester:
    """监控系统测试器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 监控系统测试开始")
        print("="*50)
        
        try:
            # 1. 基础功能测试
            await self.test_basic_functionality()
            
            # 2. 数据收集测试
            await self.test_data_collection()
            
            # 3. 仪表板测试
            await self.test_dashboard()
            
            # 4. 告警系统测试
            await self.test_alert_system()
            
            # 5. 性能测试
            await self.test_performance_monitoring()
            
            # 6. 数据导出测试
            await self.test_data_export()
            
            print("\n🎉 所有测试完成！")
            
        except Exception as e:
            print(f"\n❌ 测试过程中发生异常: {e}")
            import traceback
            traceback.print_exc()
    
    async def test_basic_functionality(self):
        """测试基础功能"""
        print("\n🔧 测试基础功能...")
        
        # 启动监控
        print("   启动监控系统...")
        start_monitoring()
        
        # 检查监控状态
        if system_monitor.is_running:
            print("   ✅ 监控系统启动成功")
        else:
            print("   ❌ 监控系统启动失败")
            return
        
        # 等待一下让系统初始化
        await asyncio.sleep(2)
        
        # 检查各个组件
        components = [
            ("performance", "性能监控"),
            ("business_metrics", "业务指标"),
            ("alert_manager", "告警管理器")
        ]
        
        for component_name, display_name in components:
            component = getattr(system_monitor, component_name, None)
            if component:
                print(f"   ✅ {display_name}组件正常")
            else:
                print(f"   ❌ {display_name}组件缺失")
    
    async def test_data_collection(self):
        """测试数据收集"""
        print("\n📊 测试数据收集...")
        
        # 模拟一些性能数据
        print("   模拟性能数据...")
        
        # 记录响应时间
        for i in range(10):
            response_time = 50 + i * 10  # 50ms到140ms
            system_monitor.performance.record_histogram("response_time", response_time)
            system_monitor.performance.increment_counter("total_requests")
            
            if i < 8:  # 80%成功率
                system_monitor.performance.increment_counter("successful_requests")
            else:
                system_monitor.performance.increment_counter("failed_requests")
        
        # 记录关键词匹配时间
        for i in range(5):
            match_time = 0.01 + i * 0.005  # 0.01ms到0.035ms
            system_monitor.performance.record_histogram("keyword_matching_time", match_time)
            system_monitor.performance.increment_counter("keyword_matching_total")
        
        # 记录业务指标
        system_monitor.business_metrics.record_acceleration_result("keyword_accelerated", True, 0.01)  # 加速成功
        system_monitor.business_metrics.record_acceleration_result("keyword_accelerated", True, 0.02)
        system_monitor.business_metrics.record_acceleration_result("keyword_accelerated", False, 150.0)  # 加速失败

        system_monitor.business_metrics.record_user_satisfaction("session_1", 4.5)
        system_monitor.business_metrics.record_user_satisfaction("session_2", 4.8)
        system_monitor.business_metrics.record_user_satisfaction("session_3", 4.2)
        
        print("   ✅ 数据收集完成")
        
        # 验证数据
        total_requests = system_monitor.performance.counters.get("total_requests", 0)
        keyword_matches = system_monitor.performance.counters.get("keyword_matching_total", 0)
        
        print(f"   📈 总请求数: {total_requests}")
        print(f"   🔍 关键词匹配数: {keyword_matches}")
        
        if total_requests > 0 and keyword_matches > 0:
            print("   ✅ 数据收集验证成功")
        else:
            print("   ❌ 数据收集验证失败")
    
    async def test_dashboard(self):
        """测试仪表板"""
        print("\n📊 测试仪表板...")
        
        try:
            # 获取实时指标
            dashboard_data = await monitoring_dashboard.get_real_time_metrics()
            
            if "error" in dashboard_data:
                print(f"   ❌ 获取仪表板数据失败: {dashboard_data['error']}")
                return
            
            # 检查数据结构
            required_sections = ["real_time_stats", "performance_summary", "health_status", "alert_summary"]
            missing_sections = []
            
            for section in required_sections:
                if section not in dashboard_data:
                    missing_sections.append(section)
            
            if not missing_sections:
                print("   ✅ 仪表板数据结构完整")
            else:
                print(f"   ⚠️ 仪表板缺少数据段: {missing_sections}")
            
            # 显示关键指标
            real_time_stats = dashboard_data.get("real_time_stats", {})
            print(f"   📊 关键指标:")
            print(f"      平均响应时间: {real_time_stats.get('avg_response_time', 0):.2f}ms")
            print(f"      成功率: {real_time_stats.get('success_rate', 0):.1f}%")
            print(f"      加速率: {real_time_stats.get('acceleration_rate', 0):.1f}%")
            print(f"      用户满意度: {real_time_stats.get('user_satisfaction', 0):.1f}/5.0")
            
        except Exception as e:
            print(f"   ❌ 仪表板测试失败: {e}")
    
    async def test_alert_system(self):
        """测试告警系统"""
        print("\n🚨 测试告警系统...")
        
        try:
            # 添加测试告警规则
            print("   添加测试告警规则...")
            
            # 高响应时间告警
            system_monitor.alert_manager.add_rule(
                name="test_high_response_time",
                condition="avg_response_time > 100",
                severity="warning",
                message="测试：平均响应时间过高"
            )
            
            # 低成功率告警
            system_monitor.alert_manager.add_rule(
                name="test_low_success_rate",
                condition="success_rate < 90",
                severity="critical",
                message="测试：成功率过低"
            )
            
            print("   ✅ 告警规则添加成功")
            
            # 检查告警
            await asyncio.sleep(1)  # 等待告警检查
            
            active_alerts = system_monitor.alert_manager.active_alerts
            print(f"   📊 活跃告警数: {len(active_alerts)}")
            
            for alert in active_alerts:
                print(f"      🚨 {alert.severity.upper()}: {alert.message}")
            
            # 清理测试告警规则
            system_monitor.alert_manager.remove_rule("test_high_response_time")
            system_monitor.alert_manager.remove_rule("test_low_success_rate")
            
            print("   ✅ 告警系统测试完成")
            
        except Exception as e:
            print(f"   ❌ 告警系统测试失败: {e}")
    
    async def test_performance_monitoring(self):
        """测试性能监控"""
        print("\n⚡ 测试性能监控...")
        
        try:
            # 模拟负载测试
            print("   执行负载测试...")
            
            start_time = time.time()
            
            # 模拟100个请求
            for i in range(100):
                # 模拟不同的响应时间
                if i % 10 == 0:
                    response_time = 200  # 偶尔有慢请求
                else:
                    response_time = 20 + (i % 5) * 10  # 20-60ms
                
                system_monitor.performance.record_histogram("response_time", response_time)
                system_monitor.performance.increment_counter("total_requests")
                
                # 模拟关键词匹配
                if i % 3 == 0:  # 33%的请求使用关键词匹配
                    match_time = 0.01 + (i % 3) * 0.005
                    system_monitor.performance.record_histogram("keyword_matching_time", match_time)
                    system_monitor.business_metrics.record_acceleration_result("keyword_accelerated", True, match_time)
                else:
                    system_monitor.business_metrics.record_acceleration_result("keyword_accelerated", False, response_time)
                
                # 短暂延迟模拟真实负载
                if i % 10 == 0:
                    await asyncio.sleep(0.01)
            
            load_test_time = (time.time() - start_time) * 1000
            print(f"   ✅ 负载测试完成，耗时: {load_test_time:.2f}ms")
            
            # 获取性能统计
            response_time_stats = system_monitor.performance.get_histogram_stats("response_time")
            keyword_stats = system_monitor.performance.get_histogram_stats("keyword_matching_time")
            
            print(f"   📊 性能统计:")
            print(f"      响应时间 - 平均: {response_time_stats.get('mean', 0):.2f}ms, P95: {response_time_stats.get('p95', 0):.2f}ms")
            print(f"      关键词匹配 - 平均: {keyword_stats.get('mean', 0):.3f}ms")
            print(f"      加速率: {system_monitor.business_metrics.get_acceleration_rate():.1f}%")
            
        except Exception as e:
            print(f"   ❌ 性能监控测试失败: {e}")
    
    async def test_data_export(self):
        """测试数据导出"""
        print("\n📁 测试数据导出...")
        
        try:
            # 导出监控数据
            filename = await monitoring_dashboard.export_metrics_data()
            
            # 检查文件是否存在
            if Path(filename).exists():
                print(f"   ✅ 数据导出成功: {filename}")
                
                # 检查文件内容
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 验证数据结构
                required_keys = ["timestamp", "real_time_stats", "performance_summary"]
                missing_keys = [key for key in required_keys if key not in data]
                
                if not missing_keys:
                    print("   ✅ 导出数据结构完整")
                else:
                    print(f"   ⚠️ 导出数据缺少字段: {missing_keys}")
                
                # 显示文件大小
                file_size = Path(filename).stat().st_size
                print(f"   📊 文件大小: {file_size} 字节")
                
            else:
                print(f"   ❌ 导出文件不存在: {filename}")
                
        except Exception as e:
            print(f"   ❌ 数据导出测试失败: {e}")
    
    def cleanup(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        
        try:
            # 停止监控
            stop_monitoring()
            print("   ✅ 监控系统已停止")
            
            # 清理测试数据文件
            export_dir = Path("monitoring_exports")
            if export_dir.exists():
                test_files = list(export_dir.glob("metrics_*.json"))
                for file in test_files[-5:]:  # 只保留最新的5个文件
                    continue
                for file in test_files[:-5]:  # 删除旧的测试文件
                    try:
                        file.unlink()
                    except:
                        pass
                print(f"   ✅ 清理了 {max(0, len(test_files) - 5)} 个旧的导出文件")
            
        except Exception as e:
            print(f"   ⚠️ 清理过程中出现错误: {e}")


async def main():
    """主函数"""
    print("🎯 监控系统测试器")
    print("="*30)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    tester = MonitoringSystemTester()
    
    try:
        await tester.run_all_tests()
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        tester.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
