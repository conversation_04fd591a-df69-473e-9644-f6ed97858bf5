#!/usr/bin/env python3
"""
聚焦完整性检查 - 专注于核心业务代码
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Any
from dataclasses import dataclass
from datetime import datetime


@dataclass
class CheckResult:
    """检查结果"""
    category: str
    item: str
    status: str  # "PASS", "FAIL", "WARNING"
    details: str
    file_path: str = ""


class FocusedCompletionChecker:
    """聚焦完整性检查器 - 只检查核心业务代码"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.results = []
        
        # 核心业务目录
        self.core_dirs = [
            "backend/agents",
            "backend/config", 
            "backend/services",
            "backend/utils"
        ]
        
        # 排除的目录和文件
        self.exclude_patterns = [
            r'.*test.*',
            r'.*backup.*',
            r'.*\.venv.*',
            r'.*__pycache__.*',
            r'.*scripts.*',
            r'.*tools.*',
            r'.*compatibility_layer.*'  # 兼容性层允许混用
        ]
    
    def run_focused_checks(self) -> Dict[str, Any]:
        """运行聚焦检查"""
        print("🎯 开始聚焦完整性检查（仅核心业务代码）...")
        
        # 1. 核心配置管理器统一性检查
        print("  📋 检查核心配置管理器统一性...")
        self.check_core_config_consistency()
        
        # 2. 核心决策引擎统一性检查
        print("  🧠 检查核心决策引擎统一性...")
        self.check_core_decision_engine_consistency()
        
        # 3. 核心状态管理统一性检查
        print("  📊 检查核心状态管理统一性...")
        self.check_core_state_management_consistency()
        
        # 4. 关键文件存在性检查
        print("  📁 检查关键文件存在性...")
        self.check_critical_files()
        
        # 5. 核心功能完整性检查
        print("  ⚙️ 检查核心功能完整性...")
        self.check_core_functionality()
        
        return self.generate_focused_report()
    
    def check_core_config_consistency(self):
        """检查核心配置管理器统一性"""
        old_usage_files = []
        new_usage_files = []
        mixed_usage_files = []
        
        old_patterns = [
            r'from.*config_manager.*import',
            r'ConfigManager\(',
            r'from.*unified_config_manager.*import.*unified_config_manager'
        ]
        
        new_patterns = [
            r'from.*unified_config_loader.*import.*get_unified_config',
            r'get_unified_config\('
        ]
        
        for file_path in self.get_core_python_files():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                old_matches = any(re.search(pattern, content) for pattern in old_patterns)
                new_matches = any(re.search(pattern, content) for pattern in new_patterns)
                
                if old_matches and new_matches:
                    mixed_usage_files.append(str(file_path.relative_to(self.project_root)))
                elif old_matches:
                    old_usage_files.append(str(file_path.relative_to(self.project_root)))
                elif new_matches:
                    new_usage_files.append(str(file_path.relative_to(self.project_root)))
                    
            except Exception as e:
                continue
        
        # 评估结果
        total_config_files = len(old_usage_files) + len(new_usage_files) + len(mixed_usage_files)
        
        if mixed_usage_files:
            self.results.append(CheckResult(
                category="核心配置管理器",
                item="新旧混用",
                status="FAIL",
                details=f"发现{len(mixed_usage_files)}个核心文件混用新旧配置管理器: {mixed_usage_files}"
            ))
        
        if old_usage_files:
            self.results.append(CheckResult(
                category="核心配置管理器",
                item="旧版本使用",
                status="FAIL",
                details=f"发现{len(old_usage_files)}个核心文件仍使用旧配置管理器: {old_usage_files}"
            ))
        
        if new_usage_files and not old_usage_files and not mixed_usage_files:
            self.results.append(CheckResult(
                category="核心配置管理器",
                item="统一性检查",
                status="PASS",
                details=f"所有{len(new_usage_files)}个核心文件都使用新的统一配置管理器"
            ))
        elif total_config_files == 0:
            self.results.append(CheckResult(
                category="核心配置管理器",
                item="使用情况",
                status="WARNING",
                details="未在核心文件中发现配置管理器使用"
            ))
    
    def check_core_decision_engine_consistency(self):
        """检查核心决策引擎统一性"""
        old_usage_files = []
        new_usage_files = []
        
        old_patterns = [
            r'from\s+.*accelerated_intent_decision_engine.*import.*AcceleratedIntentDecisionEngine',
            r'from\s+.*intent_decision_engine.*import',
            r'AcceleratedIntentDecisionEngine\(',
            r'class.*AcceleratedIntentDecisionEngine'
        ]
        
        new_patterns = [
            r'SimplifiedDecisionEngine',
            r'get_simplified_decision_engine',
            r'from.*simplified_decision_engine.*import'
        ]
        
        for file_path in self.get_core_python_files():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                old_matches = any(re.search(pattern, content) for pattern in old_patterns)
                new_matches = any(re.search(pattern, content) for pattern in new_patterns)
                
                if old_matches:
                    old_usage_files.append(str(file_path.relative_to(self.project_root)))
                if new_matches:
                    new_usage_files.append(str(file_path.relative_to(self.project_root)))
                    
            except Exception as e:
                continue
        
        # 评估结果
        if old_usage_files:
            self.results.append(CheckResult(
                category="核心决策引擎",
                item="旧版本使用",
                status="FAIL",
                details=f"发现{len(old_usage_files)}个核心文件仍使用旧决策引擎: {old_usage_files}"
            ))
        
        if new_usage_files:
            self.results.append(CheckResult(
                category="核心决策引擎",
                item="新版本使用",
                status="PASS",
                details=f"发现{len(new_usage_files)}个核心文件使用新的简化决策引擎"
            ))
        
        if not old_usage_files and not new_usage_files:
            self.results.append(CheckResult(
                category="核心决策引擎",
                item="使用情况",
                status="WARNING",
                details="未在核心文件中发现决策引擎使用"
            ))
    
    def check_core_state_management_consistency(self):
        """检查核心状态管理统一性"""
        unified_state_usage = []
        direct_state_access = []
        
        unified_patterns = [
            r'from.*unified_state_manager.*import',
            r'get_state_manager\(',
            r'UnifiedStateManager'
        ]
        
        direct_patterns = [
            r'session\[.*state.*\]',
            r'\.state\s*=\s*["\']'
        ]
        
        for file_path in self.get_core_python_files():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                unified_matches = any(re.search(pattern, content) for pattern in unified_patterns)
                direct_matches = any(re.search(pattern, content) for pattern in direct_patterns)
                
                if unified_matches:
                    unified_state_usage.append(str(file_path.relative_to(self.project_root)))
                if direct_matches:
                    direct_state_access.append(str(file_path.relative_to(self.project_root)))
                    
            except Exception as e:
                continue
        
        # 评估结果
        if direct_state_access:
            self.results.append(CheckResult(
                category="核心状态管理",
                item="直接访问",
                status="WARNING",
                details=f"发现{len(direct_state_access)}个核心文件直接访问状态: {direct_state_access}"
            ))
        
        if unified_state_usage:
            self.results.append(CheckResult(
                category="核心状态管理",
                item="统一管理",
                status="PASS",
                details=f"发现{len(unified_state_usage)}个核心文件使用统一状态管理器"
            ))
    
    def check_critical_files(self):
        """检查关键文件存在性"""
        critical_files = [
            'backend/config/unified_config.yaml',
            'backend/config/unified_config_loader.py',
            'backend/agents/unified_state_manager.py',
            'backend/agents/conversation_state_machine.py',
            'backend/agents/simplified_decision_engine.py',
            'backend/agents/unified_llm_client_factory.py'
        ]
        
        missing_files = []
        existing_files = []
        
        for file_path in critical_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                existing_files.append(file_path)
            else:
                missing_files.append(file_path)
        
        # 评估结果
        if missing_files:
            self.results.append(CheckResult(
                category="关键文件",
                item="文件缺失",
                status="FAIL",
                details=f"缺少{len(missing_files)}个关键文件: {missing_files}"
            ))
        
        if existing_files:
            self.results.append(CheckResult(
                category="关键文件",
                item="文件存在",
                status="PASS",
                details=f"所有{len(existing_files)}个关键文件都存在"
            ))
    
    def check_core_functionality(self):
        """检查核心功能完整性"""
        # 检查统一配置文件内容
        config_file = self.project_root / 'backend/config/unified_config.yaml'
        if config_file.exists():
            try:
                import yaml
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                required_sections = ['system', 'llm', 'conversation', 'keyword_rules', 'message_templates']
                missing_sections = [section for section in required_sections if section not in config_data]
                
                if missing_sections:
                    self.results.append(CheckResult(
                        category="核心功能",
                        item="配置完整性",
                        status="FAIL",
                        details=f"统一配置文件缺少必需部分: {missing_sections}"
                    ))
                else:
                    self.results.append(CheckResult(
                        category="核心功能",
                        item="配置完整性",
                        status="PASS",
                        details="统一配置文件包含所有必需部分"
                    ))
                    
            except Exception as e:
                self.results.append(CheckResult(
                    category="核心功能",
                    item="配置格式",
                    status="FAIL",
                    details=f"统一配置文件格式错误: {e}"
                ))
        else:
            self.results.append(CheckResult(
                category="核心功能",
                item="配置文件",
                status="FAIL",
                details="统一配置文件不存在"
            ))
        
        # 检查LLM配置类型
        if config_file.exists():
            try:
                import yaml
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                if 'llm' in config_data:
                    llm_types = list(config_data['llm'].keys())
                    expected_types = ['default', 'domain_classification', 'conversation', 'requirement_collection']
                    
                    if len(llm_types) >= 4:
                        self.results.append(CheckResult(
                            category="核心功能",
                            item="LLM配置类型",
                            status="PASS",
                            details=f"LLM配置包含{len(llm_types)}种类型: {llm_types[:4]}..."
                        ))
                    else:
                        self.results.append(CheckResult(
                            category="核心功能",
                            item="LLM配置类型",
                            status="WARNING",
                            details=f"LLM配置类型较少: {llm_types}"
                        ))
                        
            except Exception as e:
                pass
    
    def get_core_python_files(self):
        """获取核心Python文件"""
        for core_dir in self.core_dirs:
            dir_path = self.project_root / core_dir
            if dir_path.exists():
                for file_path in dir_path.rglob('*.py'):
                    # 排除测试和备份文件
                    if not any(re.match(pattern, str(file_path)) for pattern in self.exclude_patterns):
                        yield file_path
    
    def generate_focused_report(self) -> Dict[str, Any]:
        """生成聚焦报告"""
        # 统计结果
        total_checks = len(self.results)
        passed_checks = len([r for r in self.results if r.status == "PASS"])
        failed_checks = len([r for r in self.results if r.status == "FAIL"])
        warning_checks = len([r for r in self.results if r.status == "WARNING"])
        
        # 按类别分组
        by_category = {}
        for result in self.results:
            if result.category not in by_category:
                by_category[result.category] = []
            by_category[result.category].append(result)
        
        # 生成报告
        report = {
            "timestamp": datetime.now().isoformat(),
            "scope": "核心业务代码",
            "summary": {
                "total_checks": total_checks,
                "passed_checks": passed_checks,
                "failed_checks": failed_checks,
                "warning_checks": warning_checks,
                "success_rate": passed_checks / total_checks if total_checks > 0 else 0
            },
            "by_category": {},
            "critical_issues": [],
            "recommendations": []
        }
        
        # 按类别整理结果
        for category, results in by_category.items():
            report["by_category"][category] = {
                "total": len(results),
                "passed": len([r for r in results if r.status == "PASS"]),
                "failed": len([r for r in results if r.status == "FAIL"]),
                "warnings": len([r for r in results if r.status == "WARNING"]),
                "details": [
                    {
                        "item": r.item,
                        "status": r.status,
                        "details": r.details,
                        "file_path": r.file_path
                    }
                    for r in results
                ]
            }
        
        # 识别关键问题
        critical_issues = [r for r in self.results if r.status == "FAIL"]
        report["critical_issues"] = [
            {
                "category": r.category,
                "item": r.item,
                "details": r.details,
                "file_path": r.file_path
            }
            for r in critical_issues
        ]
        
        # 生成建议
        if failed_checks == 0:
            report["recommendations"].append("🎉 核心业务代码完整性检查全部通过！")
        else:
            report["recommendations"].append(f"需要修复{failed_checks}个关键问题")
        
        if warning_checks > 0:
            report["recommendations"].append(f"建议关注{warning_checks}个警告项")
        
        return report


def main():
    """主函数"""
    checker = FocusedCompletionChecker()
    report = checker.run_focused_checks()
    
    # 保存报告
    with open("focused_completion_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 打印摘要
    print(f"\n📊 聚焦完整性检查完成!")
    print(f"检查范围: {report['scope']}")
    print(f"总检查项: {report['summary']['total_checks']}")
    print(f"通过: {report['summary']['passed_checks']}")
    print(f"失败: {report['summary']['failed_checks']}")
    print(f"警告: {report['summary']['warning_checks']}")
    print(f"成功率: {report['summary']['success_rate']:.1%}")
    
    # 打印关键问题
    if report['critical_issues']:
        print(f"\n🚨 关键问题 ({len(report['critical_issues'])}个):")
        for issue in report['critical_issues']:
            print(f"  - {issue['category']}: {issue['item']}")
            print(f"    {issue['details']}")
    
    # 打印建议
    if report['recommendations']:
        print(f"\n💡 建议:")
        for rec in report['recommendations']:
            print(f"  - {rec}")
    
    print(f"\n📄 详细报告已保存到: focused_completion_report.json")


if __name__ == "__main__":
    main()
