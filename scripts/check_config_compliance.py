#!/usr/bin/env python3
"""
配置规范检查脚本

根据 .augment/rules/开发规范.md 检查配置文件是否符合规范：
1. 检查配置重复和冗余
2. 检查硬编码问题
3. 检查配置命名规范
4. 检查配置结构一致性
5. 生成配置质量报告

使用方法：
    python scripts/check_config_compliance.py
    python scripts/check_config_compliance.py --fix  # 自动修复部分问题
"""

import os
import sys
import yaml
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Set, Tuple
from collections import defaultdict, Counter
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class ConfigComplianceChecker:
    """配置规范检查器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.config_dir = project_root / "backend" / "config"
        self.violations = []
        self.warnings = []
        self.suggestions = []
        
    def check_all_compliance(self) -> Dict[str, Any]:
        """检查所有配置规范"""
        print("🔍 开始配置规范检查...")
        
        results = {
            "violations": [],
            "warnings": [],
            "suggestions": [],
            "summary": {}
        }
        
        # 1. 检查配置重复
        print("📋 检查配置重复...")
        duplicates = self.check_config_duplicates()
        if duplicates:
            results["violations"].extend(duplicates)
        
        # 2. 检查硬编码
        print("🔍 检查硬编码问题...")
        hardcodes = self.check_hardcode_violations()
        if hardcodes:
            results["violations"].extend(hardcodes)
        
        # 3. 检查命名规范
        print("📝 检查命名规范...")
        naming_issues = self.check_naming_conventions()
        if naming_issues:
            results["warnings"].extend(naming_issues)
        
        # 4. 检查配置结构
        print("🏗️ 检查配置结构...")
        structure_issues = self.check_config_structure()
        if structure_issues:
            results["warnings"].extend(structure_issues)
        
        # 5. 生成改进建议
        print("💡 生成改进建议...")
        suggestions = self.generate_suggestions()
        results["suggestions"].extend(suggestions)
        
        # 6. 生成摘要
        results["summary"] = {
            "total_violations": len(results["violations"]),
            "total_warnings": len(results["warnings"]),
            "total_suggestions": len(results["suggestions"]),
            "compliance_score": self.calculate_compliance_score(results)
        }
        
        return results
    
    def check_config_duplicates(self) -> List[Dict[str, Any]]:
        """检查配置重复问题"""
        violations = []
        
        # 检查unified_config.yaml中的重复配置
        unified_config_path = self.config_dir / "unified_config.yaml"
        if unified_config_path.exists():
            with open(unified_config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查问候消息重复
            greeting_templates = self.extract_greeting_templates(config)
            if len(greeting_templates) > 8:  # 超过合理数量
                violations.append({
                    "type": "CONFIG_DUPLICATION",
                    "severity": "HIGH",
                    "file": "unified_config.yaml",
                    "location": "message_templates.greeting",
                    "description": f"发现{len(greeting_templates)}个问候模板，存在重复",
                    "suggestion": "合并相似的问候模板，保留3-5个核心模板即可"
                })
            
            # 检查关键词配置重复
            if self.has_duplicate_keyword_configs(config):
                violations.append({
                    "type": "CONFIG_DUPLICATION", 
                    "severity": "HIGH",
                    "file": "unified_config.yaml",
                    "location": "keyword_rules vs keywords_config.yaml",
                    "description": "关键词配置在多个地方重复定义",
                    "suggestion": "使用统一的keywords_config.yaml，移除unified_config.yaml中的重复配置"
                })
        
        return violations
    
    def check_hardcode_violations(self) -> List[Dict[str, Any]]:
        """检查硬编码违规"""
        violations = []
        
        # 检查Python文件中的硬编码
        python_files = [
            "backend/agents/conversation_flow/core_refactored.py",
            "backend/agents/context_analyzer.py",
            "backend/handlers/conversation_handler.py"
        ]
        
        for file_path in python_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                hardcodes = self.scan_file_for_hardcodes(full_path)
                for hardcode in hardcodes:
                    violations.append({
                        "type": "HARDCODE_VIOLATION",
                        "severity": "HIGH",
                        "file": file_path,
                        "location": f"Line {hardcode['line']}",
                        "description": f"发现硬编码: {hardcode['content'][:100]}...",
                        "suggestion": "将硬编码内容移动到配置文件中"
                    })
        
        return violations
    
    def check_naming_conventions(self) -> List[Dict[str, Any]]:
        """检查命名规范"""
        warnings = []
        
        # 检查配置键名规范
        unified_config_path = self.config_dir / "unified_config.yaml"
        if unified_config_path.exists():
            with open(unified_config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查问候模板命名
            greeting_config = config.get("message_templates", {}).get("greeting", {})
            for key in greeting_config.keys():
                if key in ["ai_assistant", "welcome_service", "requirement_analyst"]:
                    warnings.append({
                        "type": "NAMING_CONVENTION",
                        "severity": "MEDIUM",
                        "file": "unified_config.yaml",
                        "location": f"message_templates.greeting.{key}",
                        "description": f"配置键名 '{key}' 不符合命名规范",
                        "suggestion": "使用更规范的命名，如 'standard', 'professional', 'simple'"
                    })
        
        return warnings
    
    def check_config_structure(self) -> List[Dict[str, Any]]:
        """检查配置结构"""
        warnings = []
        
        # 检查配置文件是否存在
        required_configs = [
            "unified_config.yaml",
            "keywords_config.yaml", 
            "unified_config.defaults.yaml"
        ]
        
        for config_file in required_configs:
            config_path = self.config_dir / config_file
            if not config_path.exists():
                warnings.append({
                    "type": "MISSING_CONFIG",
                    "severity": "HIGH",
                    "file": config_file,
                    "location": "N/A",
                    "description": f"缺少必需的配置文件: {config_file}",
                    "suggestion": f"创建 {config_file} 配置文件"
                })
        
        return warnings
    
    def generate_suggestions(self) -> List[Dict[str, Any]]:
        """生成改进建议"""
        suggestions = []
        
        suggestions.append({
            "type": "OPTIMIZATION",
            "priority": "HIGH",
            "title": "统一问候模板管理",
            "description": "将分散的问候模板统一管理，减少重复",
            "action": "保留3-5个核心问候模板，移除重复和相似的模板"
        })
        
        suggestions.append({
            "type": "MIGRATION",
            "priority": "HIGH", 
            "title": "完成关键词配置迁移",
            "description": "将所有关键词配置迁移到keywords_config.yaml",
            "action": "移除unified_config.yaml中的keyword_rules配置"
        })
        
        suggestions.append({
            "type": "STANDARDIZATION",
            "priority": "MEDIUM",
            "title": "规范化配置命名",
            "description": "统一配置键名的命名规范",
            "action": "使用标准的命名约定，如snake_case，避免缩写"
        })
        
        return suggestions
    
    def extract_greeting_templates(self, config: Dict[str, Any]) -> List[str]:
        """提取问候模板"""
        greeting_config = config.get("message_templates", {}).get("greeting", {})
        return list(greeting_config.keys())
    
    def has_duplicate_keyword_configs(self, config: Dict[str, Any]) -> bool:
        """检查是否有重复的关键词配置"""
        has_keyword_rules = "keyword_rules" in config
        has_keyword_acceleration = config.get("conversation", {}).get("keyword_acceleration", {}).get("enabled", False)
        keywords_config_exists = (self.config_dir / "keywords_config.yaml").exists()
        
        return (has_keyword_rules or has_keyword_acceleration) and keywords_config_exists
    
    def scan_file_for_hardcodes(self, file_path: Path) -> List[Dict[str, Any]]:
        """扫描文件中的硬编码"""
        hardcodes = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for i, line in enumerate(lines, 1):
                # 检查多行字符串硬编码
                if '"""' in line and ('您好' in line or '我是' in line):
                    hardcodes.append({
                        "line": i,
                        "content": line.strip(),
                        "type": "MULTILINE_STRING"
                    })
                
                # 检查单行字符串硬编码
                if any(pattern in line for pattern in [
                    '"您好！我是', '"我来帮助您', '"让我们从以下', '"请从以下选项'
                ]):
                    hardcodes.append({
                        "line": i,
                        "content": line.strip(),
                        "type": "INLINE_STRING"
                    })
        
        except Exception as e:
            print(f"扫描文件 {file_path} 时出错: {e}")
        
        return hardcodes
    
    def calculate_compliance_score(self, results: Dict[str, Any]) -> float:
        """计算合规分数"""
        violations = len(results["violations"])
        warnings = len(results["warnings"])
        
        # 基础分数100分
        score = 100.0
        
        # 违规扣分（每个违规扣10分）
        score -= violations * 10
        
        # 警告扣分（每个警告扣5分）
        score -= warnings * 5
        
        # 确保分数不低于0
        return max(0.0, score)
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """生成检查报告"""
        report = []
        report.append("# 配置规范检查报告")
        report.append(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 摘要
        summary = results["summary"]
        report.append("## 📊 检查摘要")
        report.append(f"- 合规分数: {summary['compliance_score']:.1f}/100")
        report.append(f"- 违规问题: {summary['total_violations']}个")
        report.append(f"- 警告问题: {summary['total_warnings']}个") 
        report.append(f"- 改进建议: {summary['total_suggestions']}个")
        report.append("")
        
        # 违规详情
        if results["violations"]:
            report.append("## 🚨 违规问题")
            for violation in results["violations"]:
                report.append(f"### {violation['type']} - {violation['severity']}")
                report.append(f"**文件**: {violation['file']}")
                report.append(f"**位置**: {violation['location']}")
                report.append(f"**描述**: {violation['description']}")
                report.append(f"**建议**: {violation['suggestion']}")
                report.append("")
        
        # 改进建议
        if results["suggestions"]:
            report.append("## 💡 改进建议")
            for suggestion in results["suggestions"]:
                report.append(f"### {suggestion['title']} - {suggestion['priority']}")
                report.append(f"**描述**: {suggestion['description']}")
                report.append(f"**行动**: {suggestion['action']}")
                report.append("")
        
        return "\n".join(report)

def main():
    parser = argparse.ArgumentParser(description="配置规范检查工具")
    parser.add_argument("--fix", action="store_true", help="自动修复部分问题")
    parser.add_argument("--output", default="config_compliance_report.md", help="报告输出文件")
    
    args = parser.parse_args()
    
    # 创建检查器
    checker = ConfigComplianceChecker(project_root)
    
    # 执行检查
    results = checker.check_all_compliance()
    
    # 生成报告
    report = checker.generate_report(results)
    
    # 保存报告
    with open(args.output, 'w', encoding='utf-8') as f:
        f.write(report)
    
    # 输出结果
    print(f"\n📋 检查完成！")
    print(f"合规分数: {results['summary']['compliance_score']:.1f}/100")
    print(f"违规问题: {results['summary']['total_violations']}个")
    print(f"警告问题: {results['summary']['total_warnings']}个")
    print(f"改进建议: {results['summary']['total_suggestions']}个")
    print(f"详细报告已保存到: {args.output}")
    
    # 返回退出码
    if results['summary']['total_violations'] > 0:
        sys.exit(1)  # 有违规问题
    elif results['summary']['total_warnings'] > 0:
        sys.exit(2)  # 有警告问题
    else:
        sys.exit(0)  # 无问题

if __name__ == "__main__":
    main()
