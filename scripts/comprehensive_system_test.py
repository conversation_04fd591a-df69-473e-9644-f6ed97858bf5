#!/usr/bin/env python3
"""
综合系统测试
测试优化后系统的所有核心功能
"""

import asyncio
import logging
import time
from typing import Dict, Any, List
from datetime import datetime
from dataclasses import dataclass


@dataclass
class TestResult:
    """测试结果"""
    test_name: str
    success: bool
    duration: float
    details: Dict[str, Any]
    error: str = ""


class ComprehensiveSystemTest:
    """综合系统测试套件"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.test_results = []

        # 添加项目根目录到Python路径
        import sys
        import os
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        print("🚀 开始综合系统测试...")
        
        test_suite = [
            ("统一配置加载器测试", self.test_unified_config_loader),
            ("统一状态管理器测试", self.test_unified_state_manager),
            ("对话状态机测试", self.test_conversation_state_machine),
            ("简化决策引擎测试", self.test_simplified_decision_engine),
            ("统一LLM客户端工厂测试", self.test_unified_llm_client_factory),
            ("端到端对话流程测试", self.test_end_to_end_conversation),
            ("并发处理测试", self.test_concurrent_processing),
            ("错误处理测试", self.test_error_handling),
            ("性能基准测试", self.test_performance_benchmark)
        ]
        
        total_tests = len(test_suite)
        passed_tests = 0
        
        for test_name, test_func in test_suite:
            print(f"\n🧪 {test_name}...")
            
            start_time = time.time()
            try:
                result = await test_func()
                duration = time.time() - start_time
                
                if result["success"]:
                    passed_tests += 1
                    print(f"   ✅ 通过 ({duration:.3f}s)")
                else:
                    print(f"   ❌ 失败 ({duration:.3f}s): {result.get('error', '未知错误')}")
                
                self.test_results.append(TestResult(
                    test_name=test_name,
                    success=result["success"],
                    duration=duration,
                    details=result,
                    error=result.get("error", "")
                ))
                
            except Exception as e:
                duration = time.time() - start_time
                error_msg = str(e)
                print(f"   ❌ 异常 ({duration:.3f}s): {error_msg}")
                
                self.test_results.append(TestResult(
                    test_name=test_name,
                    success=False,
                    duration=duration,
                    details={},
                    error=error_msg
                ))
        
        # 生成测试报告
        report = self.generate_test_report(total_tests, passed_tests)
        
        print(f"\n📊 测试完成: {passed_tests}/{total_tests} 通过 ({passed_tests/total_tests:.1%})")
        
        return report
    
    async def test_unified_config_loader(self) -> Dict[str, Any]:
        """测试统一配置加载器"""
        try:
            from backend.config.unified_config_loader import get_unified_config
            
            loader = get_unified_config()
            
            # 测试基本功能
            system_config = loader.get_system_config()
            llm_types = loader.get_available_llm_types()
            conversation_config = loader.get_conversation_config()
            
            # 验证配置完整性
            checks = {
                "system_config_loaded": bool(system_config),
                "llm_types_available": len(llm_types) >= 8,
                "conversation_config_loaded": bool(conversation_config),
                "keyword_rules_loaded": bool(loader.get_keyword_rules()),
                "message_templates_loaded": bool(loader.get_message_templates())
            }
            
            success = all(checks.values())
            
            return {
                "success": success,
                "checks": checks,
                "llm_types_count": len(llm_types),
                "system_version": system_config.get("version", "unknown")
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_unified_state_manager(self) -> Dict[str, Any]:
        """测试统一状态管理器"""
        try:
            from backend.agents.unified_state_manager import get_state_manager, ConversationState
            
            manager = get_state_manager()
            session_id = "test_state_session"
            user_id = "test_state_user"
            
            # 测试状态获取和设置
            initial_state = await manager.get_conversation_state(session_id, user_id)
            
            # 测试状态转换
            success1 = await manager.set_conversation_state(
                session_id, user_id, ConversationState.COLLECTING_INFO, "test"
            )
            
            new_state = await manager.get_conversation_state(session_id, user_id)
            
            # 测试会话状态
            session_state = await manager.get_session_state(session_id, user_id)
            
            # 测试状态重置
            success2 = await manager.reset_session_state(session_id, user_id)
            
            checks = {
                "initial_state_correct": initial_state == ConversationState.IDLE,
                "state_transition_success": success1,
                "new_state_correct": new_state == ConversationState.COLLECTING_INFO,
                "session_state_complete": bool(session_state),
                "reset_success": success2
            }
            
            return {
                "success": all(checks.values()),
                "checks": checks,
                "available_states": len(manager.get_available_states())
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_conversation_state_machine(self) -> Dict[str, Any]:
        """测试对话状态机"""
        try:
            from backend.agents.conversation_state_machine import get_conversation_state_machine
            
            state_machine = get_conversation_state_machine()
            session_id = "test_sm_session"
            user_id = "test_sm_user"
            
            # 重置会话
            await state_machine.reset_conversation(session_id, user_id)
            
            # 测试状态转换序列
            test_sequence = [
                ("你好", "IDLE"),
                ("我想做一个网站", "COLLECTING_INFO"),
                ("这是一个电商网站", "COLLECTING_INFO"),
                ("确认", "DOCUMENTING"),
                ("好的", "COMPLETED")
            ]
            
            results = []
            for message, expected_state in test_sequence:
                result = await state_machine.process_message(message, session_id, user_id)
                current_state = await state_machine.get_current_state(session_id, user_id)
                
                results.append({
                    "message": message,
                    "expected_state": expected_state,
                    "actual_state": current_state.value,
                    "success": result.success,
                    "state_match": current_state.value == expected_state
                })
            
            success_count = sum(1 for r in results if r["success"] and r["state_match"])
            
            return {
                "success": success_count == len(test_sequence),
                "sequence_results": results,
                "success_rate": success_count / len(test_sequence),
                "total_states": len(state_machine.states)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_simplified_decision_engine(self) -> Dict[str, Any]:
        """测试简化决策引擎"""
        try:
            from backend.agents.simplified_decision_engine import get_simplified_decision_engine
            from backend.agents.unified_state_manager import ConversationState
            
            engine = get_simplified_decision_engine()
            
            # 测试决策规则验证
            validation = engine.validate_decision_rules()
            
            # 测试各种决策场景
            test_cases = [
                ("greeting", ConversationState.IDLE, "send_greeting"),
                ("business_requirement", ConversationState.IDLE, "start_requirement_collection"),
                ("provide_information", ConversationState.COLLECTING_INFO, "collect_information"),
                ("confirm", ConversationState.COLLECTING_INFO, "start_document_generation"),
                ("confirm", ConversationState.DOCUMENTING, "confirm_document")
            ]
            
            decision_results = []
            for intent, state, expected_action in test_cases:
                result = engine.make_decision(intent, state)
                decision_results.append({
                    "intent": intent,
                    "state": state.value,
                    "expected_action": expected_action,
                    "actual_action": result.action,
                    "match": result.action == expected_action
                })
            
            success_count = sum(1 for r in decision_results if r["match"])
            
            return {
                "success": validation["valid"] and success_count == len(test_cases),
                "validation": validation,
                "decision_results": decision_results,
                "success_rate": success_count / len(test_cases),
                "available_actions": len(engine.get_available_actions())
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_unified_llm_client_factory(self) -> Dict[str, Any]:
        """测试统一LLM客户端工厂"""
        try:
            from backend.agents.unified_llm_client_factory import create_llm_client, get_available_llm_types
            
            # 测试可用类型
            available_types = get_available_llm_types()
            
            # 测试客户端创建（不实际调用LLM）
            test_types = ["default", "domain_classification", "conversation"]
            creation_results = []
            
            for llm_type in test_types:
                try:
                    client = create_llm_client(llm_type)
                    creation_results.append({
                        "type": llm_type,
                        "success": client is not None,
                        "client_type": type(client).__name__
                    })
                except Exception as e:
                    creation_results.append({
                        "type": llm_type,
                        "success": False,
                        "error": str(e)
                    })
            
            success_count = sum(1 for r in creation_results if r["success"])
            
            return {
                "success": len(available_types) >= 8 and success_count == len(test_types),
                "available_types": available_types,
                "creation_results": creation_results,
                "success_rate": success_count / len(test_types)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_end_to_end_conversation(self) -> Dict[str, Any]:
        """测试端到端对话流程"""
        try:
            from backend.agents.conversation_state_machine import get_conversation_state_machine
            from backend.agents.simplified_decision_engine import get_simplified_decision_engine
            
            state_machine = get_conversation_state_machine()
            decision_engine = get_simplified_decision_engine()
            
            session_id = "test_e2e_session"
            user_id = "test_e2e_user"
            
            # 重置会话
            await state_machine.reset_conversation(session_id, user_id)
            
            # 完整对话流程
            conversation_flow = [
                "你好",
                "我想做一个电商网站",
                "主要卖服装和配饰",
                "需要支付功能",
                "确认",
                "好的"
            ]
            
            flow_results = []
            for i, message in enumerate(conversation_flow):
                # 处理消息
                result = await state_machine.process_message(message, session_id, user_id)
                current_state = await state_machine.get_current_state(session_id, user_id)
                
                flow_results.append({
                    "step": i + 1,
                    "message": message,
                    "success": result.success,
                    "state": current_state.value,
                    "response_available": bool(result.response)
                })
            
            success_count = sum(1 for r in flow_results if r["success"])
            
            return {
                "success": success_count == len(conversation_flow),
                "flow_results": flow_results,
                "success_rate": success_count / len(conversation_flow),
                "final_state": flow_results[-1]["state"] if flow_results else "unknown"
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_concurrent_processing(self) -> Dict[str, Any]:
        """测试并发处理"""
        try:
            from backend.agents.conversation_state_machine import get_conversation_state_machine
            
            state_machine = get_conversation_state_machine()
            
            # 创建多个并发会话
            concurrent_sessions = []
            for i in range(10):
                session_id = f"concurrent_session_{i}"
                user_id = f"concurrent_user_{i}"
                concurrent_sessions.append((session_id, user_id))
            
            # 并发处理消息
            tasks = []
            for session_id, user_id in concurrent_sessions:
                task = state_machine.process_message("我想做一个网站", session_id, user_id)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            success_count = sum(1 for r in results if not isinstance(r, Exception) and r.success)
            
            return {
                "success": success_count == len(concurrent_sessions),
                "concurrent_sessions": len(concurrent_sessions),
                "success_count": success_count,
                "success_rate": success_count / len(concurrent_sessions)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_error_handling(self) -> Dict[str, Any]:
        """测试错误处理"""
        try:
            from backend.agents.conversation_state_machine import get_conversation_state_machine
            from backend.agents.simplified_decision_engine import get_simplified_decision_engine
            from backend.agents.unified_state_manager import ConversationState
            
            state_machine = get_conversation_state_machine()
            decision_engine = get_simplified_decision_engine()
            
            # 测试各种错误情况
            error_tests = []
            
            # 1. 空消息处理
            try:
                result = await state_machine.process_message("", "test_session", "test_user")
                error_tests.append({"test": "empty_message", "handled": result.success})
            except Exception:
                error_tests.append({"test": "empty_message", "handled": False})
            
            # 2. 无效状态决策
            try:
                result = decision_engine.make_decision("unknown_intent", ConversationState.IDLE)
                error_tests.append({"test": "unknown_intent", "handled": result.action is not None})
            except Exception:
                error_tests.append({"test": "unknown_intent", "handled": False})
            
            # 3. 长消息处理
            try:
                long_message = "x" * 1000
                result = await state_machine.process_message(long_message, "test_session", "test_user")
                error_tests.append({"test": "long_message", "handled": result.success})
            except Exception:
                error_tests.append({"test": "long_message", "handled": False})
            
            handled_count = sum(1 for t in error_tests if t["handled"])
            
            return {
                "success": handled_count == len(error_tests),
                "error_tests": error_tests,
                "handled_rate": handled_count / len(error_tests)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_performance_benchmark(self) -> Dict[str, Any]:
        """测试性能基准"""
        try:
            from backend.agents.conversation_state_machine import get_conversation_state_machine
            from backend.agents.simplified_decision_engine import get_simplified_decision_engine
            from backend.agents.unified_state_manager import ConversationState
            
            state_machine = get_conversation_state_machine()
            decision_engine = get_simplified_decision_engine()
            
            # 性能测试
            performance_results = {}
            
            # 1. 决策引擎性能
            start_time = time.time()
            for _ in range(1000):
                decision_engine.make_decision("greeting", ConversationState.IDLE)
            decision_time = time.time() - start_time
            performance_results["decision_engine_1000_calls"] = decision_time
            
            # 2. 状态机处理性能
            start_time = time.time()
            for i in range(100):
                await state_machine.process_message("你好", f"perf_session_{i}", f"perf_user_{i}")
            state_machine_time = time.time() - start_time
            performance_results["state_machine_100_calls"] = state_machine_time
            
            # 性能基准
            benchmarks = {
                "decision_avg_ms": (decision_time / 1000) * 1000,  # 平均每次决策时间(ms)
                "state_machine_avg_ms": (state_machine_time / 100) * 1000,  # 平均每次处理时间(ms)
                "decision_meets_target": (decision_time / 1000) < 0.001,  # <1ms
                "state_machine_meets_target": (state_machine_time / 100) < 0.015  # <15ms
            }
            
            return {
                "success": benchmarks["decision_meets_target"] and benchmarks["state_machine_meets_target"],
                "performance_results": performance_results,
                "benchmarks": benchmarks
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def generate_test_report(self, total_tests: int, passed_tests: int) -> Dict[str, Any]:
        """生成测试报告"""
        return {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": total_tests - passed_tests,
                "success_rate": passed_tests / total_tests,
                "total_duration": sum(r.duration for r in self.test_results)
            },
            "test_results": [
                {
                    "test_name": r.test_name,
                    "success": r.success,
                    "duration": r.duration,
                    "error": r.error
                }
                for r in self.test_results
            ],
            "detailed_results": [r.details for r in self.test_results]
        }


async def main():
    """主函数"""
    tester = ComprehensiveSystemTest()
    report = await tester.run_all_tests()
    
    # 保存测试报告
    import json
    with open("comprehensive_system_test_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细测试报告已保存到: comprehensive_system_test_report.json")
    
    # 显示总结
    summary = report["summary"]
    print(f"\n📊 测试总结:")
    print(f"   总测试数: {summary['total_tests']}")
    print(f"   通过测试: {summary['passed_tests']}")
    print(f"   失败测试: {summary['failed_tests']}")
    print(f"   成功率: {summary['success_rate']:.1%}")
    print(f"   总耗时: {summary['total_duration']:.3f}s")


if __name__ == "__main__":
    asyncio.run(main())
