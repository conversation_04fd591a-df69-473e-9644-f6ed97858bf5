#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量清理未使用导入脚本

基于冗余代码分析报告，批量清理明确未使用的导入
"""

import os
import sys
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 需要清理的文件和对应的未使用导入
CLEANUP_TASKS = [
    {
        "file": "backend/agents/factory.py",
        "line": 411,
        "old": "from backend.utils.prompt_loader import PromptLoader",
        "new": "# PromptLoader import removed - unused"
    },
    {
        "file": "backend/agents/conversation_state_machine.py", 
        "line": 9,
        "old": "from typing import Dict, Any, Optional, Tuple",
        "new": "from typing import Dict, Any, Optional"
    },
    {
        "file": "backend/config/knowledge_base_config.py",
        "line": 8,
        "old": "import os",
        "new": "# os import removed - unused"
    },
    {
        "file": "backend/config/unified_config_loader.py",
        "line": 10,
        "old": "from typing import Dict, Any, List, Optional",
        "new": "from typing import Dict, Any, List"
    },
    {
        "file": "backend/handlers/action_executor_interface.py",
        "line": 10,
        "old": "from typing import Dict, Any, Optional",
        "new": "from typing import Dict, Any"
    },
    {
        "file": "backend/handlers/composite_handler.py",
        "line": 10,
        "old": "from typing import Dict, Any, List",
        "new": "from typing import List"
    },
    {
        "file": "backend/utils/intent_manager.py",
        "line": 14,
        "old": "import os",
        "new": "# os import removed - unused"
    },
    {
        "file": "backend/utils/progress_indicator.py",
        "line": 6,
        "old": "import asyncio",
        "new": "# asyncio import removed - unused"
    },
    {
        "file": "backend/services/component_pool_manager.py",
        "line": 13,
        "old": "import asyncio",
        "new": "# asyncio import removed - unused"
    },
    {
        "file": "backend/services/component_pool_manager.py",
        "line": 16,
        "old": "from typing import Dict, Any, List, Optional, Set",
        "new": "from typing import Dict, Any, List, Optional"
    }
]


def cleanup_file(task):
    """清理单个文件"""
    file_path = Path(task["file"])
    
    if not file_path.exists():
        logger.warning(f"文件不存在: {file_path}")
        return False
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 检查目标行是否匹配
        target_line = task["line"] - 1  # 转换为0基索引
        if target_line >= len(lines):
            logger.warning(f"行号超出范围: {file_path}:{task['line']}")
            return False
        
        current_line = lines[target_line].strip()
        expected_line = task["old"].strip()
        
        if current_line != expected_line:
            logger.warning(f"行内容不匹配: {file_path}:{task['line']}")
            logger.warning(f"期望: {expected_line}")
            logger.warning(f"实际: {current_line}")
            return False
        
        # 执行替换
        if task["new"].startswith("#"):
            # 注释掉该行
            lines[target_line] = f"# {lines[target_line]}"
        else:
            # 替换内容
            lines[target_line] = task["new"] + "\n"
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        logger.info(f"✅ 清理完成: {file_path}:{task['line']}")
        return True
        
    except Exception as e:
        logger.error(f"清理失败 {file_path}: {e}")
        return False


def main():
    """主函数"""
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    logger.info("开始批量清理未使用导入...")
    
    success_count = 0
    total_count = len(CLEANUP_TASKS)
    
    for task in CLEANUP_TASKS:
        if cleanup_file(task):
            success_count += 1
    
    logger.info(f"批量清理完成: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        logger.info("🎉 所有未使用导入清理完成！")
        return True
    else:
        logger.warning("⚠️ 部分清理任务失败，请手动检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
