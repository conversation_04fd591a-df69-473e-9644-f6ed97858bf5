#!/usr/bin/env python3
"""
代码质量监控仪表板生成器
生成HTML格式的质量监控仪表板
"""

import os
import json
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List

class QualityDashboard:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.reports_dir = self.project_root / "reports"
        self.reports_dir.mkdir(exist_ok=True)
        
    def run_all_checks(self) -> Dict:
        """运行所有质量检查"""
        print("🔍 运行质量检查...")
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'redundant_code': self.check_redundant_code(),
            'config_consistency': self.check_config_consistency(),
            'unused_imports': self.check_unused_imports(),
            'code_metrics': self.get_code_metrics(),
        }
        
        return results
    
    def check_redundant_code(self) -> Dict:
        """检查冗余代码"""
        try:
            result = subprocess.run(
                ["python", "scripts/cleanup_redundant_code.py"],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            # 解析输出获取问题数量
            output = result.stdout
            if "未发现需要清理的冗余代码" in output:
                return {'status': 'clean', 'issues': 0}
            else:
                # 尝试从输出中提取问题数量
                lines = output.split('\n')
                for line in lines:
                    if "发现" in line and "个问题" in line:
                        try:
                            count = int(line.split("发现")[1].split("个问题")[0].strip())
                            return {'status': 'issues_found', 'issues': count}
                        except:
                            pass
                return {'status': 'unknown', 'issues': -1}
                
        except Exception as e:
            return {'status': 'error', 'error': str(e), 'issues': -1}
    
    def check_config_consistency(self) -> Dict:
        """检查配置一致性"""
        try:
            result = subprocess.run(
                ["python", "scripts/check_config_consistency.py"],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            output = result.stdout
            if "未发现配置一致性问题" in output:
                return {'status': 'clean', 'issues': 0}
            else:
                # 尝试从输出中提取问题数量
                lines = output.split('\n')
                for line in lines:
                    if "发现" in line and "个配置一致性问题" in line:
                        try:
                            count = int(line.split("发现")[1].split("个配置一致性问题")[0].strip())
                            return {'status': 'issues_found', 'issues': count}
                        except:
                            pass
                return {'status': 'unknown', 'issues': -1}
                
        except Exception as e:
            return {'status': 'error', 'error': str(e), 'issues': -1}
    
    def check_unused_imports(self) -> Dict:
        """检查未使用导入"""
        try:
            result = subprocess.run(
                ["python", "scripts/check_unused_imports.py"],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            output = result.stdout
            if "未发现未使用的导入" in output:
                return {'status': 'clean', 'issues': 0}
            else:
                # 尝试从输出中提取问题数量
                lines = output.split('\n')
                for line in lines:
                    if "发现" in line and "个未使用的导入" in line:
                        try:
                            count = int(line.split("发现")[1].split("个未使用的导入")[0].strip())
                            return {'status': 'issues_found', 'issues': count}
                        except:
                            pass
                return {'status': 'unknown', 'issues': -1}
                
        except Exception as e:
            return {'status': 'error', 'error': str(e), 'issues': -1}
    
    def get_code_metrics(self) -> Dict:
        """获取代码指标"""
        backend_path = self.project_root / "backend"
        
        metrics = {
            'total_files': 0,
            'total_lines': 0,
            'python_files': 0,
            'config_files': 0,
        }
        
        if backend_path.exists():
            # 统计Python文件
            for py_file in backend_path.rglob("*.py"):
                if not py_file.name.startswith('.'):
                    metrics['python_files'] += 1
                    metrics['total_files'] += 1
                    
                    try:
                        with open(py_file, 'r', encoding='utf-8') as f:
                            metrics['total_lines'] += len(f.readlines())
                    except:
                        pass
            
            # 统计配置文件
            config_path = backend_path / "config"
            if config_path.exists():
                for config_file in config_path.rglob("*.yaml"):
                    metrics['config_files'] += 1
                    metrics['total_files'] += 1
        
        return metrics
    
    def generate_html_dashboard(self, results: Dict) -> str:
        """生成HTML仪表板"""
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码质量监控仪表板</title>
    <style>
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
        .header {{ background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .metrics-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
        .metric-card {{ background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .metric-title {{ font-size: 18px; font-weight: 600; margin-bottom: 10px; color: #333; }}
        .metric-value {{ font-size: 32px; font-weight: 700; margin-bottom: 5px; }}
        .metric-status {{ padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; }}
        .status-clean {{ background: #d4edda; color: #155724; }}
        .status-issues {{ background: #f8d7da; color: #721c24; }}
        .status-error {{ background: #f1c40f; color: #856404; }}
        .timestamp {{ color: #666; font-size: 14px; }}
        .footer {{ text-align: center; margin-top: 40px; color: #666; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 代码质量监控仪表板</h1>
            <p class="timestamp">最后更新: {timestamp}</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-title">🧹 冗余代码检查</div>
                <div class="metric-value {redundant_color}">{redundant_issues}</div>
                <div class="metric-status {redundant_status_class}">{redundant_status}</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">⚙️ 配置一致性</div>
                <div class="metric-value {config_color}">{config_issues}</div>
                <div class="metric-status {config_status_class}">{config_status}</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">📦 未使用导入</div>
                <div class="metric-value {import_color}">{import_issues}</div>
                <div class="metric-status {import_status_class}">{import_status}</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">📊 代码指标</div>
                <div style="font-size: 16px; margin-bottom: 10px;">
                    <div>Python文件: <strong>{{python_files}}</strong></div>
                    <div>配置文件: <strong>{{config_files}}</strong></div>
                    <div>总行数: <strong>{{total_lines:,}}</strong></div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>🎉 五重重构完美收官 - 系统现代化升级成功！</p>
        </div>
    </div>
</body>
</html>
        """
        
        # 处理状态和颜色
        def get_status_info(check_result):
            if check_result['status'] == 'clean':
                return 'status-clean', '✅ 无问题', 'color: #28a745;'
            elif check_result['status'] == 'issues_found':
                return 'status-issues', f'⚠️ {check_result["issues"]} 个问题', 'color: #dc3545;'
            else:
                return 'status-error', '❓ 检查异常', 'color: #ffc107;'
        
        redundant_class, redundant_status, redundant_color = get_status_info(results['redundant_code'])
        config_class, config_status, config_color = get_status_info(results['config_consistency'])
        import_class, import_status, import_color = get_status_info(results['unused_imports'])
        
        return html_template.format(
            timestamp=datetime.fromisoformat(results['timestamp']).strftime('%Y-%m-%d %H:%M:%S'),
            redundant_issues=results['redundant_code']['issues'] if results['redundant_code']['issues'] >= 0 else '?',
            redundant_status=redundant_status,
            redundant_status_class=redundant_class,
            redundant_color=redundant_color,
            config_issues=results['config_consistency']['issues'] if results['config_consistency']['issues'] >= 0 else '?',
            config_status=config_status,
            config_status_class=config_class,
            config_color=config_color,
            import_issues=results['unused_imports']['issues'] if results['unused_imports']['issues'] >= 0 else '?',
            import_status=import_status,
            import_status_class=import_class,
            import_color=import_color,
            python_files=results['code_metrics']['python_files'],
            config_files=results['code_metrics']['config_files'],
            total_lines=results['code_metrics']['total_lines']
        )
    
    def save_results(self, results: Dict):
        """保存检查结果"""
        # 保存JSON格式
        json_file = self.reports_dir / "quality_report.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # 生成HTML仪表板
        html_content = self.generate_html_dashboard(results)
        html_file = self.reports_dir / "quality_dashboard.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"📊 质量报告已保存:")
        print(f"   JSON: {json_file}")
        print(f"   HTML: {html_file}")

def main():
    """主函数"""
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    dashboard = QualityDashboard(project_root)
    
    # 运行所有检查
    results = dashboard.run_all_checks()
    
    # 保存结果
    dashboard.save_results(results)
    
    # 输出摘要
    total_issues = (
        (results['redundant_code']['issues'] if results['redundant_code']['issues'] >= 0 else 0) +
        (results['config_consistency']['issues'] if results['config_consistency']['issues'] >= 0 else 0) +
        (results['unused_imports']['issues'] if results['unused_imports']['issues'] >= 0 else 0)
    )
    
    if total_issues == 0:
        print("🎉 所有质量检查通过！")
    else:
        print(f"⚠️ 发现 {total_issues} 个质量问题")
    
    print(f"🌐 在浏览器中查看仪表板: file://{dashboard.reports_dir / 'quality_dashboard.html'}")

if __name__ == "__main__":
    main()
