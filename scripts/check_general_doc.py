#!/usr/bin/env python3
"""检查general.md文档在知识库中的状态"""

import os
import sys
import chromadb

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.config.knowledge_base_config import get_knowledge_base_config_manager

def check_general_doc():
    config_manager = get_knowledge_base_config_manager()
    config = get_unified_config().get_config()
    
    chroma_path = config.chroma_db.get('path', 'backend/data/chroma_db')
    collection_name = config.chroma_db.get('collection_name', 'hybrid_knowledge_base')
    
    try:
        client = chromadb.PersistentClient(path=chroma_path)
        collection = client.get_collection(collection_name)
        
        # 获取所有文档并手动过滤
        all_results = collection.get(include=['documents', 'metadatas'])
        
        # 手动查找包含general.md的文档
        general_docs = []
        general_metas = []
        
        for doc, meta in zip(all_results['documents'], all_results['metadatas']):
            source_file = meta.get('source_file', '')
            if 'general.md' in source_file:
                general_docs.append(doc)
                general_metas.append(meta)
        
        if general_docs:
            print(f"✅ 找到 {len(general_docs)} 个来自 general.md 的文档片段")
            
            for i, (doc, meta) in enumerate(zip(general_docs[:5], general_metas[:5])):
                print(f"\n片段 {i+1}:")
                print(f"  来源文件: {meta.get('source_file', 'unknown')}")
                print(f"  角色: {meta.get('role', 'unknown')}")
                print(f"  类别: {meta.get('category', 'unknown')}")
                print(f"  内容预览: {doc[:200]}...")
                print(f"  {'='*50}")
        else:
            print("❌ 未找到 general.md 相关内容")
            
            print(f"\n知识库总共有 {len(all_results['metadatas'])} 个文档")
            
            # 显示所有来源文件
            sources = set()
            for meta in all_results['metadatas']:
                source = meta.get('source_file', 'unknown')
                sources.add(source)
            
            print(f"\n现有来源文件 (共{len(sources)}个):")
            for source in sorted(sources):
                print(f"  - {source}")
        
        # 测试相关查询
        print(f"\n🔍 测试相关查询:")
        test_queries = [
            "由己平台介绍",
            "用工模式",
            "服务类型",
            "IT程序开发",
            "直接用工"
        ]
        
        for query in test_queries:
            try:
                results = collection.query(
                    query_texts=[query],
                    n_results=2,
                    include=['documents', 'metadatas', 'distances']
                )
                
                if results['documents'][0]:
                    best_match = results['documents'][0][0]
                    best_meta = results['metadatas'][0][0]
                    similarity = 1 - results['distances'][0][0]
                    
                    print(f"\n  查询: '{query}'")
                    print(f"  最佳匹配 (相似度: {similarity:.3f}): {best_match[:100]}...")
                    print(f"  来源: {best_meta.get('source_file', 'unknown')}")
                else:
                    print(f"\n  查询: '{query}' - 无匹配结果")
                    
            except Exception as e:
                print(f"\n  查询: '{query}' - 查询失败: {e}")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_general_doc()
