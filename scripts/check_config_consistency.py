#!/usr/bin/env python3
"""
配置一致性检查工具
检查是否有硬编码配置、重复配置定义等问题
"""

import os
import re
import ast
import yaml
import sys
from pathlib import Path
from typing import List, Dict, Set, Tuple
from collections import defaultdict

class ConfigConsistencyChecker:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.backend_path = self.project_root / "backend"
        self.config_path = self.backend_path / "config"
        self.issues = []
        
        # 硬编码模式
        self.hardcode_patterns = [
            r'temperature\s*=\s*0\.\d+',
            r'max_tokens\s*=\s*\d+',
            r'timeout\s*=\s*\d+',
            r'threshold\s*=\s*0\.\d+',
            r'retry\s*=\s*\d+',
            r'max_retries\s*=\s*\d+',
        ]
        
        # 配置调用模式
        self.config_call_patterns = [
            r'get_unified_config\(\)',
            r'config_service\.',
            r'self\.config\.',
        ]
        
    def check_hardcoded_values(self) -> List[Dict]:
        """检查硬编码配置值"""
        hardcode_issues = []
        
        for py_file in self.backend_path.rglob("*.py"):
            if py_file.name.startswith('.'):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for line_num, line in enumerate(content.split('\n'), 1):
                    for pattern in self.hardcode_patterns:
                        if re.search(pattern, line):
                            # 排除注释和字符串中的匹配
                            if not (line.strip().startswith('#') or 
                                   line.strip().startswith('"""') or
                                   line.strip().startswith("'''")):
                                hardcode_issues.append({
                                    'type': 'hardcoded_config',
                                    'file': str(py_file.relative_to(self.project_root)),
                                    'line': line_num,
                                    'content': line.strip(),
                                    'pattern': pattern
                                })
                                
            except Exception as e:
                print(f"警告: 无法读取文件 {py_file}: {e}")
                
        return hardcode_issues
    
    def check_duplicate_config_calls(self) -> List[Dict]:
        """检查重复的配置调用"""
        duplicate_issues = []
        
        for py_file in self.backend_path.rglob("*.py"):
            if py_file.name.startswith('.'):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 统计每个函数/方法中的配置调用次数
                config_calls = []
                for line_num, line in enumerate(content.split('\n'), 1):
                    for pattern in self.config_call_patterns:
                        if re.search(pattern, line) and not line.strip().startswith('#'):
                            config_calls.append((line_num, line.strip()))
                
                # 如果同一个文件中有多次配置调用，可能存在优化空间
                if len(config_calls) > 3:
                    duplicate_issues.append({
                        'type': 'multiple_config_calls',
                        'file': str(py_file.relative_to(self.project_root)),
                        'count': len(config_calls),
                        'calls': config_calls[:5]  # 只显示前5个
                    })
                    
            except Exception as e:
                print(f"警告: 无法读取文件 {py_file}: {e}")
                
        return duplicate_issues
    
    def check_config_file_consistency(self) -> List[Dict]:
        """检查配置文件的一致性"""
        consistency_issues = []
        
        # 检查必需的配置文件
        required_configs = [
            'unified_config.yaml',
            'message_templates.yaml',
            'business_rules.yaml',
            'thresholds.yaml'
        ]
        
        for config_file in required_configs:
            config_path = self.config_path / config_file
            if not config_path.exists():
                consistency_issues.append({
                    'type': 'missing_config_file',
                    'file': config_file,
                    'message': f'缺少必需的配置文件: {config_file}'
                })
        
        # 检查配置文件格式
        for config_file in self.config_path.glob("*.yaml"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    yaml.safe_load(f)
            except yaml.YAMLError as e:
                consistency_issues.append({
                    'type': 'invalid_yaml',
                    'file': str(config_file.relative_to(self.project_root)),
                    'message': f'YAML格式错误: {e}'
                })
            except PermissionError as e:
                print(f"警告: 无法读取配置文件 {config_file}: {e}")
            except Exception as e:
                print(f"警告: 读取配置文件异常 {config_file}: {e}")
        
        return consistency_issues
    
    def check_unused_config_keys(self) -> List[Dict]:
        """检查未使用的配置键"""
        unused_issues = []
        
        # 读取所有配置键
        config_keys = set()
        for config_file in self.config_path.glob("*.yaml"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                    if config_data:
                        self._extract_keys(config_data, "", config_keys)
            except (PermissionError, OSError) as e:
                print(f"警告: 无法读取配置文件 {config_file}: {e}")
            except Exception as e:
                print(f"警告: 读取配置文件异常 {config_file}: {e}")
        
        # 检查哪些配置键在代码中被使用
        used_keys = set()
        for py_file in self.backend_path.rglob("*.py"):
            if py_file.name.startswith('.'):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 查找配置键的使用
                for key in config_keys:
                    if f'"{key}"' in content or f"'{key}'" in content:
                        used_keys.add(key)
                        
            except Exception as e:
                print(f"警告: 无法读取文件 {py_file}: {e}")
        
        # 找出未使用的配置键
        unused_keys = config_keys - used_keys
        for key in unused_keys:
            unused_issues.append({
                'type': 'unused_config_key',
                'key': key,
                'message': f'配置键未被使用: {key}'
            })
        
        return unused_issues
    
    def _extract_keys(self, data, prefix, keys_set):
        """递归提取配置键"""
        if isinstance(data, dict):
            for key, value in data.items():
                full_key = f"{prefix}.{key}" if prefix else key
                keys_set.add(full_key)
                if isinstance(value, dict):
                    self._extract_keys(value, full_key, keys_set)
    
    def run_all_checks(self) -> Dict:
        """运行所有检查"""
        print("🔍 开始配置一致性检查...")
        
        results = {
            'hardcoded_values': self.check_hardcoded_values(),
            'duplicate_calls': self.check_duplicate_config_calls(),
            'file_consistency': self.check_config_file_consistency(),
            'unused_keys': self.check_unused_config_keys()
        }
        
        return results
    
    def generate_report(self, results: Dict) -> str:
        """生成检查报告"""
        report = ["# 配置一致性检查报告\n"]
        
        total_issues = sum(len(issues) for issues in results.values())
        
        if total_issues == 0:
            report.append("✅ 未发现配置一致性问题\n")
            return "\n".join(report)
        
        report.append(f"⚠️ 发现 {total_issues} 个配置一致性问题\n")
        
        # 硬编码配置值
        if results['hardcoded_values']:
            report.append("## 🚫 硬编码配置值\n")
            for issue in results['hardcoded_values']:
                report.append(f"- **{issue['file']}:{issue['line']}**")
                report.append(f"  ```python")
                report.append(f"  {issue['content']}")
                report.append(f"  ```")
                report.append("")
        
        # 重复配置调用
        if results['duplicate_calls']:
            report.append("## 🔄 重复配置调用\n")
            for issue in results['duplicate_calls']:
                report.append(f"- **{issue['file']}**: {issue['count']} 次配置调用")
                for line_num, line in issue['calls']:
                    report.append(f"  - 第{line_num}行: `{line}`")
                report.append("")
        
        # 配置文件一致性
        if results['file_consistency']:
            report.append("## 📁 配置文件问题\n")
            for issue in results['file_consistency']:
                report.append(f"- **{issue['type']}**: {issue['message']}")
            report.append("")
        
        # 未使用的配置键
        if results['unused_keys']:
            report.append("## 🗑️ 未使用的配置键\n")
            for issue in results['unused_keys']:
                report.append(f"- `{issue['key']}`")
            report.append("")
        
        return "\n".join(report)

def main():
    """主函数"""
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    checker = ConfigConsistencyChecker(project_root)
    
    # 运行检查
    results = checker.run_all_checks()
    
    # 生成报告
    report = checker.generate_report(results)
    
    # 保存报告
    report_file = os.path.join(project_root, "config_consistency_report.md")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📊 配置一致性检查完成，报告已保存到: {report_file}")
    
    # 输出摘要
    total_issues = sum(len(issues) for issues in results.values())
    if total_issues > 0:
        print(f"⚠️ 发现 {total_issues} 个配置一致性问题")
        if '--check-only' in sys.argv:
            sys.exit(1)
    else:
        print("✅ 未发现配置一致性问题")

if __name__ == "__main__":
    main()
