#!/usr/bin/env python3
"""
项目健康度检查工具
用于检查架构调整过程中的一致性问题、无用代码和文档同步问题
"""

import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import json


class ProjectHealthChecker:
    """项目健康度检查器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.issues = {
            "consistency_issues": [],
            "dead_code": [],
            "documentation_issues": []
        }
    
    def run_full_health_check(self) -> Dict[str, Any]:
        """运行完整的健康度检查"""
        print("🔍 开始项目健康度检查...")
        
        # 1. 架构一致性检查
        print("  📋 检查架构一致性...")
        self.check_config_usage_consistency()
        self.check_decision_engine_consistency()
        self.check_llm_usage_consistency()
        
        # 2. 无用代码检查
        print("  🧹 检查无用代码...")
        self.check_unused_imports()
        self.check_unused_functions()
        self.check_deprecated_files()
        
        # 3. 文档同步检查
        print("  📚 检查文档同步...")
        self.check_architecture_docs()
        self.check_config_docs()
        
        # 生成报告
        report = self.generate_report()
        print("✅ 健康度检查完成!")
        
        return report
    
    def check_config_usage_consistency(self):
        """检查配置使用的一致性"""
        old_config_patterns = [
            r"from.*\.config_manager import config_manager",
            r"^[^#]*config_manager\.",  # 不在注释中的config_manager.
            r"ConfigManager\(\)",
            r"from.*llm_config_manager import LLMConfigManager",
            r"LLMConfigManager\(\)"
        ]

        new_config_patterns = [
            r"from backend\.config import config_service",
            r"config_service\.",
            r"from.*unified_config_loader import get_unified_config",
            r"get_unified_config\(\)"
        ]
        
        old_usage_files = []
        new_usage_files = []
        
        for py_file in self.project_root.rglob("*.py"):
            if any(skip in str(py_file) for skip in ["test", "__pycache__", "backup", "migration_backup", "import_cleanup_backup", "tools/", ".history"]):
                continue
                
            try:
                content = py_file.read_text(encoding='utf-8')
                
                # 检查旧配置使用
                file_path = str(py_file.relative_to(self.project_root))

                # 排除一些合理的使用情况
                if any(exclude in file_path for exclude in [
                    "knowledge_base_config.py",  # 知识库配置管理器
                    "compatibility_layer.py",   # 兼容性层
                    "project_health_checker.py", # 健康检查脚本本身
                    "component_pool_manager.py"  # 组件池管理器
                ]):
                    continue

                for pattern in old_config_patterns:
                    if re.search(pattern, content):
                        # 排除注释中的使用
                        lines = content.split('\n')
                        actual_usage = False
                        for line in lines:
                            if re.search(pattern, line) and not line.strip().startswith('#'):
                                actual_usage = True
                                break
                        if actual_usage:
                            old_usage_files.append(file_path)
                        break
                
                # 检查新配置使用
                for pattern in new_config_patterns:
                    if re.search(pattern, content):
                        new_usage_files.append(str(py_file.relative_to(self.project_root)))
                        break
                        
            except Exception as e:
                print(f"  ⚠️  读取文件失败: {py_file} - {e}")
        
        if old_usage_files and new_usage_files:
            self.issues["consistency_issues"].append({
                "type": "config_inconsistency",
                "severity": "high",
                "message": "项目中同时使用新旧配置管理器",
                "old_usage_files": old_usage_files,
                "new_usage_files": new_usage_files,
                "recommendation": "统一使用config_service"
            })
    
    def check_decision_engine_consistency(self):
        """检查决策引擎使用的一致性"""
        decision_engines = []

        for py_file in self.project_root.rglob("*.py"):
            # 排除历史文件、测试文件、缓存文件等
            if any(skip in str(py_file) for skip in ["test", "__pycache__", "backup", "migration_backup", "import_cleanup_backup", "tools/", ".history"]):
                continue

            try:
                content = py_file.read_text(encoding='utf-8')

                # 查找决策引擎类定义（只查找实际的类定义，不包括导入和使用）
                engine_patterns = [
                    r"^class\s+.*DecisionEngine",
                    r"^class\s+.*IntentEngine",
                    r"^class\s+SimplifiedDecisionEngine",
                    r"^class\s+HybridIntentRecognitionEngine"
                ]

                for pattern in engine_patterns:
                    matches = re.findall(pattern, content, re.MULTILINE)
                    for match in matches:
                        decision_engines.append({
                            "file": str(py_file.relative_to(self.project_root)),
                            "engine": match.strip()
                        })

            except Exception:
                continue
        
        if len(decision_engines) > 2:  # 允许有新旧两个引擎并存
            self.issues["consistency_issues"].append({
                "type": "decision_engine_inconsistency",
                "severity": "medium",
                "message": f"发现{len(decision_engines)}种决策引擎实现",
                "engines": decision_engines,
                "recommendation": "统一使用一种决策引擎"
            })
    
    def check_llm_usage_consistency(self):
        """检查LLM使用的一致性"""
        llm_creation_patterns = []
        
        for py_file in self.project_root.rglob("*.py"):
            if any(skip in str(py_file) for skip in ["test", "__pycache__", "backup", "migration_backup", "import_cleanup_backup", "tools/", ".history"]):
                continue
                
            try:
                content = py_file.read_text(encoding='utf-8')
                
                # 查找LLM客户端创建方式
                patterns = [
                    r"LLMClient\(",
                    r"OpenAI\(",
                    r"ChatOpenAI\(",
                    r"openai\.ChatCompletion"
                ]
                
                for pattern in patterns:
                    if re.search(pattern, content):
                        llm_creation_patterns.append({
                            "file": str(py_file.relative_to(self.project_root)),
                            "pattern": pattern
                        })
                        
            except Exception:
                continue
        
        # 按模式分组
        pattern_groups = {}
        for item in llm_creation_patterns:
            pattern = item["pattern"]
            if pattern not in pattern_groups:
                pattern_groups[pattern] = []
            pattern_groups[pattern].append(item["file"])
        
        if len(pattern_groups) > 1:
            self.issues["consistency_issues"].append({
                "type": "llm_usage_inconsistency",
                "severity": "medium", 
                "message": "发现多种LLM客户端创建方式",
                "patterns": pattern_groups,
                "recommendation": "统一使用一种LLM客户端创建方式"
            })
    
    def check_unused_imports(self):
        """检查未使用的导入"""
        unused_imports = []
        
        for py_file in self.project_root.rglob("*.py"):
            if any(skip in str(py_file) for skip in ["test", "__pycache__", "backup", "migration_backup", "import_cleanup_backup", "tools/", ".history"]):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 简单的未使用导入检查
                import_lines = re.findall(r'^(import .+|from .+ import .+)$', content, re.MULTILINE)
                
                for import_line in import_lines:
                    # 提取导入的模块名
                    if import_line.startswith('from'):
                        match = re.search(r'from .+ import (.+)', import_line)
                        if match:
                            imported_names = [name.strip() for name in match.group(1).split(',')]
                    else:
                        match = re.search(r'import (.+)', import_line)
                        if match:
                            imported_names = [match.group(1).strip()]
                    
                    # 检查是否在代码中使用
                    for name in imported_names:
                        if name not in content.replace(import_line, ''):
                            unused_imports.append({
                                "file": str(py_file.relative_to(self.project_root)),
                                "import": import_line,
                                "unused_name": name
                            })
                            
            except Exception:
                continue
        
        if unused_imports:
            self.issues["dead_code"].append({
                "type": "unused_imports",
                "severity": "low",
                "count": len(unused_imports),
                "items": unused_imports[:10],  # 只显示前10个
                "recommendation": "删除未使用的导入语句"
            })
    
    def check_unused_functions(self):
        """检查未使用的函数"""
        # 这里实现一个简化版本的未使用函数检查
        function_definitions = {}
        function_calls = set()
        
        for py_file in self.project_root.rglob("*.py"):
            if any(skip in str(py_file) for skip in ["test", "__pycache__", "backup", "migration_backup", "import_cleanup_backup", "tools/", ".history"]):
                continue
                
            try:
                content = py_file.read_text(encoding='utf-8')
                
                # 查找函数定义
                func_defs = re.findall(r'def (\w+)\(', content)
                for func_name in func_defs:
                    if not func_name.startswith('_'):  # 忽略私有函数
                        function_definitions[func_name] = str(py_file.relative_to(self.project_root))
                
                # 查找函数调用
                func_calls = re.findall(r'(\w+)\(', content)
                function_calls.update(func_calls)
                
            except Exception:
                continue
        
        # 找出未被调用的函数
        unused_functions = []
        for func_name, file_path in function_definitions.items():
            if func_name not in function_calls:
                unused_functions.append({
                    "function": func_name,
                    "file": file_path
                })
        
        if unused_functions:
            self.issues["dead_code"].append({
                "type": "unused_functions",
                "severity": "medium",
                "count": len(unused_functions),
                "items": unused_functions[:10],  # 只显示前10个
                "recommendation": "检查并删除未使用的函数"
            })
    
    def check_deprecated_files(self):
        """检查废弃的文件"""
        deprecated_patterns = [
            r".*deprecated.*\.py$",
            r".*_old\.py$",      # 明确标记为old的文件
            r".*\.old\.py$",     # 明确标记为old的文件
            r".*backup.*\.py$",
            r".*_副本.*\.py$",
            r".*_temp\.py$",     # 明确标记为temp的文件
            r".*\.temp\.py$",    # 明确标记为temp的文件
            r".*_tmp\.py$",      # 明确标记为tmp的文件
            r".*\.tmp\.py$",     # 明确标记为tmp的文件
            r".*\.bak\.py$",     # 备份文件
            r".*_bak\.py$",      # 备份文件
            r".*_backup\.py$",   # 备份文件
            r".*\.backup\.py$"   # 备份文件
        ]
        
        deprecated_files = []
        
        for py_file in self.project_root.rglob("*.py"):
            file_path = str(py_file)

            # 排除虚拟环境和第三方库
            if any(exclude in file_path for exclude in [
                ".venv", "venv", "node_modules", "site-packages", "__pycache__"
            ]):
                continue

            for pattern in deprecated_patterns:
                if re.search(pattern, file_path, re.IGNORECASE):
                    deprecated_files.append(str(py_file.relative_to(self.project_root)))
                    break
        
        if deprecated_files:
            self.issues["dead_code"].append({
                "type": "deprecated_files",
                "severity": "medium",
                "count": len(deprecated_files),
                "files": deprecated_files,
                "recommendation": "检查并删除废弃的文件"
            })
    
    def check_architecture_docs(self):
        """检查架构文档的准确性"""
        docs_dir = self.project_root / "docs"
        if not docs_dir.exists():
            return
        
        # 检查文档中提到的组件是否存在
        documented_components = set()
        actual_components = set()
        
        # 扫描文档中提到的组件
        for doc_file in docs_dir.rglob("*.md"):
            try:
                content = doc_file.read_text(encoding='utf-8')
                
                # 查找类似组件名的词汇
                component_patterns = [
                    r"([A-Z][a-zA-Z]*Engine)",
                    r"([A-Z][a-zA-Z]*Manager)",
                    r"([A-Z][a-zA-Z]*Router)",
                    r"([A-Z][a-zA-Z]*Matcher)"
                ]
                
                for pattern in component_patterns:
                    matches = re.findall(pattern, content)
                    documented_components.update(matches)
                    
            except Exception:
                continue
        
        # 扫描实际存在的组件
        for py_file in self.project_root.rglob("*.py"):
            if any(skip in str(py_file) for skip in ["test", "__pycache__", "backup", "migration_backup", "import_cleanup_backup", "tools/", ".history"]):
                continue
                
            try:
                content = py_file.read_text(encoding='utf-8')
                
                # 查找类定义
                class_defs = re.findall(r'class ([A-Z][a-zA-Z]*)', content)
                actual_components.update(class_defs)
                
            except Exception:
                continue
        
        # 找出不匹配的部分
        missing_components = documented_components - actual_components
        undocumented_components = actual_components - documented_components
        
        if missing_components:
            self.issues["documentation_issues"].append({
                "type": "missing_components",
                "severity": "medium",
                "message": "文档中描述的组件在代码中不存在",
                "components": list(missing_components),
                "recommendation": "更新文档，移除不存在的组件描述"
            })
        
        if undocumented_components:
            # 过滤掉一些明显不需要文档的组件
            filtered_components = [c for c in undocumented_components 
                                 if not c.endswith('Test') and not c.startswith('_')]
            
            if filtered_components:
                self.issues["documentation_issues"].append({
                    "type": "undocumented_components",
                    "severity": "low",
                    "message": "代码中存在的组件未在文档中描述",
                    "components": filtered_components[:10],  # 只显示前10个
                    "recommendation": "为重要组件添加文档描述"
                })
    
    def check_config_docs(self):
        """检查配置文档的准确性"""
        # 检查是否存在配置文档
        config_docs = list((self.project_root / "docs").rglob("*配置*.md"))
        config_files = list((self.project_root / "backend" / "config").rglob("*.yaml"))
        
        if config_files and not config_docs:
            self.issues["documentation_issues"].append({
                "type": "missing_config_docs",
                "severity": "medium", 
                "message": "存在配置文件但缺少配置文档",
                "config_files": [str(f.relative_to(self.project_root)) for f in config_files],
                "recommendation": "为配置文件创建说明文档"
            })
    
    def generate_report(self) -> Dict[str, Any]:
        """生成健康度报告"""
        total_issues = (len(self.issues["consistency_issues"]) + 
                       len(self.issues["dead_code"]) + 
                       len(self.issues["documentation_issues"]))
        
        # 计算健康度评分 - 改进的评分系统
        health_score = self._calculate_health_score()
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "health_score": health_score,
            "total_issues": total_issues,
            "issues": self.issues,
            "recommendations": self.generate_recommendations()
        }
        
        return report

    def _calculate_health_score(self) -> int:
        """计算改进的健康度评分"""
        base_score = 100

        # 架构一致性评分 (40分)
        consistency_score = 40
        for issue in self.issues["consistency_issues"]:
            if issue["severity"] == "high":
                consistency_score -= 20
            elif issue["severity"] == "medium":
                consistency_score -= 10
            else:
                consistency_score -= 5
        consistency_score = max(0, consistency_score)

        # 代码质量评分 (40分)
        code_quality_score = 40
        for issue in self.issues["dead_code"]:
            if issue["type"] == "unused_imports":
                # 无用导入按数量计算，但设置上限
                import_count = issue.get("count", 0)
                penalty = min(20, import_count // 1000)  # 每1000个扣1分，最多扣20分
                code_quality_score -= penalty
            elif issue["type"] == "deprecated_files":
                # 废弃文件按数量计算
                file_count = issue.get("count", 0)
                penalty = min(15, file_count // 500)  # 每500个扣1分，最多扣15分
                code_quality_score -= penalty
            else:
                code_quality_score -= 5
        code_quality_score = max(0, code_quality_score)

        # 文档同步评分 (20分)
        doc_score = 20
        for issue in self.issues["documentation_issues"]:
            if issue["severity"] == "high":
                doc_score -= 10
            elif issue["severity"] == "medium":
                doc_score -= 5
            else:
                doc_score -= 2
        doc_score = max(0, doc_score)

        total_score = consistency_score + code_quality_score + doc_score
        return min(100, max(0, total_score))
    
    def generate_recommendations(self) -> List[Dict[str, Any]]:
        """生成改进建议"""
        recommendations = []
        
        # 根据问题生成建议
        for issue in self.issues["consistency_issues"]:
            if issue["severity"] == "high":
                recommendations.append({
                    "priority": "high",
                    "category": "architecture_consistency",
                    "issue": issue["message"],
                    "action": issue["recommendation"],
                    "estimated_effort": "2-3天"
                })
        
        for issue in self.issues["dead_code"]:
            if issue["severity"] == "medium":
                recommendations.append({
                    "priority": "medium",
                    "category": "code_cleanup", 
                    "issue": issue.get("type", ""),
                    "action": issue["recommendation"],
                    "estimated_effort": "1-2天"
                })
        
        for issue in self.issues["documentation_issues"]:
            recommendations.append({
                "priority": "low",
                "category": "documentation_sync",
                "issue": issue["message"],
                "action": issue["recommendation"],
                "estimated_effort": "0.5-1天"
            })
        
        return recommendations
    
    def print_report(self, report: Dict[str, Any]):
        """打印报告到控制台"""
        print(f"\n📊 项目健康度报告")
        print(f"=" * 50)
        print(f"检查时间: {report['timestamp']}")
        print(f"健康度评分: {report['health_score']}/100")
        print(f"发现问题总数: {report['total_issues']}")
        
        if report['issues']['consistency_issues']:
            print(f"\n🔧 架构一致性问题 ({len(report['issues']['consistency_issues'])}个):")
            for issue in report['issues']['consistency_issues']:
                print(f"  - {issue['message']} (严重程度: {issue['severity']})")
        
        if report['issues']['dead_code']:
            print(f"\n🧹 无用代码问题 ({len(report['issues']['dead_code'])}个):")
            for issue in report['issues']['dead_code']:
                print(f"  - {issue['type']}: {issue.get('count', 0)}个项目")
        
        if report['issues']['documentation_issues']:
            print(f"\n📚 文档同步问题 ({len(report['issues']['documentation_issues'])}个):")
            for issue in report['issues']['documentation_issues']:
                print(f"  - {issue['message']} (严重程度: {issue['severity']})")
        
        if report['recommendations']:
            print(f"\n💡 改进建议:")
            for rec in report['recommendations']:
                print(f"  {rec['priority'].upper()}: {rec['action']} (预计: {rec['estimated_effort']})")
    
    def save_report(self, report: Dict[str, Any], filename: str = "health_report.json"):
        """保存报告到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"📄 报告已保存到: {filename}")


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = "/Users/<USER>/由己ai项目/需求采集项目"
    
    checker = ProjectHealthChecker(project_root)
    report = checker.run_full_health_check()
    
    checker.print_report(report)
    checker.save_report(report)


if __name__ == "__main__":
    main()
