#!/usr/bin/env python3
"""
交互式知识库探索工具

提供友好的交互界面，帮助用户探索知识库内容
"""

import os
import sys
import chromadb

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.config.knowledge_base_config import get_knowledge_base_config_manager

class KnowledgeBaseExplorer:
    def __init__(self):
        self.config_manager = get_knowledge_base_config_manager()
        self.config = self.get_unified_config().get_config()
        self.client = None
        self.collection = None
        self._connect()
    
    def _connect(self):
        """连接到知识库"""
        try:
            chroma_path = self.config.chroma_db.get('path', 'backend/data/chroma_db')
            collection_name = self.config.chroma_db.get('collection_name', 'hybrid_knowledge_base')
            
            self.client = chromadb.PersistentClient(path=chroma_path)
            self.collection = self.client.get_collection(collection_name)
            print("✅ 已连接到知识库")
        except Exception as e:
            print(f"❌ 连接知识库失败: {e}")
    
    def show_menu(self):
        """显示主菜单"""
        print("\n" + "="*50)
        print("🔍 知识库探索工具")
        print("="*50)
        print("1. 查看知识库概览")
        print("2. 按角色浏览内容")
        print("3. 按类别浏览内容")
        print("4. 搜索特定内容")
        print("5. 查看热门话题")
        print("6. 退出")
        print("-"*50)
    
    def show_overview(self):
        """显示知识库概览"""
        if not self.collection:
            print("❌ 知识库未连接")
            return
        
        try:
            results = self.collection.get(include=['metadatas'])
            total = len(results['metadatas'])
            
            roles = set()
            categories = set()
            sources = set()
            
            for meta in results['metadatas']:
                roles.add(meta.get('role', 'unknown'))
                categories.add(meta.get('category', 'unknown'))
                sources.add(meta.get('source_file', 'unknown'))
            
            print(f"\n📊 知识库概览:")
            print(f"  总文档片段: {total}")
            print(f"  角色类型: {len(roles)} 种 ({', '.join(sorted(roles))})")
            print(f"  内容类别: {len(categories)} 种")
            print(f"  来源文件: {len(sources)} 个")
            
        except Exception as e:
            print(f"❌ 获取概览失败: {e}")
    
    def browse_by_role(self):
        """按角色浏览"""
        if not self.collection:
            return
        
        try:
            results = self.collection.get(include=['metadatas'])
            roles = {}
            
            for i, meta in enumerate(results['metadatas']):
                role = meta.get('role', 'unknown')
                if role not in roles:
                    roles[role] = []
                roles[role].append(i)
            
            print(f"\n👥 可用角色:")
            for i, (role, indices) in enumerate(roles.items(), 1):
                print(f"  {i}. {role} ({len(indices)} 个文档)")
            
            choice = input("\n选择角色编号 (回车返回): ").strip()
            if choice.isdigit():
                role_list = list(roles.keys())
                if 1 <= int(choice) <= len(role_list):
                    selected_role = role_list[int(choice)-1]
                    self._show_role_content(selected_role)
                    
        except Exception as e:
            print(f"❌ 浏览角色失败: {e}")
    
    def _show_role_content(self, role: str):
        """显示特定角色的内容"""
        try:
            # 使用where条件过滤
            results = self.collection.get(
                where={"role": role},
                include=['documents', 'metadatas']
            )
            
            print(f"\n📄 角色 '{role}' 的内容 (共{len(results['documents'])}个):")
            
            for i, (doc, meta) in enumerate(zip(results['documents'][:5], results['metadatas'][:5])):
                source = meta.get('source_file', 'unknown')
                print(f"\n  {i+1}. 来源: {source}")
                print(f"     内容: {doc[:150]}...")
                
            if len(results['documents']) > 5:
                print(f"\n  ... 还有 {len(results['documents'])-5} 个文档")
                
        except Exception as e:
            print(f"❌ 显示角色内容失败: {e}")
    
    def search_content(self):
        """搜索内容"""
        if not self.collection:
            return
        
        query = input("\n🔍 请输入搜索关键词: ").strip()
        if not query:
            return
        
        try:
            results = self.collection.query(
                query_texts=[query],
                n_results=5,
                include=['documents', 'metadatas', 'distances']
            )
            
            if results['documents'][0]:
                print(f"\n🎯 找到 {len(results['documents'][0])} 个相关结果:")
                
                for i, (doc, meta, distance) in enumerate(zip(
                    results['documents'][0],
                    results['metadatas'][0], 
                    results['distances'][0]
                )):
                    role = meta.get('role', 'unknown')
                    source = meta.get('source_file', 'unknown')
                    similarity = 1 - distance
                    
                    print(f"\n  结果 {i+1} (相似度: {similarity:.2f})")
                    print(f"    角色: {role}")
                    print(f"    来源: {source}")
                    print(f"    内容: {doc[:200]}...")
            else:
                print("❌ 未找到相关内容")
                
        except Exception as e:
            print(f"❌ 搜索失败: {e}")
    
    def run(self):
        """运行探索工具"""
        if not self.collection:
            print("❌ 无法连接到知识库，请先构建知识库")
            return
        
        while True:
            self.show_menu()
            choice = input("请选择操作 (1-6): ").strip()
            
            if choice == '1':
                self.show_overview()
            elif choice == '2':
                self.browse_by_role()
            elif choice == '3':
                print("📂 按类别浏览功能开发中...")
            elif choice == '4':
                self.search_content()
            elif choice == '5':
                print("🔥 热门话题功能开发中...")
            elif choice == '6':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请重试")
            
            input("\n按回车继续...")

if __name__ == "__main__":
    explorer = KnowledgeBaseExplorer()
    explorer.run()