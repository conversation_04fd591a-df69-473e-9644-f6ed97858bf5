#!/usr/bin/env python3
"""
知识库内容检查工具

帮助用户了解知识库中的内容，包括：
- 文档数量和类型
- 主要话题分布
- 角色分类统计
- 示例问题推荐
"""

import os
import sys
import chromadb
from collections import Counter
from typing import Dict

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.config.knowledge_base_config import get_knowledge_base_config_manager

def analyze_knowledge_base():
    """分析知识库内容并生成报告"""
    
    get_knowledge_base_config_manager()
    config = get_unified_config().get_config()
    
    # 连接ChromaDB
    chroma_path = config.chroma_db.get('path', 'backend/data/chroma_db')
    collection_name = config.chroma_db.get('collection_name', 'hybrid_knowledge_base')
    
    try:
        client = chromadb.PersistentClient(path=chroma_path)
        collection = client.get_collection(collection_name)
        
        # 获取所有文档
        results = collection.get(include=['documents', 'metadatas'])
        
        if not results['documents']:
            print("❌ 知识库为空，请先运行 scripts/build_knowledge_base.py 构建知识库")
            return
        
        print(f"📊 知识库分析报告")
        print(f"=" * 50)
        
        # 基本统计
        total_docs = len(results['documents'])
        print(f"📄 总文档数量: {total_docs}")
        
        # 角色分布统计
        role_stats = Counter()
        category_stats = Counter()
        source_stats = Counter()
        
        for metadata in results['metadatas']:
            role = metadata.get('role', 'unknown')
            category = metadata.get('category', 'unknown')
            source = metadata.get('source_file', 'unknown')
            
            role_stats[role] += 1
            category_stats[category] += 1
            source_stats[source] += 1
        
        # 显示角色分布
        print(f"\n👥 角色分布:")
        for role, count in role_stats.most_common():
            print(f"  - {role}: {count} 个文档")
        
        # 显示类别分布
        print(f"\n📂 类别分布:")
        for category, count in category_stats.most_common():
            print(f"  - {category}: {count} 个文档")
        
        # 显示来源文件
        print(f"\n📁 主要来源文件:")
        for source, count in source_stats.most_common(10):
            print(f"  - {source}: {count} 个片段")
        
        # 生成示例问题
        print(f"\n💡 建议的查询示例:")
        generate_sample_queries(results, role_stats, category_stats)
        
        # 内容预览
        print(f"\n📖 内容预览 (前3个文档片段):")
        for i, (doc, metadata) in enumerate(zip(results['documents'][:3], results['metadatas'][:3])):
            role = metadata.get('role', 'unknown')
            source = metadata.get('source_file', 'unknown')
            print(f"\n  片段 {i+1} [{role}] - {source}:")
            print(f"    {doc[:200]}...")
        
    except Exception as e:
        print(f"❌ 分析知识库失败: {e}")

def generate_sample_queries(results: Dict, role_stats: Counter, category_stats: Counter):
    """根据知识库内容生成示例查询"""
    
    queries = []
    
    # 基于角色的查询
    for role in role_stats.keys():
        if role != 'unknown':
            queries.append(f"role:{role} 如何使用")
    
    # 基于类别的查询
    for category in list(category_stats.keys())[:3]:
        if category != 'unknown':
            queries.append(f"category:{category}")
    
    # 通用查询示例
    common_queries = [
        "如何注册账号",
        "登录问题",
        "功能介绍",
        "使用教程",
        "常见问题",
        "limit:10 帮助"
    ]
    
    queries.extend(common_queries)
    
    for i, query in enumerate(queries[:8], 1):
        print(f"  {i}. {query}")

if __name__ == "__main__":
    analyze_knowledge_base()