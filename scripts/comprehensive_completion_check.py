#!/usr/bin/env python3
"""
全面完整性检查脚本
检查项目优化的完整性，避免遗漏问题
"""

import os
import re
import json
import ast
from pathlib import Path
from typing import Dict, List, Set, Any
from dataclasses import dataclass
from datetime import datetime


@dataclass
class CheckResult:
    """检查结果"""
    category: str
    item: str
    status: str  # "PASS", "FAIL", "WARNING"
    details: str
    file_path: str = ""
    line_number: int = 0


class ComprehensiveCompletionChecker:
    """全面完整性检查器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.results = []
        
        # 需要检查的文件扩展名
        self.code_extensions = {'.py', '.yaml', '.yml', '.json', '.md'}
        
        # 排除的目录
        self.exclude_dirs = {
            '.git', '__pycache__', '.pytest_cache', 'node_modules',
            '.venv', 'venv', '.history', 'logs', 'backups'
        }
        
        # 旧的配置管理器模式
        self.old_config_patterns = [
            r'from.*config_manager.*import',
            r'ConfigManager\(',
            r'config_manager\.',
            r'from.*unified_config_manager.*import',
            r'UnifiedConfigManager\(',
            r'unified_config_manager\.'
        ]
        
        # 新的配置管理器模式
        self.new_config_patterns = [
            r'from.*unified_config_loader.*import',
            r'get_unified_config\(',
            r'unified_config_loader\.'
        ]
        
        # 废弃的文件模式
        self.deprecated_patterns = [
            r'.*deprecated.*',
            r'.*_old\.',
            r'.*_backup\.',
            r'.*副本.*',
            r'.*_copy\.',
            r'.*\.bak$',
            r'.*\.backup$'
        ]
        
        # 旧的决策引擎模式
        self.old_decision_patterns = [
            r'AcceleratedIntentDecisionEngine',
            r'HybridIntentRecognitionEngine',
            r'intent_decision_engine(?!\.py)',  # 排除文件名本身
            r'accelerated_intent_decision_engine(?!\.py)'  # 排除文件名本身
        ]
        
        # 新的决策引擎模式
        self.new_decision_patterns = [
            r'SimplifiedDecisionEngine',
            r'get_simplified_decision_engine',
            r'simplified_decision_engine'
        ]
    
    def run_all_checks(self) -> Dict[str, Any]:
        """运行所有检查"""
        print("🔍 开始全面完整性检查...")
        
        # 1. 配置管理器统一性检查
        print("  📋 检查配置管理器统一性...")
        self.check_config_manager_consistency()
        
        # 2. 决策引擎统一性检查
        print("  🧠 检查决策引擎统一性...")
        self.check_decision_engine_consistency()
        
        # 3. LLM客户端统一性检查
        print("  🤖 检查LLM客户端统一性...")
        self.check_llm_client_consistency()
        
        # 4. 状态管理统一性检查
        print("  📊 检查状态管理统一性...")
        self.check_state_management_consistency()
        
        # 5. 废弃文件清理检查
        print("  🧹 检查废弃文件清理...")
        self.check_deprecated_files()
        
        # 6. 无用导入检查
        print("  📦 检查无用导入...")
        self.check_unused_imports()
        
        # 7. 配置文件一致性检查
        print("  ⚙️ 检查配置文件一致性...")
        self.check_config_file_consistency()
        
        # 8. 文档同步检查
        print("  📚 检查文档同步...")
        self.check_documentation_sync()
        
        # 9. 测试覆盖检查
        print("  🧪 检查测试覆盖...")
        self.check_test_coverage()
        
        # 10. 性能关键路径检查
        print("  ⚡检查性能关键路径...")
        self.check_performance_critical_paths()
        
        return self.generate_report()
    
    def check_config_manager_consistency(self):
        """检查配置管理器统一性"""
        old_usage_files = []
        new_usage_files = []
        mixed_usage_files = []
        
        for file_path in self.get_python_files():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                old_matches = any(re.search(pattern, content) for pattern in self.old_config_patterns)
                new_matches = any(re.search(pattern, content) for pattern in self.new_config_patterns)
                
                if old_matches and new_matches:
                    mixed_usage_files.append(str(file_path))
                elif old_matches:
                    old_usage_files.append(str(file_path))
                elif new_matches:
                    new_usage_files.append(str(file_path))
                    
            except Exception as e:
                self.results.append(CheckResult(
                    category="配置管理器",
                    item="文件读取错误",
                    status="WARNING",
                    details=f"无法读取文件: {e}",
                    file_path=str(file_path)
                ))
        
        # 评估结果
        if mixed_usage_files:
            self.results.append(CheckResult(
                category="配置管理器",
                item="新旧混用",
                status="FAIL",
                details=f"发现{len(mixed_usage_files)}个文件同时使用新旧配置管理器: {mixed_usage_files[:5]}"
            ))
        
        if old_usage_files:
            self.results.append(CheckResult(
                category="配置管理器",
                item="旧版本使用",
                status="FAIL",
                details=f"发现{len(old_usage_files)}个文件仍使用旧配置管理器: {old_usage_files[:5]}"
            ))
        
        if new_usage_files and not old_usage_files and not mixed_usage_files:
            self.results.append(CheckResult(
                category="配置管理器",
                item="统一性检查",
                status="PASS",
                details=f"所有{len(new_usage_files)}个文件都使用新的统一配置管理器"
            ))
    
    def check_decision_engine_consistency(self):
        """检查决策引擎统一性"""
        old_usage_files = []
        new_usage_files = []
        mixed_usage_files = []
        
        for file_path in self.get_python_files():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                old_matches = any(re.search(pattern, content) for pattern in self.old_decision_patterns)
                new_matches = any(re.search(pattern, content) for pattern in self.new_decision_patterns)
                
                if old_matches and new_matches:
                    mixed_usage_files.append(str(file_path))
                elif old_matches:
                    old_usage_files.append(str(file_path))
                elif new_matches:
                    new_usage_files.append(str(file_path))
                    
            except Exception as e:
                continue
        
        # 评估结果
        if mixed_usage_files:
            self.results.append(CheckResult(
                category="决策引擎",
                item="新旧混用",
                status="FAIL",
                details=f"发现{len(mixed_usage_files)}个文件同时使用新旧决策引擎: {mixed_usage_files[:5]}"
            ))
        
        if old_usage_files:
            self.results.append(CheckResult(
                category="决策引擎",
                item="旧版本使用",
                status="WARNING",
                details=f"发现{len(old_usage_files)}个文件仍使用旧决策引擎: {old_usage_files[:5]}"
            ))
        
        if new_usage_files:
            self.results.append(CheckResult(
                category="决策引擎",
                item="新版本使用",
                status="PASS",
                details=f"发现{len(new_usage_files)}个文件使用新的简化决策引擎"
            ))
    
    def check_llm_client_consistency(self):
        """检查LLM客户端统一性"""
        unified_factory_usage = []
        direct_client_creation = []
        
        patterns = {
            'unified_factory': [
                r'from.*unified_llm_client_factory.*import',
                r'create_llm_client\(',
                r'get_available_llm_types\('
            ],
            'direct_creation': [
                r'OpenAI\(',
                r'ChatOpenAI\(',
                r'openai\.Client\(',
                r'LLMService\('
            ]
        }
        
        for file_path in self.get_python_files():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                unified_matches = any(re.search(pattern, content) for pattern in patterns['unified_factory'])
                direct_matches = any(re.search(pattern, content) for pattern in patterns['direct_creation'])
                
                if unified_matches:
                    unified_factory_usage.append(str(file_path))
                if direct_matches:
                    direct_client_creation.append(str(file_path))
                    
            except Exception as e:
                continue
        
        # 评估结果
        if direct_client_creation:
            self.results.append(CheckResult(
                category="LLM客户端",
                item="直接创建",
                status="WARNING",
                details=f"发现{len(direct_client_creation)}个文件直接创建LLM客户端: {direct_client_creation[:3]}"
            ))
        
        if unified_factory_usage:
            self.results.append(CheckResult(
                category="LLM客户端",
                item="统一工厂",
                status="PASS",
                details=f"发现{len(unified_factory_usage)}个文件使用统一LLM客户端工厂"
            ))
    
    def check_state_management_consistency(self):
        """检查状态管理统一性"""
        unified_state_usage = []
        direct_state_access = []
        
        patterns = {
            'unified_state': [
                r'from.*unified_state_manager.*import',
                r'get_state_manager\(',
                r'UnifiedStateManager'
            ],
            'direct_access': [
                r'session\[.*state.*\]',
                r'state\s*=\s*["\']',
                r'\.state\s*='
            ]
        }
        
        for file_path in self.get_python_files():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                unified_matches = any(re.search(pattern, content) for pattern in patterns['unified_state'])
                direct_matches = any(re.search(pattern, content) for pattern in patterns['direct_access'])
                
                if unified_matches:
                    unified_state_usage.append(str(file_path))
                if direct_matches:
                    direct_state_access.append(str(file_path))
                    
            except Exception as e:
                continue
        
        # 评估结果
        if direct_state_access:
            self.results.append(CheckResult(
                category="状态管理",
                item="直接访问",
                status="WARNING",
                details=f"发现{len(direct_state_access)}个文件直接访问状态: {direct_state_access[:3]}"
            ))
        
        if unified_state_usage:
            self.results.append(CheckResult(
                category="状态管理",
                item="统一管理",
                status="PASS",
                details=f"发现{len(unified_state_usage)}个文件使用统一状态管理器"
            ))
    
    def check_deprecated_files(self):
        """检查废弃文件"""
        deprecated_files = []
        
        for file_path in self.get_all_files():
            file_name = file_path.name.lower()
            
            # 检查文件名是否匹配废弃模式
            for pattern in self.deprecated_patterns:
                if re.match(pattern, file_name):
                    deprecated_files.append(str(file_path))
                    break
        
        # 检查特定的废弃文件
        specific_deprecated = [
            'backend/agents/intent_decision_engine.py',
            'backend/agents/accelerated_intent_decision_engine.py',
            'backend/config/config_manager.py',
            'backend/agents/enhanced_semantic_matcher.py'
        ]
        
        existing_deprecated = []
        for file_path in specific_deprecated:
            full_path = self.project_root / file_path
            if full_path.exists():
                existing_deprecated.append(file_path)
        
        # 评估结果
        if deprecated_files:
            self.results.append(CheckResult(
                category="废弃文件",
                item="模式匹配",
                status="FAIL",
                details=f"发现{len(deprecated_files)}个疑似废弃文件: {deprecated_files[:5]}"
            ))
        
        if existing_deprecated:
            self.results.append(CheckResult(
                category="废弃文件",
                item="特定文件",
                status="FAIL",
                details=f"发现{len(existing_deprecated)}个应该删除的废弃文件: {existing_deprecated}"
            ))
        
        if not deprecated_files and not existing_deprecated:
            self.results.append(CheckResult(
                category="废弃文件",
                item="清理检查",
                status="PASS",
                details="未发现废弃文件"
            ))
    
    def check_unused_imports(self):
        """检查无用导入"""
        unused_imports_count = 0
        files_with_unused_imports = []
        
        for file_path in self.get_python_files():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 简单的无用导入检测
                lines = content.split('\n')
                imports = []
                
                for i, line in enumerate(lines):
                    line = line.strip()
                    if line.startswith('import ') or line.startswith('from '):
                        # 提取导入的模块名
                        if ' import ' in line:
                            parts = line.split(' import ')
                            if len(parts) == 2:
                                imported_items = [item.strip() for item in parts[1].split(',')]
                                for item in imported_items:
                                    item = item.split(' as ')[0].strip()
                                    if item and not self.is_import_used(item, content, i):
                                        unused_imports_count += 1
                                        if str(file_path) not in files_with_unused_imports:
                                            files_with_unused_imports.append(str(file_path))
                        else:
                            # import module
                            module = line.replace('import ', '').strip()
                            if module and not self.is_import_used(module, content, i):
                                unused_imports_count += 1
                                if str(file_path) not in files_with_unused_imports:
                                    files_with_unused_imports.append(str(file_path))
                                    
            except Exception as e:
                continue
        
        # 评估结果
        if unused_imports_count > 1000:
            self.results.append(CheckResult(
                category="无用导入",
                item="数量统计",
                status="FAIL",
                details=f"发现{unused_imports_count}个疑似无用导入，涉及{len(files_with_unused_imports)}个文件"
            ))
        elif unused_imports_count > 100:
            self.results.append(CheckResult(
                category="无用导入",
                item="数量统计",
                status="WARNING",
                details=f"发现{unused_imports_count}个疑似无用导入，涉及{len(files_with_unused_imports)}个文件"
            ))
        else:
            self.results.append(CheckResult(
                category="无用导入",
                item="数量统计",
                status="PASS",
                details=f"无用导入数量较少: {unused_imports_count}个"
            ))
    
    def check_config_file_consistency(self):
        """检查配置文件一致性"""
        config_files = []
        unified_config_exists = False
        
        # 查找配置文件
        for file_path in self.get_all_files():
            if file_path.suffix in {'.yaml', '.yml', '.json'}:
                if 'config' in file_path.name.lower():
                    config_files.append(str(file_path))
                    if 'unified_config' in file_path.name.lower():
                        unified_config_exists = True
        
        # 检查统一配置文件
        unified_config_path = self.project_root / 'backend/config/unified_config.yaml'
        if unified_config_path.exists():
            unified_config_exists = True
            
            # 检查配置文件内容
            try:
                import yaml
                with open(unified_config_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                required_sections = ['system', 'llm', 'conversation', 'keyword_rules', 'message_templates']
                missing_sections = [section for section in required_sections if section not in config_data]
                
                if missing_sections:
                    self.results.append(CheckResult(
                        category="配置文件",
                        item="内容完整性",
                        status="FAIL",
                        details=f"统一配置文件缺少必需部分: {missing_sections}"
                    ))
                else:
                    self.results.append(CheckResult(
                        category="配置文件",
                        item="内容完整性",
                        status="PASS",
                        details="统一配置文件包含所有必需部分"
                    ))
                    
            except Exception as e:
                self.results.append(CheckResult(
                    category="配置文件",
                    item="格式检查",
                    status="FAIL",
                    details=f"统一配置文件格式错误: {e}"
                ))
        
        # 评估结果
        if not unified_config_exists:
            self.results.append(CheckResult(
                category="配置文件",
                item="统一配置",
                status="FAIL",
                details="未找到统一配置文件"
            ))
        
        if len(config_files) > 3:
            self.results.append(CheckResult(
                category="配置文件",
                item="文件数量",
                status="WARNING",
                details=f"发现{len(config_files)}个配置文件，可能存在冗余: {config_files[:5]}"
            ))
    
    def check_documentation_sync(self):
        """检查文档同步"""
        doc_files = []
        code_components = set()
        documented_components = set()
        
        # 查找文档文件
        docs_dir = self.project_root / 'docs'
        if docs_dir.exists():
            for file_path in docs_dir.rglob('*.md'):
                doc_files.append(str(file_path))
        
        # 提取代码组件
        for file_path in self.get_python_files():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 提取类名
                class_matches = re.findall(r'class\s+(\w+)', content)
                code_components.update(class_matches)
                
                # 提取函数名
                func_matches = re.findall(r'def\s+(\w+)', content)
                code_components.update(func_matches)
                
            except Exception as e:
                continue
        
        # 检查文档中提到的组件
        for doc_file in doc_files:
            try:
                with open(doc_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for component in code_components:
                    if component in content:
                        documented_components.add(component)
                        
            except Exception as e:
                continue
        
        # 评估结果
        documentation_rate = len(documented_components) / len(code_components) if code_components else 0
        
        if documentation_rate < 0.5:
            self.results.append(CheckResult(
                category="文档同步",
                item="覆盖率",
                status="FAIL",
                details=f"文档覆盖率过低: {documentation_rate:.1%} ({len(documented_components)}/{len(code_components)})"
            ))
        elif documentation_rate < 0.8:
            self.results.append(CheckResult(
                category="文档同步",
                item="覆盖率",
                status="WARNING",
                details=f"文档覆盖率中等: {documentation_rate:.1%} ({len(documented_components)}/{len(code_components)})"
            ))
        else:
            self.results.append(CheckResult(
                category="文档同步",
                item="覆盖率",
                status="PASS",
                details=f"文档覆盖率良好: {documentation_rate:.1%} ({len(documented_components)}/{len(code_components)})"
            ))
    
    def check_test_coverage(self):
        """检查测试覆盖"""
        test_files = []
        source_files = []
        
        # 查找测试文件
        tests_dir = self.project_root / 'tests'
        if tests_dir.exists():
            for file_path in tests_dir.rglob('*.py'):
                if file_path.name.startswith('test_'):
                    test_files.append(str(file_path))
        
        # 查找源文件
        backend_dir = self.project_root / 'backend'
        if backend_dir.exists():
            for file_path in backend_dir.rglob('*.py'):
                if not file_path.name.startswith('__'):
                    source_files.append(str(file_path))
        
        # 评估结果
        if not test_files:
            self.results.append(CheckResult(
                category="测试覆盖",
                item="测试文件",
                status="FAIL",
                details="未找到测试文件"
            ))
        else:
            coverage_ratio = len(test_files) / len(source_files) if source_files else 0
            
            if coverage_ratio < 0.3:
                self.results.append(CheckResult(
                    category="测试覆盖",
                    item="覆盖比例",
                    status="FAIL",
                    details=f"测试覆盖比例过低: {coverage_ratio:.1%} ({len(test_files)}/{len(source_files)})"
                ))
            elif coverage_ratio < 0.6:
                self.results.append(CheckResult(
                    category="测试覆盖",
                    item="覆盖比例",
                    status="WARNING",
                    details=f"测试覆盖比例中等: {coverage_ratio:.1%} ({len(test_files)}/{len(source_files)})"
                ))
            else:
                self.results.append(CheckResult(
                    category="测试覆盖",
                    item="覆盖比例",
                    status="PASS",
                    details=f"测试覆盖比例良好: {coverage_ratio:.1%} ({len(test_files)}/{len(source_files)})"
                ))
    
    def check_performance_critical_paths(self):
        """检查性能关键路径"""
        critical_files = [
            'backend/agents/simplified_decision_engine.py',
            'backend/agents/unified_state_manager.py',
            'backend/agents/conversation_state_machine.py',
            'backend/config/unified_config_loader.py'
        ]
        
        missing_files = []
        existing_files = []
        
        for file_path in critical_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                existing_files.append(file_path)
            else:
                missing_files.append(file_path)
        
        # 评估结果
        if missing_files:
            self.results.append(CheckResult(
                category="性能关键路径",
                item="关键文件",
                status="FAIL",
                details=f"缺少{len(missing_files)}个关键文件: {missing_files}"
            ))
        else:
            self.results.append(CheckResult(
                category="性能关键路径",
                item="关键文件",
                status="PASS",
                details=f"所有{len(existing_files)}个关键文件都存在"
            ))
    
    def is_import_used(self, import_name: str, content: str, import_line: int) -> bool:
        """检查导入是否被使用（简单检测）"""
        lines = content.split('\n')
        
        # 在导入行之后查找使用
        for i in range(import_line + 1, len(lines)):
            line = lines[i]
            if import_name in line and not line.strip().startswith('#'):
                return True
        
        return False
    
    def get_python_files(self):
        """获取所有Python文件"""
        for file_path in self.project_root.rglob('*.py'):
            if not any(exclude_dir in file_path.parts for exclude_dir in self.exclude_dirs):
                yield file_path
    
    def get_all_files(self):
        """获取所有相关文件"""
        for file_path in self.project_root.rglob('*'):
            if file_path.is_file() and file_path.suffix in self.code_extensions:
                if not any(exclude_dir in file_path.parts for exclude_dir in self.exclude_dirs):
                    yield file_path
    
    def generate_report(self) -> Dict[str, Any]:
        """生成检查报告"""
        # 统计结果
        total_checks = len(self.results)
        passed_checks = len([r for r in self.results if r.status == "PASS"])
        failed_checks = len([r for r in self.results if r.status == "FAIL"])
        warning_checks = len([r for r in self.results if r.status == "WARNING"])
        
        # 按类别分组
        by_category = {}
        for result in self.results:
            if result.category not in by_category:
                by_category[result.category] = []
            by_category[result.category].append(result)
        
        # 生成报告
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_checks": total_checks,
                "passed_checks": passed_checks,
                "failed_checks": failed_checks,
                "warning_checks": warning_checks,
                "success_rate": passed_checks / total_checks if total_checks > 0 else 0
            },
            "by_category": {},
            "critical_issues": [],
            "recommendations": []
        }
        
        # 按类别整理结果
        for category, results in by_category.items():
            report["by_category"][category] = {
                "total": len(results),
                "passed": len([r for r in results if r.status == "PASS"]),
                "failed": len([r for r in results if r.status == "FAIL"]),
                "warnings": len([r for r in results if r.status == "WARNING"]),
                "details": [
                    {
                        "item": r.item,
                        "status": r.status,
                        "details": r.details,
                        "file_path": r.file_path
                    }
                    for r in results
                ]
            }
        
        # 识别关键问题
        critical_issues = [r for r in self.results if r.status == "FAIL"]
        report["critical_issues"] = [
            {
                "category": r.category,
                "item": r.item,
                "details": r.details,
                "file_path": r.file_path
            }
            for r in critical_issues
        ]
        
        # 生成建议
        if failed_checks > 0:
            report["recommendations"].append("立即修复所有FAIL状态的问题")
        if warning_checks > 0:
            report["recommendations"].append("考虑修复WARNING状态的问题以提升代码质量")
        if passed_checks / total_checks < 0.8:
            report["recommendations"].append("整体完成度不足80%，需要进一步优化")
        
        return report


def main():
    """主函数"""
    checker = ComprehensiveCompletionChecker()
    report = checker.run_all_checks()
    
    # 保存报告
    with open("comprehensive_completion_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 打印摘要
    print(f"\n📊 全面完整性检查完成!")
    print(f"总检查项: {report['summary']['total_checks']}")
    print(f"通过: {report['summary']['passed_checks']}")
    print(f"失败: {report['summary']['failed_checks']}")
    print(f"警告: {report['summary']['warning_checks']}")
    print(f"成功率: {report['summary']['success_rate']:.1%}")
    
    # 打印关键问题
    if report['critical_issues']:
        print(f"\n🚨 关键问题 ({len(report['critical_issues'])}个):")
        for issue in report['critical_issues'][:5]:
            print(f"  - {issue['category']}: {issue['item']} - {issue['details']}")
    
    # 打印建议
    if report['recommendations']:
        print(f"\n💡 建议:")
        for rec in report['recommendations']:
            print(f"  - {rec}")
    
    print(f"\n📄 详细报告已保存到: comprehensive_completion_report.json")


if __name__ == "__main__":
    main()
