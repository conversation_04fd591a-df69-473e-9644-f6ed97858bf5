#!/usr/bin/env python3
"""
安全的导入清理脚本
使用autoflake清理无用导入，但先备份文件
"""

import shutil
import subprocess
from pathlib import Path
from typing import List


class SafeImportCleaner:
    """安全的导入清理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / "import_cleanup_backup"
        self.cleaned_files = []
        
    def create_backup(self):
        """创建备份"""
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)
        
        self.backup_dir.mkdir(exist_ok=True)
        
        # 备份backend目录
        backend_src = self.project_root / "backend"
        backend_backup = self.backup_dir / "backend"
        
        if backend_src.exists():
            shutil.copytree(backend_src, backend_backup)
            print(f"✅ 已备份backend目录到: {backend_backup}")
        
        # 备份scripts目录
        scripts_src = self.project_root / "scripts"
        scripts_backup = self.backup_dir / "scripts"
        
        if scripts_src.exists():
            shutil.copytree(scripts_src, scripts_backup)
            print(f"✅ 已备份scripts目录到: {scripts_backup}")
    
    def check_unused_imports(self, directory: str) -> List[str]:
        """检查无用导入"""
        try:
            result = subprocess.run([
                "autoflake", 
                "--check", 
                "--recursive", 
                "--remove-all-unused-imports",
                directory
            ], capture_output=True, text=True, cwd=self.project_root)
            
            # 解析输出，找出有问题的文件
            files_with_issues = []
            for line in result.stdout.split('\n'):
                if ': Unused imports/variables detected' in line:
                    file_path = line.split(':')[0]
                    files_with_issues.append(file_path)
            
            return files_with_issues
            
        except Exception as e:
            print(f"❌ 检查无用导入失败: {e}")
            return []
    
    def clean_imports_safe(self, directory: str, dry_run: bool = False):
        """安全清理导入"""
        print(f"🧹 开始清理 {directory} 目录的无用导入...")
        
        # 构建autoflake命令
        cmd = [
            "autoflake",
            "--recursive",
            "--remove-all-unused-imports",
            "--remove-unused-variables",
            "--ignore-init-module-imports"  # 保留__init__.py中的导入
        ]
        
        if not dry_run:
            cmd.append("--in-place")
        
        cmd.append(directory)
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                if dry_run:
                    print("🔍 预览模式 - 以下文件将被修改:")
                    print(result.stdout)
                else:
                    print("✅ 导入清理完成")
                    if result.stdout:
                        print("修改的文件:")
                        print(result.stdout)
            else:
                print(f"❌ 清理失败: {result.stderr}")
                
        except Exception as e:
            print(f"❌ 执行清理命令失败: {e}")
    
    def clean_specific_files(self, file_paths: List[str], dry_run: bool = False):
        """清理特定文件"""
        print(f"🎯 清理特定文件 ({len(file_paths)} 个文件)...")
        
        for file_path in file_paths:
            full_path = self.project_root / file_path
            if not full_path.exists():
                print(f"⚠️  文件不存在: {file_path}")
                continue
            
            # 构建命令
            cmd = [
                "autoflake",
                "--remove-all-unused-imports",
                "--remove-unused-variables"
            ]
            
            if not dry_run:
                cmd.append("--in-place")
            
            cmd.append(str(full_path))
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    if dry_run:
                        if result.stdout:
                            print(f"📄 {file_path}: 将被修改")
                    else:
                        if result.stdout:
                            print(f"✅ {file_path}: 已清理")
                            self.cleaned_files.append(file_path)
                        else:
                            print(f"ℹ️  {file_path}: 无需修改")
                else:
                    print(f"❌ {file_path}: 清理失败 - {result.stderr}")
                    
            except Exception as e:
                print(f"❌ 处理文件失败 {file_path}: {e}")
    
    def verify_syntax(self, file_paths: List[str]) -> bool:
        """验证清理后的文件语法是否正确"""
        print("🔍 验证清理后的文件语法...")
        
        all_valid = True
        for file_path in file_paths:
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
            
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 尝试编译Python代码
                compile(content, str(full_path), 'exec')
                print(f"✅ {file_path}: 语法正确")
                
            except SyntaxError as e:
                print(f"❌ {file_path}: 语法错误 - {e}")
                all_valid = False
            except Exception as e:
                print(f"⚠️  {file_path}: 验证失败 - {e}")
        
        return all_valid
    
    def rollback_changes(self):
        """回滚更改"""
        if not self.backup_dir.exists():
            print("❌ 没有找到备份目录，无法回滚")
            return
        
        print("🔄 开始回滚更改...")
        
        # 恢复backend目录
        backend_backup = self.backup_dir / "backend"
        backend_target = self.project_root / "backend"
        
        if backend_backup.exists():
            if backend_target.exists():
                shutil.rmtree(backend_target)
            shutil.copytree(backend_backup, backend_target)
            print("✅ 已恢复backend目录")
        
        # 恢复scripts目录
        scripts_backup = self.backup_dir / "scripts"
        scripts_target = self.project_root / "scripts"
        
        if scripts_backup.exists():
            if scripts_target.exists():
                shutil.rmtree(scripts_target)
            shutil.copytree(scripts_backup, scripts_target)
            print("✅ 已恢复scripts目录")
        
        print("✅ 回滚完成")
    
    def run_safe_cleanup(self, target_dirs: List[str] = None):
        """运行安全清理"""
        if target_dirs is None:
            target_dirs = ["backend", "scripts"]
        
        print("🚀 开始安全的导入清理...")
        
        # 1. 创建备份
        print("\n📁 步骤1: 创建备份")
        self.create_backup()
        
        # 2. 检查无用导入
        print("\n🔍 步骤2: 检查无用导入")
        all_files_with_issues = []
        for directory in target_dirs:
            if (self.project_root / directory).exists():
                files_with_issues = self.check_unused_imports(directory)
                all_files_with_issues.extend(files_with_issues)
                print(f"{directory}: 发现 {len(files_with_issues)} 个文件有无用导入")
        
        if not all_files_with_issues:
            print("✅ 没有发现无用导入")
            return
        
        print(f"总计: {len(all_files_with_issues)} 个文件需要清理")
        
        # 3. 预览清理效果
        print("\n👀 步骤3: 预览清理效果")
        self.clean_specific_files(all_files_with_issues[:10], dry_run=True)  # 只预览前10个
        
        # 4. 执行清理
        print("\n🧹 步骤4: 执行清理")
        self.clean_specific_files(all_files_with_issues)
        
        # 5. 验证语法
        print("\n✅ 步骤5: 验证语法")
        if self.cleaned_files:
            syntax_valid = self.verify_syntax(self.cleaned_files)
            
            if not syntax_valid:
                print("❌ 发现语法错误，建议回滚")
                return False
        
        print(f"\n🎉 清理完成!")
        print(f"清理了 {len(self.cleaned_files)} 个文件")
        print(f"备份位置: {self.backup_dir}")
        
        return True


def main():
    """主函数"""
    import sys
    
    cleaner = SafeImportCleaner()
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        cleaner.rollback_changes()
    else:
        success = cleaner.run_safe_cleanup()
        if not success:
            print("\n⚠️  清理过程中出现问题，可以运行以下命令回滚:")
            print("python scripts/safe_import_cleanup.py rollback")


if __name__ == "__main__":
    main()
