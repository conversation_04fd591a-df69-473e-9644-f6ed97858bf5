#!/usr/bin/env python3
"""
简化功能测试
专注于核心功能的快速验证
"""

import asyncio
import logging
import time
import sys
import os
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class SimplifiedFunctionTest:
    """简化功能测试"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.test_results = []
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        print("🚀 开始简化功能测试...")
        
        test_suite = [
            ("统一配置加载器", self.test_config_loader),
            ("简化决策引擎", self.test_decision_engine),
            ("对话状态机", self.test_state_machine),
            ("统一状态管理器", self.test_state_manager),
            ("端到端对话流程", self.test_end_to_end_flow),
            ("性能基准", self.test_performance)
        ]
        
        total_tests = len(test_suite)
        passed_tests = 0
        
        for test_name, test_func in test_suite:
            print(f"\n🧪 {test_name}...")
            
            start_time = time.time()
            try:
                result = await test_func()
                duration = time.time() - start_time
                
                if result["success"]:
                    passed_tests += 1
                    print(f"   ✅ 通过 ({duration:.3f}s)")
                else:
                    print(f"   ❌ 失败 ({duration:.3f}s): {result.get('error', '未知错误')}")
                
                self.test_results.append({
                    "test_name": test_name,
                    "success": result["success"],
                    "duration": duration,
                    "details": result
                })
                
            except Exception as e:
                duration = time.time() - start_time
                print(f"   ❌ 异常 ({duration:.3f}s): {str(e)}")
                
                self.test_results.append({
                    "test_name": test_name,
                    "success": False,
                    "duration": duration,
                    "error": str(e)
                })
        
        success_rate = passed_tests / total_tests
        print(f"\n📊 测试完成: {passed_tests}/{total_tests} 通过 ({success_rate:.1%})")
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": success_rate,
            "test_results": self.test_results
        }
    
    async def test_config_loader(self) -> Dict[str, Any]:
        """测试统一配置加载器"""
        try:
            from backend.config.unified_config_loader import get_unified_config
            
            config = get_unified_config()
            
            # 基本功能测试
            system_config = config.get_system_config()
            llm_config = config.get_llm_config("default")
            conversation_config = config.get_conversation_config()
            
            checks = {
                "system_config_loaded": bool(system_config),
                "llm_config_loaded": bool(llm_config),
                "conversation_config_loaded": bool(conversation_config),
                "config_file_exists": config.config_file.exists()
            }
            
            success = all(checks.values())
            
            return {
                "success": success,
                "checks": checks,
                "config_file": str(config.config_file)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_decision_engine(self) -> Dict[str, Any]:
        """测试简化决策引擎"""
        try:
            from backend.agents.simplified_decision_engine import get_simplified_decision_engine
            from backend.agents.unified_state_manager import ConversationState
            
            engine = get_simplified_decision_engine()
            
            # 测试决策
            test_cases = [
                ("greeting", ConversationState.IDLE),
                ("business_requirement", ConversationState.IDLE),
                ("confirm", ConversationState.COLLECTING_INFO),
                ("confirm", ConversationState.DOCUMENTING)
            ]
            
            results = []
            for intent, state in test_cases:
                result = engine.make_decision(intent, state)
                results.append({
                    "intent": intent,
                    "state": state.value,
                    "action": result.action,
                    "success": bool(result.action)
                })
            
            success_count = sum(1 for r in results if r["success"])
            
            return {
                "success": success_count == len(test_cases),
                "results": results,
                "success_rate": success_count / len(test_cases)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_state_machine(self) -> Dict[str, Any]:
        """测试对话状态机"""
        try:
            from backend.agents.conversation_state_machine import get_conversation_state_machine
            
            state_machine = get_conversation_state_machine()
            session_id = "test_session"
            user_id = "test_user"
            
            # 重置会话
            await state_machine.reset_conversation(session_id, user_id)
            
            # 测试消息处理
            test_messages = [
                "你好",
                "我想做一个网站",
                "确认"
            ]
            
            results = []
            for message in test_messages:
                result = await state_machine.process_message(message, session_id, user_id)
                current_state = await state_machine.get_current_state(session_id, user_id)
                
                results.append({
                    "message": message,
                    "success": result.success,
                    "state": current_state.value
                })
            
            success_count = sum(1 for r in results if r["success"])
            
            return {
                "success": success_count == len(test_messages),
                "results": results,
                "success_rate": success_count / len(test_messages)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_state_manager(self) -> Dict[str, Any]:
        """测试统一状态管理器"""
        try:
            from backend.agents.unified_state_manager import get_state_manager, ConversationState
            
            manager = get_state_manager()
            session_id = "test_state_session"
            user_id = "test_state_user"
            
            # 测试状态操作
            initial_state = await manager.get_conversation_state(session_id, user_id)
            
            success1 = await manager.set_conversation_state(
                session_id, user_id, ConversationState.COLLECTING_INFO, "test"
            )
            
            new_state = await manager.get_conversation_state(session_id, user_id)
            
            success2 = await manager.reset_session_state(session_id, user_id)
            
            checks = {
                "initial_state_correct": initial_state == ConversationState.IDLE,
                "state_transition_success": success1,
                "new_state_correct": new_state == ConversationState.COLLECTING_INFO,
                "reset_success": success2
            }
            
            return {
                "success": all(checks.values()),
                "checks": checks
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_end_to_end_flow(self) -> Dict[str, Any]:
        """测试端到端对话流程"""
        try:
            from backend.agents.conversation_state_machine import get_conversation_state_machine
            
            state_machine = get_conversation_state_machine()
            session_id = "test_e2e_session"
            user_id = "test_e2e_user"
            
            # 重置会话
            await state_machine.reset_conversation(session_id, user_id)
            
            # 完整对话流程
            conversation_flow = [
                "你好",
                "我想做一个电商网站",
                "确认"
            ]
            
            flow_results = []
            for message in conversation_flow:
                result = await state_machine.process_message(message, session_id, user_id)
                current_state = await state_machine.get_current_state(session_id, user_id)
                
                flow_results.append({
                    "message": message,
                    "success": result.success,
                    "state": current_state.value
                })
            
            success_count = sum(1 for r in flow_results if r["success"])
            
            return {
                "success": success_count == len(conversation_flow),
                "flow_results": flow_results,
                "success_rate": success_count / len(conversation_flow)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_performance(self) -> Dict[str, Any]:
        """测试性能基准"""
        try:
            from backend.agents.simplified_decision_engine import get_simplified_decision_engine
            from backend.agents.unified_state_manager import ConversationState
            
            engine = get_simplified_decision_engine()
            
            # 决策性能测试
            start_time = time.time()
            for _ in range(1000):
                engine.make_decision("greeting", ConversationState.IDLE)
            decision_time = time.time() - start_time
            
            avg_decision_time = (decision_time / 1000) * 1000  # ms
            
            benchmarks = {
                "avg_decision_time_ms": avg_decision_time,
                "meets_target": avg_decision_time < 1.0  # <1ms
            }
            
            return {
                "success": benchmarks["meets_target"],
                "benchmarks": benchmarks
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}


async def main():
    """主函数"""
    tester = SimplifiedFunctionTest()
    results = await tester.run_all_tests()
    
    print(f"\n📋 测试总结:")
    print(f"   总测试数: {results['total_tests']}")
    print(f"   通过测试: {results['passed_tests']}")
    print(f"   成功率: {results['success_rate']:.1%}")
    
    if results['success_rate'] >= 0.8:
        print(f"\n🎉 测试结果优秀！系统功能正常。")
    elif results['success_rate'] >= 0.6:
        print(f"\n✅ 测试结果良好，核心功能正常。")
    else:
        print(f"\n⚠️  测试结果需要改进。")


if __name__ == "__main__":
    asyncio.run(main())
