#!/usr/bin/env python3
"""
知识库文档检查脚本 - 检查知识库中的文档是否已被向量化
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from backend.agents.factory import agent_factory


async def check_knowledge_base_documents():
    """检查知识库中的文档"""
    print("📚 知识库文档检查")
    print("=" * 60)
    
    try:
        # 获取知识库管理器
        kb_manager = agent_factory.get_knowledge_base_manager()
        
        if not kb_manager:
            print("❌ 知识库管理器不可用")
            return
        
        # 健康检查
        print("\n🏥 知识库健康检查:")
        health_info = await kb_manager.health_check()
        
        print("📋 健康状态:")
        for key, value in health_info.items():
            status = "✅" if value else "❌"
            print(f"   {key}: {status} {value}")
        
        # 获取统计信息
        print("\n📊 知识库统计信息:")
        stats = kb_manager.get_statistics()
        
        if "error" in stats:
            print(f"❌ 获取统计信息失败: {stats['error']}")
            return
        
        print("📈 基本统计:")
        print(f"   总文档数: {stats.get('total_documents', 0)}")
        print(f"   总块数: {stats.get('total_chunks', 0)}")
        print(f"   集合块数: {stats.get('collection_chunks', 0)}")
        print(f"   ChromaDB可用: {'✅' if stats.get('chroma_available') else '❌'}")
        
        # 角色分布
        role_dist = stats.get('role_distribution', {})
        if role_dist:
            print(f"\n👥 角色分布:")
            for role, count in role_dist.items():
                print(f"   {role}: {count} 个文档")
        
        # 类别分布
        category_dist = stats.get('category_distribution', {})
        if category_dist:
            print(f"\n📂 类别分布:")
            for category, count in category_dist.items():
                print(f"   {category}: {count} 个文档")
        
        # 列出所有文档
        all_docs = kb_manager.list_documents()
        print(f"\n📋 总文档数: {len(all_docs)}")
        
        if all_docs:
            print("\n📄 文档列表 (前10个):")
            for doc in all_docs[:10]:
                print(f"   📝 {doc.title}")
                print(f"      ID: {doc.doc_id}")
                print(f"      角色: {doc.role}")
                print(f"      类别: {doc.category}")
                print(f"      块数: {doc.chunk_count}")
                print(f"      更新时间: {doc.updated_at.strftime('%Y-%m-%d %H:%M:%S')}")
                print()
        
        # 按角色过滤
        company_docs = kb_manager.list_documents(role="company")
        developer_docs = kb_manager.list_documents(role="developer")
        
        print(f"🏢 雇主文档: {len(company_docs)} 个")
        print(f"👨‍💻 开发者文档: {len(developer_docs)} 个")
        
        # 检查是否包含由己帮助目录的文档
        print("\n🔍 检查 '由己帮助' 目录文档:")
        
        # 获取由己帮助目录下的文件列表
        help_dir = os.path.join(os.getcwd(), 'docs', 'archive', 'historical-designs', '由己帮助')
        
        if not os.path.exists(help_dir):
            print(f"❌ 目录不存在: {help_dir}")
            return
        
        # 检查公司文档
        company_dir = os.path.join(help_dir, 'company')
        if os.path.exists(company_dir):
            company_files = [f for f in os.listdir(company_dir) if f.endswith('.md')]
            print(f"   公司文档目录: {len(company_files)} 个文件")
            
            # 检查这些文件是否在知识库中
            found_count = 0
            for doc in company_docs:
                if any(file in doc.source_path for file in company_files):
                    found_count += 1
            
            print(f"   在知识库中找到: {found_count}/{len(company_files)} 个公司文档")
        
        # 检查开发者文档
        developer_dir = os.path.join(help_dir, 'developer')
        if os.path.exists(developer_dir):
            developer_files = [f for f in os.listdir(developer_dir) if f.endswith('.md')]
            print(f"   开发者文档目录: {len(developer_files)} 个文件")
            
            # 检查这些文件是否在知识库中
            found_count = 0
            for doc in developer_docs:
                if any(file in doc.source_path for file in developer_files):
                    found_count += 1
            
            print(f"   在知识库中找到: {found_count}/{len(developer_files)} 个开发者文档")
        
        print("\n✅ 知识库文档检查完成")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")


if __name__ == "__main__":
    asyncio.run(check_knowledge_base_documents())