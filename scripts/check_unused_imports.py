#!/usr/bin/env python3
"""
未使用导入检查工具
检查Python文件中未使用的导入语句
"""

import os
import ast
import sys
from pathlib import Path
from typing import List, Dict, Set, Tuple

class UnusedImportChecker:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.backend_path = self.project_root / "backend"
        self.issues = []
        
    def extract_imports_and_usage(self, file_path: Path) -> Tuple[Set[str], Set[str]]:
        """提取文件中的导入和使用情况"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            imports = set()
            used_names = set()
            
            # 提取导入
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        name = alias.asname if alias.asname else alias.name
                        imports.add(name)
                elif isinstance(node, ast.ImportFrom):
                    for alias in node.names:
                        if alias.name == '*':
                            # 通配符导入，跳过检查
                            continue
                        name = alias.asname if alias.asname else alias.name
                        imports.add(name)
            
            # 提取使用的名称
            for node in ast.walk(tree):
                if isinstance(node, ast.Name):
                    used_names.add(node.id)
                elif isinstance(node, ast.Attribute):
                    # 处理 module.function 形式的调用
                    if isinstance(node.value, ast.Name):
                        used_names.add(node.value.id)
            
            return imports, used_names
            
        except Exception as e:
            print(f"警告: 无法解析文件 {file_path}: {e}")
            return set(), set()
    
    def check_file(self, file_path: Path) -> List[Dict]:
        """检查单个文件的未使用导入"""
        imports, used_names = self.extract_imports_and_usage(file_path)
        
        unused_imports = []
        for import_name in imports:
            # 检查导入是否被使用
            if import_name not in used_names:
                # 特殊情况处理
                if self.is_special_import(import_name, file_path):
                    continue
                    
                unused_imports.append({
                    'file': str(file_path.relative_to(self.project_root)),
                    'import': import_name,
                    'type': 'unused_import'
                })
        
        return unused_imports
    
    def is_special_import(self, import_name: str, file_path: Path) -> bool:
        """检查是否是特殊导入（可能被间接使用）"""
        # __init__.py 文件中的导入可能是为了暴露API
        if file_path.name == '__init__.py':
            return True
            
        # 某些常见的工具导入
        special_imports = {
            'logging', 'logger', 'sys', 'os', 'json', 'yaml',
            'datetime', 'time', 'uuid', 'hashlib', 'base64'
        }
        
        if import_name in special_imports:
            # 进一步检查是否真的未使用
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 检查是否在字符串中使用（如日志消息）
                if f'"{import_name}"' in content or f"'{import_name}'" in content:
                    return True
                    
                # 检查是否在注释中提到
                for line in content.split('\n'):
                    if line.strip().startswith('#') and import_name in line:
                        return True
                        
            except Exception:
                pass
        
        return False
    
    def check_all_files(self) -> List[Dict]:
        """检查所有Python文件"""
        all_issues = []
        
        for py_file in self.backend_path.rglob("*.py"):
            if py_file.name.startswith('.'):
                continue
                
            issues = self.check_file(py_file)
            all_issues.extend(issues)
        
        return all_issues
    
    def generate_report(self, issues: List[Dict]) -> str:
        """生成检查报告"""
        if not issues:
            return "✅ 未发现未使用的导入\n"
        
        report = [f"# 未使用导入检查报告\n"]
        report.append(f"⚠️ 发现 {len(issues)} 个未使用的导入\n")
        
        # 按文件分组
        files_issues = {}
        for issue in issues:
            file_path = issue['file']
            if file_path not in files_issues:
                files_issues[file_path] = []
            files_issues[file_path].append(issue['import'])
        
        for file_path, imports in sorted(files_issues.items()):
            report.append(f"## {file_path}\n")
            for import_name in sorted(imports):
                report.append(f"- `{import_name}`")
            report.append("")
        
        return "\n".join(report)
    
    def fix_unused_imports(self, issues: List[Dict]) -> int:
        """自动修复未使用的导入"""
        fixed_count = 0
        
        # 按文件分组
        files_issues = {}
        for issue in issues:
            file_path = issue['file']
            if file_path not in files_issues:
                files_issues[file_path] = []
            files_issues[file_path].append(issue['import'])
        
        for file_path, unused_imports in files_issues.items():
            full_path = self.project_root / file_path
            
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                new_lines = []
                for line in lines:
                    # 检查是否是要删除的导入行
                    should_remove = False
                    for unused_import in unused_imports:
                        if (f"import {unused_import}" in line or 
                            f"from {unused_import}" in line or
                            f", {unused_import}" in line or
                            f"{unused_import}," in line):
                            should_remove = True
                            break
                    
                    if not should_remove:
                        new_lines.append(line)
                    else:
                        fixed_count += 1
                
                # 写回文件
                with open(full_path, 'w', encoding='utf-8') as f:
                    f.writelines(new_lines)
                    
                print(f"✅ 修复文件: {file_path}")
                
            except Exception as e:
                print(f"❌ 修复文件失败 {file_path}: {e}")
        
        return fixed_count

def main():
    """主函数"""
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    checker = UnusedImportChecker(project_root)
    
    print("🔍 开始检查未使用的导入...")
    
    # 检查所有文件
    issues = checker.check_all_files()
    
    # 生成报告
    report = checker.generate_report(issues)
    
    # 保存报告
    report_file = os.path.join(project_root, "unused_imports_report.md")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📊 未使用导入检查完成，报告已保存到: {report_file}")
    
    # 输出结果
    if issues:
        print(f"⚠️ 发现 {len(issues)} 个未使用的导入")
        
        # 询问是否自动修复
        if '--fix' in sys.argv:
            print("🔧 开始自动修复...")
            fixed_count = checker.fix_unused_imports(issues)
            print(f"✅ 已修复 {fixed_count} 个未使用的导入")
        else:
            print("💡 使用 --fix 参数可以自动修复这些问题")
            
        if '--check-only' in sys.argv:
            sys.exit(1)
    else:
        print("✅ 未发现未使用的导入")

if __name__ == "__main__":
    main()
