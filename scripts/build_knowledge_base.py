import chromadb
import os
import re
import logging
from typing import List, Dict, Any, Tuple
from chromadb.utils import embedding_functions

# 导入配置管理器
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from backend.config.knowledge_base_config import get_knowledge_base_config_manager

# 定义知识库存储路径
CHROMA_DB_PATH = os.path.join(os.getcwd(), 'backend', 'data', 'chroma_db')
# 定义知识库文档的根目录
KNOWLEDGE_BASE_ROOTS = [
    os.path.join(os.getcwd(), 'docs', 'archive', 'historical-designs', '由己帮助'),
    os.path.join(os.getcwd(), 'backend', 'data', 'knowledge_base')
]

class DocumentChunker:
    """文档分块器 - 实现递归字符分块和语义分块策略"""
    
    def __init__(self, chunk_size: int = 800, chunk_overlap: int = 100, max_chunks_per_doc: int = 50):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.max_chunks_per_doc = max_chunks_per_doc
        
        # 分隔符优先级（从高到低）
        self.separators = [
            "\n\n",  # 段落分隔
            "\n",    # 行分隔
            "。",    # 中文句号
            "！",    # 中文感叹号
            "？",    # 中文问号
            "；",    # 中文分号
            "，",    # 中文逗号
            ".",     # 英文句号
            "!",     # 英文感叹号
            "?",     # 英文问号
            ";",     # 英文分号
            ",",     # 英文逗号
            " ",     # 空格
            ""       # 字符级别分割
        ]
    
    def chunk_document(self, content: str, metadata: Dict[str, Any]) -> List[Tuple[str, Dict[str, Any]]]:
        """
        对文档进行分块处理
        
        Args:
            content: 文档内容
            metadata: 文档元数据
            
        Returns:
            List[Tuple[str, Dict[str, Any]]]: 分块后的内容和元数据列表
        """
        if not content or not content.strip():
            return []
        
        # 预处理：清理内容
        content = self._preprocess_content(content)
        
        # 如果内容小于块大小，直接返回
        if len(content) <= self.chunk_size:
            chunk_metadata = metadata.copy()
            chunk_metadata.update({
                "chunk_index": 0,
                "total_chunks": 1,
                "chunk_size": len(content)
            })
            return [(content, chunk_metadata)]
        
        # 递归分块
        chunks = self._recursive_split(content)
        
        # 限制块数量
        if len(chunks) > self.max_chunks_per_doc:
            chunks = chunks[:self.max_chunks_per_doc]
        
        # 添加元数据
        result = []
        for i, chunk in enumerate(chunks):
            chunk_metadata = metadata.copy()
            chunk_metadata.update({
                "chunk_index": i,
                "total_chunks": len(chunks),
                "chunk_size": len(chunk)
            })
            result.append((chunk, chunk_metadata))
        
        return result
    
    def _preprocess_content(self, content: str) -> str:
        """预处理文档内容"""
        # 移除多余的空白字符
        content = re.sub(r'\n\s*\n', '\n\n', content)
        content = re.sub(r' +', ' ', content)
        
        # 移除Markdown标记（保留内容）
        content = re.sub(r'^#+\s*', '', content, flags=re.MULTILINE)  # 标题
        content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)  # 粗体
        content = re.sub(r'\*(.*?)\*', r'\1', content)  # 斜体
        content = re.sub(r'`(.*?)`', r'\1', content)  # 行内代码
        
        return content.strip()
    
    def _recursive_split(self, text: str) -> List[str]:
        """递归分割文本"""
        if len(text) <= self.chunk_size:
            return [text] if text.strip() else []
        
        # 尝试使用不同的分隔符
        for separator in self.separators:
            if separator in text:
                chunks = self._split_by_separator(text, separator)
                if chunks:
                    return chunks
        
        # 如果所有分隔符都失败，强制按字符分割
        return self._force_split(text)
    
    def _split_by_separator(self, text: str, separator: str) -> List[str]:
        """使用指定分隔符分割文本"""
        if not separator:
            return self._force_split(text)
        
        parts = text.split(separator)
        chunks = []
        current_chunk = ""
        
        for part in parts:
            # 如果当前部分加上分隔符后仍然小于块大小
            test_chunk = current_chunk + separator + part if current_chunk else part
            
            if len(test_chunk) <= self.chunk_size:
                current_chunk = test_chunk
            else:
                # 保存当前块
                if current_chunk:
                    chunks.append(current_chunk)
                
                # 如果单个部分太大，递归分割
                if len(part) > self.chunk_size:
                    sub_chunks = self._recursive_split(part)
                    chunks.extend(sub_chunks)
                    current_chunk = ""
                else:
                    current_chunk = part
        
        # 添加最后一个块
        if current_chunk:
            chunks.append(current_chunk)
        
        # 处理重叠
        return self._add_overlap(chunks)
    
    def _force_split(self, text: str) -> List[str]:
        """强制按字符分割"""
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + self.chunk_size
            chunk = text[start:end]
            chunks.append(chunk)
            start = end - self.chunk_overlap
        
        return chunks
    
    def _add_overlap(self, chunks: List[str]) -> List[str]:
        """为块添加重叠"""
        if len(chunks) <= 1 or self.chunk_overlap <= 0:
            return chunks
        
        overlapped_chunks = [chunks[0]]
        
        for i in range(1, len(chunks)):
            prev_chunk = chunks[i-1]
            current_chunk = chunks[i]
            
            # 从前一个块的末尾取重叠部分
            if len(prev_chunk) > self.chunk_overlap:
                overlap = prev_chunk[-self.chunk_overlap:]
                overlapped_chunk = overlap + current_chunk
            else:
                overlapped_chunk = current_chunk
            
            overlapped_chunks.append(overlapped_chunk)
        
        return overlapped_chunks


class KnowledgeBaseBuilder:
    """知识库构建器 - 集成配置管理和健康检查"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config_manager = get_knowledge_base_config_manager()
        self.config = self.config_manager.get_config()
        
        # 从配置获取参数
        self.chroma_db_path = self.config.chroma_db.get('path', CHROMA_DB_PATH)
        self.collection_name = self.config.chroma_db.get('collection_name', 'hybrid_knowledge_base')
        self.embedding_model = self.config.chroma_db.get('embedding_model', 'moka-ai/m3e-base')
        
        # 初始化文档分块器
        doc_config = self.config.document_processing
        self.chunker = DocumentChunker(
            chunk_size=doc_config.get('chunk_size', 800),
            chunk_overlap=doc_config.get('chunk_overlap', 100),
            max_chunks_per_doc=doc_config.get('max_chunks_per_doc', 50)
        )
        
        self.supported_formats = doc_config.get('supported_formats', ['md', 'txt'])
    
    def build_knowledge_base(self, rebuild: bool = True) -> bool:
        """
        构建知识库
        
        Args:
            rebuild: 是否重建（删除现有集合）
            
        Returns:
            bool: 构建是否成功
        """
        try:
            print("=== 知识库构建开始 ===")
            
            # 验证配置
            if not self._validate_config():
                return False
            
            # 初始化ChromaDB客户端
            print(f"初始化ChromaDB客户端: {self.chroma_db_path}")
            client = chromadb.PersistentClient(path=self.chroma_db_path)
            
            # 处理现有集合
            if rebuild:
                try:
                    print(f"删除现有集合: {self.collection_name}")
                    client.delete_collection(name=self.collection_name)
                    print("现有集合已删除")
                except Exception as e:
                    print(f"删除集合时出错（可能不存在）: {e}")
            
            # 创建嵌入函数
            print(f"设置嵌入模型: {self.embedding_model}")
            embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
                model_name=self.embedding_model
            )
            
            # 创建或获取集合
            print(f"创建集合: {self.collection_name}")
            collection = client.get_or_create_collection(
                name=self.collection_name,
                embedding_function=embedding_function,
                metadata={"hnsw:space": "cosine"} # 指定使用余弦距离
            )
            
            # 处理文档
            documents, metadatas, ids = self._process_documents()
            
            if not documents:
                print("警告: 没有找到要处理的文档")
                return False
            
            # 添加到集合
            print(f"添加 {len(documents)} 个文档块到知识库...")
            collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            
            print("文档添加成功")
            
            # 健康检查
            if self._health_check(collection):
                print("=== 知识库构建完成 ===")
                return True
            else:
                print("=== 知识库构建失败（健康检查未通过）===")
                return False
                
        except Exception as e:
            self.logger.error(f"知识库构建失败: {e}")
            print(f"错误: {e}")
            return False
    
    def _validate_config(self) -> bool:
        """验证配置"""
        print("验证配置...")

        validation_result = self.config_manager.validate_config()
        if not validation_result['valid']:
            print("配置验证失败:")
            for error in validation_result['errors']:
                print(f"  - {error}")
            return False

        if validation_result['warnings']:
            print("配置警告:")
            for warning in validation_result['warnings']:
                print(f"  - {warning}")

        # 检查文档根目录
        for root in KNOWLEDGE_BASE_ROOTS:
            if not os.path.exists(root):
                print(f"错误: 知识库文档目录不存在: {root}")
                return False

        print("配置验证通过")
        return True
    
    def _process_documents(self) -> Tuple[List[str], List[Dict[str, Any]], List[str]]:
        """处理文档，返回分块后的内容、元数据和ID"""
        documents = []
        metadatas = []
        ids = []
        
        print(f"处理文档目录: {KNOWLEDGE_BASE_ROOTS}")
        
        chunk_counter = 0
        doc_counter = 0
        
        for knowledge_base_root in KNOWLEDGE_BASE_ROOTS:
            # 遍历角色目录
            for role_dir in os.listdir(knowledge_base_root):
                role_path = os.path.join(knowledge_base_root, role_dir)
                
                if not os.path.isdir(role_path):
                    continue
            
            print(f"处理角色目录: {role_dir}")
            
            # 遍历文档文件
            for filename in os.listdir(role_path):
                file_ext = filename.split('.')[-1].lower()
                
                if file_ext not in self.supported_formats:
                    continue
                
                filepath = os.path.join(role_path, filename)
                
                try:
                    # 读取文档内容
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if not content.strip():
                        print(f"  跳过空文档: {filename}")
                        continue
                    
                    # 创建基础元数据
                    base_metadata = {
                        "role": role_dir,
                        "source_file": filename,
                        "file_path": filepath,
                        "file_size": len(content),
                        "doc_id": doc_counter
                    }
                    
                    # 分块处理
                    chunks = self.chunker.chunk_document(content, base_metadata)
                    
                    print(f"  处理文档: {filename} -> {len(chunks)} 个块")
                    
                    # 添加到结果列表
                    for chunk_content, chunk_metadata in chunks:
                        documents.append(chunk_content)
                        metadatas.append(chunk_metadata)
                        ids.append(f"chunk_{role_dir}_{doc_counter}_{chunk_metadata['chunk_index']}")
                        chunk_counter += 1
                    
                    doc_counter += 1
                    
                except Exception as e:
                    print(f"  错误处理文档 {filename}: {e}")
                    continue
        
        print(f"文档处理完成: {doc_counter} 个文档, {chunk_counter} 个块")
        return documents, metadatas, ids
    
    def _health_check(self, collection) -> bool:
        """健康检查"""
        print("\n=== 执行健康检查 ===")
        
        try:
            # 检查集合统计
            count = collection.count()
            print(f"集合中的文档数量: {count}")
            
            if count == 0:
                print("错误: 集合为空")
                return False
            
            # 测试查询
            test_queries = [
                "如何注册账号？",
                "雇主如何发布任务？",
                "自由职业者如何提现？"
            ]
            
            for query in test_queries:
                print(f"\n测试查询: '{query}'")
                
                try:
                    results = collection.query(
                        query_texts=[query],
                        n_results=2
                    )
                    
                    if results['documents'][0]:
                        print(f"  找到 {len(results['documents'][0])} 个相关文档")
                        for i, (doc, metadata, distance) in enumerate(zip(
                            results['documents'][0],
                            results['metadatas'][0],
                            results['distances'][0]
                        )):
                            print(f"    结果 {i+1}: {metadata.get('source_file', 'unknown')} "
                                  f"(角色: {metadata.get('role', 'unknown')}, "
                                  f"相似度: {1-distance:.3f})")
                    else:
                        print("  未找到相关文档")
                        
                except Exception as e:
                    print(f"  查询失败: {e}")
                    return False
            
            # 测试角色过滤
            print(f"\n测试角色过滤查询...")
            try:
                results = collection.query(
                    query_texts=["注册"],
                    n_results=2,
                    where={"role": {"$eq": "company"}}
                )
                print(f"  公司角色文档数量: {len(results['documents'][0])}")
                
                results = collection.query(
                    query_texts=["注册"],
                    n_results=2,
                    where={"role": {"$eq": "developer"}}
                )
                print(f"  开发者角色文档数量: {len(results['documents'][0])}")
                
            except Exception as e:
                print(f"  角色过滤查询失败: {e}")
                return False
            
            print("健康检查通过")
            return True
            
        except Exception as e:
            print(f"健康检查失败: {e}")
            return False


def build_knowledge_base():
    """主函数 - 保持向后兼容"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    builder = KnowledgeBaseBuilder()
    success = builder.build_knowledge_base(rebuild=True)
    
    if success:
        print("\n知识库构建成功！")
    else:
        print("\n知识库构建失败！")
        exit(1)


if __name__ == "__main__":
    build_knowledge_base()