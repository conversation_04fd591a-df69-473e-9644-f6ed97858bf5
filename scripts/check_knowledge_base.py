import chromadb
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 定义知识库存储路径
CHROMA_DB_PATH = os.path.join(os.getcwd(), 'backend', 'data', 'chroma_db')
COLLECTION_NAME = 'hybrid_knowledge_base'

def check_knowledge_base():
    """检查知识库状态"""
    print(f"=== 知识库状态检查 ===")
    print(f"ChromaDB 路径: {CHROMA_DB_PATH}")
    
    try:
        # 初始化ChromaDB客户端
        client = chromadb.PersistentClient(path=CHROMA_DB_PATH)
        
        # 获取所有集合
        collections = client.list_collections()
        print(f"发现 {len(collections)} 个集合:")
        for coll in collections:
            print(f"  - {coll.name}")
        
        # 检查目标集合
        try:
            collection = client.get_collection(name=COLLECTION_NAME)
            print(f"\n集合 '{COLLECTION_NAME}' 信息:")
            count = collection.count()
            print(f"  文档数量: {count}")
            
            # 获取角色统计
            print("\n按角色统计文档:")
            try:
                # 获取公司角色文档
                company_results = collection.query(
                    query_texts=[""],
                    n_results=1,
                    where={"role": {"$eq": "company"}}
                )
                company_count = len(company_results['ids'][0]) if company_results['ids'][0] else 0
                print(f"  公司(company)角色文档: {company_count}")
                
                # 获取开发者角色文档
                developer_results = collection.query(
                    query_texts=[""],
                    n_results=1,
                    where={"role": {"$eq": "developer"}}
                )
                developer_count = len(developer_results['ids'][0]) if developer_results['ids'][0] else 0
                print(f"  开发者(developer)角色文档: {developer_count}")
                
            except Exception as e:
                print(f"  获取角色统计失败: {e}")
            
            # 测试查询
            test_query = "如何注册账号"
            print(f"\n测试查询: '{test_query}'")
            results = collection.query(
                query_texts=[test_query],
                n_results=3
            )
            
            if results['documents'][0]:
                print(f"找到 {len(results['documents'][0])} 个相关文档:")
                for i, (doc, metadata, distance) in enumerate(zip(
                    results['documents'][0],
                    results['metadatas'][0],
                    results['distances'][0]
                )):
                    print(f"\n结果 {i+1}:")
                    print(f"  文件: {metadata.get('source_file', 'unknown')}")
                    print(f"  角色: {metadata.get('role', 'unknown')}")
                    print(f"  相似度: {1-distance:.3f}")
                    print(f"  内容片段: {doc[:100]}...")
            else:
                print("未找到相关文档")
                
        except Exception as e:
            print(f"获取集合 '{COLLECTION_NAME}' 失败: {e}")
            print("知识库可能尚未构建，请运行 scripts/build_knowledge_base.py")
            
    except Exception as e:
        print(f"连接到ChromaDB失败: {e}")
        print("请确保ChromaDB已正确安装并且路径存在")

if __name__ == "__main__":
    check_knowledge_base()