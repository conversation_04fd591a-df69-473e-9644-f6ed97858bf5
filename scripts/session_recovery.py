#!/usr/bin/env python3
"""
会话恢复脚本
用于在Augment会话中断后快速恢复项目状态
"""

import json
from pathlib import Path
from datetime import datetime


class SessionRecovery:
    """会话恢复管理器"""
    
    def __init__(self, project_root: str = "/Users/<USER>/由己ai项目/需求采集项目"):
        self.project_root = Path(project_root)
        self.recovery_file = self.project_root / "session_recovery.json"
        self.docs_dir = self.project_root / "docs" / "需求采集状态感知优化"
    
    def save_session_state(self, current_task: str, completed_tasks: list, notes: str = ""):
        """保存当前会话状态"""
        state = {
            "timestamp": datetime.now().isoformat(),
            "project_root": str(self.project_root),
            "current_task": current_task,
            "completed_tasks": completed_tasks,
            "notes": notes,
            "health_score": self._get_latest_health_score(),
            "key_files": {
                "health_checker": "scripts/project_health_checker.py",
                "health_report": "health_report.json",
                "task_docs": "docs/需求采集状态感知优化/",
                "execution_log": "docs/需求采集状态感知优化/07-执行状态记录.md"
            }
        }
        
        with open(self.recovery_file, 'w', encoding='utf-8') as f:
            json.dump(state, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 会话状态已保存到: {self.recovery_file}")
    
    def load_session_state(self):
        """加载会话状态"""
        if not self.recovery_file.exists():
            print("❌ 未找到会话恢复文件")
            return None
        
        with open(self.recovery_file, 'r', encoding='utf-8') as f:
            state = json.load(f)
        
        return state
    
    def print_recovery_info(self):
        """打印恢复信息"""
        state = self.load_session_state()
        if not state:
            print("❌ 无法加载会话状态")
            return
        
        print("🔄 会话恢复信息")
        print("=" * 50)
        print(f"上次保存时间: {state['timestamp']}")
        print(f"项目路径: {state['project_root']}")
        print(f"当前任务: {state['current_task']}")
        print(f"已完成任务数: {len(state['completed_tasks'])}")
        print(f"最新健康度评分: {state.get('health_score', '未知')}")
        
        if state.get('notes'):
            print(f"备注: {state['notes']}")
        
        print("\n📁 关键文件位置:")
        for name, path in state['key_files'].items():
            print(f"  {name}: {path}")
        
        print("\n✅ 已完成的任务:")
        for task in state['completed_tasks']:
            print(f"  - {task}")
    
    def _get_latest_health_score(self):
        """获取最新的健康度评分"""
        health_report = self.project_root / "health_report.json"
        if health_report.exists():
            try:
                with open(health_report, 'r', encoding='utf-8') as f:
                    report = json.load(f)
                return report.get('health_score', 'unknown')
            except:
                return 'unknown'
        return 'unknown'
    
    def generate_recovery_commands(self):
        """生成恢复命令"""
        commands = [
            "# 会话恢复命令",
            f"cd {self.project_root}",
            "",
            "# 1. 检查项目状态",
            "python scripts/project_health_checker.py",
            "",
            "# 2. 查看执行记录",
            "cat docs/需求采集状态感知优化/07-执行状态记录.md",
            "",
            "# 3. 在Augment中查看任务状态",
            "# 使用 view_tasklist 命令",
            "",
            "# 4. 继续执行未完成的任务",
            "# 根据任务列表继续执行"
        ]
        
        recovery_script = self.project_root / "recovery_commands.sh"
        with open(recovery_script, 'w', encoding='utf-8') as f:
            f.write('\n'.join(commands))
        
        print(f"📝 恢复命令已保存到: {recovery_script}")
        return commands
    
    def create_checkpoint(self, checkpoint_name: str):
        """创建检查点"""
        checkpoint_dir = self.project_root / "checkpoints"
        checkpoint_dir.mkdir(exist_ok=True)
        
        checkpoint_file = checkpoint_dir / f"{checkpoint_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 收集当前状态信息
        checkpoint_data = {
            "name": checkpoint_name,
            "timestamp": datetime.now().isoformat(),
            "health_score": self._get_latest_health_score(),
            "files_status": self._get_files_status(),
            "git_status": self._get_git_status()
        }
        
        with open(checkpoint_file, 'w', encoding='utf-8') as f:
            json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
        
        print(f"📍 检查点已创建: {checkpoint_file}")
    
    def _get_files_status(self):
        """获取关键文件状态"""
        key_files = [
            "scripts/project_health_checker.py",
            "health_report.json",
            "docs/需求采集状态感知优化/07-执行状态记录.md"
        ]
        
        status = {}
        for file_path in key_files:
            full_path = self.project_root / file_path
            status[file_path] = {
                "exists": full_path.exists(),
                "size": full_path.stat().st_size if full_path.exists() else 0,
                "modified": datetime.fromtimestamp(full_path.stat().st_mtime).isoformat() if full_path.exists() else None
            }
        
        return status
    
    def _get_git_status(self):
        """获取Git状态（如果有的话）"""
        try:
            import subprocess
            result = subprocess.run(['git', 'status', '--porcelain'], 
                                  capture_output=True, text=True, cwd=self.project_root)
            return {
                "has_changes": bool(result.stdout.strip()),
                "status": result.stdout.strip()
            }
        except:
            return {"has_changes": False, "status": "Git not available"}


def main():
    """主函数"""
    import sys
    
    recovery = SessionRecovery()
    
    if len(sys.argv) < 2:
        print("用法:")
        print("  python session_recovery.py info          # 显示恢复信息")
        print("  python session_recovery.py save <task>   # 保存当前状态")
        print("  python session_recovery.py checkpoint <name>  # 创建检查点")
        print("  python session_recovery.py commands      # 生成恢复命令")
        return
    
    command = sys.argv[1]
    
    if command == "info":
        recovery.print_recovery_info()
    elif command == "save" and len(sys.argv) > 2:
        current_task = sys.argv[2]
        completed = []  # 这里可以从任务管理系统获取
        recovery.save_session_state(current_task, completed)
    elif command == "checkpoint" and len(sys.argv) > 2:
        checkpoint_name = sys.argv[2]
        recovery.create_checkpoint(checkpoint_name)
    elif command == "commands":
        recovery.generate_recovery_commands()
    else:
        print("❌ 无效的命令")


if __name__ == "__main__":
    main()
