#!/usr/bin/env python3
"""
预提交检查脚本
在代码提交前运行质量检查，确保代码符合规范
"""

import os
import sys
import subprocess
from pathlib import Path

class PreCommitChecker:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.scripts_path = self.project_root / "scripts"
        self.passed_checks = []
        self.failed_checks = []
        
    def run_command(self, command: list, description: str) -> bool:
        """运行命令并返回是否成功"""
        print(f"🔍 {description}...")
        
        try:
            result = subprocess.run(
                command,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0:
                print(f"✅ {description} - 通过")
                self.passed_checks.append(description)
                return True
            else:
                print(f"❌ {description} - 失败")
                if result.stdout:
                    print(f"输出: {result.stdout}")
                if result.stderr:
                    print(f"错误: {result.stderr}")
                self.failed_checks.append(description)
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {description} - 超时")
            self.failed_checks.append(f"{description} (超时)")
            return False
        except Exception as e:
            print(f"💥 {description} - 异常: {e}")
            self.failed_checks.append(f"{description} (异常)")
            return False
    
    def check_redundant_code(self) -> bool:
        """检查冗余代码"""
        return self.run_command(
            [sys.executable, str(self.scripts_path / "cleanup_redundant_code.py"), "--check-only"],
            "冗余代码检查"
        )
    
    def check_config_consistency(self) -> bool:
        """检查配置一致性"""
        return self.run_command(
            [sys.executable, str(self.scripts_path / "check_config_consistency.py"), "--check-only"],
            "配置一致性检查"
        )
    
    def check_unused_imports(self) -> bool:
        """检查未使用的导入"""
        return self.run_command(
            [sys.executable, str(self.scripts_path / "check_unused_imports.py")],
            "未使用导入检查"
        )
    
    def check_code_style(self) -> bool:
        """检查代码风格"""
        # 检查Python代码风格
        backend_path = self.project_root / "backend"
        if backend_path.exists():
            return self.run_command(
                ["python", "-m", "flake8", str(backend_path), "--max-line-length=120", "--ignore=E203,W503"],
                "代码风格检查"
            )
        return True
    
    def run_unit_tests(self) -> bool:
        """运行单元测试"""
        tests_path = self.project_root / "tests"
        if tests_path.exists():
            return self.run_command(
                [sys.executable, "-m", "pytest", str(tests_path), "-v", "--tb=short"],
                "单元测试"
            )
        return True
    
    def check_yaml_syntax(self) -> bool:
        """检查YAML文件语法"""
        config_path = self.project_root / "backend" / "config"
        if not config_path.exists():
            return True
            
        yaml_files = list(config_path.rglob("*.yaml")) + list(config_path.rglob("*.yml"))
        
        for yaml_file in yaml_files:
            if not self.run_command(
                [sys.executable, "-c", f"import yaml; yaml.safe_load(open('{yaml_file}'))"],
                f"YAML语法检查: {yaml_file.name}"
            ):
                return False
        
        return True
    
    def run_all_checks(self) -> bool:
        """运行所有检查"""
        print("🚀 开始预提交检查...\n")
        
        checks = [
            ("YAML语法检查", self.check_yaml_syntax),
            ("冗余代码检查", self.check_redundant_code),
            ("配置一致性检查", self.check_config_consistency),
            ("未使用导入检查", self.check_unused_imports),
            ("代码风格检查", self.check_code_style),
            ("单元测试", self.run_unit_tests),
        ]
        
        all_passed = True
        
        for check_name, check_func in checks:
            try:
                if not check_func():
                    all_passed = False
            except Exception as e:
                print(f"💥 {check_name} 执行异常: {e}")
                all_passed = False
            print()  # 空行分隔
        
        return all_passed
    
    def print_summary(self):
        """打印检查摘要"""
        print("=" * 60)
        print("📊 预提交检查摘要")
        print("=" * 60)
        
        if self.passed_checks:
            print(f"✅ 通过的检查 ({len(self.passed_checks)}):")
            for check in self.passed_checks:
                print(f"   - {check}")
            print()
        
        if self.failed_checks:
            print(f"❌ 失败的检查 ({len(self.failed_checks)}):")
            for check in self.failed_checks:
                print(f"   - {check}")
            print()
        
        total_checks = len(self.passed_checks) + len(self.failed_checks)
        success_rate = len(self.passed_checks) / total_checks * 100 if total_checks > 0 else 0
        
        print(f"📈 成功率: {success_rate:.1f}% ({len(self.passed_checks)}/{total_checks})")
        
        if self.failed_checks:
            print("\n🔧 修复建议:")
            print("1. 查看上述失败的检查详情")
            print("2. 修复相关问题")
            print("3. 重新运行检查")
            print("4. 确保所有检查通过后再提交代码")
        else:
            print("\n🎉 所有检查通过！代码可以提交。")

def main():
    """主函数"""
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    checker = PreCommitChecker(project_root)
    
    # 运行所有检查
    all_passed = checker.run_all_checks()
    
    # 打印摘要
    checker.print_summary()
    
    # 根据检查结果退出
    if not all_passed:
        print("\n❌ 预提交检查失败，请修复问题后重试")
        sys.exit(1)
    else:
        print("\n✅ 预提交检查通过")
        sys.exit(0)

if __name__ == "__main__":
    main()
