# Pre-commit hooks 配置
# 用于在代码提交前自动检查硬编码问题

repos:
  # Python代码格式化和检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-merge-conflict
      - id: check-added-large-files
      - id: debug-statements

  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=100]

  # Python导入排序
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black, --line-length=100]

  # Python代码检查
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=100, --ignore=E203,W503]

  # 自定义硬编码检查
  - repo: local
    hooks:
      - id: hardcode-detector
        name: 硬编码检测
        entry: python tools/hardcode_detector.py
        language: system
        files: \.py$
        args: [backend/]
        pass_filenames: false
        verbose: true
        
      - id: config-validation
        name: 配置文件验证
        entry: python tools/config_validator.py
        language: system
        files: ^backend/config/.*\.yaml$
        pass_filenames: false
        verbose: true

  # YAML文件检查
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.32.0
    hooks:
      - id: yamllint
        args: [-d, relaxed]
        files: ^backend/config/.*\.yaml$

  # 安全检查
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, backend/, -f, json, -o, bandit-report.json]
        files: \.py$
