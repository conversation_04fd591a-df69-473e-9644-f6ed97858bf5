#!/usr/bin/env python3
"""
ChromaDB知识库管理仪表板

基于Streamlit的轻量级Web界面，用于管理和可视化ChromaDB向量数据库内容。
提供文档查看、查询测试、元数据管理等功能。
"""

import streamlit as st
import chromadb
from chromadb.utils import embedding_functions
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, List, Any, Optional
import json
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.config.knowledge_base_config import get_knowledge_base_config_manager


class KnowledgeBaseDashboard:
    """知识库管理仪表板"""
    
    def __init__(self):
        """初始化仪表板"""
        st.set_page_config(
            page_title="知识库管理仪表板",
            page_icon="📚",
            layout="wide"
        )
        
        self.config_manager = get_knowledge_base_config_manager()
        self.config = self.config_manager.get_config()
        
        # 初始化ChromaDB
        self._initialize_chromadb()
        
    def _initialize_chromadb(self):
        """初始化ChromaDB连接"""
        try:
            chroma_path = self.config.chroma_db.get('path', 'backend/data/chroma_db')
            collection_name = self.config.chroma_db.get('collection_name', 'hybrid_knowledge_base')
            embedding_model = self.config.chroma_db.get('embedding_model', 'moka-ai/m3e-base')
            
            # 创建客户端
            self.chroma_client = chromadb.PersistentClient(path=chroma_path)
            
            # 设置嵌入函数
            self.embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
                model_name=embedding_model
            )
            
            # 获取集合
            self.collection = self.chroma_client.get_collection(
                name=collection_name,
                embedding_function=self.embedding_function
            )
            
            st.success("✅ 成功连接到ChromaDB")
            
        except Exception as e:
            st.error(f"❌ 连接ChromaDB失败: {e}")
            self.collection = None
    
    def run(self):
        """运行仪表板"""
        st.title("📚 知识库管理仪表板")
        st.markdown("---")
        
        if not self.collection:
            st.error("无法连接到ChromaDB，请检查配置和数据库状态")
            return
        
        # 侧边栏
        with st.sidebar:
            st.header("导航")
            page = st.selectbox(
                "选择功能",
                ["概览", "文档浏览", "查询测试", "统计分析", "系统状态"]
            )
            
            st.header("配置信息")
            st.json({
                "数据库路径": self.config.chroma_db.get('path'),
                "集合名称": self.config.chroma_db.get('collection_name'),
                "嵌入模型": self.config.chroma_db.get('embedding_model')
            })
        
        # 主内容区域
        if page == "概览":
            self.show_overview()
        elif page == "文档浏览":
            self.show_document_browser()
        elif page == "查询测试":
            self.show_query_tester()
        elif page == "统计分析":
            self.show_statistics()
        elif page == "系统状态":
            self.show_system_status()
    
    def show_overview(self):
        """显示概览页面"""
        st.header("📊 知识库概览")
        
        try:
            # 获取基本统计
            total_docs = self.collection.count()
            
            # 获取所有文档
            all_results = self.collection.get(include=["metadatas"])
            
            if all_results and all_results.get('metadatas'):
                # 创建DataFrame
                df = pd.DataFrame(all_results['metadatas'])
                
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("总文档数", total_docs)
                
                with col2:
                    unique_docs = df['doc_id'].nunique() if 'doc_id' in df.columns else "N/A"
                    st.metric("唯一文档数", unique_docs)
                
                with col3:
                    roles = df['role'].value_counts() if 'role' in df.columns else pd.Series()
                    st.metric("角色类型", len(roles))
                
                with col4:
                    categories = df['category'].value_counts() if 'category' in df.columns else pd.Series()
                    st.metric("分类数量", len(categories))
                
                # 显示角色分布
                if not roles.empty:
                    st.subheader("角色分布")
                    fig_roles = px.pie(
                        values=roles.values,
                        names=roles.index,
                        title="按角色分布的文档数量"
                    )
                    st.plotly_chart(fig_roles, use_container_width=True)
                
                # 显示分类分布
                if not categories.empty:
                    st.subheader("分类分布")
                    fig_categories = px.bar(
                        x=categories.index,
                        y=categories.values,
                        title="按分类分布的文档数量",
                        labels={'x': '分类', 'y': '数量'}
                    )
                    st.plotly_chart(fig_categories, use_container_width=True)
                    
        except Exception as e:
            st.error(f"获取概览数据失败: {e}")
    
    def show_document_browser(self):
        """显示文档浏览器"""
        st.header("📖 文档浏览器")
        
        try:
            # 获取所有文档
            all_results = self.collection.get(include=["documents", "metadatas"])
            
            if not all_results or not all_results.get('documents'):
                st.info("知识库中没有文档")
                return
            
            # 创建DataFrame
            df = pd.DataFrame({
                'document': all_results['documents'],
                'metadata': all_results['metadatas'],
                'id': all_results['ids']
            })
            
            # 展开元数据
            metadata_df = pd.json_normalize(df['metadata'].tolist())
            df = pd.concat([df, metadata_df], axis=1)
            
            # 筛选器
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if 'role' in df.columns:
                    selected_role = st.multiselect(
                        "按角色筛选",
                        options=df['role'].unique(),
                        default=[]
                    )
                    if selected_role:
                        df = df[df['role'].isin(selected_role)]
            
            with col2:
                if 'category' in df.columns:
                    selected_category = st.multiselect(
                        "按分类筛选",
                        options=df['category'].unique(),
                        default=[]
                    )
                    if selected_category:
                        df = df[df['category'].isin(selected_category)]
            
            with col3:
                search_term = st.text_input("搜索内容")
                if search_term:
                    df = df[df['document'].str.contains(search_term, case=False, na=False)]
            
            # 显示文档列表
            st.subheader(f"找到 {len(df)} 个文档块")
            
            for idx, row in df.iterrows():
                with st.expander(f"📄 {row.get('title', '未命名文档')} - 块 {row.get('chunk_index', 0)}"):
                    col1, col2 = st.columns([3, 1])
                    
                    with col1:
                        st.text_area(
                            "内容",
                            value=row['document'][:500] + "..." if len(row['document']) > 500 else row['document'],
                            height=150,
                            key=f"content_{idx}"
                        )
                    
                    with col2:
                        st.json({
                            "文档ID": row.get('doc_id', 'N/A'),
                            "角色": row.get('role', 'N/A'),
                            "分类": row.get('category', 'N/A'),
                            "源文件": row.get('source_path', 'N/A'),
                            "块索引": row.get('chunk_index', 'N/A')
                        })
        
        except Exception as e:
            st.error(f"加载文档失败: {e}")
    
    def show_query_tester(self):
        """显示查询测试器"""
        st.header("🔍 查询测试器")
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            query_text = st.text_area(
                "输入查询内容",
                placeholder="例如：如何注册开发者账号？",
                height=100
            )
        
        with col2:
            top_k = st.slider("返回结果数量", 1, 20, 5)
            similarity_threshold = st.slider("相似度阈值", 0.0, 1.0, 0.7)
            role_filter = st.selectbox(
                "角色过滤",
                ["全部", "company", "developer", "general"]
            )
        
        if st.button("执行查询", type="primary") and query_text:
            try:
                # 构建查询参数
                query_params = {
                    "query_texts": [query_text],
                    "n_results": top_k,
                    "include": ["documents", "metadatas", "distances"]
                }
                
                # 添加角色过滤
                if role_filter != "全部":
                    query_params["where"] = {"role": {"$eq": role_filter}}
                
                # 执行查询
                results = self.collection.query(**query_params)
                
                if results['documents'][0]:
                    st.subheader("查询结果")
                    
                    for i, (doc, metadata, distance) in enumerate(zip(
                        results['documents'][0],
                        results['metadatas'][0],
                        results['distances'][0]
                    )):
                        similarity_score = 1 - distance
                        
                        if similarity_score >= similarity_threshold:
                            with st.expander(
                                f"结果 {i+1} - 相似度: {similarity_score:.3f} - {metadata.get('title', '未命名')}"
                            ):
                                col1, col2 = st.columns([3, 1])
                                
                                with col1:
                                    st.text_area(
                                        "内容",
                                        value=doc[:300] + "..." if len(doc) > 300 else doc,
                                        height=100,
                                        key=f"query_result_{i}"
                                    )
                                
                                with col2:
                                    st.json({
                                        "相似度": f"{similarity_score:.3f}",
                                        "角色": metadata.get('role', 'N/A'),
                                        "分类": metadata.get('category', 'N/A'),
                                        "源文件": metadata.get('source_path', 'N/A')
                                    })
                else:
                    st.info("没有找到相关文档")
                    
            except Exception as e:
                st.error(f"查询失败: {e}")
    
    def show_statistics(self):
        """显示统计分析"""
        st.header("📈 统计分析")
        
        try:
            # 获取所有数据
            all_results = self.collection.get(include=["metadatas", "documents"])
            
            if not all_results or not all_results.get('metadatas'):
                st.info("没有数据可供分析")
                return
            
            # 创建DataFrame
            df = pd.DataFrame(all_results['metadatas'])
            content_df = pd.DataFrame({'content': all_results['documents']})
            df = pd.concat([df, content_df], axis=1)
            
            # 内容长度分析
            df['content_length'] = df['content'].str.len()
            
            col1, col2 = st.columns(2)
            
            with col1:
                # 内容长度分布
                fig_length = px.histogram(
                    df,
                    x='content_length',
                    nbins=20,
                    title="文档内容长度分布"
                )
                st.plotly_chart(fig_length, use_container_width=True)
            
            with col2:
                # 角色和分类的交叉分析
                if 'role' in df.columns and 'category' in df.columns:
                    pivot = pd.crosstab(df['role'], df['category'])
                    fig_heatmap = px.imshow(
                        pivot,
                        labels=dict(x="分类", y="角色", color="数量"),
                        title="角色-分类分布热力图"
                    )
                    st.plotly_chart(fig_heatmap, use_container_width=True)
            
            # 详细统计表
            st.subheader("详细统计")
            
            stats = {
                "总文档数": len(df),
                "平均内容长度": f"{df['content_length'].mean():.0f} 字符",
                "最长文档": f"{df['content_length'].max()} 字符",
                "最短文档": f"{df['content_length'].min()} 字符"
            }
            
            if 'role' in df.columns:
                role_stats = df['role'].value_counts()
                for role, count in role_stats.items():
                    stats[f"{role}角色文档"] = count
            
            st.json(stats)
            
        except Exception as e:
            st.error(f"统计分析失败: {e}")
    
    def show_system_status(self):
        """显示系统状态"""
        st.header("🔧 系统状态")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("ChromaDB状态")
            try:
                collection_count = self.collection.count()
                st.success(f"✅ 连接正常")
                st.metric("集合中文档数", collection_count)
                
                # 集合信息
                st.json({
                    "集合名称": self.collection.name,
                    "数据库路径": self.config.chroma_db.get('path'),
                    "嵌入模型": self.config.chroma_db.get('embedding_model')
                })
                
            except Exception as e:
                st.error(f"❌ 连接异常: {e}")
        
        with col2:
            st.subheader("配置检查")
            config_status = {
                "知识库配置": "✅ 正常" if self.config_manager else "❌ 异常",
                "ChromaDB客户端": "✅ 正常" if self.chroma_client else "❌ 异常",
                "嵌入函数": "✅ 正常" if self.embedding_function else "❌ 异常",
                "集合": "✅ 正常" if self.collection else "❌ 异常"
            }
            st.json(config_status)


def main():
    """主函数"""
    dashboard = KnowledgeBaseDashboard()
    dashboard.run()


if __name__ == "__main__":
    main()
