#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
架构一致性检查工具

检查系统中是否存在多种不同的架构模式，识别潜在的架构不一致问题
"""

import os
import ast
import re
from typing import Dict, List, Set, Any
from dataclasses import dataclass
from pathlib import Path


@dataclass
class ArchitecturePattern:
    """架构模式"""
    name: str
    description: str
    files: List[str]
    examples: List[str]


@dataclass
class ArchitectureIssue:
    """架构问题"""
    file_path: str
    line_number: int
    issue_type: str
    pattern: str
    description: str
    suggestion: str


class ArchitectureChecker:
    """架构一致性检查器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.patterns = []
        self.issues = []
        
    def check_architecture_consistency(self) -> Dict[str, Any]:
        """检查架构一致性"""
        print("🔍 开始架构一致性检查...")
        
        # 1. 检查Action处理模式
        action_patterns = self._check_action_handling_patterns()
        
        # 2. 检查路由模式
        routing_patterns = self._check_routing_patterns()
        
        # 3. 检查方法调用模式
        method_call_patterns = self._check_method_call_patterns()
        
        # 4. 检查配置使用模式
        config_patterns = self._check_config_patterns()
        
        # 5. 生成报告
        report = self._generate_report(action_patterns, routing_patterns, method_call_patterns, config_patterns)
        
        return report
    
    def _check_action_handling_patterns(self) -> Dict[str, List[str]]:
        """检查Action处理模式"""
        patterns = {
            "ActionExecutor + Handler": [],
            "直接方法调用": [],
            "映射表路由": [],
            "硬编码if-elif": []
        }
        
        backend_dir = os.path.join(self.project_root, "backend")
        
        for root, dirs, files in os.walk(backend_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # 检查ActionExecutor模式
                        if 'ActionExecutor' in content or 'action_executor' in content:
                            patterns["ActionExecutor + Handler"].append(file_path)
                        
                        # 检查直接方法调用
                        if re.search(r'await\s+self\.handle_\w+\(', content):
                            patterns["直接方法调用"].append(file_path)
                        
                        # 检查映射表路由
                        if 'action_to_method' in content or 'action_router' in content:
                            patterns["映射表路由"].append(file_path)
                        
                        # 检查硬编码if-elif
                        if re.search(r'if\s+.*action.*==.*:\s*\n.*elif\s+.*action.*==', content, re.MULTILINE):
                            patterns["硬编码if-elif"].append(file_path)
                            
                    except Exception as e:
                        print(f"警告：无法处理文件 {file_path}: {e}")
        
        return patterns
    
    def _check_routing_patterns(self) -> Dict[str, List[str]]:
        """检查路由模式"""
        patterns = {
            "Handler注册模式": [],
            "字典映射模式": [],
            "switch-case模式": [],
            "动态导入模式": []
        }
        
        backend_dir = os.path.join(self.project_root, "backend")
        
        for root, dirs, files in os.walk(backend_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # Handler注册模式
                        if 'register_handler' in content or 'ActionHandlerRegistry' in content:
                            patterns["Handler注册模式"].append(file_path)
                        
                        # 字典映射模式
                        if re.search(r'\w+_to_\w+\s*=\s*{', content):
                            patterns["字典映射模式"].append(file_path)
                        
                        # switch-case模式（Python中的if-elif链）
                        if re.search(r'if\s+\w+\s*==.*:\s*\n.*elif\s+\w+\s*==.*:\s*\n.*elif', content, re.MULTILINE):
                            patterns["switch-case模式"].append(file_path)
                        
                        # 动态导入模式
                        if 'importlib' in content and 'import_module' in content:
                            patterns["动态导入模式"].append(file_path)
                            
                    except Exception as e:
                        print(f"警告：无法处理文件 {file_path}: {e}")
        
        return patterns
    
    def _check_method_call_patterns(self) -> Dict[str, List[str]]:
        """检查方法调用模式"""
        patterns = {
            "统一接口调用": [],
            "直接方法调用": [],
            "反射调用": [],
            "回调函数": []
        }
        
        backend_dir = os.path.join(self.project_root, "backend")
        
        for root, dirs, files in os.walk(backend_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # 统一接口调用
                        if re.search(r'await\s+\w+\.handle\(', content):
                            patterns["统一接口调用"].append(file_path)
                        
                        # 直接方法调用
                        if re.search(r'await\s+self\.\w+\(', content):
                            patterns["直接方法调用"].append(file_path)
                        
                        # 反射调用
                        if 'getattr' in content or 'hasattr' in content:
                            patterns["反射调用"].append(file_path)
                        
                        # 回调函数
                        if re.search(r'callback|handler.*\(', content):
                            patterns["回调函数"].append(file_path)
                            
                    except Exception as e:
                        print(f"警告：无法处理文件 {file_path}: {e}")
        
        return patterns
    
    def _check_config_patterns(self) -> Dict[str, List[str]]:
        """检查配置使用模式"""
        patterns = {
            "配置驱动": [],
            "硬编码": [],
            "环境变量": [],
            "混合模式": []
        }
        
        backend_dir = os.path.join(self.project_root, "backend")
        
        for root, dirs, files in os.walk(backend_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        has_config = 'config_manager' in content
                        has_hardcode = re.search(r'["\'][^"\']*["\']', content) and not re.search(r'config_manager', content)
                        has_env = 'os.environ' in content or 'getenv' in content
                        
                        if has_config and not has_hardcode:
                            patterns["配置驱动"].append(file_path)
                        elif has_hardcode and not has_config:
                            patterns["硬编码"].append(file_path)
                        elif has_env:
                            patterns["环境变量"].append(file_path)
                        elif has_config and has_hardcode:
                            patterns["混合模式"].append(file_path)
                            
                    except Exception as e:
                        print(f"警告：无法处理文件 {file_path}: {e}")
        
        return patterns
    
    def _generate_report(self, action_patterns, routing_patterns, method_call_patterns, config_patterns) -> Dict[str, Any]:
        """生成检查报告"""
        report = {
            "summary": {},
            "patterns": {
                "action_handling": action_patterns,
                "routing": routing_patterns,
                "method_calls": method_call_patterns,
                "configuration": config_patterns
            },
            "recommendations": []
        }
        
        # 分析主要架构模式
        main_action_pattern = max(action_patterns.items(), key=lambda x: len(x[1]))
        main_routing_pattern = max(routing_patterns.items(), key=lambda x: len(x[1]))
        
        report["summary"] = {
            "主要Action处理模式": main_action_pattern[0],
            "主要路由模式": main_routing_pattern[0],
            "架构一致性": self._calculate_consistency_score(action_patterns, routing_patterns)
        }
        
        # 生成建议
        report["recommendations"] = self._generate_recommendations(action_patterns, routing_patterns)
        
        return report
    
    def _calculate_consistency_score(self, action_patterns, routing_patterns) -> float:
        """计算架构一致性分数"""
        total_files = sum(len(files) for files in action_patterns.values())
        if total_files == 0:
            return 1.0
        
        main_pattern_files = max(len(files) for files in action_patterns.values())
        return main_pattern_files / total_files
    
    def _generate_recommendations(self, action_patterns, routing_patterns) -> List[str]:
        """生成架构改进建议"""
        recommendations = []
        
        # 检查是否存在多种Action处理模式
        active_patterns = [name for name, files in action_patterns.items() if files]
        if len(active_patterns) > 2:
            recommendations.append(f"发现{len(active_patterns)}种不同的Action处理模式，建议统一使用ActionExecutor + Handler模式")
        
        # 检查映射表使用
        if action_patterns["映射表路由"]:
            recommendations.append("发现映射表路由模式，建议迁移到Handler注册模式以提高可维护性")
        
        # 检查硬编码
        if action_patterns["硬编码if-elif"]:
            recommendations.append("发现硬编码if-elif模式，建议使用配置驱动的Handler模式")
        
        return recommendations


def main():
    """主函数"""
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    checker = ArchitectureChecker(project_root)
    
    report = checker.check_architecture_consistency()
    
    print("\n" + "="*60)
    print("📊 架构一致性检查报告")
    print("="*60)
    
    print(f"\n🎯 架构概览:")
    for key, value in report["summary"].items():
        print(f"  • {key}: {value}")
    
    print(f"\n📋 详细模式分析:")
    for category, patterns in report["patterns"].items():
        print(f"\n  {category.upper()}:")
        for pattern, files in patterns.items():
            if files:
                print(f"    • {pattern}: {len(files)} 个文件")
                for file in files[:3]:  # 只显示前3个文件
                    rel_path = os.path.relpath(file, project_root)
                    print(f"      - {rel_path}")
                if len(files) > 3:
                    print(f"      ... 还有 {len(files) - 3} 个文件")
    
    print(f"\n💡 改进建议:")
    for i, recommendation in enumerate(report["recommendations"], 1):
        print(f"  {i}. {recommendation}")
    
    print("\n" + "="*60)


if __name__ == "__main__":
    main()
