#!/bin/bash
# 知识库管理仪表板启动脚本

echo "🚀 启动知识库管理仪表板..."
echo "📍 项目路径: $(pwd)"
echo ""

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查Streamlit是否安装
if ! python3 -c "import streamlit" &> /dev/null; then
    echo "📦 安装Streamlit..."
    pip install streamlit
fi

# 检查依赖
echo "📦 检查依赖..."
pip install streamlit pandas plotly chromadb

# 启动仪表板
echo "🎯 启动知识库管理仪表板..."
echo "🔗 访问地址: http://localhost:8501"
echo ""

# 切换到项目根目录
cd "$(dirname "$0")/.."

# 启动Streamlit应用
streamlit run tools/knowledge_base_dashboard.py
