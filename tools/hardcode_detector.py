#!/usr/bin/env python3
"""
硬编码检测工具

这个工具用于检测Python代码中的硬编码问题，包括：
1. 硬编码字符串
2. 硬编码数值
3. 硬编码SQL查询
4. 硬编码配置参数

使用方法：
    python tools/hardcode_detector.py [目录路径]
    python tools/hardcode_detector.py backend/agents/
"""

import ast
import os
import re
import sys
import json
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
from pathlib import Path


@dataclass
class HardcodeIssue:
    """硬编码问题数据类"""
    file_path: str
    line_number: int
    column: int
    issue_type: str
    content: str
    severity: str
    suggestion: str


class HardcodeDetector(ast.NodeVisitor):
    """硬编码检测器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.issues: List[HardcodeIssue] = []
        self.source_lines: List[str] = []
        
        # 硬编码检测规则配置
        self.rules = {
            # 字符串硬编码检测规则
            'string_patterns': [
                (r'SELECT\s+.*FROM\s+\w+', '数据库查询硬编码', 'HIGH'),
                (r'INSERT\s+INTO\s+\w+', '数据库插入硬编码', 'HIGH'),
                (r'UPDATE\s+\w+\s+SET', '数据库更新硬编码', 'HIGH'),
                (r'DELETE\s+FROM\s+\w+', '数据库删除硬编码', 'HIGH'),
                (r'抱歉|对不起|很遗憾', '错误消息硬编码', 'MEDIUM'),
                (r'您好|你好|欢迎', '问候消息硬编码', 'MEDIUM'),
                (r'请.*详细.*信息', '澄清请求硬编码', 'MEDIUM'),
                (r'http://|https://', 'URL硬编码', 'MEDIUM'),
                (r'/[a-zA-Z0-9_/]+\.(log|txt|json|yaml)', '文件路径硬编码', 'LOW'),
            ],
            
            # 数值硬编码检测规则
            'number_patterns': [
                (0.7, '阈值硬编码', 'MEDIUM'),
                (0.8, '阈值硬编码', 'MEDIUM'),
                (0.9, '阈值硬编码', 'MEDIUM'),
                (3, '重试次数硬编码', 'MEDIUM'),
                (5, '超时时间硬编码', 'MEDIUM'),
                (10, '限制数量硬编码', 'LOW'),
            ],
            
            # 允许的硬编码例外
            'exceptions': [
                'get_unified_config().get_',  # 配置管理器调用
                'self._get_query(',     # 数据库查询模板调用
                'logger.',              # 日志消息
                '__name__',             # 模块名
                '__file__',             # 文件名
                'test_',                # 测试相关
                'mock_',                # 模拟数据
            ]
        }
    
    def detect_hardcodes(self, source_code: str) -> List[HardcodeIssue]:
        """检测硬编码问题"""
        self.source_lines = source_code.split('\n')
        
        try:
            tree = ast.parse(source_code)
            self.visit(tree)
        except SyntaxError as e:
            self.issues.append(HardcodeIssue(
                file_path=self.file_path,
                line_number=e.lineno or 0,
                column=e.offset or 0,
                issue_type='SYNTAX_ERROR',
                content=str(e),
                severity='HIGH',
                suggestion='修复语法错误'
            ))
        
        return self.issues
    
    def visit_Str(self, node: ast.Str) -> None:
        """检测字符串硬编码"""
        self._check_string_hardcode(node, node.s)
        self.generic_visit(node)
    
    def visit_Constant(self, node: ast.Constant) -> None:
        """检测常量硬编码（Python 3.8+）"""
        if isinstance(node.value, str):
            self._check_string_hardcode(node, node.value)
        elif isinstance(node.value, (int, float)):
            self._check_number_hardcode(node, node.value)
        self.generic_visit(node)
    
    def visit_Num(self, node: ast.Num) -> None:
        """检测数值硬编码（Python < 3.8）"""
        self._check_number_hardcode(node, node.n)
        self.generic_visit(node)
    
    def _check_string_hardcode(self, node: ast.AST, value: str) -> None:
        """检查字符串硬编码"""
        if len(value.strip()) < 3:  # 忽略太短的字符串
            return
        
        # 检查是否在例外列表中
        line_content = self._get_line_content(node.lineno)
        if any(exception in line_content for exception in self.rules['exceptions']):
            return
        
        # 检查字符串模式
        for pattern, issue_type, severity in self.rules['string_patterns']:
            if re.search(pattern, value, re.IGNORECASE):
                suggestion = self._get_string_suggestion(issue_type)
                self.issues.append(HardcodeIssue(
                    file_path=self.file_path,
                    line_number=node.lineno,
                    column=node.col_offset,
                    issue_type=issue_type,
                    content=value[:100] + ('...' if len(value) > 100 else ''),
                    severity=severity,
                    suggestion=suggestion
                ))
                break
    
    def _check_number_hardcode(self, node: ast.AST, value: float) -> None:
        """检查数值硬编码"""
        # 忽略常见的无意义数值
        if value in [0, 1, -1, 2]:
            return
        
        # 检查是否在例外列表中
        line_content = self._get_line_content(node.lineno)
        if any(exception in line_content for exception in self.rules['exceptions']):
            return
        
        # 检查数值模式
        for target_value, issue_type, severity in self.rules['number_patterns']:
            if abs(value - target_value) < 0.001:  # 浮点数比较
                suggestion = self._get_number_suggestion(issue_type, value)
                self.issues.append(HardcodeIssue(
                    file_path=self.file_path,
                    line_number=node.lineno,
                    column=node.col_offset,
                    issue_type=issue_type,
                    content=str(value),
                    severity=severity,
                    suggestion=suggestion
                ))
                break
    
    def _get_line_content(self, line_number: int) -> str:
        """获取指定行的内容"""
        if 1 <= line_number <= len(self.source_lines):
            return self.source_lines[line_number - 1]
        return ""
    
    def _get_string_suggestion(self, issue_type: str) -> str:
        """获取字符串硬编码的修复建议"""
        suggestions = {
            '数据库查询硬编码': '使用 get_unified_config().get_database_query() 或 self._get_query()',
            '错误消息硬编码': '使用 get_unified_config().get_message_template()',
            '问候消息硬编码': '使用 get_unified_config().get_message_template()',
            '澄清请求硬编码': '使用 get_unified_config().get_message_template()',
            'URL硬编码': '使用配置文件或环境变量',
            '文件路径硬编码': '使用配置文件或相对路径',
        }
        return suggestions.get(issue_type, '考虑使用配置文件管理此硬编码')
    
    def _get_number_suggestion(self, issue_type: str, value: float) -> str:
        """获取数值硬编码的修复建议"""
        suggestions = {
            '阈值硬编码': f'使用 get_unified_config().get_threshold("key", {value})',
            '重试次数硬编码': f'使用 get_unified_config().get_business_rule("retry.max_attempts", {value})',
            '超时时间硬编码': f'使用 get_unified_config().get_threshold("timeout", {value})',
            '限制数量硬编码': f'使用 get_unified_config().get_business_rule("limit", {value})',
        }
        return suggestions.get(issue_type, f'考虑使用配置文件管理数值 {value}')


def scan_directory(directory: str) -> List[HardcodeIssue]:
    """扫描目录中的所有Python文件"""
    all_issues = []
    
    for root, dirs, files in os.walk(directory):
        # 跳过一些不需要检查的目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'venv', '.venv']]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        source_code = f.read()
                    
                    detector = HardcodeDetector(file_path)
                    issues = detector.detect_hardcodes(source_code)
                    all_issues.extend(issues)
                    
                except Exception as e:
                    print(f"警告：无法处理文件 {file_path}: {e}")
    
    return all_issues


def generate_report(issues: List[HardcodeIssue], output_format: str = 'text') -> str:
    """生成检测报告"""
    if output_format == 'json':
        return json.dumps([
            {
                'file_path': issue.file_path,
                'line_number': issue.line_number,
                'column': issue.column,
                'issue_type': issue.issue_type,
                'content': issue.content,
                'severity': issue.severity,
                'suggestion': issue.suggestion
            }
            for issue in issues
        ], indent=2, ensure_ascii=False)
    
    # 文本格式报告
    report = []
    report.append("=" * 80)
    report.append("硬编码检测报告")
    report.append("=" * 80)
    
    # 按严重程度分组
    severity_groups = {'HIGH': [], 'MEDIUM': [], 'LOW': []}
    for issue in issues:
        severity_groups[issue.severity].append(issue)
    
    # 统计信息
    report.append(f"\n总计发现 {len(issues)} 个硬编码问题：")
    report.append(f"  高危险: {len(severity_groups['HIGH'])} 个")
    report.append(f"  中危险: {len(severity_groups['MEDIUM'])} 个")
    report.append(f"  低危险: {len(severity_groups['LOW'])} 个")
    
    # 详细问题列表
    for severity in ['HIGH', 'MEDIUM', 'LOW']:
        if severity_groups[severity]:
            report.append(f"\n{severity} 级别问题:")
            report.append("-" * 40)
            
            for issue in severity_groups[severity]:
                report.append(f"\n文件: {issue.file_path}")
                report.append(f"位置: 第{issue.line_number}行, 第{issue.column}列")
                report.append(f"类型: {issue.issue_type}")
                report.append(f"内容: {issue.content}")
                report.append(f"建议: {issue.suggestion}")
    
    return "\n".join(report)


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python tools/hardcode_detector.py <目录路径> [输出格式]")
        print("输出格式: text (默认) 或 json")
        sys.exit(1)
    
    directory = sys.argv[1]
    output_format = sys.argv[2] if len(sys.argv) > 2 else 'text'
    
    if not os.path.exists(directory):
        print(f"错误：目录 {directory} 不存在")
        sys.exit(1)
    
    print(f"正在扫描目录: {directory}")
    issues = scan_directory(directory)
    
    report = generate_report(issues, output_format)
    print(report)
    
    # 保存报告到文件
    report_file = f"hardcode_report.{output_format}"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n报告已保存到: {report_file}")
    
    # 返回适当的退出码
    high_issues = sum(1 for issue in issues if issue.severity == 'HIGH')
    if high_issues > 0:
        print(f"\n警告：发现 {high_issues} 个高危险硬编码问题！")
        sys.exit(1)
    else:
        print("\n✅ 未发现高危险硬编码问题")
        sys.exit(0)


if __name__ == "__main__":
    main()
