#!/usr/bin/env python3
"""
交互式日志查看器
"""

import json
import sys
from pathlib import Path
import argparse
from datetime import datetime
import re

class LogViewer:
    def __init__(self):
        self.logs = []
        self.filtered_logs = []
        
    def load_logs(self, file_path):
        """加载日志文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        try:
                            log_data = json.loads(line.strip())
                            log_data['_line_num'] = line_num
                            self.logs.append(log_data)
                        except json.JSONDecodeError:
                            print(f"警告: 第{line_num}行不是有效的JSON格式")
            
            self.filtered_logs = self.logs.copy()
            print(f"成功加载 {len(self.logs)} 条日志记录")
            
        except Exception as e:
            print(f"加载日志文件失败: {e}")
            return False
        return True
    
    def filter_by_level(self, level):
        """按日志级别过滤"""
        self.filtered_logs = [log for log in self.logs if log.get('level', '').upper() == level.upper()]
        print(f"过滤结果: {len(self.filtered_logs)} 条记录")
    
    def filter_by_session(self, session_id):
        """按会话ID过滤"""
        self.filtered_logs = [log for log in self.logs if session_id in log.get('session_id', '')]
        print(f"过滤结果: {len(self.filtered_logs)} 条记录")
    
    def filter_by_keyword(self, keyword):
        """按关键词过滤"""
        self.filtered_logs = [log for log in self.logs 
                             if keyword.lower() in log.get('message', '').lower()]
        print(f"过滤结果: {len(self.filtered_logs)} 条记录")
    
    def filter_by_time_range(self, start_time, end_time):
        """按时间范围过滤"""
        filtered = []
        for log in self.logs:
            try:
                log_time = datetime.strptime(log.get('timestamp', ''), "%Y-%m-%d %H:%M:%S,%f")
                if start_time <= log_time <= end_time:
                    filtered.append(log)
            except:
                continue
        self.filtered_logs = filtered
        print(f"过滤结果: {len(self.filtered_logs)} 条记录")
    
    def show_business_flow(self):
        """显示业务流程"""
        business_logs = [log for log in self.filtered_logs 
                        if log.get('is_business') or '业务节点' in log.get('message', '')]
        
        print("\n=== 业务流程追踪 ===")
        for log in business_logs:
            timestamp = log.get('timestamp', '').split(',')[0].split(' ')[1]
            message = log.get('message', '')
            session = log.get('session_id', '')[:8] if log.get('session_id') else ''
            print(f"[{timestamp}] {session} | {message}")
    
    def show_errors(self):
        """显示错误日志"""
        error_logs = [log for log in self.filtered_logs 
                     if log.get('level') in ['ERROR', 'CRITICAL']]
        
        print("\n=== 错误日志 ===")
        for log in error_logs:
            timestamp = log.get('timestamp', '').split(',')[0]
            message = log.get('message', '')
            logger = log.get('logger', '')
            print(f"[{timestamp}] {logger} | {message}")
    
    def show_performance(self):
        """显示性能相关日志"""
        perf_logs = [log for log in self.filtered_logs 
                    if 'duration' in log or '耗时' in log.get('message', '') or 'LLM调用' in log.get('message', '')]
        
        print("\n=== 性能统计 ===")
        for log in perf_logs:
            timestamp = log.get('timestamp', '').split(',')[0].split(' ')[1]
            message = log.get('message', '')
            duration = log.get('duration', log.get('generation_duration'))
            if duration:
                print(f"[{timestamp}] {message} (耗时: {duration:.2f}s)")
            else:
                print(f"[{timestamp}] {message}")
    
    def show_summary(self):
        """显示日志摘要"""
        if not self.filtered_logs:
            print("没有日志数据")
            return
            
        # 统计各级别日志数量
        level_counts = {}
        session_counts = {}
        
        for log in self.filtered_logs:
            level = log.get('level', 'UNKNOWN')
            level_counts[level] = level_counts.get(level, 0) + 1
            
            session = log.get('session_id', 'unknown')
            if session != 'system':
                session_counts[session] = session_counts.get(session, 0) + 1
        
        print("\n=== 日志摘要 ===")
        print(f"总记录数: {len(self.filtered_logs)}")
        print("\n日志级别分布:")
        for level, count in sorted(level_counts.items()):
            print(f"  {level}: {count}")
        
        if session_counts:
            print(f"\n活跃会话数: {len(session_counts)}")
            top_sessions = sorted(session_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            print("最活跃会话:")
            for session, count in top_sessions:
                print(f"  {session[:8]}: {count} 条记录")
    
    def interactive_mode(self):
        """交互模式"""
        print("\n=== 交互式日志查看器 ===")
        print("可用命令:")
        print("  level <级别>     - 按级别过滤 (INFO/DEBUG/ERROR等)")
        print("  session <ID>     - 按会话ID过滤")
        print("  keyword <关键词> - 按关键词过滤")
        print("  business         - 显示业务流程")
        print("  errors           - 显示错误日志")
        print("  performance      - 显示性能日志")
        print("  summary          - 显示摘要")
        print("  reset            - 重置过滤器")
        print("  quit             - 退出")
        
        while True:
            try:
                cmd = input("\n> ").strip().split()
                if not cmd:
                    continue
                    
                if cmd[0] == 'quit':
                    break
                elif cmd[0] == 'level' and len(cmd) > 1:
                    self.filter_by_level(cmd[1])
                elif cmd[0] == 'session' and len(cmd) > 1:
                    self.filter_by_session(cmd[1])
                elif cmd[0] == 'keyword' and len(cmd) > 1:
                    self.filter_by_keyword(' '.join(cmd[1:]))
                elif cmd[0] == 'business':
                    self.show_business_flow()
                elif cmd[0] == 'errors':
                    self.show_errors()
                elif cmd[0] == 'performance':
                    self.show_performance()
                elif cmd[0] == 'summary':
                    self.show_summary()
                elif cmd[0] == 'reset':
                    self.filtered_logs = self.logs.copy()
                    print(f"过滤器已重置，当前 {len(self.filtered_logs)} 条记录")
                else:
                    print("未知命令，请重试")
                    
            except KeyboardInterrupt:
                print("\n再见!")
                break
            except Exception as e:
                print(f"命令执行错误: {e}")

def main():
    parser = argparse.ArgumentParser(description='交互式日志查看器')
    parser.add_argument('log_file', help='日志文件路径')
    parser.add_argument('--summary', action='store_true', help='只显示摘要')
    parser.add_argument('--business', action='store_true', help='只显示业务流程')
    parser.add_argument('--errors', action='store_true', help='只显示错误')
    
    args = parser.parse_args()
    
    viewer = LogViewer()
    if not viewer.load_logs(args.log_file):
        return 1
    
    if args.summary:
        viewer.show_summary()
    elif args.business:
        viewer.show_business_flow()
    elif args.errors:
        viewer.show_errors()
    else:
        viewer.interactive_mode()

if __name__ == '__main__':
    main()