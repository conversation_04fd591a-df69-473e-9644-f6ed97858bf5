#!/usr/bin/env python3
"""
配置文件验证工具

这个工具用于验证配置文件的格式和内容是否正确，包括：
1. YAML格式验证
2. 必需字段检查
3. 数据类型验证
4. 配置完整性检查

使用方法：
    python tools/config_validator.py
    python tools/config_validator.py --config-dir backend/config/
"""

import os
import sys
import yaml
import json
import argparse
from typing import Dict, Any, List, Optional
from pathlib import Path


class ConfigValidator:
    """配置文件验证器"""
    
    def __init__(self, config_dir: str = "backend/config"):
        self.config_dir = Path(config_dir)
        self.errors: List[str] = []
        self.warnings: List[str] = []
        
        # 统一配置文件验证规则
        self.validation_rules = {
            'unified_config.yaml': {
                'required_sections': [
                    'system', 'llm', 'conversation', 'keyword_rules',
                    'business_rules', 'message_templates', 'database',
                    'performance', 'security', 'strategies', 'knowledge_base'
                ],
                'data_types': {
                    'system': dict,
                    'llm': dict,
                    'conversation': dict,
                    'keyword_rules': dict,
                    'business_rules': dict,
                    'message_templates': dict,
                    'database': dict,
                    'performance': dict,
                    'security': dict,
                    'strategies': dict,
                    'knowledge_base': dict
                }
            }
        }
    
    def validate_all_configs(self) -> bool:
        """验证所有配置文件"""
        print(f"正在验证配置目录: {self.config_dir}")
        
        if not self.config_dir.exists():
            self.errors.append(f"配置目录不存在: {self.config_dir}")
            return False
        
        success = True
        
        # 验证每个配置文件
        for config_file, rules in self.validation_rules.items():
            file_path = self.config_dir / config_file
            if not self._validate_config_file(file_path, rules):
                success = False
        
        # 验证配置完整性
        if not self._validate_config_integrity():
            success = False
        
        return success
    
    def _validate_config_file(self, file_path: Path, rules: Dict[str, Any]) -> bool:
        """验证单个配置文件"""
        print(f"\n验证文件: {file_path}")
        
        if not file_path.exists():
            self.errors.append(f"配置文件不存在: {file_path}")
            return False
        
        try:
            # 读取和解析YAML文件
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            if config_data is None:
                self.errors.append(f"配置文件为空: {file_path}")
                return False
            
            # 验证必需的节
            success = self._validate_required_sections(file_path, config_data, rules)
            
            # 验证数据类型
            if not self._validate_data_types(file_path, config_data, rules):
                success = False
            
            # 验证特定文件的内容
            if not self._validate_file_specific_content(file_path, config_data):
                success = False
            
            if success:
                print(f"✅ {file_path.name} 验证通过")
            else:
                print(f"❌ {file_path.name} 验证失败")
            
            return success
            
        except yaml.YAMLError as e:
            self.errors.append(f"YAML格式错误 {file_path}: {e}")
            return False
        except Exception as e:
            self.errors.append(f"验证文件时出错 {file_path}: {e}")
            return False
    
    def _validate_required_sections(self, file_path: Path, config_data: Dict[str, Any], rules: Dict[str, Any]) -> bool:
        """验证必需的配置节"""
        required_sections = rules.get('required_sections', [])
        success = True
        
        for section in required_sections:
            if section not in config_data:
                self.errors.append(f"缺少必需的配置节 '{section}' 在文件 {file_path}")
                success = False
            elif not config_data[section]:
                self.warnings.append(f"配置节 '{section}' 为空 在文件 {file_path}")
        
        return success
    
    def _validate_data_types(self, file_path: Path, config_data: Dict[str, Any], rules: Dict[str, Any]) -> bool:
        """验证数据类型"""
        data_types = rules.get('data_types', {})
        success = True
        
        for section, expected_type in data_types.items():
            if section in config_data:
                if not isinstance(config_data[section], expected_type):
                    self.errors.append(
                        f"配置节 '{section}' 类型错误，期望 {expected_type.__name__}，"
                        f"实际 {type(config_data[section]).__name__} 在文件 {file_path}"
                    )
                    success = False
        
        return success
    
    def _validate_file_specific_content(self, file_path: Path, config_data: Dict[str, Any]) -> bool:
        """验证特定文件的内容"""
        file_name = file_path.name
        success = True
        
        if file_name == 'database_queries.yaml':
            success = self._validate_database_queries(file_path, config_data)
        elif file_name == 'message_templates.yaml':
            success = self._validate_message_templates(file_path, config_data)
        
        return success
    

    
    def _validate_database_queries(self, file_path: Path, config_data: Dict[str, Any]) -> bool:
        """验证数据库查询配置"""
        success = True
        
        # 检查SQL查询的基本格式
        sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE']
        
        for section_name, section_data in config_data.items():
            if isinstance(section_data, dict):
                for query_name, query_sql in section_data.items():
                    if isinstance(query_sql, str):
                        # 检查是否包含SQL关键字
                        if not any(keyword in query_sql.upper() for keyword in sql_keywords):
                            self.warnings.append(
                                f"查询 '{section_name}.{query_name}' 可能不是有效的SQL语句 在文件 {file_path}"
                            )
                        
                        # 检查参数占位符
                        if '?' not in query_sql and '{' not in query_sql:
                            self.warnings.append(
                                f"查询 '{section_name}.{query_name}' 没有参数占位符 在文件 {file_path}"
                            )
        
        return success
    
    def _validate_message_templates(self, file_path: Path, config_data: Dict[str, Any]) -> bool:
        """验证消息模板配置"""
        success = True
        
        # 检查模板中的占位符格式
        def check_placeholders(text: str, template_path: str):
            if isinstance(text, str):
                # 检查未闭合的占位符
                open_braces = text.count('{')
                close_braces = text.count('}')
                if open_braces != close_braces:
                    self.warnings.append(
                        f"模板 '{template_path}' 中的占位符可能不匹配 在文件 {file_path}"
                    )
        
        def traverse_templates(data: Any, path: str = ""):
            if isinstance(data, dict):
                for key, value in data.items():
                    new_path = f"{path}.{key}" if path else key
                    traverse_templates(value, new_path)
            elif isinstance(data, str):
                check_placeholders(data, path)
        
        traverse_templates(config_data)
        return success
    
    def _validate_config_integrity(self) -> bool:
        """验证配置完整性"""
        print("\n验证配置完整性...")
        success = True
        
        try:
            # 尝试加载配置管理器
            sys.path.insert(0, str(Path.cwd()))
            from backend.config.unified_config_loader import get_unified_config
            
            # 测试关键配置的访问
            test_configs = [
                ('business_rule', 'priority.document_modification', 7),
                ('threshold', 'extraction.completeness_threshold', 0.7),
                ('message_template', 'error.general.unknown_error', None),
                ('database_query', 'focus_points.check_exists', None),
            ]
            
            for config_type, key, default in test_configs:
                try:
                    if config_type == 'business_rule':
                        value = get_unified_config().get_business_rule(key, default)
                    elif config_type == 'threshold':
                        value = get_unified_config().get_threshold(key, default)
                    elif config_type == 'message_template':
                        value = get_unified_config().get_message_template(key)
                    elif config_type == 'database_query':
                        value = get_unified_config().get_database_query(key)
                    
                    if value is None:
                        self.warnings.append(f"配置 '{key}' 返回 None")
                        
                except Exception as e:
                    self.errors.append(f"访问配置 '{key}' 时出错: {e}")
                    success = False
            
            if success:
                print("✅ 配置完整性验证通过")
            else:
                print("❌ 配置完整性验证失败")
                
        except ImportError as e:
            self.errors.append(f"无法导入配置管理器: {e}")
            success = False
        except Exception as e:
            self.errors.append(f"配置完整性验证时出错: {e}")
            success = False
        
        return success
    
    def _get_nested_value(self, data: Dict[str, Any], key_path: str) -> Any:
        """获取嵌套字典中的值"""
        keys = key_path.split('.')
        current = data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        return current
    
    def print_report(self) -> None:
        """打印验证报告"""
        print("\n" + "=" * 80)
        print("配置验证报告")
        print("=" * 80)
        
        if not self.errors and not self.warnings:
            print("✅ 所有配置文件验证通过，未发现问题")
            return
        
        if self.errors:
            print(f"\n❌ 发现 {len(self.errors)} 个错误:")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")
        
        if self.warnings:
            print(f"\n⚠️  发现 {len(self.warnings)} 个警告:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. {warning}")
        
        print("\n建议:")
        if self.errors:
            print("- 修复所有错误后再提交代码")
        if self.warnings:
            print("- 检查警告项目，确认是否需要调整")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='配置文件验证工具')
    parser.add_argument(
        '--config-dir',
        default='backend/config',
        help='配置文件目录路径 (默认: backend/config)'
    )
    parser.add_argument(
        '--strict',
        action='store_true',
        help='严格模式：警告也视为错误'
    )
    
    args = parser.parse_args()
    
    validator = ConfigValidator(args.config_dir)
    success = validator.validate_all_configs()
    
    validator.print_report()
    
    # 在严格模式下，警告也视为失败
    if args.strict and validator.warnings:
        success = False
    
    if success:
        print("\n✅ 配置验证成功")
        sys.exit(0)
    else:
        print("\n❌ 配置验证失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
