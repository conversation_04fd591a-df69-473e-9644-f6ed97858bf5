import sqlite3
import os

def check_document(session_id):
    """
    Checks for documents associated with a specific session_id in the database.
    """
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backend', 'data', 'aidatabase.db')
    
    if not os.path.exists(db_path):
        print(f"错误：数据库文件未找到 at {db_path}")
        return

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"正在查询 session_id: {session_id} 的文档...")
        
        query = "SELECT * FROM documents WHERE conversation_id = ?"
        cursor.execute(query, (session_id,))
        
        rows = cursor.fetchall()
        
        if rows:
            print(f"为 session_id '{session_id}' 找到了 {len(rows)} 条文档记录:")
            for row in rows:
                print(row)
        else:
            print(f"在数据库中没有找到 session_id '{session_id}' 的任何文档记录。")
            
    except sqlite3.Error as e:
        print(f"数据库查询出错: {e}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()

if __name__ == "__main__":
    # 从日志中获取的 session_id
    target_session_id = "27920140-aa3a-4494-8cba-0d17839a6c84"
    check_document(target_session_id)
