#!/usr/bin/env python3
"""
知识库命令行管理工具
无需Web服务器，直接在终端查看和管理ChromaDB
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.config.knowledge_base_config import get_knowledge_base_config_manager
import chromadb
from chromadb.utils import embedding_functions

class KnowledgeBaseCLI:
    def __init__(self):
        self.config_manager = get_knowledge_base_config_manager()
        self.config = self.config_manager.get_config()
        self._initialize_chromadb()
    
    def _initialize_chromadb(self):
        try:
            chroma_path = self.config.chroma_db.get('path', 'backend/data/chroma_db')
            collection_name = self.config.chroma_db.get('collection_name', 'hybrid_knowledge_base')
            embedding_model = self.config.chroma_db.get('embedding_model', 'moka-ai/m3e-base')
            
            self.chroma_client = chromadb.PersistentClient(path=chroma_path)
            self.embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
                model_name=embedding_model
            )
            self.collection = self.chroma_client.get_collection(
                name=collection_name,
                embedding_function=self.embedding_function
            )
            print("✅ 成功连接到ChromaDB")
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            sys.exit(1)
    
    def show_stats(self):
        """显示统计信息"""
        try:
            total_docs = self.collection.count()
            all_results = self.collection.get(include=["metadatas"])
            
            print(f"\n📊 知识库统计:")
            print(f"总文档数: {total_docs}")
            
            if all_results and all_results.get('metadatas'):
                from collections import Counter
                roles = [m.get('role') for m in all_results['metadatas'] if m.get('role')]
                categories = [m.get('category') for m in all_results['metadatas'] if m.get('category')]
                
                print(f"角色类型: {len(set(roles))}")
                print(f"分类数量: {len(set(categories))}")
                
                if roles:
                    print("\n角色分布:")
                    for role, count in Counter(roles).items():
                        print(f"  {role}: {count}")
                
                if categories:
                    print("\n分类分布:")
                    for cat, count in Counter(categories).items():
                        print(f"  {cat}: {count}")
                        
        except Exception as e:
            print(f"获取统计失败: {e}")
    
    def search_docs(self, query, top_k=5, role=None):
        """搜索文档"""
        try:
            query_params = {
                "query_texts": [query],
                "n_results": top_k,
                "include": ["documents", "metadatas", "distances"]
            }
            
            if role and role != "all":
                query_params["where"] = {"role": {"$eq": role}}
            
            results = self.collection.query(**query_params)
            
            print(f"\n🔍 查询: {query}")
            print("=" * 50)
            
            if results['documents'][0]:
                for i, (doc, metadata, distance) in enumerate(zip(
                    results['documents'][0],
                    results['metadatas'][0],
                    results['distances'][0]
                )):
                    similarity = 1 - distance
                    print(f"\n结果 {i+1} (相似度: {similarity:.3f})")
                    print(f"标题: {metadata.get('title', '未命名')}")
                    print(f"角色: {metadata.get('role', 'N/A')}")
                    print(f"分类: {metadata.get('category', 'N/A')}")
                    print(f"内容: {doc[:200]}...")
            else:
                print("没有找到相关文档")
                
        except Exception as e:
            print(f"查询失败: {e}")
    
    def list_docs(self, limit=10):
        """列出文档"""
        try:
            results = self.collection.get(
                include=["documents", "metadatas"],
                limit=limit
            )
            
            print(f"\n📋 文档列表 (显示前{limit}个):")
            print("=" * 50)
            
            for i, (doc, metadata) in enumerate(zip(results['documents'], results['metadatas'])):
                print(f"\n{i+1}. {metadata.get('title', '未命名')}")
                print(f"   角色: {metadata.get('role', 'N/A')}")
                print(f"   分类: {metadata.get('category', 'N/A')}")
                print(f"   长度: {len(doc)} 字符")
                
        except Exception as e:
            print(f"获取文档列表失败: {e}")

def main():
    cli = KnowledgeBaseCLI()
    
    print("\n" + "="*60)
    print("知识库命令行管理工具")
    print("="*60)
    
    while True:
        print("\n请选择操作:")
        print("1. 查看统计信息")
        print("2. 搜索文档")
        print("3. 列出文档")
        print("4. 退出")
        
        choice = input("\n输入选项 (1-4): ").strip()
        
        if choice == "1":
            cli.show_stats()
        elif choice == "2":
            query = input("输入搜索内容: ").strip()
            if query:
                role = input("角色过滤 (company/developer/general/all): ").strip()
                cli.search_docs(query, role=role if role != "all" else None)
        elif choice == "3":
            limit = input("显示数量 (默认10): ").strip()
            limit = int(limit) if limit.isdigit() else 10
            cli.list_docs(limit)
        elif choice == "4":
            print("再见!")
            break
        else:
            print("无效选项，请重试")

if __name__ == "__main__":
    main()
