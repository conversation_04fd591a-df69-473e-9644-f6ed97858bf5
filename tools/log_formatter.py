#!/usr/bin/env python3
"""
日志格式化工具 - 将JSON格式日志转换为易读格式
"""

import json
import sys
from datetime import datetime
from pathlib import Path
import argparse

class LogFormatter:
    def __init__(self):
        self.colors = {
            'INFO': '\033[32m',     # 绿色
            'DEBUG': '\033[36m',    # 青色
            'WARNING': '\033[33m',  # 黄色
            'ERROR': '\033[31m',    # 红色
            'CRITICAL': '\033[35m', # 紫色
            'RESET': '\033[0m'      # 重置
        }
        
    def format_timestamp(self, timestamp_str):
        """格式化时间戳"""
        try:
            dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S,%f")
            return dt.strftime("%H:%M:%S.%f")[:-3]  # 只保留3位毫秒
        except:
            return timestamp_str
    
    def format_log_entry(self, log_entry, use_colors=True):
        """格式化单条日志"""
        try:
            if isinstance(log_entry, str):
                data = json.loads(log_entry.strip())
            else:
                data = log_entry
                
            # 基本信息
            timestamp = self.format_timestamp(data.get('timestamp', ''))
            level = data.get('level', 'INFO')
            logger = data.get('logger', '').split('.')[-1]  # 只取最后一部分
            message = data.get('message', '')
            
            # 颜色处理
            color = self.colors.get(level, '') if use_colors else ''
            reset = self.colors['RESET'] if use_colors else ''
            
            # 基础格式
            formatted = f"{color}[{timestamp}] {level:5} {logger:20}{reset} | {message}"
            
            # 添加重要的上下文信息
            context_parts = []
            
            if data.get('session_id') and data['session_id'] != 'system':
                context_parts.append(f"会话:{data['session_id'][:8]}")
                
            if data.get('user_id') and data['user_id'] != 'user_default':
                context_parts.append(f"用户:{data['user_id']}")
                
            if data.get('stage') and data['stage'] != 'default':
                context_parts.append(f"阶段:{data['stage']}")
                
            if data.get('type'):
                context_parts.append(f"类型:{data['type']}")
                
            if context_parts:
                formatted += f" [{', '.join(context_parts)}]"
            
            return formatted
            
        except Exception as e:
            return f"[解析错误] {log_entry}"
    
    def format_file(self, input_file, output_file=None, use_colors=True):
        """格式化整个日志文件"""
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            formatted_lines = []
            for line in lines:
                if line.strip():
                    formatted_lines.append(self.format_log_entry(line, use_colors))
            
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(formatted_lines))
                print(f"格式化完成，输出到: {output_file}")
            else:
                for line in formatted_lines:
                    print(line)
                    
        except Exception as e:
            print(f"处理文件时出错: {e}")

def main():
    parser = argparse.ArgumentParser(description='日志格式化工具')
    parser.add_argument('input_file', help='输入的JSON日志文件')
    parser.add_argument('-o', '--output', help='输出文件路径（可选）')
    parser.add_argument('--no-color', action='store_true', help='禁用颜色输出')
    parser.add_argument('-f', '--follow', action='store_true', help='实时跟踪日志文件')
    
    args = parser.parse_args()
    
    formatter = LogFormatter()
    
    if args.follow:
        # 实时跟踪模式
        import time
        try:
            with open(args.input_file, 'r', encoding='utf-8') as f:
                # 先读取现有内容
                f.seek(0, 2)  # 移到文件末尾
                while True:
                    line = f.readline()
                    if line:
                        if line.strip():
                            print(formatter.format_log_entry(line, not args.no_color))
                    else:
                        time.sleep(0.1)
        except KeyboardInterrupt:
            print("\n停止跟踪")
    else:
        formatter.format_file(args.input_file, args.output, not args.no_color)

if __name__ == '__main__':
    main()