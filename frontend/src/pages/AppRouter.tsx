import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
// HomePage is no longer needed
// import HomePage from "./HomePage";
import ConversationPage from "./ConversationPage";

const AppRouter = () => (
  <Router>
    <Routes>
      {/* Route root path directly to ConversationPage */}
      <Route path="/" element={<ConversationPage />} />
      {/* Remove the /chat route as it's now the root */}
      {/* <Route path="/chat" element={<ConversationPage />} /> */}
    </Routes>
  </Router>
);

export default AppRouter;