import React from 'react';
import MessageItem from './MessageItem';

interface MessageListProps {
  messages: { text: string; sender: 'user' | 'ai' }[];
  isLoading?: boolean;
  isStreaming?: boolean;
  progressStage?: string;
  progressPercentage?: number;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  isLoading,
  isStreaming = false,
  progressStage,
  progressPercentage
}) => {
  return (
    <div className="space-y-4">
      {messages.map((message, index) => (
        <MessageItem
          key={index}
          message={message}
          isLoading={isLoading && index === messages.length - 1 && message.sender === 'ai'}
          isStreaming={isStreaming && index === messages.length - 1 && message.sender === 'ai'}
          progressStage={index === messages.length - 1 && message.sender === 'ai' ? progressStage : undefined}
          progressPercentage={index === messages.length - 1 && message.sender === 'ai' ? progressPercentage : undefined}
        />
      ))}
      {/* 当正在加载且最后一条消息是用户消息时，显示思考动画 */}
      {isLoading && messages.length > 0 && messages[messages.length - 1].sender === 'user' && (
        <MessageItem
          message={{ text: '', sender: 'ai' }}
          isLoading={true}
          isStreaming={false}
          progressStage={progressStage}
          progressPercentage={progressPercentage}
        />
      )}
    </div>
  );
};

export default MessageList;
