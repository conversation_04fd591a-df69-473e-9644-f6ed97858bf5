import React, { useState } from 'react';
import { Textarea } from '../components/ui/textarea'; // Import Textarea
import { Button } from '../components/ui/button';

interface InputAreaProps {
  onSendMessage: (text: string) => void;
  disabled?: boolean; // 添加 disabled 属性
}

const InputArea: React.FC<InputAreaProps> = ({ onSendMessage, disabled = false }) => {
  const [inputText, setInputText] = useState('');

  const handleSendMessage = () => {
    if (inputText.trim()) {
      onSendMessage(inputText.trim());
      setInputText('');
    }
  };

  // Updated handler for Textarea
  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault(); // Prevent default Enter behavior (new line)
      handleSendMessage();
    }
    // Allow Shift+Enter for new lines (default behavior)
  };

  return (
    <div className="relative flex w-full">
      {/* 主输入容器 - Gemini风格 */}
      <div className="relative flex w-full bg-white rounded-2xl border border-gray-200 shadow-sm hover:shadow-xl transition-all duration-200 focus-within:border-blue-300 focus-within:shadow-xl">
        <Textarea
          value={inputText}
          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setInputText(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="问一问 由己小助手..."
          disabled={disabled}
          className="flex-grow border-none focus-visible:ring-0 focus-visible:ring-offset-0 px-6 py-4 text-base font-normal leading-6 placeholder-gray-400 min-h-[100px] max-h-[300px] resize-none pr-16 bg-transparent rounded-3xl"
          style={{ fontSize: '1rem' }}
        />

        {/* 发送按钮 - 现代化设计 */}
        <Button
          onClick={handleSendMessage}
          className={`absolute bottom-2 right-2 p-3 rounded-full h-10 w-10 transition-all duration-200 ${
            inputText.trim() && !disabled
              ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg'
              : 'bg-gray-100 hover:bg-gray-200 text-gray-400'
          }`}
          variant="ghost"
          size="icon"
          disabled={!inputText.trim() || disabled}
        >
          {/* 发送图标 - 更现代的箭头设计 */}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            className="transform rotate-0"
          >
            <path
              d="M7 11L12 6L17 11M12 18V7"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </Button>
      </div>

      {/* 提示文字 */}
      <div className="absolute -bottom-6 left-2 text-xs text-gray-400">
        按 Enter 发送，Shift + Enter 换行
      </div>
    </div>
  );
};

export default InputArea;
