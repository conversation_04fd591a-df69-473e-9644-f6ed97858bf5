import React from 'react';
// Import shared types
import { DomainResult, CategoryResult, FocusPointStatus } from '../types';


interface AnalysisResultAreaProps {
  domainResult: DomainResult | null;
  categoryResult: CategoryResult | null;
  focusPointsStatus: FocusPointStatus[] | null; // Uses the imported FocusPointStatus now
}

const AnalysisResultArea: React.FC<AnalysisResultAreaProps> = ({
  domainResult,
  categoryResult,
  focusPointsStatus,
}) => {
  // 领域ID到名称的映射
  const getDomainName = (domainId: string): string => {
    const domainMapping: { [key: string]: string } = {
      'LY_001': '平面设计',
      'LY_002': 'UI/UX 设计',
      'LY_003': '营销推广',
      'LY_004': '法律咨询',
      'LY_005': '软件开发',
      'LY_100': '其他',
      // 兼容旧的ID格式（保持向后兼容）
      'web_development': 'Web开发',
      'mobile_development': '移动开发',
      'software_development': '软件开发',
      'design_creative': '设计创意',
      'business_management': '商业管理',
      'general': '通用领域'
    };
    return domainMapping[domainId] || domainId;
  };

  // 类别ID到名称的映射
  const getCategoryName = (categoryId: string): string => {
    const categoryMapping: { [key: string]: string } = {
      'LB_001': 'Web应用开发',
      'LB_002': '移动应用开发',
      'LB_003': '桌面应用开发',
      'LB_004': '系统软件开发',
      'LB_005': '游戏开发',
      'LB_006': '数据分析',
      'LB_007': '人工智能',
      'LB_008': '区块链',
      'LB_009': '用户界面设计',
      'LB_010': '用户体验设计',
      // 兼容旧的ID格式（保持向后兼容）
      'web_application': 'Web应用开发',
      'mobile_application': '移动应用开发',
      'desktop_application': '桌面应用开发',
      'system_software': '系统软件开发',
      'game_development': '游戏开发',
      'data_analysis': '数据分析',
      'artificial_intelligence': '人工智能',
      'blockchain': '区块链'
    };
    return categoryMapping[categoryId] || categoryId;
  };

  // Helper function to determine display status based on status field
  const getDisplayStatus = (status: string, value: string | null): string => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'processing':
        return '处理中';
      case 'pending':
        return '待处理';
      default:
        // Fallback to value-based logic for compatibility
        return value !== null ? '已覆盖' : '未覆盖';
    }
  };

  // Helper function to determine status color
  const getStatusColor = (status: string, value: string | null): string => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-700';
      case 'processing':
        return 'bg-blue-100 text-blue-700';
      case 'pending':
        return 'bg-orange-100 text-orange-700';
      default:
        // Fallback to value-based logic for compatibility
        const isCovered = value !== null;
        return isCovered ? 'bg-green-100 text-green-700' : 'bg-orange-100 text-orange-700';
    }
  };

  return (
    <div className="h-full bg-white flex flex-col">
      <div className="p-3 border-b border-gray-200 bg-gray-50 flex-shrink-0">
        <h3 className="text-base font-semibold text-gray-800 flex items-center">
          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></div>
          AI 分析结果
        </h3>
      </div>
      <div className="p-3 flex-1 overflow-y-auto text-sm flex flex-col space-y-3">

        {/* 领域分类结果区域 - 紧凑版 */}
        <div className="bg-white border border-gray-200 rounded-lg p-2 flex-shrink-0">
          <h4 className="text-xs font-medium mb-2 text-gray-800 flex items-center">
            <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1.5"></div>
            领域分类
          </h4>
          {domainResult ? (
            <div className="bg-gray-50 p-2 rounded-md space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-gray-600">领域ID:</span>
                <span className="text-xs text-gray-800">{domainResult.domain_id}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-gray-600">领域名称:</span>
                <span className="text-xs text-gray-800">{getDomainName(domainResult.domain_id)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-gray-600">置信度:</span>
                <span className="text-xs text-gray-800">{(domainResult.confidence * 100).toFixed(1)}%</span>
              </div>
              {domainResult.status && (
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-gray-600">状态:</span>
                  <span className={`text-xs px-1.5 py-0.5 rounded ${
                    domainResult.status === 'completed' ? 'bg-green-100 text-green-700' :
                    domainResult.status === 'processing' ? 'bg-blue-100 text-blue-700' :
                    'bg-orange-100 text-orange-700'
                  }`}>
                    {domainResult.status === 'completed' ? '已完成' :
                     domainResult.status === 'processing' ? '处理中' : '待处理'}
                  </span>
                </div>
              )}
              {domainResult.reasoning && (
                <div className="mt-1">
                  <span className="text-xs font-medium text-gray-600">推理过程:</span>
                  <p className="text-xs text-gray-700 mt-0.5 leading-relaxed">{domainResult.reasoning}</p>
                </div>
              )}
            </div>
          ) : (
            <p className="text-xs text-gray-500 italic">暂无领域分类结果。</p>
          )}
        </div>

        {/* 类别识别结果区域 - 紧凑版 */}
        <div className="bg-white border border-gray-200 rounded-lg p-2 flex-shrink-0">
          <h4 className="text-xs font-medium mb-2 text-gray-800 flex items-center">
            <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mr-1.5"></div>
            类别识别
          </h4>
          {categoryResult ? (
            <div className="bg-gray-50 p-2 rounded-md space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-gray-600">类别ID:</span>
                <span className="text-xs text-gray-800">{categoryResult.category_id}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-gray-600">类别名称:</span>
                <span className="text-xs text-gray-800">
                  {categoryResult.category_name || getCategoryName(categoryResult.category_id)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-gray-600">置信度:</span>
                <span className="text-xs text-gray-800">{(categoryResult.confidence * 100).toFixed(1)}%</span>
              </div>
              {categoryResult.status && (
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-gray-600">状态:</span>
                  <span className={`text-xs px-1.5 py-0.5 rounded ${
                    categoryResult.status === 'completed' ? 'bg-green-100 text-green-700' :
                    categoryResult.status === 'processing' ? 'bg-blue-100 text-blue-700' :
                    'bg-orange-100 text-orange-700'
                  }`}>
                    {categoryResult.status === 'completed' ? '已完成' :
                     categoryResult.status === 'processing' ? '处理中' : '待处理'}
                  </span>
                </div>
              )}
              {categoryResult.reasoning && (
                <div className="mt-1">
                  <span className="text-xs font-medium text-gray-600">推理过程:</span>
                  <p className="text-xs text-gray-700 mt-0.5 leading-relaxed">{categoryResult.reasoning}</p>
                </div>
              )}
            </div>
          ) : (
            <p className="text-xs text-gray-500 italic">暂无类别识别结果。</p>
          )}
        </div>

        {/* 关注点状态区域 - 填充剩余空间 */}
        <div className="bg-white border border-gray-200 rounded-lg p-3 flex-1 flex flex-col min-h-0">
          <h4 className="text-sm font-medium mb-2 text-gray-800 flex items-center flex-shrink-0">
            <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mr-2"></div>
            关注点状态
          </h4>

          {/* 整体进度概览 */}
          {focusPointsStatus && focusPointsStatus.length > 0 && (
            <div className="mb-3 p-2 bg-gray-50 rounded-md flex-shrink-0">
              {(() => {
                const completedCount = focusPointsStatus.filter(item => item.status === 'completed').length;
                const processingCount = focusPointsStatus.filter(item => item.status === 'processing').length;
                const totalCount = focusPointsStatus.length;
                const progressPercentage = (completedCount / totalCount) * 100;

                return (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium text-gray-700">
                        采集进度 ({completedCount}/{totalCount})
                      </span>
                      <span className="text-xs text-blue-600 font-medium">
                        {Math.round(progressPercentage)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-green-500 h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${progressPercentage}%` }}
                      ></div>
                    </div>
                    {processingCount > 0 && (
                      <div className="text-xs text-orange-600">
                        🎯 正在处理 {processingCount} 个关注点
                      </div>
                    )}
                  </div>
                );
              })()}
            </div>
          )}
          {focusPointsStatus && focusPointsStatus.length > 0 ? (
            <div className="space-y-1 flex-1 overflow-y-auto">
              {focusPointsStatus.map((item, index) => {
                const isCovered = item.value !== null;
                const displayStatus = getDisplayStatus(item.status, item.value);
                const statusColor = getStatusColor(item.status, item.value);
                return (
                  <div key={index} className="bg-gray-50 p-1.5 rounded-md">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center space-x-1.5 flex-1 min-w-0">
                        <span className={`text-xs font-medium px-1.5 py-0.5 rounded ${
                          item.priority === 'P0' ? 'bg-red-100 text-red-800' :
                          item.priority === 'P1' ? 'bg-yellow-100 text-yellow-800' :
                          item.priority === 'P2' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.priority}
                        </span>
                        <p className="text-xs text-gray-700 font-medium truncate">{item.point}</p>
                      </div>
                      <span className={`text-xs font-medium px-1.5 py-0.5 rounded ${statusColor} ml-2`}>
                        {displayStatus}
                      </span>
                    </div>
                    {isCovered && (
                      <div className="text-xs text-gray-600 bg-white p-1.5 rounded border mt-1" title={item.value ?? undefined}>
                        <span className="font-medium">值:</span>
                        <span className="ml-1 break-words">{item.value}</span>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <p className="text-xs text-gray-500 italic">暂无关注点状态信息。</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AnalysisResultArea;
