import React, { useState } from 'react';
import MessageItem from './MessageItem';

const TextSegmentationDemo: React.FC = () => {
  const [selectedExample, setSelectedExample] = useState('structured');

  const examples = {
    structured: {
      title: '结构化内容示例',
      content: `# 项目需求分析报告

## 功能需求

### 核心功能
- **用户管理系统**：支持用户注册、登录、权限管理
- **数据分析模块**：提供实时数据统计和可视化图表
- **文件管理**：支持文件上传、下载、在线预览

### 扩展功能
1. 消息通知系统
2. 多语言支持
3. 移动端适配

## 技术要求

**后端技术栈**：
- Node.js + Express
- MongoDB 数据库
- Redis 缓存

**前端技术栈**：
- React + TypeScript
- Tailwind CSS
- Chart.js 图表库

## 项目时间安排

| 阶段 | 时间 | 内容 |
|------|------|------|
| 第一阶段 | 2周 | 基础架构搭建 |
| 第二阶段 | 3周 | 核心功能开发 |
| 第三阶段 | 1周 | 测试和优化 |`
    },
    conversational: {
      title: '对话式内容示例',
      content: `您好！关于您提到的项目需求，我想了解更多细节。

首先，您希望这个系统支持多少用户同时在线？这会影响我们的架构设计选择。比如说，如果是小型团队使用（50人以下），我们可以选择相对简单的架构；如果是大型企业级应用（1000人以上），就需要考虑分布式架构了。

另外，对于数据安全有什么特殊要求吗？建议我们从以下几个方面来考虑：

- 数据加密存储
- 用户权限分级管理  
- 操作日志记录
- 定期数据备份

您觉得这个方向怎么样？我们可以根据您的具体需求来制定合适的技术方案。如果您有其他关注的点，也请告诉我。`
    },
    general: {
      title: '一般内容示例',
      content: `这是一个关于文本分段换行功能的演示。我们的系统现在可以智能识别不同类型的内容，并为每种类型提供最适合的显示效果。

对于结构化内容，系统会增加标题间距，优化列表显示，让层次结构更加清晰。对于对话式内容，系统会采用更紧凑的段落间距，让对话流程更加自然。

这种智能化的处理方式可以显著提升用户的阅读体验，让不同类型的内容都能以最佳的方式呈现给用户。

系统的核心优势包括：自动内容类型检测、智能段落分隔、响应式布局适配、以及良好的可维护性。`
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-gray-50 min-h-screen">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">
          文本分段换行功能演示
        </h1>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            选择内容类型：
          </label>
          <div className="flex space-x-4">
            {Object.entries(examples).map(([key, example]) => (
              <button
                key={key}
                onClick={() => setSelectedExample(key)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  selectedExample === key
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {example.title}
              </button>
            ))}
          </div>
        </div>

        <div className="border rounded-lg p-4 bg-gray-50">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">
            {examples[selectedExample as keyof typeof examples].title}
          </h2>
          
          <div className="bg-white rounded-md border">
            <MessageItem
              message={{
                text: examples[selectedExample as keyof typeof examples].content,
                sender: 'ai'
              }}
              isLoading={false}
              isStreaming={false}
            />
          </div>
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-md font-semibold text-blue-900 mb-2">
            功能特点：
          </h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• <strong>智能内容检测</strong>：自动识别结构化、对话式、一般内容</li>
            <li>• <strong>自适应样式</strong>：根据内容类型调整段落间距和标题样式</li>
            <li>• <strong>中文优化</strong>：针对中文阅读习惯优化分段逻辑</li>
            <li>• <strong>响应式设计</strong>：在不同屏幕尺寸下保持良好效果</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default TextSegmentationDemo;
