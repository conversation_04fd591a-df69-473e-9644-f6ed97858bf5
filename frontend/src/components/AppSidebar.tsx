import React from 'react';
import { Button } from '@/components/ui/button'; // Assuming Button is needed for "New Conversation"

const AppSidebar: React.FC = () => {
  // Placeholder data for conversations
  const conversations = [
    { title: '当前会话', active: true },
    { title: '需求采集示例1', active: false },
    { title: '需求采集示例2', active: false },
    { title: '需求采集示例3', active: false }
  ];

  return (
    <div className="flex flex-col h-screen w-64 bg-gray-100 border-r border-gray-200">
      {/* Logo Area Placeholder */}
      <div className="px-4 py-3 border-b border-gray-200">
        <h1 className="text-lg font-semibold text-gray-800">需求采集系统</h1>
      </div>

      {/* New Conversation Button */}
      <div className="px-3 py-2">
        <Button variant="outline" className="flex w-full items-center justify-center">
          {/* Plus icon placeholder */}
          <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M12 5v14M5 12h14"></path>
          </svg>
          新建会话
        </Button>
      </div>

      {/* Conversation Categories */}
      <div className="mt-2 px-4 text-xs text-gray-600">最近会话</div>

      {/* Conversation List */}
      <div className="flex-1 overflow-y-auto">
        <div className="flex flex-col">
          {conversations.map((conversation, index) => (
            <div
              key={index}
              className={`group flex items-center justify-between px-4 py-2 text-sm text-gray-700 hover:bg-gray-200 cursor-pointer ${
                conversation.active ? 'bg-gray-200 font-semibold' : ''
              }`}
            >
              <div className="truncate">{conversation.title}</div>
              {conversation.active && (
                <button className="invisible rounded p-1 text-gray-500 hover:bg-gray-300 group-hover:visible">
                  {/* More icon placeholder */}
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="1" />
                    <circle cx="19" cy="12" r="1" />
                    <circle cx="5" cy="12" r="1" />
                  </svg>
                </button>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AppSidebar;