import React, { useRef, useEffect } from 'react'; // Import useRef and useEffect
import MessageList from './MessageList';
import InputArea from './InputArea';

interface ChatAreaProps {
  messages: { text: string; sender: 'user' | 'ai' }[];
  isLoading?: boolean;
  isStreaming?: boolean;
  onSendMessage: (text: string) => void;
  progressStage?: string;
  progressPercentage?: number;
}

const ChatArea: React.FC<ChatAreaProps> = ({
  messages,
  isLoading,
  isStreaming = false,
  onSendMessage,
  progressStage,
  progressPercentage
}) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null); // Ref for the scrollable div

  // Function to scroll to the bottom
  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current;
      scrollElement.scrollTop = scrollElement.scrollHeight;

      // Force scroll to bottom with smooth behavior as fallback
      scrollElement.scrollTo({
        top: scrollElement.scrollHeight,
        behavior: 'smooth'
      });
    }
  };

  // Effect to scroll down when messages change
  useEffect(() => {
    // Add a small delay to ensure DOM is updated
    const timer = setTimeout(() => {
      scrollToBottom();
    }, 50); // Adjust delay if needed
    return () => clearTimeout(timer); // Cleanup timer
  }, [messages]); // Dependency on messages array

  // Effect to scroll down during streaming (when content is being typed)
  useEffect(() => {
    if (isStreaming) {
      const scrollInterval = setInterval(() => {
        scrollToBottom();
      }, 100); // Scroll every 100ms during streaming
      return () => clearInterval(scrollInterval);
    }
  }, [isStreaming]); // Dependency on streaming state

  return (
    <div className="flex flex-col h-full w-full bg-gray-50 overflow-hidden"> {/* Gemini 风格的浅灰背景 */}
      {/* Main scrollable message area */}
      <div className="flex-1 overflow-y-auto " ref={scrollAreaRef}>
        {/* Centered container with max-width */}
        <div className="mx-auto w-full px-0 py-8 pb-32" style={{ maxWidth: '760px' }}> {/* 增加底部间距pb-32，防止被输入框遮挡 */}
          <MessageList
            messages={messages}
            isLoading={isLoading}
            isStreaming={isStreaming}
            progressStage={progressStage}
            progressPercentage={progressPercentage}
          />
        </div>
      </div>

      {/* Fixed input area container (full width for background/border) */}
      <div className="bg-gradient-to-t from-white via-white to-gray-50/50  z-10"> {/* 更现代的渐变背景 */}
        {/* Centered input area with max-width */}
        <div className="mx-auto w-full px-0 py-6 pb-8" style={{ maxWidth: '760px' }}> {/* 自定义宽度760px，增加底部间距给提示文字留空间 */}
          <InputArea onSendMessage={onSendMessage} disabled={isLoading} />
        </div>
      </div>
    </div>
  );
};

export default ChatArea;
