import React, { useState, useEffect } from 'react';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, CheckCircle, Clock, Zap } from 'lucide-react';

interface ProgressIndicatorProps {
  visible: boolean;
  stage?: string;
  percentage?: number;
  message?: string;
  estimatedTime?: number;
  onComplete?: () => void;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  visible,
  stage = 'processing',
  percentage = 0,
  message = '正在处理...',
  estimatedTime,
  onComplete
}) => {
  const [currentPercentage, setCurrentPercentage] = useState(0);
  const [displayMessage, setDisplayMessage] = useState(message);

  // 平滑进度条动画
  useEffect(() => {
    if (percentage > currentPercentage) {
      const increment = (percentage - currentPercentage) / 10;
      const timer = setInterval(() => {
        setCurrentPercentage(prev => {
          const next = prev + increment;
          if (next >= percentage) {
            clearInterval(timer);
            return percentage;
          }
          return next;
        });
      }, 50);
      return () => clearInterval(timer);
    }
  }, [percentage, currentPercentage]);

  // 消息更新动画
  useEffect(() => {
    setDisplayMessage(message);
  }, [message]);

  // 完成回调
  useEffect(() => {
    if (percentage >= 100 && onComplete) {
      const timer = setTimeout(onComplete, 1000);
      return () => clearTimeout(timer);
    }
  }, [percentage, onComplete]);

  const getStageIcon = () => {
    switch (stage) {
      case 'starting':
        return <Zap className="w-4 h-4 text-blue-500" />;
      case 'analyzing':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'processing':
        return <Loader2 className="w-4 h-4 text-green-500 animate-spin" />;
      case 'extracting':
        return <Loader2 className="w-4 h-4 text-orange-500 animate-spin" />;
      case 'generating':
        return <Loader2 className="w-4 h-4 text-purple-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      default:
        return <Loader2 className="w-4 h-4 text-gray-500 animate-spin" />;
    }
  };

  const getProgressColor = () => {
    if (percentage >= 100) return 'bg-green-500';
    if (percentage >= 75) return 'bg-blue-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-gray-400';
  };

  if (!visible) return null;

  return (
    <Card className="w-full max-w-md mx-auto mb-4 shadow-lg border-l-4 border-l-blue-500">
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* 顶部：图标和消息 */}
          <div className="flex items-center space-x-3">
            {getStageIcon()}
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-800 leading-relaxed">
                {displayMessage}
              </p>
            </div>
          </div>

          {/* 进度条 */}
          <div className="space-y-2">
            <Progress 
              value={currentPercentage} 
              className="h-2"
            />
            <div className="flex justify-between items-center text-xs text-gray-500">
              <span>{Math.round(currentPercentage)}%</span>
              {estimatedTime && estimatedTime > 0 && (
                <div className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>预计还需 {estimatedTime}秒</span>
                </div>
              )}
            </div>
          </div>

          {/* 阶段指示器 */}
          <div className="flex justify-between items-center">
            <div className="flex space-x-1">
              {['starting', 'analyzing', 'processing', 'generating', 'completed'].map((stageKey, index) => (
                <div
                  key={stageKey}
                  className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                    index <= Math.floor(currentPercentage / 20) 
                      ? 'bg-blue-500' 
                      : 'bg-gray-200'
                  }`}
                />
              ))}
            </div>
            {percentage >= 100 && (
              <span className="text-xs text-green-600 font-medium">
                ✅ 完成
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProgressIndicator;
