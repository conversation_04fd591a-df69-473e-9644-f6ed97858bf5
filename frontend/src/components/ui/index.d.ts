declare module "@/components/ui/input" {
  import { ComponentProps } from "react";
  export function Input(props: ComponentProps<"input">): JSX.Element;
}

declare module "@/components/ui/button" {
  import { ComponentProps } from "react";
  
  type ButtonVariants = {
    variant: {
      default: string;
      destructive: string;
      outline: string;
      secondary: string;
      ghost: string;
      link: string;
    };
    size: {
      default: string;
      sm: string;
      lg: string;
      icon: string;
    };
    defaultVariants: {
      variant: "default";
      size: "default";
    };
  };

  export const buttonVariants: (options: ButtonVariants) => string;
  
  export function Button(
    props: ComponentProps<"button"> & {
      variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
      size?: "default" | "sm" | "lg" | "icon";
      asChild?: boolean;
    }
  ): JSX.Element;
}