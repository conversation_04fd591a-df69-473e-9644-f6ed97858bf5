export interface Message {
  text: string;
  sender: 'user' | 'ai';
}

export interface DomainResult {
  domain_id: string;
  confidence: number;
  reasoning?: string;
  status?: string;
}

export interface CategoryResult {
  category_id: string;
  category_name?: string;
  confidence: number;
  reasoning?: string;
  status?: string;
}

// Updated definition matching backend response structure
export interface FocusPointStatus {
  priority: string;
  point: string; // 关注点名称
  status: string; // 关注点状态 (pending, processing, completed)
  value: string | null; // 提取的值
}

// Type for the expected structure of the /chat API response data
export interface ChatResponseData {
    response: string;
    domain_result: DomainResult | null;
    category_result: CategoryResult | null;
    focus_points_status: FocusPointStatus[] | null; // Use the updated FocusPointStatus
    follow_up: string | null;
    session_id: string;
}