@tailwind base;
@tailwind components;
@tailwind utilities;

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 自定义动画已移除 - 只保留SVG渐变流动效果 */

@layer components {
  /* 增强的 Markdown 样式 */
  .prose.prose-sm {
    font-size: 16px !important;
    line-height: 28px !important;
    font-weight: 400 !important;
    letter-spacing: normal !important;
    color: #1B1C1D !important;
    max-width: none !important;
  }

  /* 标题样式 - 清晰的层次结构 */
  .prose.prose-sm h1 {
    font-size: 1.8rem !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin-top: 3rem !important;
    margin-bottom: 2rem !important;
    border-bottom: 1px solid #e5e7eb !important;
    padding-bottom: 2rem !important;
  }

  .prose.prose-sm h2 {
    font-size: 1.3rem !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin-top: 3rem !important;
    margin-bottom: 1rem !important;
  }

  .prose.prose-sm h3 {
    font-size: 1.1rem !important;
    font-weight: 500 !important;
    color: #4b5563 !important;
    margin-top: 1rem !important;
    margin-bottom: 0.5rem !important;
  }

  /* 段落样式 */
  .prose.prose-sm p {
    font-size: 16px !important;
    line-height: 28px !important;
    font-weight: 400 !important;
    letter-spacing: normal !important;
    margin-bottom: 1rem !important;
    color: #1B1C1D !important;
  }

  /* 列表间距  */
  .prose.prose-sm ul {
    margin-bottom: 2rem !important;
    padding-left: 1.5rem !important;
    list-style-type: disc !important;
  }

  .prose.prose-sm ol {
    margin-bottom: 1.5rem !important;
    padding-left: 1.5rem !important;
    list-style-type: decimal !important;
  }

  .prose.prose-sm li {
    font-size: 16px !important;
    line-height: 28px !important;
    font-weight: 400 !important;
    letter-spacing: normal !important;
    color: #1B1C1D !important;
  }

  /* 内容类型特定样式 */

  /* 结构化内容样式 - 更大的间距，更清晰的层次 */
  .prose.structured-content {
    line-height: 1.7 !important;
  }

  .prose.structured-content h1 {
    margin-top: 2rem !important;
    margin-bottom: 1.5rem !important;
    border-bottom: 2px solid #e5e7eb !important;
    padding-bottom: 0.5rem !important;
  }

  .prose.structured-content h2 {
    margin-top: 1.5rem !important;
    margin-bottom: 1rem !important;
  }

  .prose.structured-content ul,
  .prose.structured-content ol {
    margin-bottom: 1.5rem !important;
    margin-top: 0.5rem !important;
  }

  .prose.structured-content li {
    margin-bottom: 0.5rem !important;
  }

  /* 对话式内容样式 - 紧凑但易读 */
  .prose.conversational-content {
    line-height: 1.6 !important;
  }

  .prose.conversational-content p {
    margin-bottom: 0.75rem !important;
  }

  .prose.conversational-content ul,
  .prose.conversational-content ol {
    margin-bottom: 1rem !important;
  }

  /* 一般内容样式 - 平衡的间距 */
  .prose.general-content {
    line-height: 1.65 !important;
    margin-bottom: 1.5rem !important;
    color: #404040 !important;
    padding-left: 0.25rem !important;
  }

  .prose.prose-sm ul > li::marker {
    color: #111827 !important;
  }

  .prose.prose-sm ol > li::marker {
    color: #111827 !important;

    /* color: #3b82f6 !important; */
    /* font-weight: 500 !important; */
  }

  /* 强调文本 - 突出显示 */
  .prose.prose-sm strong {
    font-size: 16px !important;
    font-weight: 600 !important;
    letter-spacing: normal !important;
    color: #1B1C1D !important;
    /* 移除黄色背景 */
    /* background-color: #fef3c7 !important; */
    /* padding: 0.125rem 0.25rem !important; */
    /* border-radius: 0.25rem !important; */
  }

  /* 代码块 - 专业外观 */
  .prose.prose-sm code {
    font-size: 0.875rem !important;
    padding: 0.25rem 0.5rem !important;
    background-color: #f3f4f6 !important;
    border: 1px solid #d1d5db !important;
    border-radius: 0.375rem !important;
    color: #374151 !important;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important;
  }

  /* 移除引用块样式 - 在聊天中不使用 */

  /* 分隔线 */
  .prose.prose-sm hr {
    margin: 1.5rem 0 !important;
    border-color: #e5e7eb !important;
  }

  /* 用户消息文本 */
  .user-message-text {
    font-size: 1rem !important;
    line-height: 1.5 !important;
    color: #ffffff !important;
  }
}