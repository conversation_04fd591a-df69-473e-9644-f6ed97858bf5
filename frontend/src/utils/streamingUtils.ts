/**
 * 模拟流式输出效果
 * @param fullText 完整文本
 * @param onUpdate 每次更新回调
 * @param onComplete 完成回调
 * @param speed 字符间隔时间(ms)
 */
export const simulateStreaming = (
  fullText: string,
  onUpdate: (text: string) => void,
  onComplete: () => void,
  speed = 10 // 字符间隔时间(ms)
) => {
  let index = 0;
  
  const streamInterval = setInterval(() => {
    if (index < fullText.length) {
      index++;
      onUpdate(fullText.substring(0, index));
    } else {
      clearInterval(streamInterval);
      onComplete();
    }
  }, speed);
  
  return () => clearInterval(streamInterval); // 返回清理函数
};