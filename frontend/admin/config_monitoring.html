<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置监控 - 后台管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #7f8c8d;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .card h3 {
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-healthy { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-critical { background-color: #e74c3c; }
        .status-error { background-color: #e74c3c; }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            color: #7f8c8d;
        }
        
        .metric-value {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .health-score {
            font-size: 2em;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        
        .health-score.healthy { color: #27ae60; }
        .health-score.warning { color: #f39c12; }
        .health-score.critical { color: #e74c3c; }
        
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-warning {
            background: #f39c12;
        }
        
        .btn-warning:hover {
            background: #e67e22;
        }
        
        .actions {
            text-align: center;
            margin: 20px 0;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }
        
        .error {
            background: #fee;
            border: 1px solid #fcc;
            color: #c33;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .success {
            background: #efe;
            border: 1px solid #cfc;
            color: #3c3;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .details-section {
            margin-top: 30px;
        }
        
        .details-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .details-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            border-radius: 8px 8px 0 0;
        }
        
        .details-content {
            padding: 20px;
        }
        
        .issue-item {
            padding: 10px;
            border-left: 4px solid #e74c3c;
            background: #fdf2f2;
            margin: 10px 0;
            border-radius: 0 4px 4px 0;
        }
        
        .issue-file {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .issue-content {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            margin: 5px 0;
            border-radius: 3px;
        }
        
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 配置监控中心</h1>
            <p>监控系统配置健康度，防止硬编码回归</p>
        </div>
        
        <div class="actions">
            <button class="btn btn-success" onclick="refreshDashboard()">🔄 刷新数据</button>
            <button class="btn btn-warning" onclick="runFullCheck()">🔍 完整检查</button>
            <button class="btn" onclick="showProjectStats()">📊 项目统计</button>
        </div>
        
        <div id="loading" class="loading" style="display: none;">
            正在加载数据...
        </div>
        
        <div id="error-message" class="error" style="display: none;"></div>
        <div id="success-message" class="success" style="display: none;"></div>
        
        <div id="dashboard" class="dashboard-grid">
            <!-- 仪表板内容将通过JavaScript动态加载 -->
        </div>
        
        <div id="details" class="details-section">
            <!-- 详细信息将通过JavaScript动态加载 -->
        </div>
    </div>

    <script>
        // API基础URL
        const API_BASE = '/admin/config-monitoring';
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshDashboard();
        });
        
        // 刷新仪表板
        async function refreshDashboard() {
            showLoading(true);
            hideMessages();
            
            try {
                const response = await fetch(`${API_BASE}/dashboard`);
                const result = await response.json();
                
                if (result.success) {
                    renderDashboard(result.data);
                } else {
                    showError('获取仪表板数据失败');
                }
            } catch (error) {
                showError('网络请求失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }
        
        // 运行完整检查
        async function runFullCheck() {
            showLoading(true);
            hideMessages();
            
            try {
                const response = await fetch(`${API_BASE}/run-full-check`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    showSuccess('完整检查已启动，请稍后刷新查看结果');
                    // 3秒后自动刷新
                    setTimeout(refreshDashboard, 3000);
                } else {
                    showError('启动完整检查失败');
                }
            } catch (error) {
                showError('网络请求失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }
        
        // 显示项目统计
        async function showProjectStats() {
            try {
                const response = await fetch(`${API_BASE}/project-stats`);
                const result = await response.json();
                
                if (result.success) {
                    renderProjectStats(result.data);
                } else {
                    showError('获取项目统计失败');
                }
            } catch (error) {
                showError('网络请求失败: ' + error.message);
            }
        }
        
        // 渲染仪表板
        function renderDashboard(data) {
            const dashboard = document.getElementById('dashboard');
            
            const configHealth = data.config_health;
            const hardcodeScan = data.hardcode_scan;
            const projectStats = data.project_stats;
            
            dashboard.innerHTML = `
                <div class="card">
                    <h3>📊 配置健康度</h3>
                    <div class="health-score ${getHealthClass(configHealth.health_score)}">
                        ${configHealth.health_score.toFixed(1)}%
                    </div>
                    <div class="metric">
                        <span class="metric-label">状态</span>
                        <span class="metric-value">
                            <span class="status-indicator status-${configHealth.status.toLowerCase()}"></span>
                            ${getStatusText(configHealth.status)}
                        </span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">总配置项</span>
                        <span class="metric-value">${configHealth.total_configs}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">有效配置</span>
                        <span class="metric-value">${configHealth.valid_configs}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">缺失配置</span>
                        <span class="metric-value">${configHealth.missing_configs}</span>
                    </div>
                </div>
                
                <div class="card">
                    <h3>🔍 硬编码扫描</h3>
                    <div class="metric">
                        <span class="metric-label">风险等级</span>
                        <span class="metric-value">
                            <span class="status-indicator status-${getRiskClass(hardcodeScan.risk_level)}"></span>
                            ${hardcodeScan.risk_level}
                        </span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">扫描文件</span>
                        <span class="metric-value">${hardcodeScan.scanned_files}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">潜在问题</span>
                        <span class="metric-value">${hardcodeScan.potential_issues}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">扫描状态</span>
                        <span class="metric-value">${hardcodeScan.status}</span>
                    </div>
                </div>
                
                <div class="card">
                    <h3>📈 项目进展</h3>
                    <div class="metric">
                        <span class="metric-label">整体进度</span>
                        <span class="metric-value">${projectStats.overall_progress}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">处理文件</span>
                        <span class="metric-value">${projectStats.processed_files}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">消除硬编码</span>
                        <span class="metric-value">${projectStats.eliminated_hardcodes}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">减少率</span>
                        <span class="metric-value">${projectStats.hardcode_reduction_rate}</span>
                    </div>
                </div>
            `;
            
            // 更新最后更新时间
            const lastUpdate = new Date(data.last_update).toLocaleString('zh-CN');
            document.querySelector('.header p').textContent = `监控系统配置健康度，防止硬编码回归 | 最后更新: ${lastUpdate}`;
        }
        
        // 渲染项目统计
        function renderProjectStats(data) {
            const details = document.getElementById('details');
            
            details.innerHTML = `
                <div class="details-card">
                    <div class="details-header">
                        <h3>📊 ${data.project_name} - 统计信息</h3>
                    </div>
                    <div class="details-content">
                        <div class="metric">
                            <span class="metric-label">当前阶段</span>
                            <span class="metric-value">${data.current_phase}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">整体进度</span>
                            <span class="metric-value">${data.overall_progress}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">已完成阶段</span>
                            <span class="metric-value">${data.statistics.completed_phases} / ${data.statistics.total_phases}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">处理文件数</span>
                            <span class="metric-value">${data.statistics.processed_files}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">消除硬编码</span>
                            <span class="metric-value">${data.statistics.eliminated_hardcodes} (高危: ${data.statistics.high_risk_eliminated}, 中危: ${data.statistics.medium_risk_eliminated})</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">配置模板数</span>
                            <span class="metric-value">${data.statistics.config_templates}</span>
                        </div>
                        
                        <h4 style="margin-top: 20px; margin-bottom: 10px;">项目里程碑</h4>
                        ${data.milestones.map(milestone => `
                            <div class="metric">
                                <span class="metric-label">${milestone.phase}</span>
                                <span class="metric-value">
                                    <span class="status-indicator status-${milestone.status === '完成' ? 'healthy' : 'warning'}"></span>
                                    ${milestone.status} - ${milestone.description}
                                </span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
        
        // 工具函数
        function getHealthClass(score) {
            if (score >= 95) return 'healthy';
            if (score >= 80) return 'warning';
            return 'critical';
        }
        
        function getStatusText(status) {
            const statusMap = {
                'HEALTHY': '健康',
                'WARNING': '警告',
                'CRITICAL': '严重',
                'ERROR': '错误'
            };
            return statusMap[status] || status;
        }
        
        function getRiskClass(risk) {
            const riskMap = {
                'LOW': 'healthy',
                'MEDIUM': 'warning',
                'HIGH': 'critical'
            };
            return riskMap[risk] || 'error';
        }
        
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        
        function showSuccess(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }
        
        function hideMessages() {
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-message').style.display = 'none';
        }
    </script>
</body>
</html>
